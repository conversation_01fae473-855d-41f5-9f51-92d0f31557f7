<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Welcome to {{ $subscription->plan->name }} Plan!</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background-color: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }
        .logo {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .welcome-badge {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: bold;
            display: inline-block;
            margin: 20px 0;
            font-size: 18px;
        }
        .plan-details {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 25px;
            border-radius: 10px;
            margin: 25px 0;
            text-align: center;
        }
        .features-list {
            background-color: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .feature-item {
            padding: 8px 0;
            border-bottom: 1px solid #e9ecef;
        }
        .feature-item:last-child {
            border-bottom: none;
        }
        .button {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 15px 30px;
            text-decoration: none;
            border-radius: 25px;
            font-weight: bold;
            display: inline-block;
            margin: 15px 0;
        }
        .billing-info {
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #2196f3;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e9ecef;
            color: #6c757d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🎉 DeliveryNexus</div>
            <h1>Welcome to Your New Plan!</h1>
            <div class="welcome-badge">{{ $subscription->plan->name }} ACTIVATED</div>
        </div>

        <div class="content">
            <p>Hi {{ $user->name }}! 👋</p>
            
            <p>Congratulations! Your subscription to the <strong>{{ $subscription->plan->name }}</strong> plan has been successfully activated.</p>

            <div class="plan-details">
                <h3>📋 Your Plan Details</h3>
                <p><strong>Plan:</strong> {{ $subscription->plan->name }}</p>
                <p><strong>Price:</strong> ₦{{ number_format($subscription->plan->price, 2) }}/{{ $subscription->plan->billing_cycle }}</p>
                <p><strong>Started:</strong> {{ $subscription->starts_at->format('M d, Y') }}</p>
                <p><strong>Next Billing:</strong> {{ $subscription->ends_at->format('M d, Y') }}</p>
            </div>

            <div class="features-list">
                <h4>✨ What's Included in Your Plan</h4>
                @if(isset($planFeatures) && count($planFeatures) > 0)
                    @foreach($planFeatures as $feature)
                        <div class="feature-item">
                            <strong>✅ {{ $feature['name'] }}</strong>
                            @if(isset($feature['description']))
                                <br><small style="color: #6c757d;">{{ $feature['description'] }}</small>
                            @endif
                        </div>
                    @endforeach
                @else
                    <div class="feature-item">✅ Full access to DeliveryNexus platform</div>
                    <div class="feature-item">✅ Priority customer support</div>
                    <div class="feature-item">✅ Advanced analytics and reporting</div>
                    <div class="feature-item">✅ API access for integrations</div>
                    <div class="feature-item">✅ Custom branding options</div>
                @endif
            </div>

            <div class="billing-info">
                <h4>💳 Billing Information</h4>
                <p><strong>Billing Cycle:</strong> {{ ucfirst($subscription->plan->billing_cycle) }}</p>
                <p><strong>Auto-Renewal:</strong> {{ $subscription->auto_renew ? 'Enabled' : 'Disabled' }}</p>
                <p><strong>Payment Method:</strong> {{ $subscription->payment_method ?? 'Card ending in ****' }}</p>
                <p><em>You'll receive a reminder before your next billing date.</em></p>
            </div>

            <div style="background: #d4edda; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h4>🚀 Getting Started</h4>
                <p>• Explore your new features in the dashboard</p>
                <p>• Set up your business profile and preferences</p>
                <p>• Invite team members to collaborate</p>
                <p>• Check out our getting started guide</p>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ url('/dashboard') }}" class="button">🎯 Go to Dashboard</a>
                <a href="{{ url('/subscription') }}" class="button" style="background: linear-gradient(135deg, #6c757d, #5a6268);">⚙️ Manage Subscription</a>
            </div>

            <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <p><strong>💡 Need Help?</strong> Our support team is here to help you make the most of your subscription. Contact us anytime!</p>
            </div>

            <p>Thank you for choosing DeliveryNexus! We're excited to help you grow your business.</p>
            
            <p>Welcome aboard! 🎉<br>
            <strong>The DeliveryNexus Team</strong></p>
        </div>

        <div class="footer">
            <p>This subscription confirmation was sent to {{ $user->email }}.</p>
            <p>Subscription ID: {{ $subscription->id }}</p>
            <p>DeliveryNexus | Lagos, Nigeria | {{ $supportEmail }}</p>
            <p>© {{ date('Y') }} DeliveryNexus. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
