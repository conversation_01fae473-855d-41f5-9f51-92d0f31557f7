<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Webhook Delivery Failed - DeliveryNexus</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f8f9fa;
        }
        .container {
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #dc3545;
        }
        .logo {
            font-size: 28px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }
        .error-badge {
            background: linear-gradient(135deg, #dc3545, #c82333);
            color: white;
            padding: 15px 25px;
            border-radius: 50px;
            display: inline-block;
            font-weight: bold;
            margin: 20px 0;
        }
        .webhook-info {
            background: #f8d7da;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #dc3545;
            margin: 20px 0;
        }
        .error-details {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 15px 0;
            border: 1px solid #dee2e6;
        }
        .retry-info {
            background: #fff3cd;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .button {
            background: #dc3545;
            color: white;
            padding: 12px 30px;
            text-decoration: none;
            border-radius: 5px;
            display: inline-block;
            margin: 10px 5px;
            font-weight: bold;
        }
        .footer {
            text-align: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #dee2e6;
            color: #6c757d;
            font-size: 14px;
        }
        @media (max-width: 600px) {
            .container { padding: 20px; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">🚚 DeliveryNexus</div>
            <h1>Webhook Delivery Failed</h1>
            <div class="error-badge">❌ DELIVERY FAILED</div>
        </div>

        <div class="content">
            <p>Hi {{ $user->name }}! ⚠️</p>
            
            <p>We encountered an issue delivering a webhook to your endpoint. Your application may not have received important event notifications.</p>

            <div class="webhook-info">
                <h3 style="color: #721c24; margin-top: 0;">🔗 Webhook Details</h3>
                <p><strong>Webhook Name:</strong> {{ $webhook->name }}</p>
                <p><strong>Endpoint URL:</strong> {{ $webhook->url }}</p>
                <p><strong>Event Type:</strong> {{ $eventType }}</p>
                <p><strong>Failure Time:</strong> {{ now()->format('M j, Y g:i A') }}</p>
                <p><strong>Attempt Number:</strong> {{ $attemptNumber }} of {{ $maxAttempts }}</p>
                <p><strong>HTTP Status:</strong> {{ $httpStatus ?? 'Connection Failed' }}</p>
            </div>

            <h3>🚨 Error Information</h3>
            <div class="error-details">
<strong>Error Message:</strong>
{{ $errorMessage ?? 'Connection timeout or endpoint unreachable' }}

<strong>Response Body:</strong>
{{ $responseBody ?? 'No response received' }}

<strong>Request Headers:</strong>
Content-Type: application/json
User-Agent: DeliveryNexus-Webhook/1.0
X-Webhook-Signature: {{ $signature ?? 'sha256=...' }}
            </div>

            <h3>🔄 Retry Information</h3>
            <div class="retry-info">
                <h4 style="color: #856404; margin-top: 0;">Automatic Retry Schedule</h4>
                @if($attemptNumber < $maxAttempts)
                <p><strong>Next Retry:</strong> {{ $nextRetryAt->format('M j, Y g:i A') }}</p>
                <p><strong>Remaining Attempts:</strong> {{ $maxAttempts - $attemptNumber }}</p>
                <p><strong>Retry Intervals:</strong> 1min, 5min, 15min, 1hr, 6hr</p>
                @else
                <p><strong>⚠️ Maximum Retries Reached</strong></p>
                <p>This webhook has been automatically disabled after {{ $maxAttempts }} failed attempts.</p>
                <p>Please fix the issue and re-enable the webhook manually.</p>
                @endif
            </div>

            <h3>🛠️ Troubleshooting Steps</h3>
            <ol>
                <li><strong>Check Endpoint Status:</strong> Ensure your server is running and accessible</li>
                <li><strong>Verify URL:</strong> Confirm the webhook URL is correct and reachable</li>
                <li><strong>Check SSL Certificate:</strong> Ensure HTTPS endpoints have valid certificates</li>
                <li><strong>Review Response Format:</strong> Your endpoint should return HTTP 200-299 status</li>
                <li><strong>Check Firewall:</strong> Ensure our IPs are not blocked</li>
                <li><strong>Validate Signature:</strong> Verify webhook signature validation is working</li>
            </ol>

            <div style="background: #e2e3e5; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <h4 style="margin-top: 0;">📋 Expected Response Format</h4>
                <div class="error-details">
HTTP/1.1 200 OK
Content-Type: application/json

{
  "status": "received",
  "message": "Webhook processed successfully"
}
                </div>
            </div>

            <h3>🔧 Common Issues & Solutions</h3>
            <div style="background: #d1ecf1; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <ul>
                    <li><strong>Timeout (504):</strong> Increase server response time or optimize processing</li>
                    <li><strong>SSL Error:</strong> Update SSL certificate or check certificate chain</li>
                    <li><strong>404 Not Found:</strong> Verify the webhook endpoint path is correct</li>
                    <li><strong>500 Server Error:</strong> Check server logs for application errors</li>
                    <li><strong>Connection Refused:</strong> Ensure the service is running on the correct port</li>
                </ul>
            </div>

            <div style="text-align: center; margin: 30px 0;">
                <a href="{{ url('/developer/webhooks/' . $webhook->id) }}" class="button">🔧 Edit Webhook</a>
                <a href="{{ url('/developer/webhooks/' . $webhook->id . '/test') }}" class="button" style="background: #28a745;">🧪 Test Endpoint</a>
                @if($attemptNumber >= $maxAttempts)
                <a href="{{ url('/developer/webhooks/' . $webhook->id . '/enable') }}" class="button" style="background: #ffc107; color: #333;">🔄 Re-enable</a>
                @endif
            </div>

            <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <h4 style="color: #856404; margin-top: 0;">💡 Best Practices</h4>
                <ul style="margin: 10px 0;">
                    <li>Implement idempotency to handle duplicate webhooks</li>
                    <li>Return HTTP 200 status as quickly as possible</li>
                    <li>Process webhook data asynchronously if needed</li>
                    <li>Log webhook requests for debugging purposes</li>
                    <li>Implement proper error handling and monitoring</li>
                </ul>
            </div>

            <div style="background: #f8d7da; padding: 15px; border-radius: 5px; margin: 20px 0;">
                <p><strong>⚠️ Important:</strong> If webhooks continue to fail, your integration may miss critical events like order updates, payment confirmations, or status changes. Please resolve this issue promptly.</p>
            </div>

            <p>If you need assistance debugging webhook issues, our developer support team is here to help!</p>
            
            <p>Best regards,<br>
            <strong>The DeliveryNexus Developer Team</strong></p>
        </div>

        <div class="footer">
            <p>This email was sent to {{ $user->email }}.</p>
            <p>DeliveryNexus Developer Services | Lagos, Nigeria | developers@{{ $emailDomain }}</p>
            <p>© {{ date('Y') }} DeliveryNexus. All rights reserved.</p>
        </div>
    </div>
</body>
</html>
