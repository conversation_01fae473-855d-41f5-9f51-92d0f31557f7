# KYC (Know Your Customer) System

## Overview

DeliveryNexus implements a comprehensive KYC verification system using QoreID for unified identity verification. The system provides automated, cost-effective, and secure verification for all user types across the platform.

## Architecture

### Core Components

1. **QoreIdService** - Handles all QoreID API interactions
2. **UnifiedKycService** - Orchestrates verification workflow and business logic
3. **UnifiedKycController** - RESTful API endpoints for verification
4. **KycWorkflowService** - Legacy compatibility and workflow management

### Verification Types

#### Comprehensive Verification
- **BVN (Bank Verification Number)** - Banking identity verification
- **NIN (National Identification Number)** - Government identity verification
- **Bank Account** - Account ownership verification
- **Risk Assessment** - AI-powered fraud detection

#### Individual Component Verification
- **BVN Only** - Quick banking verification
- **NIN Only** - Government identity check
- **Bank Account Only** - Account name resolution

## API Endpoints

### Base URL
All KYC endpoints are available under `/api/v1/kyc/`

### Authentication
All endpoints require user authentication via Laravel Sanctum token.

### Endpoints

#### 1. Comprehensive Verification
```http
POST /api/v1/kyc/verify
```

**Request Body:**
```json
{
  "verification_type": "comprehensive",
  "bvn": "***********",
  "nin": "***********", 
  "account_number": "**********",
  "bank_code": "044",
  "target_level": "advanced",
  "customer_data": {
    "first_name": "John",
    "last_name": "Doe",
    "phone_number": "***********",
    "date_of_birth": "1990-01-01"
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "verification_score": 95,
    "risk_level": "low",
    "kyc_level": "advanced",
    "reference": "QOREID_KYC_**********_abc123",
    "qoreid_reference": "QID_REF_123456"
  },
  "message": "KYC verification completed successfully"
}
```

#### 2. Component Verification
```http
POST /api/v1/kyc/verify-component
```

**Request Body:**
```json
{
  "type": "bvn",
  "bvn": "***********",
  "customer_data": {
    "first_name": "John",
    "last_name": "Doe"
  }
}
```

#### 3. Get Verification Status
```http
GET /api/v1/kyc/status
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user_id": "uuid",
    "current_kyc_level": "intermediate",
    "overall_score": 85,
    "risk_level": "low",
    "completion_status": {
      "required_count": 5,
      "completed_count": 4,
      "completion_percentage": 80.0,
      "is_complete": false
    },
    "verifications": {
      "bvn": {
        "type": "bvn",
        "status": "verified",
        "verified_at": "2024-01-01T12:00:00Z",
        "score": 95
      },
      "bank_account": {
        "type": "bank_account", 
        "status": "verified",
        "verified_at": "2024-01-01T11:00:00Z",
        "score": 100
      }
    },
    "next_steps": [
      {
        "step": "nin_verification",
        "title": "Verify NIN",
        "description": "Verify your National Identification Number",
        "priority": "high"
      }
    ],
    "benefits": [
      "Access to interstate delivery",
      "Higher transaction limits",
      "Premium support"
    ]
  }
}
```

#### 4. Generate KYC Report
```http
GET /api/v1/kyc/report
```

#### 5. Get Verification Methods
```http
GET /api/v1/kyc/methods
```

#### 6. Get Supported Banks
```http
GET /api/v1/kyc/banks
```

#### 7. Get Pricing Information
```http
GET /api/v1/kyc/pricing
```

#### 8. Get Verification Statistics
```http
GET /api/v1/kyc/statistics
```

## KYC Levels

### Basic Level
- **Requirements**: Email and phone verification
- **Benefits**: Basic platform access
- **Transaction Limits**: ₦50,000 per transaction
- **Features**: Local delivery only

### Intermediate Level  
- **Requirements**: Basic + bank account verification
- **Benefits**: Enhanced platform access
- **Transaction Limits**: ₦500,000 per transaction
- **Features**: Interstate delivery, business features

### Advanced Level
- **Requirements**: Intermediate + BVN/NIN verification
- **Benefits**: Full platform access
- **Transaction Limits**: Unlimited
- **Features**: Enterprise features, API access, premium support

## Verification Scores

### Score Ranges
- **90-100**: Excellent - Low risk, full access
- **70-89**: Good - Medium risk, standard access
- **50-69**: Fair - Medium-high risk, limited access
- **0-49**: Poor - High risk, restricted access

### Risk Levels
- **Low Risk** (Score 85+): Minimal fraud risk
- **Medium Risk** (Score 65-84): Standard monitoring
- **High Risk** (Score <65): Enhanced monitoring required

## Configuration

### Environment Variables
```env
# QoreID Configuration
QOREID_ENABLED=true
QOREID_API_KEY=your_api_key
QOREID_SECRET_KEY=your_secret_key
QOREID_BASE_URL=https://api.qoreid.com/v1
QOREID_WORKFLOW_ID=your_workflow_id
QOREID_WEBHOOK_SECRET=your_webhook_secret

# KYC Settings
UNIFIED_KYC_ENABLED=true
KYC_DEFAULT_METHOD=qoreid
KYC_CACHE_ENABLED=true
KYC_CACHE_TTL=86400
KYC_RATE_LIMITING_ENABLED=true
KYC_MAX_ATTEMPTS=10
KYC_RATE_WINDOW=60
```

### Feature Flags
Located in `config/kyc.php`:

```php
'unified_kyc_enabled' => env('UNIFIED_KYC_ENABLED', true),
'default_verification_method' => env('KYC_DEFAULT_METHOD', 'qoreid'),
'score_thresholds' => [
    'basic' => 50,
    'intermediate' => 70, 
    'advanced' => 90,
],
'cache' => [
    'enabled' => env('KYC_CACHE_ENABLED', true),
    'ttl' => env('KYC_CACHE_TTL', 86400),
],
```

## Database Schema

### Tables

#### qoreid_verifications
```sql
CREATE TABLE qoreid_verifications (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    verification_type VARCHAR(50),
    qoreid_reference VARCHAR(255) UNIQUE,
    our_reference VARCHAR(255),
    verification_data JSONB,
    verification_score INTEGER,
    risk_level VARCHAR(50),
    status ENUM('pending', 'completed', 'failed'),
    verified_at TIMESTAMP,
    created_at TIMESTAMP,
    updated_at TIMESTAMP
);
```

#### identity_verifications (updated)
- Added `qoreid_reference` column
- Added `risk_level` column

#### bank_account_verifications (updated)  
- Added `qoreid_reference` column
- Added `risk_level` column

## Error Handling

### Common Error Codes
- `KYC_VERIFICATION_FAILED` - General verification failure
- `INVALID_BVN_FORMAT` - BVN format validation error
- `INVALID_NIN_FORMAT` - NIN format validation error
- `QOREID_SERVICE_ERROR` - QoreID API error
- `RATE_LIMIT_EXCEEDED` - Too many verification attempts

### Error Response Format
```json
{
  "success": false,
  "error": {
    "code": "KYC_VERIFICATION_FAILED",
    "message": "BVN verification failed",
    "details": {
      "field": "bvn",
      "reason": "Invalid BVN number"
    }
  }
}
```

## Security

### Data Protection
- All sensitive data encrypted in transit (HTTPS)
- PII data encrypted at rest
- BVN/NIN numbers hashed for storage
- Audit logging for all verification attempts

### Rate Limiting
- 10 verification attempts per hour per user
- IP-based rate limiting for abuse prevention
- Exponential backoff for failed attempts

### Compliance
- NDPR (Nigeria Data Protection Regulation) compliant
- PCI DSS compliance for payment data
- SOC 2 Type II compliance through QoreID

## Monitoring & Analytics

### Metrics Tracked
- Verification success rates by type
- Average verification time
- Cost per verification
- User completion rates by KYC level
- Fraud detection accuracy

### Logging
- All verification attempts logged
- Performance metrics tracked
- Error rates monitored
- Cost tracking per verification

## Testing

### Mock Responses
Development and testing environments support mock responses:

```env
KYC_MOCK_RESPONSES=true
KYC_SKIP_VERIFICATION=true
KYC_AUTO_APPROVE=true
```

### Test Data
```json
{
  "test_bvn": "***********",
  "test_nin": "***********", 
  "test_account": "**********",
  "test_bank_code": "044"
}
```

## Migration from Legacy System

See [KYC Migration Guide](./kyc-migration.md) for detailed migration information.

## Support

### Troubleshooting
1. Check application logs: `storage/logs/laravel.log`
2. Verify QoreID service status
3. Check rate limiting status
4. Validate input data format

### QoreID Support
- Documentation: https://docs.qoreid.com
- Support Email: <EMAIL>
- Status Page: https://status.qoreid.com

### Internal Support
- Check feature flags in `config/kyc.php`
- Review environment variables
- Monitor verification success rates
- Use mock responses for testing
