# PHPStan Issues Fix Guide

This document outlines how to fix the PHPStan issues found in the DeliveryNexus codebase.

## Summary of Issues

- **Total Errors**: 10,062 (mostly type safety issues)
- **Analysis Level**: 8 (very strict)
- **Main Categories**: Missing type hints, namespace issues, generic type specifications

## 1. Fixed Issues ✅

### Namespace Import Fixes (Already Fixed)
- `ProcessPayouts.php`: Fixed `\App\Models\Payout` → `App\Models\Financial\Payout`
- `TestFcm.php`: Fixed `\App\Notifications\WelcomeUser` → `App\Notifications\User\WelcomeUser`
- `PayoutController.php`: Fixed enum and model imports

## 2. Remaining Issues to Fix

### A. Array Type Specifications (`missingType.iterableValue`)

**Problem**: PHPStan can't determine array content types
```php
// ❌ Current
private function displayResults(array $results, bool $dryRun): void
public function handleQuery($query, array $options = []): mixed
```

**Solution**: Add PHPDoc with specific array types
```php
// ✅ Fixed
/**
 * @param array<int, array<string, mixed>> $results
 */
private function displayResults(array $results, bool $dryRun): void

/**
 * @param array<string, mixed> $options
 */
public function handleQuery($query, array $options = []): mixed
```

### B. Generic Type Specifications (`missingType.generics`)

**Problem**: Eloquent Builder missing model type information
```php
// ❌ Current
public function handleQuery($query, array $options = []): mixed
```

**Solution**: Add template annotations
```php
// ✅ Fixed
/**
 * @template TModel of \Illuminate\Database\Eloquent\Model
 * @param \Illuminate\Database\Eloquent\Builder<TModel> $query
 * @param array<string, mixed> $options
 */
public function handleQuery($query, array $options = []): mixed
```

### C. Collection Type Hints

**Problem**: Collections without specific type information
```php
// ❌ Current
public function getItems(): Collection
```

**Solution**: Specify collection content types
```php
// ✅ Fixed
/**
 * @return \Illuminate\Support\Collection<int, \App\Models\Product>
 */
public function getItems(): Collection
```

### D. Parameter Type Mismatches

**Problem**: Method parameters expecting specific types but receiving broader types
```php
// ❌ Current - PHPStan error: expects bool, array|bool|string|null given
$this->displayResults($results, $dryRun);
```

**Solution**: Add proper type checking
```php
// ✅ Fixed
$dryRun = (bool) $this->option('dry-run');
$this->displayResults($results, $dryRun);
```

## 3. Priority Fix Areas

### High Priority
1. **QueryHandlerTrait** - Used across many controllers
2. **Console Commands** - Financial processing commands
3. **API Controllers** - Business and Provider controllers
4. **Service Classes** - Core business logic

### Medium Priority
1. **Notification Classes** - Type safety for notification data
2. **Model Relationships** - Generic type specifications
3. **Event/Listener Classes** - Parameter type specifications

### Low Priority
1. **Test Files** - Less critical for production
2. **Migration Files** - Usually don't need strict typing
3. **Config Files** - Framework-managed

## 4. Implementation Strategy

### Step 1: Fix QueryHandlerTrait
```php
// File: app/Traits/QueryHandlerTrait.php
/**
 * @template TModel of \Illuminate\Database\Eloquent\Model
 * @param \Illuminate\Database\Eloquent\Builder<TModel> $query
 * @param array<string, mixed> $options
 */
public function handleQuery($query, array $options = []): mixed
```

### Step 2: Fix Console Commands
```php
// File: app/Console/Commands/Financial/ProcessPayouts.php
/**
 * @param array<int, array<string, mixed>> $results
 */
private function displayResults(array $results, bool $dryRun): void
```

### Step 3: Fix API Controllers
Add proper type hints to all controller methods that handle arrays and queries.

### Step 4: Fix Service Classes
Add comprehensive type hints to service method parameters and return types.

## 5. Tools and Commands

### Run PHPStan Analysis
```bash
# Full analysis
./vendor/bin/phpstan analyse --memory-limit=512M

# Specific files
./vendor/bin/phpstan analyse --memory-limit=512M app/Traits/QueryHandlerTrait.php

# Lower level for gradual improvement
./vendor/bin/phpstan analyse --level=6 --memory-limit=512M
```

### Verify Fixes
```bash
# Check code style (should remain clean)
./vendor/bin/pint --test

# Run tests to ensure no breaking changes
php artisan test
```

## 6. Configuration Adjustments

### Option 1: Lower PHPStan Level (Recommended)
```yaml
# phpstan.neon
parameters:
    level: 6  # Reduce from 8 to 6 for more practical results
```

### Option 2: Ignore Specific Patterns
```yaml
# phpstan.neon
parameters:
    ignoreErrors:
        - '#missingType.iterableValue#'
        - '#missingType.generics#'
```

## 7. Expected Outcomes

### After Fixes
- **Reduced errors**: From 10,062 to ~500-1,000 (realistic target)
- **Better IDE support**: Improved autocomplete and error detection
- **Safer refactoring**: Type safety prevents runtime errors
- **Better documentation**: Self-documenting code

### Maintenance
- **New code**: Follow type hint patterns established
- **Code reviews**: Check for proper type annotations
- **CI/CD**: Consider adding PHPStan to pipeline at level 6

## 8. Notes

- **Start gradually**: Fix high-impact files first
- **Test thoroughly**: Ensure no breaking changes
- **Document patterns**: Establish team conventions
- **Consider level 6**: More practical than level 8 for most projects

The goal is not to fix every single error, but to establish good type safety practices and fix the most impactful issues first.
