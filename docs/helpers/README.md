# Helper Classes

## Overview

Helper classes consolidate common functionality across the application to eliminate code duplication and ensure consistency. These static utility classes provide reusable methods for common operations.

## Available Helpers

### 🔑 PasswordHelper
**File**: `app/Helpers/PasswordHelper.php`
**Purpose**: Unified password generation and validation

**Key Methods**:
- `generateTemporaryPassword()` - Secure 12-character password for admin-created users
- `generateBrandedTemporaryPassword()` - DeliveryNexus branded password (DN + random)
- `getRegistrationRules()` - Standard password validation rules
- `getAdminRules()` - Stricter validation for admin passwords
- `validateStrength()` - Manual password strength validation

**Usage**:
```php
// Generate temporary password
$password = PasswordHelper::generateTemporaryPassword();

// Get validation rules
$rules = PasswordHelper::getRegistrationRules();

// Validate strength
$result = PasswordHelper::validateStrength($password);
```

### 📧 ValidationHelper
**File**: `app/Helpers/ValidationHelper.php`
**Purpose**: Unified validation rules for common patterns

**Key Methods**:
- `getNameRules()` - First name and last name validation
- `getEmailRules()` - Email validation with uniqueness
- `getPhoneRules()` - Nigerian phone number validation
- `getPaginationRules()` - Standard pagination parameters
- `getBusinessRules()` - Business registration validation
- `getAddressRules()` - Address validation rules

**Usage**:
```php
// Combine multiple rule sets
$rules = ValidationHelper::combineRules(
    ValidationHelper::getNameRules(),
    ValidationHelper::getEmailRules(),
    ValidationHelper::getPhoneRules()
);

// Get business-specific rules
$businessRules = ValidationHelper::getBusinessRules();
```

### 🔢 OtpHelper
**File**: `app/Helpers/OtpHelper.php`
**Purpose**: Unified OTP generation and management

**Key Methods**:
- `generate()` - Generate numeric OTP
- `generateAlphanumeric()` - Generate alphanumeric OTP
- `generateAndStore()` - Generate, store, and rate limit OTP
- `verify()` - Verify OTP against stored value
- `generateEmailVerification()` - Email verification OTP
- `generatePasswordReset()` - Password reset OTP

**Usage**:
```php
// Generate email verification OTP
$result = OtpHelper::generateEmailVerification($email);
if ($result['success']) {
    $otp = $result['otp'];
    // Send OTP via email
}

// Verify OTP
$isValid = OtpHelper::verifyEmailVerification($email, $otp);
```

### 🚦 RateLimitHelper
**File**: `app/Helpers/RateLimitHelper.php`
**Purpose**: Unified rate limiting patterns

**Key Methods**:
- `checkLoginAttempts()` - Rate limit login attempts
- `checkPasswordResetAttempts()` - Rate limit password reset
- `checkEmailVerificationAttempts()` - Rate limit email verification
- `checkApiAttempts()` - Rate limit API requests
- `clearUserLimits()` - Clear all limits for a user

**Usage**:
```php
// Check login rate limit
$result = RateLimitHelper::checkLoginAttempts($request);
if (!$result['allowed']) {
    return response()->json([
        'error' => 'Too many attempts',
        'retry_after' => $result['retry_after']
    ], 429);
}
```

### 🗄️ CacheHelper
**File**: `app/Helpers/CacheHelper.php`
**Purpose**: Tenant-aware caching (existing)

**Key Methods**:
- `key()` - Generate tenant-aware cache key
- `put()` - Store value in tenant cache
- `get()` - Retrieve value from tenant cache
- `remember()` - Cache with callback
- `forget()` - Remove from cache

### 🎨 FormatHelper
**File**: `app/Helpers/FormatHelper.php`
**Purpose**: Unified formatting operations

**Key Methods**:
- `formatNigerianPhone()` - Format phone numbers to international format
- `formatCurrency()` - Format currency amounts with symbols
- `formatFileSize()` - Human readable file sizes
- `formatDistance()` - Format meters/kilometers
- `formatAddress()` - Format address components
- `formatOrderNumber()` - Generate order numbers with prefix

**Usage**:
```php
// Format phone number
$formatted = FormatHelper::formatNigerianPhone('08012345678');
// Result: +2348012345678

// Format currency
$price = FormatHelper::formatCurrency(1500.50, 'NGN');
// Result: ₦1,500.50

// Format file size
$size = FormatHelper::formatFileSize(1048576);
// Result: 1.00 MB
```

### 📁 FileHelper
**File**: `app/Helpers/FileHelper.php`
**Purpose**: Unified file operations

**Key Methods**:
- `generateUniqueFilename()` - Create unique filenames with timestamps
- `validateImage()` - Validate uploaded image files
- `validateDocument()` - Validate uploaded document files
- `storeFile()` - Store files with automatic path generation
- `deleteFile()` - Delete files from storage
- `generateFileHash()` - Generate SHA256 hash for deduplication

**Usage**:
```php
// Validate image upload
$validation = FileHelper::validateImage($uploadedFile);
if (!$validation['valid']) {
    return response()->json(['errors' => $validation['errors']], 422);
}

// Store file with auto-generated path
$result = FileHelper::storeFile($file, 'products', $productId, 'r2');
$url = $result['url'];
```

## Benefits

### Code Reuse
- **Eliminated Duplication**: Removed 20+ duplicate password generation methods
- **Consistent Validation**: Single source of truth for validation rules
- **Unified OTP**: Consolidated OTP generation across 10+ services
- **Standard Rate Limiting**: Consistent rate limiting patterns

### Maintainability
- **Single Source of Truth**: Changes in one place affect entire application
- **Easier Testing**: Helper methods can be tested independently
- **Consistent Behavior**: Same patterns across all controllers and services

### Developer Experience
- **Easy Discovery**: All utility functions in predictable locations
- **Clear Documentation**: Comprehensive examples and usage patterns
- **Type Safety**: Full type hinting and PHPDoc blocks

## Migration from Duplicated Code

### Before (Duplicated)
```php
// In multiple controllers
private function generateTemporaryPassword(): string
{
    $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
    $lowercase = 'abcdefghijklmnopqrstuvwxyz';
    // ... 20+ lines of duplicate code
}

// In multiple form requests
public function rules(): array
{
    return [
        'email' => 'required|string|email|max:255|unique:users,email',
        // ... duplicate validation rules
    ];
}
```

### After (Consolidated)
```php
// Single helper call
$password = PasswordHelper::generateTemporaryPassword();

// Reusable validation rules
$rules = ValidationHelper::getEmailRules();
```

## Best Practices

### When to Use Helpers
- ✅ Static utility functions
- ✅ Common validation patterns
- ✅ Repeated business logic
- ✅ Configuration-based operations

### When NOT to Use Helpers
- ❌ Complex business logic (use Services)
- ❌ Database operations (use Repositories)
- ❌ Stateful operations (use Classes)
- ❌ Framework-specific logic (use Traits)

### Naming Conventions
- Use descriptive method names: `generateTemporaryPassword()` not `genPass()`
- Group related methods: `getEmailRules()`, `getPhoneRules()`
- Use consistent prefixes: `get*Rules()`, `generate*()`, `check*Attempts()`

### Documentation
- Include comprehensive PHPDoc blocks
- Provide usage examples
- Document parameter types and return values
- Explain complex logic with comments

## Testing Helpers

Helpers should be thoroughly tested:

```php
class PasswordHelperTest extends TestCase
{
    public function test_generates_secure_password(): void
    {
        $password = PasswordHelper::generateTemporaryPassword();
        
        expect($password)->toHaveLength(12);
        expect($password)->toMatch('/[A-Z]/'); // Uppercase
        expect($password)->toMatch('/[a-z]/'); // Lowercase
        expect($password)->toMatch('/[0-9]/'); // Numbers
        expect($password)->toMatch('/[!@#$%^&*]/'); // Symbols
    }
}
```

## Future Enhancements

### Planned Helpers
- **DateHelper** - Date formatting and timezone handling
- **FileHelper** - File upload and processing utilities
- **CurrencyHelper** - Money formatting and conversion
- **NotificationHelper** - Notification channel selection logic

### Integration Opportunities
- **Form Requests** - Auto-generate validation rules
- **API Resources** - Consistent data transformation
- **Middleware** - Reusable middleware logic
- **Commands** - Common CLI operations
