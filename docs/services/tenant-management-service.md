# TenantManagementService

## Overview

The `TenantManagementService` provides unified tenant management operations for both businesses and delivery providers. It eliminates code duplication by handling common operations polymorphically.

## Location
`app/Services/Tenant/TenantManagementService.php`

## Key Features

### Polymorphic Tenant Support
- Works with `Business|DeliveryProvider` union types
- Runtime type detection with `instanceof`
- Dynamic behavior based on tenant type
- Consistent API across tenant types

### Unified Operations
- Staff member creation
- Team member status changes
- Tenant status changes
- Role assignment and management

## Methods

### createStaffMember()
Creates a staff member for any tenant type.

```php
public function createStaffMember(
    Business|DeliveryProvider $tenant,
    array $staffData,
    User $createdBy,
    bool $autoVerifyEmail = false,
    bool $autoVerifyPhone = false
): array
```

**Parameters:**
- `$tenant` - Business or DeliveryProvider instance
- `$staffData` - Array with first_name, last_name, email, phone_number, role
- `$createdBy` - User creating the staff member
- `$autoVerifyEmail` - Auto-verify email address
- `$autoVerifyPhone` - Auto-verify phone number

**Returns:**
```php
[
    'user' => User,
    'temporary_password' => string,
    'credentials' => [
        'email' => string,
        'temporary_password' => string,
        'login_url' => string,
    ]
]
```

**Events Dispatched:**
- `TenantStaffAdded`

### changeTeamMemberStatus()
Changes team member status for any tenant type.

```php
public function changeTeamMemberStatus(
    User $teamMember,
    Business|DeliveryProvider $tenant,
    string $action,
    User $changedBy,
    ?string $newRole = null,
    ?string $message = null
): void
```

**Parameters:**
- `$teamMember` - User whose status is changing
- `$tenant` - Business or DeliveryProvider instance
- `$action` - activated, deactivated, role_assigned, ownership_transferred, removed
- `$changedBy` - User making the change
- `$newRole` - New role (for role_assigned action)
- `$message` - Optional message for the team member

**Events Dispatched:**
- `TenantTeamMemberStatusChanged`

### changeTenantStatus()
Changes tenant status for businesses or providers.

```php
public function changeTenantStatus(
    Business|DeliveryProvider $tenant,
    string $newStatus,
    ?string $reason = null,
    ?User $changedBy = null
): void
```

**Parameters:**
- `$tenant` - Business or DeliveryProvider instance
- `$newStatus` - active, inactive, suspended, verified, pending, rejected
- `$reason` - Optional reason for status change
- `$changedBy` - User making the change (usually admin)

**Events Dispatched:**
- `TenantStatusChanged`

## Usage Examples

### Creating Business Staff
```php
$result = $tenantManagementService->createStaffMember(
    $business,
    [
        'first_name' => 'John',
        'last_name' => 'Doe',
        'email' => '<EMAIL>',
        'phone_number' => '+2348012345678',
        'role' => 'manager'
    ],
    auth()->user(),
    true, // auto verify email
    false // don't auto verify phone
);

$user = $result['user'];
$credentials = $result['credentials'];
```

### Creating Provider Staff
```php
$result = $tenantManagementService->createStaffMember(
    $provider,
    [
        'first_name' => 'Jane',
        'last_name' => 'Smith',
        'email' => '<EMAIL>',
        'role' => 'driver'
    ],
    auth()->user()
);
```

### Activating Team Member
```php
$tenantManagementService->changeTeamMemberStatus(
    $teamMember,
    $business, // or $provider
    'activated',
    auth()->user(),
    null,
    'Your account has been activated. Welcome back!'
);
```

### Changing Tenant Status
```php
$tenantManagementService->changeTenantStatus(
    $business, // or $provider
    'active',
    'Verification completed successfully',
    auth()->user()
);
```

## Integration

### Controllers
Controllers should use the service instead of implementing tenant operations directly:

```php
// ✅ Good - Use service
$result = $this->tenantManagementService->createStaffMember(
    $tenant,
    $staffData,
    auth()->user()
);

// ❌ Bad - Duplicate logic
$user = User::create([...]);
BusinessTeamMember::create([...]);
$user->assign($role);
StaffAdded::dispatch(...);
```

### Event Integration
The service automatically dispatches unified events:
- `TenantStaffAdded` → `SendTenantManagementNotification`
- `TenantTeamMemberStatusChanged` → `SendTenantManagementNotification`
- `TenantStatusChanged` → `SendTenantManagementNotification`

## Benefits

### Code Reuse
- Single implementation for business and provider operations
- Eliminates ~60% of duplicate tenant management code
- Consistent behavior across tenant types

### Maintainability
- Changes in one place affect both tenant types
- Easier to add new tenant types
- Single source of truth for tenant operations

### Consistency
- Same notification patterns
- Unified error handling
- Consistent API responses

### Extensibility
- Polymorphic design supports new tenant types
- Easy to add new operations
- Event-driven architecture supports new listeners
