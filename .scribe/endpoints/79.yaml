name: 'Provider Profile'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/provider/profile
    metadata:
      groupName: 'Provider Profile'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get provider profile.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Provider profile retrieved successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "company_name": "Fast Delivery Co",
              "contact_email": "<EMAIL>",
              "contact_phone": "***********",
              "address": "123 Provider St, Lagos",
              "status": "active",
              "is_verified": true,
              "service_areas": ["Lagos", "Abuja"],
              "vehicle_types": ["motorcycle", "van"],
              "operating_hours": {
                "monday": {"open": "08:00", "close": "20:00"},
                "tuesday": {"open": "08:00", "close": "20:00"}
              },
              "created_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/provider/profile
    metadata:
      groupName: 'Provider Profile'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update provider profile.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      company_name:
        name: company_name
        description: 'Company name.'
        required: false
        example: 'Fast Delivery Co'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      contact_email:
        name: contact_email
        description: 'Contact email.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      contact_phone:
        name: contact_phone
        description: 'Contact phone.'
        required: false
        example: '***********'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      address:
        name: address
        description: 'Business address.'
        required: false
        example: '123 Provider St, Lagos'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      description:
        name: description
        description: 'Business description.'
        required: false
        example: 'Professional delivery services'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      company_name: 'Fast Delivery Co'
      contact_email: <EMAIL>
      contact_phone: '***********'
      address: '123 Provider St, Lagos'
      description: 'Professional delivery services'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Provider profile updated successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "company_name": "Fast Delivery Co",
              "contact_email": "<EMAIL>",
              "contact_phone": "***********",
              "address": "123 Provider St, Lagos",
              "description": "Professional delivery services"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
