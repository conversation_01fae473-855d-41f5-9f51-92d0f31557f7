name: 'Central Customer - Delivery Provider Search'
description: ''
endpoints:
  -
    httpMethods:
      - POST
    uri: api/v1/customer/search/delivery-providers
    metadata:
      groupName: 'Central Customer - Delivery Provider Search'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Search delivery providers.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      query:
        name: query
        description: 'Search query for provider name, description, etc.'
        required: false
        example: express
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: 'Filter by provider status.'
        required: false
        example: active
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      country_id:
        name: country_id
        description: 'Filter by country.'
        required: false
        example: NG
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      state_id:
        name: state_id
        description: 'Filter by state.'
        required: false
        example: LA
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_internal_provider:
        name: is_internal_provider
        description: 'Filter internal/external providers.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      min_rating:
        name: min_rating
        description: 'Minimum rating filter.'
        required: false
        example: 4.0
        type: number
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      max_rating:
        name: max_rating
        description: 'Maximum rating filter.'
        required: false
        example: 5.0
        type: number
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort:
        name: sort
        description: 'Sort results (relevance, name_asc, name_desc, rating_desc, rating_asc, newest, oldest).'
        required: false
        example: rating_desc
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (max 50).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      page:
        name: page
        description: 'Page number.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      query: express
      status: active
      country_id: NG
      state_id: LA
      is_internal_provider: false
      min_rating: 4.0
      max_rating: 5.0
      sort: rating_desc
      per_page: 15
      page: 1
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Delivery providers found successfully",
            "data": {
              "data": [
                {
                  "id": "uuid",
                  "company_name": "Express Delivery",
                  "description": "Fast and reliable delivery service",
                  "status": "active",
                  "performance_rating_avg": 4.5,
                  "is_internal_provider": false
                }
              ],
              "current_page": 1,
              "per_page": 15,
              "total": 25,
              "last_page": 2
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/search/delivery-providers/suggestions
    metadata:
      groupName: 'Central Customer - Delivery Provider Search'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get delivery provider search suggestions.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      query:
        name: query
        description: 'Search query.'
        required: true
        example: exp
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      limit:
        name: limit
        description: 'Number of suggestions (max 20).'
        required: false
        example: 10
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      query: exp
      limit: 10
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Search suggestions retrieved successfully",
            "data": [
              "Express Delivery",
              "Express Logistics",
              "Expert Couriers"
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/search/delivery-providers/top-rated
    metadata:
      groupName: 'Central Customer - Delivery Provider Search'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get top-rated delivery providers.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      limit:
        name: limit
        description: 'Number of top providers (max 20).'
        required: false
        example: 10
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      limit: 10
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Top-rated delivery providers retrieved successfully",
            "data": [
              {
                "id": "uuid",
                "company_name": "Premium Delivery",
                "performance_rating_avg": 4.9,
                "status": "active"
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
