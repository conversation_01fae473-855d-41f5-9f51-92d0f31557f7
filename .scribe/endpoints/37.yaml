name: 'Admin <PERSON>'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/admin/kyc/overview
    metadata:
      groupName: 'Admin KYC'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get KYC overview statistics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "KYC overview retrieved successfully",
            "data": {
              "overview": {
                "total_verifications": 1250,
                "pending_reviews": 45,
                "approved_verifications": 1100,
                "rejected_verifications": 105,
                "basic_level_users": 800,
                "intermediate_level_users": 350,
                "advanced_level_users": 100
              },
              "verification_types": {
                "email_verified": 1200,
                "phone_verified": 1150,
                "bank_verified": 450,
                "bvn_verified": 200,
                "nin_verified": 150
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/kyc/pending-reviews
    metadata:
      groupName: 'Admin KYC'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get pending KYC reviews.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.851477Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: a5d04b89-69e3-49e7-a8b0-2403650219ab
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-njhn4Hhr0wo1+/zahbpY2Q==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/kyc/users/{user}/documents'
    metadata:
      groupName: 'Admin KYC'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get KYC documents for a specific user.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      user:
        name: user
        description: 'The user.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      user: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.856471Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 10aee48f-7299-4f28-a09b-fef32091f272
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-jvjBTC6VlcFvT5xj54bUaQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/kyc/documents/{document}/approve'
    metadata:
      groupName: 'Admin KYC'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Approve KYC document.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      document:
        name: document
        description: 'The document.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      document: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      notes:
        name: notes
        description: 'Must not be greater than 1000 characters.'
        required: false
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      notes: b
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/kyc/documents/{document}/reject'
    metadata:
      groupName: 'Admin KYC'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Reject KYC document.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      document:
        name: document
        description: 'The document.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      document: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      rejection_reason:
        name: rejection_reason
        description: 'Must not be greater than 1000 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notes:
        name: notes
        description: 'Must not be greater than 1000 characters.'
        required: false
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      rejection_reason: b
      notes: 'n'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/kyc/statistics
    metadata:
      groupName: 'Admin KYC'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get KYC statistics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      period:
        name: period
        description: ''
        required: false
        example: week
        type: string
        enumValues:
          - today
          - week
          - month
          - quarter
          - year
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      period: week
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.864006Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 3599f457-018d-4238-804f-3faf94155f31
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-313qPOLUyWcJvfmNXsRnIA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
