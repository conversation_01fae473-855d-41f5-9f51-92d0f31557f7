name: Endpoints
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"DeliveryNexus API","version":"1.0.0","documentation":"http:\/\/deliverynexus.test\/docs"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 99fba5d7-dbe8-436e-b9eb-2702fae02c36
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-TDPHge416JJnIYiaPXZLWw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/login
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Smart login for web dashboard (detects central vs tenant context).'
      description: "Uses Laravel Sanctum's built-in stateful authentication."
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      email:
        name: email
        description: 'Must be a valid email address. Must not be greater than 255 characters.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'Must be at least 1 character.'
        required: true
        example: '+-0pBNvYgxwmi/#iw'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      device_name:
        name: device_name
        description: 'Must not be greater than 255 characters.'
        required: false
        example: u
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      remember_me:
        name: remember_me
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      email: <EMAIL>
      password: '+-0pBNvYgxwmi/#iw'
      device_name: u
      remember_me: true
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/password/reset
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Send password reset code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      identifier:
        name: identifier
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      identifier: architecto
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/password/confirm
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Reset password with token.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      identifier:
        name: identifier
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      token:
        name: token
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'Must be at least 8 characters.'
        required: true
        example: ']|{+-0pBNvYg'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      identifier: architecto
      token: architecto
      password: ']|{+-0pBNvYg'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/register
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Register a new user (Phase 1: Simple email + password).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Full name of the user. Must not be greater than 255 characters.'
        required: true
        example: 'John Doe'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      email:
        name: email
        description: 'Email address for the account. Must be a valid email address. Must not be greater than 255 characters.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'Password for the account.'
        required: true
        example: SecurePassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: 'Nigerian phone number (optional, format: +234XXXXXXXXXX). Must match the regex /^\+234[0-9]{10}$/.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      timezone:
        name: timezone
        description: 'User timezone (optional, defaults to Africa/Lagos). Must be a valid time zone, such as <code>Africa/Accra</code>.'
        required: false
        example: Africa/Lagos
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      first_name:
        name: first_name
        description: 'First name (internal field, auto-extracted from name). Must not be greater than 255 characters.'
        required: false
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: 'Last name (internal field, auto-extracted from name). Must not be greater than 255 characters.'
        required: false
        example: Doe
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 'John Doe'
      email: <EMAIL>
      password: SecurePassword123!
      phone_number: '+*************'
      timezone: Africa/Lagos
      first_name: John
      last_name: Doe
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/select-account-type
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Select account type (Phase 2).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      account_type:
        name: account_type
        description: 'Type of account to create - "customer", "business", or "delivery_provider".'
        required: true
        example: customer
        type: string
        enumValues:
          - customer
          - business
          - delivery_provider
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      account_type: customer
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/onboard/business
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Business onboarding (Phase 3).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      business_name:
        name: business_name
        description: 'Name of the business. Must be at least 2 characters. Must not be greater than 100 characters.'
        required: true
        example: 'Delicious Eats Restaurant'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_type:
        name: business_type
        description: 'Type of business - "food", "retail", "service", "logistics".'
        required: true
        example: food
        type: string
        enumValues:
          - food
          - retail
          - fashion
          - service
          - other
        exampleWasSpecified: false
        nullable: false
        custom: []
      country_id:
        name: country_id
        description: 'Country ID where the business is located. The <code>id</code> of an existing record in the countries table.'
        required: true
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Description of the business (optional). Must not be greater than 500 characters.'
        required: false
        example: 'A family-owned restaurant serving authentic Nigerian cuisine'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_email:
        name: contact_email
        description: 'Contact email for the business (optional). Must be a valid email address. Must not be greater than 100 characters.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_phone:
        name: contact_phone
        description: 'Contact phone number for the business (optional). Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      business_name: 'Delicious Eats Restaurant'
      business_type: food
      country_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      description: 'A family-owned restaurant serving authentic Nigerian cuisine'
      contact_email: <EMAIL>
      contact_phone: '+*************'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/onboard/provider
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Provider onboarding (Phase 3).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      provider_name:
        name: provider_name
        description: 'Name of the delivery provider. Must be at least 2 characters. Must not be greater than 100 characters.'
        required: true
        example: 'Swift Delivery Services'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      provider_type:
        name: provider_type
        description: 'Tier of delivery provider - "individual", "small_fleet", "medium_fleet", "enterprise_fleet".'
        required: true
        example: individual
        type: string
        enumValues:
          - individual
          - small_fleet
          - medium_fleet
          - enterprise_fleet
        exampleWasSpecified: false
        nullable: false
        custom: []
      country_id:
        name: country_id
        description: 'Country ID where the provider operates. The <code>id</code> of an existing record in the countries table.'
        required: true
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Description of the delivery provider (optional). Must not be greater than 500 characters.'
        required: false
        example: 'Professional delivery services covering Lagos and surrounding areas'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_email:
        name: contact_email
        description: 'Contact email for the provider (optional). Must be a valid email address. Must not be greater than 100 characters.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_phone:
        name: contact_phone
        description: 'Contact phone number for the provider (optional). Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      provider_name: 'Swift Delivery Services'
      provider_type: individual
      country_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      description: 'Professional delivery services covering Lagos and surrounding areas'
      contact_email: <EMAIL>
      contact_phone: '+*************'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/logout
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Logout user from web dashboard.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/logout-all
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Logout from all devices.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/refresh
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Refresh session for web dashboard.'
      description: 'For stateful authentication, session refresh is handled automatically by Laravel.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/auth/web/me
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get current authenticated user for web dashboard.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:12.918339Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 46cf0322-5a7e-48e0-b8bb-cdadd10ff5eb
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-z+9Q8+rFzilVOZOpFZPtWg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
          set-cookie: 'XSRF-TOKEN=eyJpdiI6IkQvQ0creFpwcGxkbDg4YWtjaXdGS1E9PSIsInZhbHVlIjoibGtuZUVDQktDTVdCZVNNb0pIVUpPYWZoYldNcHJQMkUzd1ZtNE9IUmFiNnFOK3cxUmRLNXV3MXY0cFd0SWtjck5XWFo4N05PckJsMHNYTUZRU2lQWmo2SGU0L2ZlaDF5b1lzb2xWVGlFV0pVNU43ejQ0QU9aeVNTSitXa2JOM0UiLCJtYWMiOiI1ZTIyODNiMDY5NjgyYjExMDM5NjlhODJjMDZhMTRkNTZjNmM0YzhmM2QyMTEwMjFhODI2MmM5NTQwMTY3YjIwIiwidGFnIjoiIn0%3D; expires=Mon, 09 Jun 2025 15:55:12 GMT; Max-Age=7200; path=/; samesite=lax; deliverynexus_api_session=eyJpdiI6IlZlQXBDU3ZiS1RjR1ZhelVENHczQ1E9PSIsInZhbHVlIjoieVg2bzNMcnNJY1RLYmFLVEMyaVhzZ05aRUhGOHNYZEFhek1Qalo4OHU2aUlsTU92S2VwYlhMQy9sRGMvM3cwREhPRHd4bU5INHRQd1ZwTEwzSnk1NnJqalhNN21DVGdLeW9hQVZMUGs4ZXZYc1I4c0l0QzNwRGJRMjlUWGZHRlYiLCJtYWMiOiI3NTM0NDI4YjhjMzc4ZTc0ZTkwMWU2Mjk5MjE3ODcxZGU4MWUwNzQ0OWE1NzlmMzdmMTBhODNmNjc3Y2FmMmJkIiwidGFnIjoiIn0%3D; expires=Mon, 09 Jun 2025 15:55:12 GMT; Max-Age=7200; path=/; httponly; samesite=lax'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/email/send-verification
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Send email verification code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/email/verify
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Verify email with code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      code:
        name: code
        description: 'The 6-digit verification code sent to your email. Must match the regex /^[0-9]{6}$/. Must be 6 characters.'
        required: true
        example: '123456'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      code: '123456'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/phone/send-verification
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Send phone verification code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/web/phone/verify
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Verify phone with code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      code:
        name: code
        description: 'The 6-digit verification code sent to your phone. Must match the regex /^[0-9]{6}$/. Must be 6 characters.'
        required: true
        example: '123456'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      code: '123456'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/health
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Basic health check endpoint.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"System is healthy","timestamp":"2025-06-09T13:55:12.937274Z","data":{"status":"ok","service":"DeliveryNexus API","version":"1.0.0"},"request_id":"2532ea92-8e6a-4f26-9d3d-ce4b1726e5fc"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 2532ea92-8e6a-4f26-9d3d-ce4b1726e5fc
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-CuHc0V5I+D/Qx57DcMzyTw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/health/detailed
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Detailed health check with dependencies.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"All systems operational","timestamp":"2025-06-09T13:55:12.948804Z","data":{"status":"ok","service":"DeliveryNexus API","version":"1.0.0","checks":{"database":{"status":"ok","message":"Database connection successful","connection":"pgsql","response_time":0.21},"cache":{"status":"ok","message":"Cache working properly","response_time":0.13},"redis":{"status":"ok","message":"Redis working properly","response_time":0.42},"queue":{"status":"ok","message":"Queue connection successful","connection":"redis"}},"system":{"php_version":"8.4.7","laravel_version":"12.16.0","memory_usage":"68.5 MB","disk_usage":"357.23 GB (38.56%)"}},"request_id":"7837e2e6-4023-4f97-ac0f-6227ff29c211"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 7837e2e6-4023-4f97-ac0f-6227ff29c211
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-V043tZgZyxrc30vXqFbyhw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/login
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Central domain login - Only allows users with no tenant_id.'
      description: 'Used by customers and platform admins on api.deliverynexus.com'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      identifier:
        name: identifier
        description: "User's email address or phone number."
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: "User's password."
        required: true
        example: password123
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      remember_me:
        name: remember_me
        description: 'Whether to remember the user for extended login.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      identifier: <EMAIL>
      password: password123
      remember_me: false
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/password/reset
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Send password reset code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      identifier:
        name: identifier
        description: 'Email address or phone number to send password reset instructions.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      identifier: <EMAIL>
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/password/confirm
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Reset password with token.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      identifier:
        name: identifier
        description: 'Email address or phone number for password reset.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      token:
        name: token
        description: 'Password reset token received via email/SMS.'
        required: true
        example: abc123def456ghi789jkl012mno345pqr678stu901vwx234yz
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'New password for the account.'
        required: true
        example: NewSecurePassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      identifier: <EMAIL>
      token: abc123def456ghi789jkl012mno345pqr678stu901vwx234yz
      password: NewSecurePassword123!
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/user-context
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get user context for mobile app login routing.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      identifier:
        name: identifier
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      identifier: architecto
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/register
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Phase 1: Register a new user (simple email + password only).'
      description: 'Registration only happens on central domain.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Full name of the user. Must not be greater than 255 characters.'
        required: true
        example: 'John Doe'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      email:
        name: email
        description: 'Email address for the account. Must be a valid email address. Must not be greater than 255 characters.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'Password for the account.'
        required: true
        example: SecurePassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: 'Nigerian phone number (optional, format: +234XXXXXXXXXX). Must match the regex /^\+234[0-9]{10}$/.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      timezone:
        name: timezone
        description: 'User timezone (optional, defaults to Africa/Lagos). Must be a valid time zone, such as <code>Africa/Accra</code>.'
        required: false
        example: Africa/Lagos
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      first_name:
        name: first_name
        description: 'First name (internal field, auto-extracted from name). Must not be greater than 255 characters.'
        required: false
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: 'Last name (internal field, auto-extracted from name). Must not be greater than 255 characters.'
        required: false
        example: Doe
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 'John Doe'
      email: <EMAIL>
      password: SecurePassword123!
      phone_number: '+*************'
      timezone: Africa/Lagos
      first_name: John
      last_name: Doe
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/select-account-type
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Phase 2: Select account type (customer, business, or delivery provider).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      account_type:
        name: account_type
        description: 'Type of account to create - "customer", "business", or "delivery_provider".'
        required: true
        example: customer
        type: string
        enumValues:
          - customer
          - business
          - delivery_provider
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      account_type: customer
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/onboard/business
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Phase 3: Business onboarding (creates tenant + business with minimal data).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      business_name:
        name: business_name
        description: 'Name of the business. Must be at least 2 characters. Must not be greater than 100 characters.'
        required: true
        example: 'Delicious Eats Restaurant'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_type:
        name: business_type
        description: 'Type of business - "food", "retail", "service", "logistics".'
        required: true
        example: food
        type: string
        enumValues:
          - food
          - retail
          - fashion
          - service
          - other
        exampleWasSpecified: false
        nullable: false
        custom: []
      country_id:
        name: country_id
        description: 'Country ID where the business is located. The <code>id</code> of an existing record in the countries table.'
        required: true
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Description of the business (optional). Must not be greater than 500 characters.'
        required: false
        example: 'A family-owned restaurant serving authentic Nigerian cuisine'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_email:
        name: contact_email
        description: 'Contact email for the business (optional). Must be a valid email address. Must not be greater than 100 characters.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_phone:
        name: contact_phone
        description: 'Contact phone number for the business (optional). Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      business_name: 'Delicious Eats Restaurant'
      business_type: food
      country_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      description: 'A family-owned restaurant serving authentic Nigerian cuisine'
      contact_email: <EMAIL>
      contact_phone: '+*************'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/onboard/provider
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Phase 3: Provider onboarding (creates tenant + provider with minimal data).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      provider_name:
        name: provider_name
        description: 'Name of the delivery provider. Must be at least 2 characters. Must not be greater than 100 characters.'
        required: true
        example: 'Swift Delivery Services'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      provider_type:
        name: provider_type
        description: 'Tier of delivery provider - "individual", "small_fleet", "medium_fleet", "enterprise_fleet".'
        required: true
        example: individual
        type: string
        enumValues:
          - individual
          - small_fleet
          - medium_fleet
          - enterprise_fleet
        exampleWasSpecified: false
        nullable: false
        custom: []
      country_id:
        name: country_id
        description: 'Country ID where the provider operates. The <code>id</code> of an existing record in the countries table.'
        required: true
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Description of the delivery provider (optional). Must not be greater than 500 characters.'
        required: false
        example: 'Professional delivery services covering Lagos and surrounding areas'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_email:
        name: contact_email
        description: 'Contact email for the provider (optional). Must be a valid email address. Must not be greater than 100 characters.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_phone:
        name: contact_phone
        description: 'Contact phone number for the provider (optional). Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      provider_name: 'Swift Delivery Services'
      provider_type: individual
      country_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      description: 'Professional delivery services covering Lagos and surrounding areas'
      contact_email: <EMAIL>
      contact_phone: '+*************'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/register/invitation
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Register from staff invitation.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      invitation_token:
        name: invitation_token
        description: 'Invitation token received via email. The <code>token</code> of an existing record in the team_invitations table.'
        required: true
        example: abc123def456ghi789jkl012mno345pqr678stu901vwx234yz
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      first_name:
        name: first_name
        description: 'First name of the staff member. Must be at least 2 characters. Must not be greater than 50 characters.'
        required: true
        example: Jane
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: 'Last name of the staff member. Must be at least 2 characters. Must not be greater than 50 characters.'
        required: true
        example: Smith
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'Password for the account (minimum 8 characters). Must be at least 8 characters.'
        required: true
        example: SecurePassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: 'Phone number (optional). Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      invitation_token: abc123def456ghi789jkl012mno345pqr678stu901vwx234yz
      first_name: Jane
      last_name: Smith
      password: SecurePassword123!
      phone_number: '+*************'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/showcase/featured-businesses
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get random businesses for homepage showcase, preferably around user location.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Businesses retrieved successfully","timestamp":"2025-06-09T13:55:13.049191Z","data":[{"id":"01974ede-b71c-7156-a611-b2bddac2324c","tenant_id":"01974ede-b71b-7218-824e-fe395144eb45","business_name":"Quick Mart","business_type":"retail","subdomain":"royal-plaza","slug":"royal-plaza","description":"Your neighborhood convenience store for daily essentials and groceries.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/009955?text=business+exercitationem","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"BN720413","tax_identification_number":"20692332-8156","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1923,"max_order_value":45357,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"http:\/\/www.torphy.com\/velit-consequatur-ut-quaerat-ratione-enim-aperiam-nostrum","tax_clearance":"https:\/\/www.kemmer.org\/perspiciatis-porro-harum-eos-id","business_permit":"http:\/\/www.quigley.com\/","owner_id":"http:\/\/www.marvin.com\/distinctio-pariatur-architecto-dignissimos","bank_statement":null},"country_id":"01974ede-b0cd-7104-8ee8-1649ff08932a","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b71e-70ea-8015-44fce2cc05d6","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b71e-70ea-8015-44fce2cc05d6","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false},{"id":"01974ede-b739-7157-b696-269936e614a6","tenant_id":"01974ede-b738-737a-b23e-46b220e4a30c","business_name":"Trendy Styles","business_type":"fashion","subdomain":"digital-grocery","slug":"digital-grocery","description":"Contemporary fashion store for the modern Nigerian woman and man.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/0033bb?text=business+accusamus","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"BN443506","tax_identification_number":"26398901-3855","global_auto_accept_orders":false,"auto_acceptance_criteria":{"min_order_value":1620,"max_order_value":12477,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":false,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"http:\/\/www.ratke.info\/perspiciatis-ut-voluptas-commodi-et-sunt-sequi-placeat","tax_clearance":null,"business_permit":null,"owner_id":"https:\/\/www.hamill.net\/numquam-dolorum-ab-dolores-praesentium","bank_statement":null},"country_id":"01974ede-b0cd-7104-8ee8-1649ff08932a","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b73b-70d0-8843-21442f65150a","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b73b-70d0-8843-21442f65150a","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false},{"id":"01974ede-b70a-714f-9ea6-b900ddd6d1ae","tenant_id":"01974ede-b709-7084-933f-909860115cc2","business_name":"Golden Spoon Restaurant","business_type":"food","subdomain":"modern-mart","slug":"modern-mart","description":"Fine dining Nigerian restaurant with elegant ambiance and exceptional service.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/004477?text=business+inventore","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"BN776285","tax_identification_number":"66261408-3456","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":594,"max_order_value":21986,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"http:\/\/www.wiegand.com\/exercitationem-velit-odit-cum-eos","tax_clearance":"http:\/\/auer.com\/quo-deserunt-non-qui-ut-odit.html","business_permit":null,"owner_id":"http:\/\/wiza.org\/quis-aut-quidem-molestiae-soluta-nobis-ea.html","bank_statement":null},"country_id":"01974ede-b0cd-7104-8ee8-1649ff08932a","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b70c-73af-bb07-175a5551007b","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b70c-73af-bb07-175a5551007b","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false},{"id":"01974ede-b752-7253-86aa-32c00aaa6ef4","tenant_id":"01974ede-b750-7066-8a05-a81f525090e8","business_name":"Tech Support Hub","business_type":"service","subdomain":"abuja-digital-store-1","slug":"abuja-digital-store-1","description":"Expert technology support and IT services for businesses and individuals.","logo_url":null,"contact_email":"<EMAIL>","contact_phone":"***********","status":"active","cac_registration_number":"IT446961","tax_identification_number":"61790483-5073","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":950,"max_order_value":28231,"verified_customers_only":true,"business_hours_only":false},"accepts_cash_on_delivery":false,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":true}},"verification_documents":{"cac_certificate":"http:\/\/www.haag.com\/enim-et-nesciunt-necessitatibus-ipsa-aliquid","tax_clearance":null,"business_permit":"http:\/\/hickle.org\/cupiditate-mollitia-praesentium-facilis-repellat-error-facilis","owner_id":"http:\/\/ritchie.net\/sint-labore-et-labore-blanditiis-repellendus.html","bank_statement":null},"country_id":"01974ede-b0cd-7104-8ee8-1649ff08932a","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b754-712f-9e76-31757b69296c","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b754-712f-9e76-31757b69296c","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":false,"has_subscription":false},{"id":"01974ede-b6fd-7368-a742-4486bb56cd61","tenant_id":"01974ede-b6fc-7142-9869-94f98936e283","business_name":"Lagos Grill","business_type":"food","subdomain":"international-cafe","slug":"international-cafe","description":"Modern Nigerian cuisine with a contemporary twist. Grilled specialties and local favorites.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/007766?text=business+hic","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"BN782839","tax_identification_number":"56621081-2128","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1117,"max_order_value":23235,"verified_customers_only":false,"business_hours_only":false},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"http:\/\/www.klein.com\/","tax_clearance":"https:\/\/powlowski.com\/dolorem-ipsum-aut-aliquid.html","business_permit":"http:\/\/bailey.org\/quod-sint-dolores-libero-incidunt-porro","owner_id":"http:\/\/donnelly.com\/consectetur-consequatur-odio-velit-sint","bank_statement":null},"country_id":"01974ede-b0cd-7104-8ee8-1649ff08932a","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b6ff-7192-ba66-f2af4712ab43","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b6ff-7192-ba66-f2af4712ab43","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false},{"id":"01974ede-b731-73ae-8289-2b5eb3914253","tenant_id":"01974ede-b72f-715d-859a-51d533134ac4","business_name":"Smart Store","business_type":"retail","subdomain":"quick-mall","slug":"quick-mall","description":"Technology-enabled retail store with smart shopping solutions.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/00aa22?text=business+tempora","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"BN463942","tax_identification_number":"76326113-2536","global_auto_accept_orders":false,"auto_acceptance_criteria":{"min_order_value":1712,"max_order_value":16365,"verified_customers_only":false,"business_hours_only":false},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"https:\/\/www.quitzon.com\/et-nesciunt-aspernatur-autem-tenetur-veniam-expedita-fuga","tax_clearance":null,"business_permit":"http:\/\/satterfield.com\/","owner_id":"http:\/\/www.will.org\/","bank_statement":null},"country_id":"01974ede-b0cd-7104-8ee8-1649ff08932a","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b733-727a-af78-608ea3162681","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b733-727a-af78-608ea3162681","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false}],"request_id":"c348d3e7-b549-45f5-8e73-398e62da914e"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: c348d3e7-b549-45f5-8e73-398e62da914e
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-aVs4DgkidTkQoQobO4jknQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/showcase/featured-providers
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get random delivery providers for homepage showcase, preferably around user location.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Delivery providers retrieved successfully","timestamp":"2025-06-09T13:55:13.067589Z","data":[{"id":"01974ede-e7d9-7110-807b-051dc2ee18b3","tenant_id":"01974ede-b6ee-70c5-a8ec-07621304284f","company_name":"Jollof Palace Delivery","description":"Internal delivery service for Jollof Palace","contact_email":"<EMAIL>","contact_phone":"08091528086","status":"active","is_internal_provider":true,"performance_rating_avg":4.58,"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:40.000000Z","updated_at":"2025-06-08T09:28:40.000000Z"},{"id":"01974ede-b814-7047-b35d-2206dc707b5f","tenant_id":"01974ede-b80f-73ba-bb65-c4e592bedaad","company_name":"Swift Delivery Nigeria","description":"Fast and reliable delivery service across Nigeria","contact_email":"<EMAIL>","contact_phone":"08012345700","status":"verified","is_internal_provider":false,"performance_rating_avg":3.62,"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z"},{"id":"01974ede-d4d6-7154-b3db-790f8615cae6","tenant_id":"01974ede-d4d2-7098-a12e-5984c8e3080f","company_name":"Port Harcourt Delivery Hub","description":"Reliable delivery solutions for Rivers State","contact_email":"<EMAIL>","contact_phone":"08012345703","status":"verified","is_internal_provider":false,"performance_rating_avg":4.01,"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:35.000000Z","updated_at":"2025-06-08T09:28:35.000000Z"},{"id":"01974ede-dd5f-7308-a5ae-1b8e1f7d720f","tenant_id":"01974ede-dd5a-73bc-bdfb-3245f46611f4","company_name":"Kano Fast Courier","description":"Northern Nigeria delivery specialist","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","is_internal_provider":false,"performance_rating_avg":4.7,"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:37.000000Z","updated_at":"2025-06-08T09:28:37.000000Z"},{"id":"01974ede-c1dc-7116-9af4-50acb1c3ea99","tenant_id":"01974ede-c1d7-7094-8d5e-80f87d8d9fe1","company_name":"Lagos Express Logistics","description":"Premium delivery service for Lagos and surrounding areas","contact_email":"<EMAIL>","contact_phone":"08012345701","status":"verified","is_internal_provider":false,"performance_rating_avg":4.76,"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:30.000000Z","updated_at":"2025-06-08T09:28:30.000000Z"},{"id":"01974ede-e962-7239-bd85-f7026ac9a863","tenant_id":"01974ede-b6f3-71b6-b384-3e63383964f8","company_name":"Suya King Delivery","description":"Internal delivery service for Suya King","contact_email":"<EMAIL>","contact_phone":"08108242344","status":"active","is_internal_provider":true,"performance_rating_avg":4.35,"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:41.000000Z","updated_at":"2025-06-08T09:28:41.000000Z"}],"request_id":"20a311c2-cda6-44d9-b63e-7eecc59c3390"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 20a311c2-cda6-44d9-b63e-7eecc59c3390
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-8J9lkfLKsRhsoSmB+RqXfA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/showcase/statistics
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get homepage statistics for showcase.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Homepage statistics retrieved successfully","timestamp":"2025-06-09T13:55:13.079253Z","data":{"total_businesses":22,"total_providers":8,"total_orders_completed":0,"cities_served":1},"request_id":"5010fb3d-6f01-4079-a515-82e3142db095"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 5010fb3d-6f01-4079-a515-82e3142db095
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-6VMlOplcca91x+1gGzbWnQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/showcase/testimonials
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get recent success stories/testimonials for homepage.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Testimonials retrieved successfully","timestamp":"2025-06-09T13:55:13.083367Z","data":[{"id":1,"customer_name":"John Doe","business_name":"Pizza Palace","rating":5,"comment":"Amazing service and fast delivery!","created_at":"2025-06-07T13:55:13.083305Z"},{"id":2,"customer_name":"Jane Smith","business_name":"Quick Logistics","rating":5,"comment":"Professional delivery service, highly recommended!","created_at":"2025-06-04T13:55:13.083344Z"}],"request_id":"a0b5fc00-b65b-48fe-bfba-98b11ade63c1"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: a0b5fc00-b65b-48fe-bfba-98b11ade63c1
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-PIcrcftyGrRvWN4VMEcs1w==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/showcase
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get all homepage showcase data in one request.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: '{"success":true,"message":"Homepage showcase data retrieved successfully","timestamp":"2025-06-09T13:55:13.092979Z","data":{"featured_businesses":[{"id":"01974ede-b72d-7326-8919-1a7083ef2348","tenant_id":"01974ede-b72b-7066-a86b-351e605546aa","business_name":"Mega Mart","business_type":"retail","subdomain":"elite-general-store","slug":"elite-general-store","description":"Large retail store with everything you need under one roof.","logo_url":null,"contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"IT275320","tax_identification_number":"96993205-1627","global_auto_accept_orders":false,"auto_acceptance_criteria":{"min_order_value":793,"max_order_value":18569,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":false,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"https:\/\/www.nitzsche.net\/velit-totam-at-dolore-exercitationem-quo","tax_clearance":"http:\/\/king.com\/","business_permit":"http:\/\/www.fadel.com\/id-et-doloribus-ut-cum-nihil.html","owner_id":"http:\/\/www.huel.com\/","bank_statement":null},"country_id":"01974ede-b0cd-7104-8ee8-1649ff08932a","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b72e-7194-9f01-e0ae2bf003fb","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b72e-7194-9f01-e0ae2bf003fb","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":false,"has_subscription":false},{"id":"01974ede-b739-7157-b696-269936e614a6","tenant_id":"01974ede-b738-737a-b23e-46b220e4a30c","business_name":"Trendy Styles","business_type":"fashion","subdomain":"digital-grocery","slug":"digital-grocery","description":"Contemporary fashion store for the modern Nigerian woman and man.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/0033bb?text=business+accusamus","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"BN443506","tax_identification_number":"26398901-3855","global_auto_accept_orders":false,"auto_acceptance_criteria":{"min_order_value":1620,"max_order_value":12477,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":false,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"http:\/\/www.ratke.info\/perspiciatis-ut-voluptas-commodi-et-sunt-sequi-placeat","tax_clearance":null,"business_permit":null,"owner_id":"https:\/\/www.hamill.net\/numquam-dolorum-ab-dolores-praesentium","bank_statement":null},"country_id":"01974ede-b0cd-7104-8ee8-1649ff08932a","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b73b-70d0-8843-21442f65150a","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b73b-70d0-8843-21442f65150a","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false},{"id":"01974ede-b720-70dc-90b0-34c6897655b4","tenant_id":"01974ede-b71f-72c9-9801-0b38c10bdb26","business_name":"Family Store","business_type":"retail","subdomain":"express-supermarket","slug":"express-supermarket","description":"Family-owned store serving the community with quality products at affordable prices.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/00cc55?text=business+corporis","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"RC747478","tax_identification_number":"38471995-5540","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1536,"max_order_value":28322,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":true}},"verification_documents":{"cac_certificate":"http:\/\/kreiger.com\/consectetur-molestias-consectetur-minima-voluptatibus-ut-voluptatum-fuga","tax_clearance":null,"business_permit":null,"owner_id":"http:\/\/herzog.info\/et-magni-culpa-impedit-incidunt-dicta-architecto-voluptas.html","bank_statement":null},"country_id":"01974ede-b0cd-7104-8ee8-1649ff08932a","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b722-72e9-a1d0-3def1a9766a7","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b722-72e9-a1d0-3def1a9766a7","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false},{"id":"01974ede-b728-70f7-b9a3-a92d4292cd6a","tenant_id":"01974ede-b727-7178-8019-0ee1365e47e6","business_name":"Corner Shop","business_type":"retail","subdomain":"quick-supermarket","slug":"quick-supermarket","description":"Convenient corner store for quick shopping and everyday needs.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/00ee66?text=business+ea","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"IT560985","tax_identification_number":"00766492-0400","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1654,"max_order_value":26896,"verified_customers_only":true,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":null,"tax_clearance":"https:\/\/www.sanford.com\/reiciendis-possimus-quam-quos-expedita-sit","business_permit":null,"owner_id":"http:\/\/yost.com\/qui-repudiandae-dolores-similique-officia-et-tenetur-quidem","bank_statement":null},"country_id":"01974ede-b0cd-7104-8ee8-1649ff08932a","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b72a-73b6-a070-921313da5130","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b72a-73b6-a070-921313da5130","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false},{"id":"01974ede-b752-7253-86aa-32c00aaa6ef4","tenant_id":"01974ede-b750-7066-8a05-a81f525090e8","business_name":"Tech Support Hub","business_type":"service","subdomain":"abuja-digital-store-1","slug":"abuja-digital-store-1","description":"Expert technology support and IT services for businesses and individuals.","logo_url":null,"contact_email":"<EMAIL>","contact_phone":"***********","status":"active","cac_registration_number":"IT446961","tax_identification_number":"61790483-5073","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":950,"max_order_value":28231,"verified_customers_only":true,"business_hours_only":false},"accepts_cash_on_delivery":false,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":true}},"verification_documents":{"cac_certificate":"http:\/\/www.haag.com\/enim-et-nesciunt-necessitatibus-ipsa-aliquid","tax_clearance":null,"business_permit":"http:\/\/hickle.org\/cupiditate-mollitia-praesentium-facilis-repellat-error-facilis","owner_id":"http:\/\/ritchie.net\/sint-labore-et-labore-blanditiis-repellendus.html","bank_statement":null},"country_id":"01974ede-b0cd-7104-8ee8-1649ff08932a","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b754-712f-9e76-31757b69296c","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b754-712f-9e76-31757b69296c","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":false,"has_subscription":false},{"id":"01974ede-b71c-7156-a611-b2bddac2324c","tenant_id":"01974ede-b71b-7218-824e-fe395144eb45","business_name":"Quick Mart","business_type":"retail","subdomain":"royal-plaza","slug":"royal-plaza","description":"Your neighborhood convenience store for daily essentials and groceries.","logo_url":"https:\/\/via.placeholder.com\/200x200.png\/009955?text=business+exercitationem","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","cac_registration_number":"BN720413","tax_identification_number":"20692332-8156","global_auto_accept_orders":true,"auto_acceptance_criteria":{"min_order_value":1923,"max_order_value":45357,"verified_customers_only":false,"business_hours_only":true},"accepts_cash_on_delivery":true,"allows_pickup":true,"operating_hours":{"monday":{"open":"08:00","close":"22:00","is_closed":false},"tuesday":{"open":"08:00","close":"22:00","is_closed":false},"wednesday":{"open":"08:00","close":"22:00","is_closed":false},"thursday":{"open":"08:00","close":"22:00","is_closed":false},"friday":{"open":"08:00","close":"23:00","is_closed":false},"saturday":{"open":"09:00","close":"23:00","is_closed":false},"sunday":{"open":"10:00","close":"21:00","is_closed":false}},"verification_documents":{"cac_certificate":"http:\/\/www.torphy.com\/velit-consequatur-ut-quaerat-ratione-enim-aperiam-nostrum","tax_clearance":"https:\/\/www.kemmer.org\/perspiciatis-porro-harum-eos-id","business_permit":"http:\/\/www.quigley.com\/","owner_id":"http:\/\/www.marvin.com\/distinctio-pariatur-architecto-dignissimos","bank_statement":null},"country_id":"01974ede-b0cd-7104-8ee8-1649ff08932a","state_id":"01974ede-b0ce-703d-9207-2c7d7c045386","primary_address_id":"01974ede-b71e-70ea-8015-44fce2cc05d6","current_subscription_id":null,"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z","primary_address":{"id":"01974ede-b71e-70ea-8015-44fce2cc05d6","address_line_1":null,"address_line_2":null,"city":null,"state":null,"postal_code":null,"country":null,"latitude":"6.********","longitude":"3.********"},"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG","currency_code":null,"phone_code":null},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"is_active":false,"is_verified":false,"has_primary_address":true,"has_logo":true,"has_subscription":false}],"featured_providers":[{"id":"01974ede-b814-7047-b35d-2206dc707b5f","tenant_id":"01974ede-b80f-73ba-bb65-c4e592bedaad","company_name":"Swift Delivery Nigeria","description":"Fast and reliable delivery service across Nigeria","contact_email":"<EMAIL>","contact_phone":"08012345700","status":"verified","is_internal_provider":false,"performance_rating_avg":3.62,"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:28.000000Z","updated_at":"2025-06-08T09:28:28.000000Z"},{"id":"01974ede-d4d6-7154-b3db-790f8615cae6","tenant_id":"01974ede-d4d2-7098-a12e-5984c8e3080f","company_name":"Port Harcourt Delivery Hub","description":"Reliable delivery solutions for Rivers State","contact_email":"<EMAIL>","contact_phone":"08012345703","status":"verified","is_internal_provider":false,"performance_rating_avg":4.01,"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:35.000000Z","updated_at":"2025-06-08T09:28:35.000000Z"},{"id":"01974ede-cc51-7311-8dbd-8e404e192a89","tenant_id":"01974ede-cc4c-73ac-91b4-a2020a89cf7c","company_name":"Abuja Quick Dispatch","description":"Same-day delivery service in Abuja and FCT","contact_email":"<EMAIL>","contact_phone":"08012345702","status":"verified","is_internal_provider":false,"performance_rating_avg":4.38,"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:33.000000Z","updated_at":"2025-06-08T09:28:33.000000Z"},{"id":"01974ede-e962-7239-bd85-f7026ac9a863","tenant_id":"01974ede-b6f3-71b6-b384-3e63383964f8","company_name":"Suya King Delivery","description":"Internal delivery service for Suya King","contact_email":"<EMAIL>","contact_phone":"08108242344","status":"active","is_internal_provider":true,"performance_rating_avg":4.35,"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:41.000000Z","updated_at":"2025-06-08T09:28:41.000000Z"},{"id":"01974ede-e596-7012-a745-fa676ac378cb","tenant_id":"01974ede-b6de-714d-b0ee-74369293ab03","company_name":"Mama Cass Kitchen Delivery","description":"Internal delivery service for Mama Cass Kitchen","contact_email":"<EMAIL>","contact_phone":"07052264169","status":"active","is_internal_provider":true,"performance_rating_avg":4.85,"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:40.000000Z","updated_at":"2025-06-08T09:28:40.000000Z"},{"id":"01974ede-dd5f-7308-a5ae-1b8e1f7d720f","tenant_id":"01974ede-dd5a-73bc-bdfb-3245f46611f4","company_name":"Kano Fast Courier","description":"Northern Nigeria delivery specialist","contact_email":"<EMAIL>","contact_phone":"***********","status":"verified","is_internal_provider":false,"performance_rating_avg":4.7,"country":{"id":"01974ede-b0cd-7104-8ee8-1649ff08932a","name":"Nigeria","code":"NG"},"state":{"id":"01974ede-b0ce-703d-9207-2c7d7c045386","name":"Lagos","code":"LA"},"created_at":"2025-06-08T09:28:37.000000Z","updated_at":"2025-06-08T09:28:37.000000Z"}],"statistics":{"total_businesses":22,"total_providers":8,"total_orders_completed":0,"cities_served":1},"testimonials":[{"id":1,"customer_name":"John Doe","business_name":"Pizza Palace","rating":5,"comment":"Amazing service and fast delivery!","created_at":"2025-06-07T13:55:13.092915Z"},{"id":2,"customer_name":"Jane Smith","business_name":"Quick Logistics","rating":5,"comment":"Professional delivery service, highly recommended!","created_at":"2025-06-04T13:55:13.092946Z"}]},"request_id":"86edc984-7452-4f67-889a-b08964fef939"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 86edc984-7452-4f67-889a-b08964fef939
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-pkqDQgYRMGzWiD4VA5c/vw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/logout
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Logout user.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/logout-all
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Logout from all devices.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/refresh
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Refresh token.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/auth/me
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get authenticated user.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.101590Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: c853aec1-4666-42bc-b2bc-7a06143b8b02
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-XRmBRf8ytjqEQ68ci04yGQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/email/send-verification
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Send email verification code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/phone/send-verification
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Send phone verification code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/email/verify
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Verify email with code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      code:
        name: code
        description: 'The 6-digit verification code sent to your email. Must match the regex /^[0-9]{6}$/. Must be 6 characters.'
        required: true
        example: '123456'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      code: '123456'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/phone/verify
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Verify phone with code.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      code:
        name: code
        description: 'The 6-digit verification code sent to your phone. Must match the regex /^[0-9]{6}$/. Must be 6 characters.'
        required: true
        example: '123456'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      code: '123456'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/auth/tenants
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get user's accessible tenants."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.108651Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 1ca77c32-5d90-4c2c-aa66-397f49d0ff55
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-A4ZiJs/ZPYqACNjd8kKspw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/auth/tenants/{tenantId}/access'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Check if user can interact with a specific tenant.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      tenantId:
        name: tenantId
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      tenantId: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.112544Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: ce9e7abf-2fa5-4259-9a1a-eedd9234f8c9
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-5mW1dMWggEWar7vjZPW/Yw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/user/profile
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get user profile for central users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.121700Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: d85ee40a-2ba9-43bc-a51a-34f8e80ed716
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-SZgreUG19SxdWCmNe9uonw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/user/profile
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update user profile for central users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      first_name:
        name: first_name
        description: "User's first name. Must not be greater than 255 characters. Must be at least 2 characters."
        required: false
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: "User's last name. Must not be greater than 255 characters. Must be at least 2 characters."
        required: false
        example: Doe
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      email:
        name: email
        description: "User's email address. Must be a valid email address. Must not be greater than 255 characters."
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: 'User''s phone number in Nigerian format. Must match the regex /^\+234[0-9]{10}$/. Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      timezone:
        name: timezone
        description: "User's timezone. Must be a valid time zone, such as <code>Africa/Accra</code>. Must not be greater than 50 characters."
        required: false
        example: Africa/Lagos
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      avatar_url:
        name: avatar_url
        description: "URL to user's avatar image. Must be a valid URL. Must not be greater than 500 characters."
        required: false
        example: 'https://example.com/avatar.jpg'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      bio:
        name: bio
        description: "User's biography. Must not be greater than 1000 characters."
        required: false
        example: 'Software developer passionate about technology'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_of_birth:
        name: date_of_birth
        description: "User's date of birth. Must be a valid date. Must be a date before <code>today</code>."
        required: false
        example: '1990-01-15'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      gender:
        name: gender
        description: "User's gender."
        required: false
        example: male
        type: string
        enumValues:
          - male
          - female
          - other
          - prefer_not_to_say
        exampleWasSpecified: false
        nullable: true
        custom: []
      address:
        name: address
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.street:
        name: address.street
        description: 'This field is required when <code>address</code> is present. Must not be greater than 255 characters.'
        required: false
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.city_id:
        name: address.city_id
        description: 'This field is required when <code>address</code> is present. Must be a valid UUID. The <code>id</code> of an existing record in the cities table.'
        required: false
        example: a4855dc5-0acb-33c3-b921-f4291f719ca0
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.state_id:
        name: address.state_id
        description: 'This field is required when <code>address</code> is present. Must be a valid UUID. The <code>id</code> of an existing record in the states table.'
        required: false
        example: c90237e9-ced5-3af6-88ea-84aeaa148878
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.country_id:
        name: address.country_id
        description: 'This field is required when <code>address</code> is present. Must be a valid UUID. The <code>id</code> of an existing record in the countries table.'
        required: false
        example: a1a0a47d-e8c3-3cf0-8e6e-c1ff9dca5d1f
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.postal_code:
        name: address.postal_code
        description: 'Must not be greater than 20 characters.'
        required: false
        example: vdljnikhwaykcmyu
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address.is_default:
        name: address.is_default
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      first_name: John
      last_name: Doe
      email: <EMAIL>
      phone_number: '+*************'
      timezone: Africa/Lagos
      avatar_url: 'https://example.com/avatar.jpg'
      bio: 'Software developer passionate about technology'
      date_of_birth: '1990-01-15'
      gender: male
      address:
        street: b
        city_id: a4855dc5-0acb-33c3-b921-f4291f719ca0
        state_id: c90237e9-ced5-3af6-88ea-84aeaa148878
        country_id: a1a0a47d-e8c3-3cf0-8e6e-c1ff9dca5d1f
        postal_code: vdljnikhwaykcmyu
        is_default: true
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/user/change-password
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Change user password for central users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      current_password:
        name: current_password
        description: 'Current password for verification.'
        required: true
        example: CurrentPassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      new_password:
        name: new_password
        description: 'New password (minimum 8 characters, must contain uppercase, lowercase, numbers, and symbols).'
        required: true
        example: NewSecurePassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      current_password: CurrentPassword123!
      new_password: NewSecurePassword123!
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/user/preferences
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get user preferences for central users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.130510Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 1a49d4a5-0016-4546-8f58-2ac772999ba0
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-jQyonS17AGdxtQfKlNr+dA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/user/preferences
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update user preferences for central users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      notifications:
        name: notifications
        description: 'Notification preferences.'
        required: false
        example:
          email_enabled: true
          sms_enabled: false
          push_enabled: true
          marketing_enabled: false
          order_updates: true
          delivery_updates: true
          promotional_offers: false
          security_alerts: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.email_enabled:
        name: notifications.email_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.sms_enabled:
        name: notifications.sms_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.push_enabled:
        name: notifications.push_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.marketing_enabled:
        name: notifications.marketing_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.order_updates:
        name: notifications.order_updates
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.delivery_updates:
        name: notifications.delivery_updates
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.promotional_offers:
        name: notifications.promotional_offers
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.security_alerts:
        name: notifications.security_alerts
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication:
        name: communication
        description: 'Communication preferences.'
        required: false
        example:
          preferred_language: en
          preferred_contact_method: email
          contact_time_start: '09:00'
          contact_time_end: '18:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.preferred_language:
        name: communication.preferred_language
        description: ''
        required: false
        example: en
        type: string
        enumValues:
          - en
          - yo
          - ig
          - ha
          - fr
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.preferred_contact_method:
        name: communication.preferred_contact_method
        description: ''
        required: false
        example: email
        type: string
        enumValues:
          - email
          - sms
          - phone
          - whatsapp
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.contact_time_start:
        name: communication.contact_time_start
        description: 'Must be a valid date in the format <code>H:i</code>.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.contact_time_end:
        name: communication.contact_time_end
        description: 'Must be a valid date in the format <code>H:i</code>.'
        required: false
        example: '18:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy:
        name: privacy
        description: 'Privacy preferences.'
        required: false
        example:
          profile_visibility: private
          location_sharing: false
          activity_tracking: true
          data_analytics: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.profile_visibility:
        name: privacy.profile_visibility
        description: ''
        required: false
        example: private
        type: string
        enumValues:
          - public
          - private
          - contacts_only
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.location_sharing:
        name: privacy.location_sharing
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.activity_tracking:
        name: privacy.activity_tracking
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.data_analytics:
        name: privacy.data_analytics
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      app:
        name: app
        description: 'Application preferences.'
        required: false
        example:
          theme: auto
          currency: NGN
          distance_unit: km
          temperature_unit: celsius
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.theme:
        name: app.theme
        description: ''
        required: false
        example: auto
        type: string
        enumValues:
          - light
          - dark
          - auto
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.currency:
        name: app.currency
        description: ''
        required: false
        example: NGN
        type: string
        enumValues:
          - NGN
          - USD
          - EUR
          - GBP
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.distance_unit:
        name: app.distance_unit
        description: ''
        required: false
        example: km
        type: string
        enumValues:
          - km
          - miles
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.temperature_unit:
        name: app.temperature_unit
        description: ''
        required: false
        example: celsius
        type: string
        enumValues:
          - celsius
          - fahrenheit
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      notifications:
        email_enabled: true
        sms_enabled: false
        push_enabled: true
        marketing_enabled: false
        order_updates: true
        delivery_updates: true
        promotional_offers: false
        security_alerts: true
      communication:
        preferred_language: en
        preferred_contact_method: email
        contact_time_start: '09:00'
        contact_time_end: '18:00'
      privacy:
        profile_visibility: private
        location_sharing: false
        activity_tracking: true
        data_analytics: true
      app:
        theme: auto
        currency: NGN
        distance_unit: km
        temperature_unit: celsius
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/user/devices
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get user's active devices/sessions."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.139096Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 59ca8032-1cef-4493-b250-3514b4b6d6a9
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-KPyI7kcnd+b3e0A6fWQAJw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/user/deactivate
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Deactivate user account for central users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      password:
        name: password
        description: 'Current password for verification. Must be at least 8 characters.'
        required: true
        example: password123
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      reason:
        name: reason
        description: 'Optional reason for account deactivation. Must not be greater than 500 characters.'
        required: false
        example: 'No longer needed'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      password: password123
      reason: 'No longer needed'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/notifications
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get user's notifications with pagination and filtering."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.149165Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: eddf8db9-357e-453c-8a98-890b5d1d8834
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-QE5nhelXfeZnWiwrCgxMyA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/notifications/stats
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get notification statistics.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.153625Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: a4f77196-f1ea-46bd-9f2b-c94d3dbe5723
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-Imkg8X/pM6cAsi4Df9nvxw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/notifications/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get specific notification.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the notification.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.157807Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 8af2ce81-4883-40f0-9490-9da693ba6992
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-6Desdx+GFt0tV+T0Akax4g==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/v1/notifications/{id}/read'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Mark notification as read.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the notification.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/notifications/mark-all-read
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Mark all notifications as read.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/notifications/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Delete notification.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the notification.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/notifications/fcm-tokens
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Register FCM token for push notifications.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      token:
        name: token
        description: 'Must be at least 10 characters.'
        required: true
        example: bngzmiyvdljnikhwaykcmyuwpwlvqwrsitcpscqldzs
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      platform:
        name: platform
        description: ''
        required: true
        example: ios
        type: string
        enumValues:
          - web
          - android
          - ios
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      token: bngzmiyvdljnikhwaykcmyuwpwlvqwrsitcpscqldzs
      platform: ios
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/notifications/fcm-tokens
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get user's FCM tokens."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.163894Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 89b0f826-d086-4239-8e9c-2b915e3c991f
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-FvR+d0Mhm9NTnJ9RG7UKOw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: api/v1/notifications/fcm-tokens
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove FCM token.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      token:
        name: token
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      token: architecto
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/notifications/preferences
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get user's notification preferences."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.169429Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: c6cd7b07-90c6-4954-9ef1-88e7b4cc13c1
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-pr96beawMp728+KBvkoMcw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/notifications/preferences
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Update user's notification preferences."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      channel:
        name: channel
        description: ''
        required: true
        example: email
        type: string
        enumValues:
          - push
          - sms
          - email
        exampleWasSpecified: false
        nullable: false
        custom: []
      type:
        name: type
        description: ''
        required: true
        example: promotional
        type: string
        enumValues:
          - order_status
          - delivery_update
          - payment_confirmation
          - promotional
          - system_announcement
        exampleWasSpecified: false
        nullable: false
        custom: []
      enabled:
        name: enabled
        description: ''
        required: true
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      channel: email
      type: promotional
      enabled: true
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/notifications/preferences/reset
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Reset notification preferences to defaults.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/kyc/verify
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Initiate comprehensive KYC verification.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      verification_type:
        name: verification_type
        description: ''
        required: true
        example: nin
        type: string
        enumValues:
          - comprehensive
          - bvn
          - nin
          - bank_account
        exampleWasSpecified: false
        nullable: false
        custom: []
      bvn:
        name: bvn
        description: 'This field is required when <code>verification_type</code> is <code>comprehensive</code> or <code>bvn</code>. Must match the regex /^\d{11}$/.'
        required: false
        example: '***********'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      nin:
        name: nin
        description: 'This field is required when <code>verification_type</code> is <code>comprehensive</code> or <code>nin</code>. Must match the regex /^\d{11}$/.'
        required: false
        example: '***********'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      account_number:
        name: account_number
        description: 'This field is required when <code>verification_type</code> is <code>comprehensive</code> or <code>bank_account</code>. Must be at least 10 characters. Must not be greater than 10 characters.'
        required: false
        example: hwaykc
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      bank_code:
        name: bank_code
        description: 'This field is required when <code>verification_type</code> is <code>comprehensive</code> or <code>bank_account</code>. Must be at least 3 characters. Must not be greater than 6 characters.'
        required: false
        example: myuw
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      target_level:
        name: target_level
        description: ''
        required: false
        example: basic
        type: string
        enumValues:
          - basic
          - intermediate
          - advanced
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data:
        name: customer_data
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data.first_name:
        name: customer_data.first_name
        description: 'Must not be greater than 255 characters.'
        required: false
        example: p
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data.last_name:
        name: customer_data.last_name
        description: 'Must not be greater than 255 characters.'
        required: false
        example: w
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data.phone_number:
        name: customer_data.phone_number
        description: 'Must not be greater than 20 characters.'
        required: false
        example: lvqwrsitcpscqldz
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data.date_of_birth:
        name: customer_data.date_of_birth
        description: 'Must be a valid date in the format <code>Y-m-d</code>.'
        required: false
        example: '2025-06-09'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      verification_type: nin
      bvn: '***********'
      nin: '***********'
      account_number: hwaykc
      bank_code: myuw
      target_level: basic
      customer_data:
        first_name: p
        last_name: w
        phone_number: lvqwrsitcpscqldz
        date_of_birth: '2025-06-09'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/kyc/verify-component
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Verify individual component (BVN, NIN, Bank Account).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      type:
        name: type
        description: ''
        required: true
        example: bvn
        type: string
        enumValues:
          - bvn
          - nin
          - bank_account
        exampleWasSpecified: false
        nullable: false
        custom: []
      bvn:
        name: bvn
        description: 'This field is required when <code>type</code> is <code>bvn</code>. Must match the regex /^\d{11}$/.'
        required: false
        example: '***********'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      nin:
        name: nin
        description: 'This field is required when <code>type</code> is <code>nin</code>. Must match the regex /^\d{11}$/.'
        required: false
        example: '***********'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      account_number:
        name: account_number
        description: 'This field is required when <code>type</code> is <code>bank_account</code>. Must be at least 10 characters. Must not be greater than 10 characters.'
        required: false
        example: hwaykc
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      bank_code:
        name: bank_code
        description: 'This field is required when <code>type</code> is <code>bank_account</code>. Must be at least 3 characters. Must not be greater than 6 characters.'
        required: false
        example: myuw
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data:
        name: customer_data
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data.first_name:
        name: customer_data.first_name
        description: 'Must not be greater than 255 characters.'
        required: false
        example: p
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data.last_name:
        name: customer_data.last_name
        description: 'Must not be greater than 255 characters.'
        required: false
        example: w
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data.phone_number:
        name: customer_data.phone_number
        description: 'Must not be greater than 20 characters.'
        required: false
        example: lvqwrsitcpscqldz
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      customer_data.date_of_birth:
        name: customer_data.date_of_birth
        description: 'Must be a valid date in the format <code>Y-m-d</code>.'
        required: false
        example: '2025-06-09'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      type: bvn
      bvn: '***********'
      nin: '***********'
      account_number: hwaykc
      bank_code: myuw
      customer_data:
        first_name: p
        last_name: w
        phone_number: lvqwrsitcpscqldz
        date_of_birth: '2025-06-09'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/kyc/status
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get user's KYC status and progress."
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.203436Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 713e24c2-7f17-4849-ad94-d3438e133804
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-jygSEt3YeSNPal+4tYn7UQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/kyc/report
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Generate comprehensive KYC report.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.207610Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: c470fa22-c1fb-4fa5-a4d2-41d20498e967
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-yYS444rGyfgLyZKZj1HGkA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/kyc/methods
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get available verification methods.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.211703Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: d898aa9d-0198-4654-b33c-931fada1095d
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-csTDPptgTq/sz5LnkOW4RQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/kyc/banks
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get supported banks for verification.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.215810Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 8e4c1878-8d3a-4468-9cb4-acfe484b0349
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-NfziKcTyvrE4yJLFL6D26g==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/kyc/pricing
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get verification pricing information.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.219874Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: dbadc2bc-45dc-4b13-bb2a-93f7c2198363
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-z7mUuwBV27XbJuWCIEPkHw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/kyc/statistics
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get verification statistics.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.224067Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 380f9814-ed00-4f1f-badf-18c7feffa335
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-Nut2XSzqkoNJphXEmesEzA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/auth/register/admin
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Register platform admin (super admin only).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      first_name:
        name: first_name
        description: 'First name of the admin user. Must not be greater than 255 characters. Must be at least 2 characters.'
        required: true
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: 'Last name of the admin user. Must not be greater than 255 characters. Must be at least 2 characters.'
        required: true
        example: Doe
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      email:
        name: email
        description: 'Email address for the admin account. Must be a valid email address. Must not be greater than 255 characters.'
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: 'Password for the admin account (minimum 8 characters).'
        required: true
        example: SecurePassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: 'Phone number for the admin (optional). Must match the regex /^(\+234[0-9]{10}. Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      role:
        name: role
        description: 'Admin role - "platform-admin" or "super-admin".'
        required: true
        example: platform-admin
        type: string
        enumValues:
          - platform-admin
          - super-admin
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      first_name: John
      last_name: Doe
      email: <EMAIL>
      password: SecurePassword123!
      phone_number: '+*************'
      role: platform-admin
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/businesses
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Browse businesses (customer discovery).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.272378Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 2287685d-96aa-410f-9c9a-f668d60cdabc
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-XrewcV4bjQ0lBE3JAAJB7w==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/businesses/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Show business details for customers.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.276929Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 7a6088ef-96d9-4229-b81f-4a28f3c25027
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-hMes8qRo8fMb51JQFL/b0A==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/businesses/{id}/products'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get business products for customers.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.281000Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: da9a571f-1fed-47f5-8492-ce1a5aaae5ec
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-n2Qw34+OZgi2c8U+kWmbCw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/businesses/{id}/menu'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get business menu (organized by categories).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.285472Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 54d6966a-e77f-401d-a02f-b658f8bd7c27
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-BSgMagCZCz0lzi+ZwyKCig==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/providers
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Browse providers (customer discovery).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.293198Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 45bfa871-b5fe-4012-ad51-39efa998f061
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-8ZuRq8XmniSAIN9Sa9jPaw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/providers/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Show provider details for customers.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the provider.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.298260Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: fcfcac56-8e7d-461c-afdd-f067cb5c2448
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-rjzrThR29zUV5aN0HNtuXw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/customer/providers/{id}/service-areas'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get provider service areas for customers.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the provider.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.303247Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 535f2b63-4197-47f8-aca8-8127e077dfad
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-DC/M8DWGv4muw0BCckBr5A==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/customer/providers/{id}/check-availability'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Check if provider serves a specific location.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the provider.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      latitude:
        name: latitude
        description: 'Must be between -90 and 90.'
        required: true
        example: -89
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      longitude:
        name: longitude
        description: 'Must be between -180 and 180.'
        required: true
        example: -179
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      latitude: -89
      longitude: -179
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/tenants/statistics
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get tenant statistics for admin dashboard.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.374927Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 0e0a8622-7cb6-4dbe-b2dc-593993a25d2f
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-NbcwqCFZ5lW9vcqL/WORjQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/tenants/check-subdomain
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Check if subdomain is available.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.380811Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 5742609b-aa8f-488a-a800-80c0c06047be
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-D/hwY+rkGyYYrmlmlG/4Kg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/tenants/{tenant}/restore'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Restore an archived tenant.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      tenant:
        name: tenant
        description: 'The tenant.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      tenant: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/v1/admin/tenants/{tenant}/status'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update tenant status.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      tenant:
        name: tenant
        description: 'The tenant.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      tenant: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      status:
        name: status
        description: 'New status for the tenant - "active", "inactive", "suspended", "pending_verification".'
        required: true
        example: active
        type: string
        enumValues:
          - active
          - pending
          - inactive
          - suspended
          - trial
          - expired
          - archived
        exampleWasSpecified: false
        nullable: false
        custom: []
      reason:
        name: reason
        description: 'Optional reason for the status change (max 500 characters). Must not be greater than 500 characters.'
        required: false
        example: 'Tenant has completed verification requirements'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      status: active
      reason: 'Tenant has completed verification requirements'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/admin/tenants
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created tenant.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Name of the tenant (business or delivery provider). Must match the regex /^[a-zA-Z0-9\s\-_&.]+$/. Must be at least 2 characters. Must not be greater than 100 characters.'
        required: true
        example: 'Delicious Eats Restaurant'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      tenant_type:
        name: tenant_type
        description: 'Type of tenant - "business" or "delivery_provider".'
        required: true
        example: business
        type: string
        enumValues:
          - user
          - business
          - provider
          - all
        exampleWasSpecified: false
        nullable: false
        custom: []
      status:
        name: status
        description: 'Initial status of the tenant (optional, defaults to pending_verification).'
        required: false
        example: pending_verification
        type: string
        enumValues:
          - active
          - pending
          - inactive
          - suspended
          - trial
          - expired
          - archived
        exampleWasSpecified: false
        nullable: false
        custom: []
      subscription_plan_id:
        name: subscription_plan_id
        description: 'ID of the subscription plan to assign (optional). Must be a valid UUID. The <code>id</code> of an existing record in the subscription_plans table.'
        required: false
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      subdomain:
        name: subdomain
        description: 'Custom subdomain for the tenant (optional, auto-generated if not provided). Must match the regex /^[a-z0-9-]+$/. Must be at least 3 characters. Must not be greater than 30 characters.'
        required: false
        example: delicious-eats
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      settings:
        name: settings
        description: 'Tenant settings configuration (optional).'
        required: false
        example:
          features:
            api_access_enabled: true
            webhook_enabled: false
            real_time_tracking: true
          limits:
            max_orders_per_month: 1000
            max_api_calls_per_day: 10000
            max_storage_mb: 1000
          security:
            two_factor_required: false
            session_timeout_minutes: 60
            password_expiry_days: 90
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      settings.features:
        name: settings.features
        description: ''
        required: false
        example:
          api_access_enabled: true
          webhook_enabled: false
          real_time_tracking: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.features.api_access_enabled:
        name: settings.features.api_access_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.features.webhook_enabled:
        name: settings.features.webhook_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.features.real_time_tracking:
        name: settings.features.real_time_tracking
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits:
        name: settings.limits
        description: ''
        required: false
        example:
          max_orders_per_month: 1000
          max_api_calls_per_day: 10000
          max_storage_mb: 1000
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits.max_orders_per_month:
        name: settings.limits.max_orders_per_month
        description: 'Must be at least 1. Must not be greater than 1000000.'
        required: false
        example: 1000
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits.max_api_calls_per_day:
        name: settings.limits.max_api_calls_per_day
        description: 'Must be at least 100. Must not be greater than 10000000.'
        required: false
        example: 10000
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits.max_storage_mb:
        name: settings.limits.max_storage_mb
        description: 'Must be at least 100. Must not be greater than 100000.'
        required: false
        example: 1000
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security:
        name: settings.security
        description: ''
        required: false
        example:
          two_factor_required: false
          session_timeout_minutes: 60
          password_expiry_days: 90
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security.two_factor_required:
        name: settings.security.two_factor_required
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security.session_timeout_minutes:
        name: settings.security.session_timeout_minutes
        description: 'Must be at least 15. Must not be greater than 1440.'
        required: false
        example: 60
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security.password_expiry_days:
        name: settings.security.password_expiry_days
        description: 'Must be at least 30. Must not be greater than 365.'
        required: false
        example: 90
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 'Delicious Eats Restaurant'
      tenant_type: business
      status: pending_verification
      subscription_plan_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      subdomain: delicious-eats
      settings:
        features:
          api_access_enabled: true
          webhook_enabled: false
          real_time_tracking: true
        limits:
          max_orders_per_month: 1000
          max_api_calls_per_day: 10000
          max_storage_mb: 1000
        security:
          two_factor_required: false
          session_timeout_minutes: 60
          password_expiry_days: 90
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/tenants/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified tenant.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the tenant.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.393804Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 0cfcf1b4-cae5-4c24-af53-a93450eb9bf8
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-LHJdp8Xh7jE4Ng/lGiOpRw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/v1/admin/tenants/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified tenant.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the tenant.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Name of the tenant (business or delivery provider). Must match the regex /^[a-zA-Z0-9\s\-_&.]+$/. Must be at least 2 characters. Must not be greater than 100 characters.'
        required: false
        example: 'Delicious Eats Restaurant'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      tenant_type:
        name: tenant_type
        description: 'Type of tenant - "business" or "delivery_provider".'
        required: false
        example: business
        type: string
        enumValues:
          - user
          - business
          - provider
          - all
        exampleWasSpecified: false
        nullable: false
        custom: []
      status:
        name: status
        description: 'Status of the tenant.'
        required: false
        example: active
        type: string
        enumValues:
          - active
          - pending
          - inactive
          - suspended
          - trial
          - expired
          - archived
        exampleWasSpecified: false
        nullable: false
        custom: []
      subscription_plan_id:
        name: subscription_plan_id
        description: 'ID of the subscription plan to assign. Must be a valid UUID. The <code>id</code> of an existing record in the subscription_plans table.'
        required: false
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      settings:
        name: settings
        description: 'Tenant settings configuration.'
        required: false
        example:
          features:
            api_access_enabled: true
            webhook_enabled: false
            real_time_tracking: true
          limits:
            max_orders_per_month: 1000
            max_api_calls_per_day: 10000
            max_storage_mb: 1000
          security:
            two_factor_required: false
            session_timeout_minutes: 60
            password_expiry_days: 90
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      settings.features:
        name: settings.features
        description: ''
        required: false
        example:
          api_access_enabled: true
          webhook_enabled: false
          real_time_tracking: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.features.api_access_enabled:
        name: settings.features.api_access_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.features.webhook_enabled:
        name: settings.features.webhook_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.features.real_time_tracking:
        name: settings.features.real_time_tracking
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits:
        name: settings.limits
        description: ''
        required: false
        example:
          max_orders_per_month: 1000
          max_api_calls_per_day: 10000
          max_storage_mb: 1000
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits.max_orders_per_month:
        name: settings.limits.max_orders_per_month
        description: 'Must be at least 1. Must not be greater than 1000000.'
        required: false
        example: 1000
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits.max_api_calls_per_day:
        name: settings.limits.max_api_calls_per_day
        description: 'Must be at least 100. Must not be greater than 10000000.'
        required: false
        example: 10000
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.limits.max_storage_mb:
        name: settings.limits.max_storage_mb
        description: 'Must be at least 100. Must not be greater than 100000.'
        required: false
        example: 1000
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security:
        name: settings.security
        description: ''
        required: false
        example:
          two_factor_required: false
          session_timeout_minutes: 60
          password_expiry_days: 90
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security.two_factor_required:
        name: settings.security.two_factor_required
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security.session_timeout_minutes:
        name: settings.security.session_timeout_minutes
        description: 'Must be at least 15. Must not be greater than 1440.'
        required: false
        example: 60
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.security.password_expiry_days:
        name: settings.security.password_expiry_days
        description: 'Must be at least 30. Must not be greater than 365.'
        required: false
        example: 90
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 'Delicious Eats Restaurant'
      tenant_type: business
      status: active
      subscription_plan_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      settings:
        features:
          api_access_enabled: true
          webhook_enabled: false
          real_time_tracking: true
        limits:
          max_orders_per_month: 1000
          max_api_calls_per_day: 10000
          max_storage_mb: 1000
        security:
          two_factor_required: false
          session_timeout_minutes: 60
          password_expiry_days: 90
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/admin/tenants/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Archive the specified tenant (soft delete).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the tenant.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/businesses
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of businesses.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.409484Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 2f7fb5b6-e2f9-4b3b-9339-693ad13a3de5
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-g1KHZzEx2oUBNt6NgmY6Rw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/admin/businesses
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      business_name:
        name: business_name
        description: 'Name of the business. Must not be greater than 255 characters. Must be at least 2 characters.'
        required: true
        example: 'Delicious Eats Restaurant'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_type:
        name: business_type
        description: 'Type of business.'
        required: true
        example: food
        type: string
        enumValues:
          - food
          - retail
          - fashion
          - service
          - other
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Business description. Must not be greater than 1000 characters.'
        required: false
        example: 'A family-owned restaurant serving authentic Nigerian cuisine'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      subdomain:
        name: subdomain
        description: 'Unique subdomain for the business (auto-generated if not provided). Must match the regex /^[a-z0-9-]+$/. Must not be greater than 63 characters. Must be at least 3 characters.'
        required: false
        example: delicious-eats
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      slug:
        name: slug
        description: 'URL-friendly slug for the business (auto-generated if not provided). Must match the regex /^[a-z0-9-]+$/. Must not be greater than 255 characters.'
        required: false
        example: delicious-eats-restaurant
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_email:
        name: contact_email
        description: 'Business contact email. Must be a valid email address. Must not be greater than 255 characters.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      contact_phone:
        name: contact_phone
        description: 'Business contact phone number. Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      country_id:
        name: country_id
        description: 'Country ID where the business is located. Must be a valid UUID. The <code>id</code> of an existing record in the countries table.'
        required: true
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      state_id:
        name: state_id
        description: 'State ID where the business is located. Must be a valid UUID. The <code>id</code> of an existing record in the states table.'
        required: false
        example: b2c3d4e5-f6g7-8901-bcde-f23456789012
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      cac_registration_number:
        name: cac_registration_number
        description: 'Corporate Affairs Commission registration number. Must not be greater than 255 characters.'
        required: false
        example: RC123456
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      tax_identification_number:
        name: tax_identification_number
        description: 'Tax identification number. Must not be greater than 255 characters.'
        required: false
        example: TIN123456789
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      global_auto_accept_orders:
        name: global_auto_accept_orders
        description: 'Whether to automatically accept orders globally.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      auto_acceptance_criteria:
        name: auto_acceptance_criteria
        description: 'Criteria for automatically accepting orders.'
        required: false
        example:
          min_value: 500
          max_distance: 5
          business_hours_only: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.min_value:
        name: auto_acceptance_criteria.min_value
        description: 'Must be at least 0.'
        required: false
        example: 500.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.max_distance:
        name: auto_acceptance_criteria.max_distance
        description: 'Must be at least 0.'
        required: false
        example: 5.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.business_hours_only:
        name: auto_acceptance_criteria.business_hours_only
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      accepts_cash_on_delivery:
        name: accepts_cash_on_delivery
        description: 'Whether the business accepts cash on delivery.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      allows_pickup:
        name: allows_pickup
        description: 'Whether the business allows customer pickup.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      operating_hours:
        name: operating_hours
        description: 'Business operating hours for each day.'
        required: false
        example:
          monday:
            open: '09:00'
            close: '22:00'
          tuesday:
            open: '09:00'
            close: '22:00'
          wednesday:
            open: '09:00'
            close: '22:00'
          thursday:
            open: '09:00'
            close: '22:00'
          friday:
            open: '09:00'
            close: '23:00'
          saturday:
            open: '10:00'
            close: '23:00'
          sunday:
            open: '12:00'
            close: '21:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday:
        name: operating_hours.monday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday.open:
        name: operating_hours.monday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday.close:
        name: operating_hours.monday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday:
        name: operating_hours.tuesday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday.open:
        name: operating_hours.tuesday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday.close:
        name: operating_hours.tuesday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday:
        name: operating_hours.wednesday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday.open:
        name: operating_hours.wednesday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday.close:
        name: operating_hours.wednesday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday:
        name: operating_hours.thursday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday.open:
        name: operating_hours.thursday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday.close:
        name: operating_hours.thursday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday:
        name: operating_hours.friday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '23:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday.open:
        name: operating_hours.friday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday.close:
        name: operating_hours.friday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '23:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday:
        name: operating_hours.saturday
        description: ''
        required: false
        example:
          open: '10:00'
          close: '23:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday.open:
        name: operating_hours.saturday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '10:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday.close:
        name: operating_hours.saturday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '23:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday:
        name: operating_hours.sunday
        description: ''
        required: false
        example:
          open: '12:00'
          close: '21:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday.open:
        name: operating_hours.sunday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '12:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday.close:
        name: operating_hours.sunday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '21:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address:
        name: address
        description: 'Business address information.'
        required: false
        example:
          street: '123 Main Street'
          city: Lagos
          state: 'Lagos State'
          postal_code: '100001'
          country: Nigeria
          latitude: 6.5244
          longitude: 3.3792
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address.address_line_1:
        name: address.address_line_1
        description: 'This field is required when <code>address</code> is present. Must not be greater than 255 characters.'
        required: false
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.address_line_2:
        name: address.address_line_2
        description: 'Must not be greater than 255 characters.'
        required: false
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address.city:
        name: address.city
        description: 'This field is required when <code>address</code> is present. Must not be greater than 100 characters.'
        required: false
        example: Lagos
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.state:
        name: address.state
        description: 'This field is required when <code>address</code> is present. Must not be greater than 100 characters.'
        required: false
        example: 'Lagos State'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.postal_code:
        name: address.postal_code
        description: 'Must not be greater than 20 characters.'
        required: false
        example: '100001'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address.latitude:
        name: address.latitude
        description: 'Must be between -90 and 90.'
        required: false
        example: 6.5244
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address.longitude:
        name: address.longitude
        description: 'Must be between -180 and 180.'
        required: false
        example: 3.3792
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      admin_user_id:
        name: admin_user_id
        description: 'ID of the user to assign as business admin. Must be a valid UUID. The <code>id</code> of an existing record in the users table.'
        required: false
        example: c3d4e5f6-g7h8-9012-cdef-g34567890123
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      business_name: 'Delicious Eats Restaurant'
      business_type: food
      description: 'A family-owned restaurant serving authentic Nigerian cuisine'
      subdomain: delicious-eats
      slug: delicious-eats-restaurant
      contact_email: <EMAIL>
      contact_phone: '+*************'
      country_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      state_id: b2c3d4e5-f6g7-8901-bcde-f23456789012
      cac_registration_number: RC123456
      tax_identification_number: TIN123456789
      global_auto_accept_orders: false
      auto_acceptance_criteria:
        min_value: 500
        max_distance: 5
        business_hours_only: true
      accepts_cash_on_delivery: false
      allows_pickup: false
      operating_hours:
        monday:
          open: '09:00'
          close: '22:00'
        tuesday:
          open: '09:00'
          close: '22:00'
        wednesday:
          open: '09:00'
          close: '22:00'
        thursday:
          open: '09:00'
          close: '22:00'
        friday:
          open: '09:00'
          close: '23:00'
        saturday:
          open: '10:00'
          close: '23:00'
        sunday:
          open: '12:00'
          close: '21:00'
      address:
        street: '123 Main Street'
        city: Lagos
        state: 'Lagos State'
        postal_code: '100001'
        country: Nigeria
        latitude: 6.5244
        longitude: 3.3792
        address_line_1: b
        address_line_2: 'n'
      admin_user_id: c3d4e5f6-g7h8-9012-cdef-g34567890123
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/businesses/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.421819Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 2c7b7207-519f-414c-a43b-8e8d112122d7
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-0XlIPDMsKrgwpb9572EOBA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/v1/admin/businesses/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      business_name:
        name: business_name
        description: 'Name of the business. Must not be greater than 255 characters. Must be at least 2 characters.'
        required: false
        example: 'Delicious Eats Restaurant'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_type:
        name: business_type
        description: 'Type of business.'
        required: false
        example: food
        type: string
        enumValues:
          - food
          - retail
          - fashion
          - service
          - other
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'Business description. Must not be greater than 1000 characters.'
        required: false
        example: 'A family-owned restaurant serving authentic Nigerian cuisine'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      subdomain:
        name: subdomain
        description: 'Unique subdomain for the business. Must match the regex /^[a-z0-9-]+$/. Must not be greater than 63 characters. Must be at least 3 characters.'
        required: false
        example: delicious-eats
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      slug:
        name: slug
        description: 'URL-friendly slug for the business. Must match the regex /^[a-z0-9-]+$/. Must not be greater than 255 characters.'
        required: false
        example: delicious-eats-restaurant
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      contact_email:
        name: contact_email
        description: 'Business contact email. Must be a valid email address. Must not be greater than 255 characters.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      contact_phone:
        name: contact_phone
        description: 'Business contact phone number. Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      country_id:
        name: country_id
        description: 'Country ID where the business is located. Must be a valid UUID. The <code>id</code> of an existing record in the countries table.'
        required: false
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      state_id:
        name: state_id
        description: 'State ID where the business is located. Must be a valid UUID. The <code>id</code> of an existing record in the states table.'
        required: false
        example: b2c3d4e5-f6g7-8901-bcde-f23456789012
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      cac_registration_number:
        name: cac_registration_number
        description: 'Corporate Affairs Commission registration number. Must not be greater than 255 characters.'
        required: false
        example: RC123456
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      tax_identification_number:
        name: tax_identification_number
        description: 'Tax identification number. Must not be greater than 255 characters.'
        required: false
        example: TIN123456789
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      global_auto_accept_orders:
        name: global_auto_accept_orders
        description: 'Whether to automatically accept orders globally.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      auto_acceptance_criteria:
        name: auto_acceptance_criteria
        description: 'Criteria for automatically accepting orders.'
        required: false
        example:
          min_value: 500
          max_distance: 5
          business_hours_only: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.min_value:
        name: auto_acceptance_criteria.min_value
        description: 'Must be at least 0.'
        required: false
        example: 500.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.max_distance:
        name: auto_acceptance_criteria.max_distance
        description: 'Must be at least 0.'
        required: false
        example: 5.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.business_hours_only:
        name: auto_acceptance_criteria.business_hours_only
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      accepts_cash_on_delivery:
        name: accepts_cash_on_delivery
        description: 'Whether the business accepts cash on delivery.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      allows_pickup:
        name: allows_pickup
        description: 'Whether the business allows customer pickup.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      operating_hours:
        name: operating_hours
        description: 'Business operating hours for each day.'
        required: false
        example:
          monday:
            open: '09:00'
            close: '22:00'
          tuesday:
            open: '09:00'
            close: '22:00'
          wednesday:
            open: '09:00'
            close: '22:00'
          thursday:
            open: '09:00'
            close: '22:00'
          friday:
            open: '09:00'
            close: '23:00'
          saturday:
            open: '10:00'
            close: '23:00'
          sunday:
            open: '12:00'
            close: '21:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday:
        name: operating_hours.monday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday.open:
        name: operating_hours.monday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday.close:
        name: operating_hours.monday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday:
        name: operating_hours.tuesday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday.open:
        name: operating_hours.tuesday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday.close:
        name: operating_hours.tuesday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday:
        name: operating_hours.wednesday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday.open:
        name: operating_hours.wednesday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday.close:
        name: operating_hours.wednesday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday:
        name: operating_hours.thursday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday.open:
        name: operating_hours.thursday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday.close:
        name: operating_hours.thursday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday:
        name: operating_hours.friday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '23:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday.open:
        name: operating_hours.friday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday.close:
        name: operating_hours.friday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '23:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday:
        name: operating_hours.saturday
        description: ''
        required: false
        example:
          open: '10:00'
          close: '23:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday.open:
        name: operating_hours.saturday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '10:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday.close:
        name: operating_hours.saturday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '23:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday:
        name: operating_hours.sunday
        description: ''
        required: false
        example:
          open: '12:00'
          close: '21:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday.open:
        name: operating_hours.sunday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '12:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday.close:
        name: operating_hours.sunday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '21:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      logo:
        name: logo
        description: 'Business logo image file (JPEG, PNG, JPG, GIF, or SVG, max 2MB). Must be an image. Must not be greater than 2048 kilobytes.'
        required: false
        example: null
        type: file
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      verification_documents:
        name: verification_documents
        description: 'Must not be greater than 500 characters.'
        required: false
        example:
          - b
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      business_name: 'Delicious Eats Restaurant'
      business_type: food
      description: 'A family-owned restaurant serving authentic Nigerian cuisine'
      subdomain: delicious-eats
      slug: delicious-eats-restaurant
      contact_email: <EMAIL>
      contact_phone: '+*************'
      country_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      state_id: b2c3d4e5-f6g7-8901-bcde-f23456789012
      cac_registration_number: RC123456
      tax_identification_number: TIN123456789
      global_auto_accept_orders: false
      auto_acceptance_criteria:
        min_value: 500
        max_distance: 5
        business_hours_only: true
      accepts_cash_on_delivery: false
      allows_pickup: false
      operating_hours:
        monday:
          open: '09:00'
          close: '22:00'
        tuesday:
          open: '09:00'
          close: '22:00'
        wednesday:
          open: '09:00'
          close: '22:00'
        thursday:
          open: '09:00'
          close: '22:00'
        friday:
          open: '09:00'
          close: '23:00'
        saturday:
          open: '10:00'
          close: '23:00'
        sunday:
          open: '12:00'
          close: '21:00'
      verification_documents:
        - b
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/admin/businesses/{id}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified business (soft delete).'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/businesses/{business}/restore'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Restore an archived business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/businesses/{business}/activate'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Activate a business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/businesses/{business}/suspend'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Suspend a business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/businesses/{business}/verify'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Mark business as verified.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/businesses/{business}/admins'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get business admins.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.436628Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: a55ab7fa-c091-4173-b9ba-e5bb3cadd3f3
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-TFQdW6EPgTC136GS7I1iaw==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/businesses/{business}/assign-admin'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Assign admin to business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      user_id:
        name: user_id
        description: 'Must be a valid UUID. The <code>id</code> of an existing record in the users table.'
        required: true
        example: 6ff8f7f6-1eb3-3525-be4a-3932c805afed
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      role:
        name: role
        description: ''
        required: true
        example: business-manager
        type: string
        enumValues:
          - business-admin
          - business-manager
          - business-owner
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      user_id: 6ff8f7f6-1eb3-3525-be4a-3932c805afed
      role: business-manager
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/businesses/{business}/transfer-admin'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Transfer business ownership.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      new_owner_id:
        name: new_owner_id
        description: 'Must be a valid UUID. The <code>id</code> of an existing record in the users table.'
        required: true
        example: 6ff8f7f6-1eb3-3525-be4a-3932c805afed
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      new_owner_id: 6ff8f7f6-1eb3-3525-be4a-3932c805afed
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/v1/admin/businesses/{business}/admins/{user}/role'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update admin role.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      user:
        name: user
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
      user: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      role:
        name: role
        description: ''
        required: true
        example: business-manager
        type: string
        enumValues:
          - business-admin
          - business-manager
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      role: business-manager
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/admin/businesses/{business}/admins/{user}'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove admin from business.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      business:
        name: business
        description: 'The business.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      user:
        name: user
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      business: architecto
      user: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/home
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: ''
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 500
        content: '{"success":false,"message":"Attempt to read property \"id\" on null","error_code":"INTERNAL_ERROR","timestamp":"2025-06-09T13:55:14.290164Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: f4a5de63-49aa-41f6-a775-4eef5ee84111
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-Q8UT22cQy8EBbYl+No6UnA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/tenant-auth/login
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Tenant domain login - Only allows users with matching tenant_id.'
      description: 'Used by business/provider staff on {tenant}.deliverynexus.com'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      identifier:
        name: identifier
        description: "User's email address or phone number."
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      password:
        name: password
        description: "User's password."
        required: true
        example: password123
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      remember_me:
        name: remember_me
        description: 'Whether to remember the user for extended login.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      identifier: <EMAIL>
      password: password123
      remember_me: false
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/tenant-auth/logout
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Logout user.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/tenant-auth/refresh
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Refresh token.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/tenant-auth/me
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get authenticated user.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:14.303950Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 0b26d505-9199-4eeb-adc0-d4bad6eedfa7
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-YvkTVagFzORCT8sacpl9jA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/provider/service-areas/{area}/activate'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Activate service area.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      area:
        name: area
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      area: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/provider/service-areas/{area}/deactivate'
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Deactivate service area.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      area:
        name: area
        description: ''
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      area: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/tenant/user/profile
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get user profile for tenant users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:14.660205Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 7dc566e1-d236-4292-9284-7c5f00f89158
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-SyX/hE/Q5C+4fQ4ORKmieA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/tenant/user/profile
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update user profile for tenant users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      first_name:
        name: first_name
        description: "User's first name. Must not be greater than 255 characters. Must be at least 2 characters."
        required: false
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: "User's last name. Must not be greater than 255 characters. Must be at least 2 characters."
        required: false
        example: Doe
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      email:
        name: email
        description: "User's email address. Must be a valid email address. Must not be greater than 255 characters."
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: 'User''s phone number in Nigerian format. Must match the regex /^\+234[0-9]{10}$/. Must not be greater than 20 characters.'
        required: false
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      timezone:
        name: timezone
        description: "User's timezone. Must be a valid time zone, such as <code>Africa/Accra</code>. Must not be greater than 50 characters."
        required: false
        example: Africa/Lagos
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      avatar_url:
        name: avatar_url
        description: "URL to user's avatar image. Must be a valid URL. Must not be greater than 500 characters."
        required: false
        example: 'https://example.com/avatar.jpg'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      bio:
        name: bio
        description: "User's biography. Must not be greater than 1000 characters."
        required: false
        example: 'Software developer passionate about technology'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      date_of_birth:
        name: date_of_birth
        description: "User's date of birth. Must be a valid date. Must be a date before <code>today</code>."
        required: false
        example: '1990-01-15'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      gender:
        name: gender
        description: "User's gender."
        required: false
        example: male
        type: string
        enumValues:
          - male
          - female
          - other
          - prefer_not_to_say
        exampleWasSpecified: false
        nullable: true
        custom: []
      address:
        name: address
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.street:
        name: address.street
        description: 'This field is required when <code>address</code> is present. Must not be greater than 255 characters.'
        required: false
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.city_id:
        name: address.city_id
        description: 'This field is required when <code>address</code> is present. Must be a valid UUID. The <code>id</code> of an existing record in the cities table.'
        required: false
        example: a4855dc5-0acb-33c3-b921-f4291f719ca0
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.state_id:
        name: address.state_id
        description: 'This field is required when <code>address</code> is present. Must be a valid UUID. The <code>id</code> of an existing record in the states table.'
        required: false
        example: c90237e9-ced5-3af6-88ea-84aeaa148878
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.country_id:
        name: address.country_id
        description: 'This field is required when <code>address</code> is present. Must be a valid UUID. The <code>id</code> of an existing record in the countries table.'
        required: false
        example: a1a0a47d-e8c3-3cf0-8e6e-c1ff9dca5d1f
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      address.postal_code:
        name: address.postal_code
        description: 'Must not be greater than 20 characters.'
        required: false
        example: vdljnikhwaykcmyu
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      address.is_default:
        name: address.is_default
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      first_name: John
      last_name: Doe
      email: <EMAIL>
      phone_number: '+*************'
      timezone: Africa/Lagos
      avatar_url: 'https://example.com/avatar.jpg'
      bio: 'Software developer passionate about technology'
      date_of_birth: '1990-01-15'
      gender: male
      address:
        street: b
        city_id: a4855dc5-0acb-33c3-b921-f4291f719ca0
        state_id: c90237e9-ced5-3af6-88ea-84aeaa148878
        country_id: a1a0a47d-e8c3-3cf0-8e6e-c1ff9dca5d1f
        postal_code: vdljnikhwaykcmyu
        is_default: false
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/tenant/user/change-password
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Change user password for tenant users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      current_password:
        name: current_password
        description: 'Current password for verification.'
        required: true
        example: CurrentPassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      new_password:
        name: new_password
        description: 'New password (minimum 8 characters, must contain uppercase, lowercase, numbers, and symbols).'
        required: true
        example: NewSecurePassword123!
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      current_password: CurrentPassword123!
      new_password: NewSecurePassword123!
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/tenant/user/preferences
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get user preferences for tenant users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:14.669328Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: fbbfd677-73c7-44b8-a430-090bbf9e930f
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-14p40N+J6/GxZQUfs255Bg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/tenant/user/preferences
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update user preferences for tenant users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      notifications:
        name: notifications
        description: 'Notification preferences.'
        required: false
        example:
          email_enabled: true
          sms_enabled: false
          push_enabled: true
          marketing_enabled: false
          order_updates: true
          delivery_updates: true
          promotional_offers: false
          security_alerts: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.email_enabled:
        name: notifications.email_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.sms_enabled:
        name: notifications.sms_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.push_enabled:
        name: notifications.push_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.marketing_enabled:
        name: notifications.marketing_enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.order_updates:
        name: notifications.order_updates
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.delivery_updates:
        name: notifications.delivery_updates
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.promotional_offers:
        name: notifications.promotional_offers
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notifications.security_alerts:
        name: notifications.security_alerts
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication:
        name: communication
        description: 'Communication preferences.'
        required: false
        example:
          preferred_language: en
          preferred_contact_method: email
          contact_time_start: '09:00'
          contact_time_end: '18:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.preferred_language:
        name: communication.preferred_language
        description: ''
        required: false
        example: en
        type: string
        enumValues:
          - en
          - yo
          - ig
          - ha
          - fr
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.preferred_contact_method:
        name: communication.preferred_contact_method
        description: ''
        required: false
        example: email
        type: string
        enumValues:
          - email
          - sms
          - phone
          - whatsapp
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.contact_time_start:
        name: communication.contact_time_start
        description: 'Must be a valid date in the format <code>H:i</code>.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      communication.contact_time_end:
        name: communication.contact_time_end
        description: 'Must be a valid date in the format <code>H:i</code>.'
        required: false
        example: '18:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy:
        name: privacy
        description: 'Privacy preferences.'
        required: false
        example:
          profile_visibility: private
          location_sharing: false
          activity_tracking: true
          data_analytics: true
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.profile_visibility:
        name: privacy.profile_visibility
        description: ''
        required: false
        example: private
        type: string
        enumValues:
          - public
          - private
          - contacts_only
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.location_sharing:
        name: privacy.location_sharing
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.activity_tracking:
        name: privacy.activity_tracking
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      privacy.data_analytics:
        name: privacy.data_analytics
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      app:
        name: app
        description: 'Application preferences.'
        required: false
        example:
          theme: auto
          currency: NGN
          distance_unit: km
          temperature_unit: celsius
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.theme:
        name: app.theme
        description: ''
        required: false
        example: auto
        type: string
        enumValues:
          - light
          - dark
          - auto
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.currency:
        name: app.currency
        description: ''
        required: false
        example: NGN
        type: string
        enumValues:
          - NGN
          - USD
          - EUR
          - GBP
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.distance_unit:
        name: app.distance_unit
        description: ''
        required: false
        example: km
        type: string
        enumValues:
          - km
          - miles
        exampleWasSpecified: false
        nullable: false
        custom: []
      app.temperature_unit:
        name: app.temperature_unit
        description: ''
        required: false
        example: celsius
        type: string
        enumValues:
          - celsius
          - fahrenheit
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      notifications:
        email_enabled: true
        sms_enabled: false
        push_enabled: true
        marketing_enabled: false
        order_updates: true
        delivery_updates: true
        promotional_offers: false
        security_alerts: true
      communication:
        preferred_language: en
        preferred_contact_method: email
        contact_time_start: '09:00'
        contact_time_end: '18:00'
      privacy:
        profile_visibility: private
        location_sharing: false
        activity_tracking: true
        data_analytics: true
      app:
        theme: auto
        currency: NGN
        distance_unit: km
        temperature_unit: celsius
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/tenant/user/deactivate
    metadata:
      groupName: Endpoints
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Deactivate user account for tenant users only.'
      description: ''
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      password:
        name: password
        description: 'Current password for verification. Must be at least 8 characters.'
        required: true
        example: password123
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      reason:
        name: reason
        description: 'Optional reason for account deactivation. Must not be greater than 500 characters.'
        required: false
        example: 'No longer needed'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      password: password123
      reason: 'No longer needed'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
