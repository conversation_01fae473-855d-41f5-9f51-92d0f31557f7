name: 'Tenant Subscriptions'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/subscription/plans
    metadata:
      groupName: 'Tenant Subscriptions'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get available subscription plans for current tenant type.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "data": {
              "tenant_type": "business",
              "plans": [
                {
                  "id": "plan-uuid",
                  "name": "Business Starter",
                  "slug": "business_starter",
                  "description": "Enhanced plan for growing businesses",
                  "target_type": "business",
                  "plan_prices": [
                    {
                      "billing_interval": "monthly",
                      "price": "10000.00",
                      "currency": "NGN",
                      "formatted_price": "NGN 10,000.00"
                    }
                  ]
                }
              ]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/subscription/current
    metadata:
      groupName: 'Tenant Subscriptions'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get current tenant's subscription status."
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "data": {
              "tenant_type": "business",
              "subscription": {
                "plan": "Business Starter",
                "status": "active",
                "expires_at": "2024-02-15T10:30:00Z",
                "auto_renew": true
              },
              "usage": {
                "daily": {
                  "limit": 1000,
                  "used": 45,
                  "remaining": 955
                }
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/subscription/subscribe
    metadata:
      groupName: 'Tenant Subscriptions'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Subscribe to a plan.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      plan_slug:
        name: plan_slug
        description: 'The subscription plan slug.'
        required: true
        example: business_starter
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      billing_interval:
        name: billing_interval
        description: 'The billing interval.'
        required: false
        example: monthly
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      payment_method_id:
        name: payment_method_id
        description: 'The payment method ID for billing.'
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
    cleanBodyParameters:
      plan_slug: business_starter
      billing_interval: monthly
      payment_method_id: architecto
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "success": true,
            "data": {
              "id": "subscription-uuid",
              "plan": {
                "name": "Business Starter",
                "slug": "business_starter"
              },
              "status": "active",
              "started_at": "2024-01-15T10:30:00Z",
              "expires_at": "2024-02-15T10:30:00Z"
            },
            "message": "Successfully subscribed to plan"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/subscription/upgrade
    metadata:
      groupName: 'Tenant Subscriptions'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Upgrade subscription to a higher plan.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      plan_slug:
        name: plan_slug
        description: 'The new subscription plan slug.'
        required: true
        example: business_business
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      billing_interval:
        name: billing_interval
        description: 'The billing interval.'
        required: false
        example: monthly
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      plan_slug: business_business
      billing_interval: monthly
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "data": {
              "id": "subscription-uuid",
              "plan": {
                "name": "Business Business",
                "slug": "business_business"
              },
              "status": "active",
              "prorated_amount": "5000.00"
            },
            "message": "Successfully upgraded subscription"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/subscription/cancel
    metadata:
      groupName: 'Tenant Subscriptions'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Cancel current subscription.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "data": {
              "cancelled_at": "2024-01-15T10:30:00Z",
              "expires_at": "2024-02-15T10:30:00Z",
              "message": "Subscription will remain active until expiry date"
            },
            "message": "Subscription cancelled successfully"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/subscription/features/{feature_slug}'
    metadata:
      groupName: 'Tenant Subscriptions'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Check access to a specific feature.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      feature_slug:
        name: feature_slug
        description: 'The feature slug to check.'
        required: true
        example: advanced_analytics
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      feature_slug: advanced_analytics
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "data": {
              "feature_slug": "advanced_analytics",
              "has_access": true,
              "limit": null,
              "unlimited": true,
              "required_plan": null
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/subscription/usage
    metadata:
      groupName: 'Tenant Subscriptions'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get API usage statistics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      from:
        name: from
        description: 'Start date for usage report (Y-m-d format).'
        required: false
        example: '2024-01-01'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      to:
        name: to
        description: 'End date for usage report (Y-m-d format).'
        required: false
        example: '2024-01-31'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      from: '2024-01-01'
      to: '2024-01-31'
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "data": {
              "period": {
                "from": "2024-01-01",
                "to": "2024-01-31"
              },
              "summary": {
                "total_requests": 15420,
                "average_daily": 497.4,
                "peak_day": "2024-01-15",
                "peak_usage": 1250
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
