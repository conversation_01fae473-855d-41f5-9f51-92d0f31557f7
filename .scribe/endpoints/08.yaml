name: 'Product Search'
description: |-

  APIs for searching and filtering products across all businesses
endpoints:
  -
    httpMethods:
      - POST
    uri: api/v1/customer/search/products
    metadata:
      groupName: 'Product Search'
      groupDescription: |-

        APIs for searching and filtering products across all businesses
      subgroup: ''
      subgroupDescription: ''
      title: 'Search Products'
      description: 'Search for products across all businesses with advanced filtering and sorting options.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      query:
        name: query
        description: 'optional The search query.'
        required: false
        example: pizza
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      filters:
        name: filters
        description: 'optional Filters to apply to the search.'
        required: false
        example: []
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      filters.category_id:
        name: filters.category_id
        description: 'optional Filter by category ID.'
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      filters.business_id:
        name: filters.business_id
        description: 'optional Filter by business ID.'
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      filters.business_type:
        name: filters.business_type
        description: 'optional Filter by business type (food, retail, service).'
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      filters.min_price:
        name: filters.min_price
        description: 'optional Minimum price filter.'
        required: false
        example: 4326.41688
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      filters.max_price:
        name: filters.max_price
        description: 'optional Maximum price filter.'
        required: false
        example: 4326.41688
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      filters.in_stock:
        name: filters.in_stock
        description: 'optional Filter for products in stock.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      filters.allows_pickup:
        name: filters.allows_pickup
        description: 'optional Filter for products that allow pickup.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      filters.tags:
        name: filters.tags
        description: 'optional Filter by product tags.'
        required: false
        example:
          - architecto
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      filters.rating_min:
        name: filters.rating_min
        description: 'optional Minimum rating filter (1-5).'
        required: false
        example: 4326.41688
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      sort:
        name: sort
        description: 'optional Sort order. Available: relevance, price_asc, price_desc, rating, newest, oldest, name_asc, name_desc.'
        required: false
        example: price_asc
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'optional Number of results per page (max 50).'
        required: false
        example: 20
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      page:
        name: page
        description: 'optional Page number.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      query: pizza
      filters:
        category_id: architecto
        business_id: architecto
        business_type: architecto
        min_price: 4326.41688
        max_price: 4326.41688
        in_stock: false
        allows_pickup: false
        tags:
          - architecto
        rating_min: 4326.41688
      sort: price_asc
      per_page: 20
      page: 1
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Products found successfully",
            "data": {
              "data": [
                {
                  "id": "uuid",
                  "name": "Margherita Pizza",
                  "description": "Classic pizza with tomato sauce and mozzarella",
                  "price": "2500.00",
                  "sale_price": null,
                  "main_image_url": "https://example.com/pizza.jpg",
                  "is_available": true,
                  "allows_pickup": true,
                  "rating": 4.5,
                  "business": {
                    "id": "uuid",
                    "business_name": "Mario's Pizza",
                    "business_type": "food"
                  },
                  "category": {
                    "id": "uuid",
                    "name": "Pizza"
                  }
                }
              ],
              "current_page": 1,
              "per_page": 20,
              "total": 150,
              "last_page": 8
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/search/products/suggestions
    metadata:
      groupName: 'Product Search'
      groupDescription: |-

        APIs for searching and filtering products across all businesses
      subgroup: ''
      subgroupDescription: ''
      title: 'Get Search Suggestions'
      description: 'Get search suggestions based on a partial query.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      q:
        name: q
        description: 'The partial search query.'
        required: true
        example: piz
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      limit:
        name: limit
        description: 'optional Maximum number of suggestions (default: 10).'
        required: false
        example: 5
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      q: piz
      limit: 5
    bodyParameters:
      q:
        name: q
        description: 'Must be at least 2 characters. Must not be greater than 100 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      limit:
        name: limit
        description: 'Must be at least 1. Must not be greater than 20.'
        required: false
        example: 12
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      q: b
      limit: 12
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Search suggestions retrieved successfully",
            "data": [
              "pizza",
              "pizza margherita",
              "pizza pepperoni"
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/search/products/popular-terms
    metadata:
      groupName: 'Product Search'
      groupDescription: |-

        APIs for searching and filtering products across all businesses
      subgroup: ''
      subgroupDescription: ''
      title: 'Get Popular Search Terms'
      description: 'Get a list of popular search terms.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      limit:
        name: limit
        description: 'optional Maximum number of terms (default: 10).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      limit: 15
    bodyParameters:
      limit:
        name: limit
        description: 'Must be at least 1. Must not be greater than 50.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      limit: 1
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Popular search terms retrieved successfully",
            "data": [
              "pizza",
              "burger",
              "chicken",
              "rice",
              "pasta"
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/search/products/trending
    metadata:
      groupName: 'Product Search'
      groupDescription: |-

        APIs for searching and filtering products across all businesses
      subgroup: ''
      subgroupDescription: ''
      title: 'Get Trending Products'
      description: 'Get a list of trending/popular products.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      limit:
        name: limit
        description: 'optional Maximum number of products (default: 20).'
        required: false
        example: 10
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      limit: 10
    bodyParameters:
      limit:
        name: limit
        description: 'Must be at least 1. Must not be greater than 50.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      limit: 1
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Trending products retrieved successfully",
            "data": [
              {
                "id": "uuid",
                "name": "Trending Product",
                "price": "1500.00",
                "rating": 4.8,
                "business": {
                  "business_name": "Popular Restaurant"
                }
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/search/products/filters
    metadata:
      groupName: 'Product Search'
      groupDescription: |-

        APIs for searching and filtering products across all businesses
      subgroup: ''
      subgroupDescription: ''
      title: 'Get Search Filters'
      description: 'Get available filter options for product search.'
      authenticated: false
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Search filters retrieved successfully",
            "data": {
              "business_types": [
                {"value": "food", "label": "Food & Restaurants"},
                {"value": "retail", "label": "Retail & Shopping"},
                {"value": "service", "label": "Services"}
              ],
              "price_ranges": [
                {"min": 0, "max": 1000, "label": "Under ₦1,000"},
                {"min": 1000, "max": 5000, "label": "₦1,000 - ₦5,000"},
                {"min": 5000, "max": 10000, "label": "₦5,000 - ₦10,000"},
                {"min": 10000, "max": null, "label": "Above ₦10,000"}
              ],
              "sort_options": [
                {"value": "relevance", "label": "Most Relevant"},
                {"value": "price_asc", "label": "Price: Low to High"},
                {"value": "price_desc", "label": "Price: High to Low"},
                {"value": "rating", "label": "Highest Rated"},
                {"value": "newest", "label": "Newest First"}
              ]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
