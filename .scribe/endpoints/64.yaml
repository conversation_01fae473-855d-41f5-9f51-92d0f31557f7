name: 'Business Orders'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/business/orders
    metadata:
      groupName: 'Business Orders'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of orders for the business.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 'Page number for pagination.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Number of items per page (max 100).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      search:
        name: search
        description: 'Search in order reference, customer name.'
        required: false
        example: ORD-2024
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: 'Filter by order status.'
        required: false
        example: pending
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      payment_status:
        name: payment_status
        description: 'Filter by payment status.'
        required: false
        example: paid
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      date_from:
        name: date_from
        description: 'Filter orders from date (Y-m-d).'
        required: false
        example: '2024-01-01'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      date_to:
        name: date_to
        description: 'Filter orders to date (Y-m-d).'
        required: false
        example: '2024-01-31'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: 'Sort by field (created_at, total_amount, status).'
        required: false
        example: created_at
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_direction:
        name: sort_direction
        description: 'Sort direction (asc, desc).'
        required: false
        example: desc
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 15
      search: ORD-2024
      status: pending
      payment_status: paid
      date_from: '2024-01-01'
      date_to: '2024-01-31'
      sort_by: created_at
      sort_direction: desc
    bodyParameters:
      page:
        name: page
        description: 'Must be at least 1.'
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Must be at least 1. Must not be greater than 100.'
        required: false
        example: 22
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      search:
        name: search
        description: 'Must not be greater than 255 characters.'
        required: false
        example: g
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      status:
        name: status
        description: ''
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      payment_status:
        name: payment_status
        description: ''
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      date_from:
        name: date_from
        description: 'Must be a valid date. Must be a valid date in the format <code>Y-m-d</code>.'
        required: false
        example: '2025-06-09'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      date_to:
        name: date_to
        description: 'Must be a valid date. Must be a valid date in the format <code>Y-m-d</code>. Must be a date after or equal to <code>date_from</code>.'
        required: false
        example: '2051-07-03'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: ''
        required: false
        example: total_amount
        type: string
        enumValues:
          - created_at
          - total_amount
          - status
          - payment_status
        exampleWasSpecified: false
        nullable: false
        custom: []
      sort_direction:
        name: sort_direction
        description: ''
        required: false
        example: asc
        type: string
        enumValues:
          - asc
          - desc
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      page: 16
      per_page: 22
      search: g
      status: architecto
      payment_status: architecto
      date_from: '2025-06-09'
      date_to: '2051-07-03'
      sort_by: total_amount
      sort_direction: asc
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Orders retrieved successfully",
            "data": {
              "orders": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "order_reference": "ORD-2024-001",
                  "customer": {
                    "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                    "name": "John Doe",
                    "email": "<EMAIL>"
                  },
                  "status": "pending",
                  "payment_status": "pending",
                  "total_amount": 5500,
                  "items_count": 3,
                  "created_at": "2024-01-15T10:30:00Z",
                  "estimated_preparation_time": 30
                }
              ],
              "pagination": {
                "current_page": 1,
                "per_page": 15,
                "total": 150,
                "last_page": 10
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/business/orders/{id}'
    metadata:
      groupName: 'Business Orders'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified order.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Order retrieved successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "order_reference": "ORD-2024-001",
              "customer": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                "name": "John Doe",
                "email": "<EMAIL>",
                "phone": "+234123456789"
              },
              "status": "pending",
              "payment_status": "pending",
              "order_type": "delivery",
              "total_amount": 5500,
              "sub_total": 5000,
              "delivery_fee": 500,
              "tax_amount": 0,
              "items": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
                  "product_name": "Jollof Rice",
                  "quantity": 2,
                  "price_per_unit": 2500,
                  "total_price": 5000,
                  "notes": "Extra spicy"
                }
              ],
              "delivery_address": {
                "street": "123 Main St",
                "city": "Lagos",
                "state": "Lagos",
                "country": "Nigeria"
              },
              "created_at": "2024-01-15T10:30:00Z",
              "estimated_preparation_time": 30,
              "customer_notes": "Please call when ready"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/v1/business/orders/{id}'
    metadata:
      groupName: 'Business Orders'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update order status and details.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      status:
        name: status
        description: 'New order status.'
        required: true
        example: accepted
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      business_notes:
        name: business_notes
        description: 'Business notes for the order.'
        required: false
        example: 'Order will be ready in 30 minutes'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      estimated_preparation_time:
        name: estimated_preparation_time
        description: 'Estimated preparation time in minutes.'
        required: false
        example: 30
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      cancellation_reason:
        name: cancellation_reason
        description: 'Reason for cancellation (required if status is cancelled).'
        required: false
        example: 'Out of stock'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      status: accepted
      business_notes: 'Order will be ready in 30 minutes'
      estimated_preparation_time: 30
      cancellation_reason: 'Out of stock'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Order updated successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "status": "accepted",
              "business_notes": "Order will be ready in 30 minutes",
              "estimated_preparation_time": 30,
              "accepted_at": "2024-01-15T11:00:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/business/orders/{order}/accept'
    metadata:
      groupName: 'Business Orders'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Accept an order.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      order:
        name: order
        description: 'The order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      order: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      business_notes:
        name: business_notes
        description: 'Business notes for the order.'
        required: false
        example: 'Order will be ready in 30 minutes'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      estimated_preparation_time:
        name: estimated_preparation_time
        description: 'Estimated preparation time in minutes.'
        required: false
        example: 30
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      business_notes: 'Order will be ready in 30 minutes'
      estimated_preparation_time: 30
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Order accepted successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "status": "confirmed",
              "business_notes": "Order will be ready in 30 minutes",
              "estimated_preparation_time": 30,
              "accepted_at": "2024-01-15T11:00:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/business/orders/{order}/reject'
    metadata:
      groupName: 'Business Orders'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Reject an order.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      order:
        name: order
        description: 'The order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      order: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      rejection_reason:
        name: rejection_reason
        description: 'Reason for rejecting the order.'
        required: true
        example: 'Out of stock'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      rejection_reason: 'Out of stock'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Order rejected successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "status": "cancelled",
              "cancellation_reason": "Out of stock",
              "cancelled_at": "2024-01-15T11:00:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/business/orders/{order}/prepare'
    metadata:
      groupName: 'Business Orders'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Mark order as being prepared.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      order:
        name: order
        description: 'The order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      order: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      business_notes:
        name: business_notes
        description: 'Business notes for the order.'
        required: false
        example: 'Started preparing your order'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      estimated_preparation_time:
        name: estimated_preparation_time
        description: 'Updated estimated preparation time in minutes.'
        required: false
        example: 25
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      business_notes: 'Started preparing your order'
      estimated_preparation_time: 25
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Order marked as being prepared",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "status": "processing",
              "business_notes": "Started preparing your order",
              "estimated_preparation_time": 25
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/business/orders/{order}/ready'
    metadata:
      groupName: 'Business Orders'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Mark order as ready for pickup/delivery.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      order:
        name: order
        description: 'The order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      order: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      business_notes:
        name: business_notes
        description: 'Business notes for the order.'
        required: false
        example: 'Order is ready for pickup'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      business_notes: 'Order is ready for pickup'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Order marked as ready",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "status": "ready_for_pickup",
              "business_notes": "Order is ready for pickup",
              "prepared_at": "2024-01-15T11:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/business/orders/{order}/complete'
    metadata:
      groupName: 'Business Orders'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Mark order as completed.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      order:
        name: order
        description: 'The order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      order: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      business_notes:
        name: business_notes
        description: 'Business notes for the order.'
        required: false
        example: 'Order completed successfully'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      business_notes: 'Order completed successfully'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Order completed successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "status": "delivered",
              "business_notes": "Order completed successfully",
              "completed_at": "2024-01-15T12:00:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/business/orders/{order}/cancel'
    metadata:
      groupName: 'Business Orders'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Cancel an order.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      order:
        name: order
        description: 'The order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      order: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      cancellation_reason:
        name: cancellation_reason
        description: 'Reason for cancelling the order.'
        required: true
        example: 'Customer requested cancellation'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      cancellation_reason: 'Customer requested cancellation'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Order cancelled successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "status": "cancelled",
              "cancellation_reason": "Customer requested cancellation",
              "cancelled_at": "2024-01-15T11:00:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
