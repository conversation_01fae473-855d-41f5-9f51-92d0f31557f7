name: 'Business Management'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/business/profile
    metadata:
      groupName: 'Business Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get current business profile.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Business profile retrieved successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "business_name": "Tasty Kitchen",
              "business_type": "food",
              "description": "Best Nigerian cuisine in Lagos",
              "logo_url": "/storage/tenants/tenant-id/business/logos/logo.jpg",
              "contact_email": "<EMAIL>",
              "contact_phone": "***********",
              "status": "active",
              "operating_hours": {
                "monday": {"open": "08:00", "close": "22:00"},
                "tuesday": {"open": "08:00", "close": "22:00"}
              },
              "global_auto_accept_orders": true,
              "auto_acceptance_criteria": {
                "min_value": 500,
                "max_distance": 5,
                "business_hours_only": true
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/business/profile
    metadata:
      groupName: 'Business Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update business profile.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: multipart/form-data
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      business_name:
        name: business_name
        description: 'optional Business name.'
        required: false
        example: '"Updated Kitchen"'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      business_type:
        name: business_type
        description: 'Type of business.'
        required: false
        example: food
        type: string
        enumValues:
          - food
          - retail
          - fashion
          - service
          - other
        exampleWasSpecified: false
        nullable: false
        custom: []
      description:
        name: description
        description: 'optional Business description.'
        required: false
        example: '"Best food in town"'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      subdomain:
        name: subdomain
        description: 'Unique subdomain for the business. Must match the regex /^[a-z0-9-]+$/. Must not be greater than 63 characters. Must be at least 3 characters.'
        required: false
        example: delicious-eats
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      slug:
        name: slug
        description: 'URL-friendly slug for the business. Must match the regex /^[a-z0-9-]+$/. Must not be greater than 255 characters.'
        required: false
        example: delicious-eats-restaurant
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      contact_email:
        name: contact_email
        description: 'optional Contact email.'
        required: false
        example: '"<EMAIL>"'
        type: email
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      contact_phone:
        name: contact_phone
        description: 'optional Contact phone.'
        required: false
        example: '"***********"'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      country_id:
        name: country_id
        description: 'Country ID where the business is located. Must be a valid UUID. The <code>id</code> of an existing record in the countries table.'
        required: false
        example: a1b2c3d4-e5f6-7890-abcd-ef1234567890
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      state_id:
        name: state_id
        description: 'State ID where the business is located. Must be a valid UUID. The <code>id</code> of an existing record in the states table.'
        required: false
        example: b2c3d4e5-f6g7-8901-bcde-f23456789012
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      cac_registration_number:
        name: cac_registration_number
        description: 'Corporate Affairs Commission registration number. Must not be greater than 255 characters.'
        required: false
        example: RC123456
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      tax_identification_number:
        name: tax_identification_number
        description: 'Tax identification number. Must not be greater than 255 characters.'
        required: false
        example: TIN123456789
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      global_auto_accept_orders:
        name: global_auto_accept_orders
        description: 'optional Enable auto-accept orders'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      auto_acceptance_criteria:
        name: auto_acceptance_criteria
        description: 'optional Criteria for auto-accepting orders'
        required: false
        example: []
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.min_value:
        name: auto_acceptance_criteria.min_value
        description: 'Must be at least 0.'
        required: false
        example: 500.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.max_distance:
        name: auto_acceptance_criteria.max_distance
        description: 'Must be at least 0.'
        required: false
        example: 5.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      auto_acceptance_criteria.business_hours_only:
        name: auto_acceptance_criteria.business_hours_only
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      accepts_cash_on_delivery:
        name: accepts_cash_on_delivery
        description: 'Whether the business accepts cash on delivery.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      allows_pickup:
        name: allows_pickup
        description: 'Whether the business allows customer pickup.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      operating_hours:
        name: operating_hours
        description: 'optional Operating hours for each day'
        required: false
        example: []
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday:
        name: operating_hours.monday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday.open:
        name: operating_hours.monday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.monday.close:
        name: operating_hours.monday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday:
        name: operating_hours.tuesday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday.open:
        name: operating_hours.tuesday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.tuesday.close:
        name: operating_hours.tuesday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday:
        name: operating_hours.wednesday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday.open:
        name: operating_hours.wednesday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.wednesday.close:
        name: operating_hours.wednesday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday:
        name: operating_hours.thursday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '22:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday.open:
        name: operating_hours.thursday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.thursday.close:
        name: operating_hours.thursday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '22:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday:
        name: operating_hours.friday
        description: ''
        required: false
        example:
          open: '09:00'
          close: '23:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday.open:
        name: operating_hours.friday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.friday.close:
        name: operating_hours.friday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '23:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday:
        name: operating_hours.saturday
        description: ''
        required: false
        example:
          open: '10:00'
          close: '23:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday.open:
        name: operating_hours.saturday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '10:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.saturday.close:
        name: operating_hours.saturday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '23:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday:
        name: operating_hours.sunday
        description: ''
        required: false
        example:
          open: '12:00'
          close: '21:00'
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday.open:
        name: operating_hours.sunday.open
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '12:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      operating_hours.sunday.close:
        name: operating_hours.sunday.close
        description: 'Must match the regex /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/.'
        required: false
        example: '21:00'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: true
        custom: []
      logo:
        name: logo
        description: 'optional Business logo image (max 2MB, JPEG/PNG/GIF/WebP)'
        required: false
        example: null
        type: file
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      verification_documents:
        name: verification_documents
        description: 'Must not be greater than 500 characters.'
        required: false
        example:
          - b
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      business_name: '"Updated Kitchen"'
      business_type: food
      description: '"Best food in town"'
      subdomain: delicious-eats
      slug: delicious-eats-restaurant
      contact_email: '"<EMAIL>"'
      contact_phone: '"***********"'
      country_id: a1b2c3d4-e5f6-7890-abcd-ef1234567890
      state_id: b2c3d4e5-f6g7-8901-bcde-f23456789012
      cac_registration_number: RC123456
      tax_identification_number: TIN123456789
      global_auto_accept_orders: false
      auto_acceptance_criteria:
        min_value: 500.0
        max_distance: 5.0
        business_hours_only: false
      accepts_cash_on_delivery: false
      allows_pickup: false
      operating_hours:
        monday:
          open: '09:00'
          close: '22:00'
        tuesday:
          open: '09:00'
          close: '22:00'
        wednesday:
          open: '09:00'
          close: '22:00'
        thursday:
          open: '09:00'
          close: '22:00'
        friday:
          open: '09:00'
          close: '23:00'
        saturday:
          open: '10:00'
          close: '23:00'
        sunday:
          open: '12:00'
          close: '21:00'
      verification_documents:
        - b
    fileParameters:
      logo: null
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Business profile updated successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "business_name": "Updated Kitchen",
              "description": "Best food in town"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/business/logo
    metadata:
      groupName: 'Business Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Upload business logo.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: multipart/form-data
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      logo:
        name: logo
        description: 'Business logo image (max 2MB, JPEG/PNG/GIF/WebP)'
        required: true
        example: null
        type: file
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters: []
    fileParameters:
      logo: null
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Business logo uploaded successfully",
            "data": {
              "logo_url": "/storage/tenants/tenant-id/business/logos/logo.jpg"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: api/v1/business/logo
    metadata:
      groupName: 'Business Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Delete business logo.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Business logo deleted successfully"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/business/statistics
    metadata:
      groupName: 'Business Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get business statistics for dashboard.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Business statistics retrieved successfully",
            "data": {
              "total_orders": 150,
              "total_revenue": 75000,
              "average_order_value": 500,
              "active_products": 25,
              "business_status": "active",
              "verification_status": true,
              "can_accept_orders": true,
              "auto_accept_enabled": true
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
