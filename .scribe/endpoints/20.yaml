name: 'Admin Provider Management'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/admin/providers
    metadata:
      groupName: 'Admin Provider Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get all delivery providers across tenants.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 'Page number for pagination.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (max 100).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      search:
        name: search
        description: 'Search providers by name or email.'
        required: false
        example: 'fast delivery'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: 'Filter by provider status.'
        required: false
        example: active
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      tenant_id:
        name: tenant_id
        description: 'Filter by tenant ID.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_verified:
        name: is_verified
        description: 'Filter by verification status.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: 'Sort by field.'
        required: false
        example: created_at
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_direction:
        name: sort_direction
        description: 'Sort direction (asc, desc).'
        required: false
        example: desc
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 15
      search: 'fast delivery'
      status: active
      tenant_id: 019723aa-3202-70dd-a0c1-3565681dd87a
      is_verified: true
      sort_by: created_at
      sort_direction: desc
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Providers retrieved successfully",
            "data": {
              "data": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "company_name": "Fast Delivery Co",
                  "contact_email": "<EMAIL>",
                  "contact_phone": "***********",
                  "status": "active",
                  "is_verified": true,
                  "average_rating": 4.7,
                  "total_deliveries": 1250,
                  "owner": {
                    "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                    "name": "John Provider",
                    "email": "<EMAIL>"
                  },
                  "tenant": {
                    "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                    "name": "Provider Tenant"
                  },
                  "created_at": "2024-01-15T10:30:00Z"
                }
              ],
              "current_page": 1,
              "per_page": 15,
              "total": 50
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/providers/{id}'
    metadata:
      groupName: 'Admin Provider Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get specific provider details.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the provider.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      provider:
        name: provider
        description: 'Provider ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
      provider: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Provider retrieved successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "company_name": "Fast Delivery Co",
              "contact_email": "<EMAIL>",
              "contact_phone": "***********",
              "address": "123 Provider St, Lagos",
              "description": "Professional delivery services",
              "status": "active",
              "is_verified": true,
              "average_rating": 4.7,
              "total_deliveries": 1250,
              "completed_deliveries": 1200,
              "owner": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "name": "John Provider",
                "email": "<EMAIL>",
                "phone": "***********"
              },
              "tenant": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "name": "Provider Tenant",
                "tenant_type": "provider"
              },
              "created_at": "2024-01-15T10:30:00Z",
              "updated_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/v1/admin/providers/{id}'
    metadata:
      groupName: 'Admin Provider Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update provider details.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the provider.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      provider:
        name: provider
        description: 'Provider ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
      provider: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      company_name:
        name: company_name
        description: 'Company name.'
        required: false
        example: 'Fast Delivery Co'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      contact_email:
        name: contact_email
        description: 'Contact email.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      contact_phone:
        name: contact_phone
        description: 'Contact phone.'
        required: false
        example: '***********'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      address:
        name: address
        description: 'Business address.'
        required: false
        example: '123 Provider St, Lagos'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      description:
        name: description
        description: 'Business description.'
        required: false
        example: 'Professional delivery services'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: 'Provider status.'
        required: false
        example: active
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      company_name: 'Fast Delivery Co'
      contact_email: <EMAIL>
      contact_phone: '***********'
      address: '123 Provider St, Lagos'
      description: 'Professional delivery services'
      status: active
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Provider updated successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "company_name": "Fast Delivery Co",
              "contact_email": "<EMAIL>",
              "status": "active"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/providers/{provider}/activate'
    metadata:
      groupName: 'Admin Provider Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Activate a provider.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      provider:
        name: provider
        description: 'Provider ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      provider: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Provider activated successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "status": "active"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/providers/{provider}/suspend'
    metadata:
      groupName: 'Admin Provider Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Suspend a provider.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      provider:
        name: provider
        description: 'Provider ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      provider: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Provider suspended successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "status": "suspended"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/admin/providers/{provider}/verify'
    metadata:
      groupName: 'Admin Provider Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Verify a provider.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      provider:
        name: provider
        description: 'Provider ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      provider: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Provider verified successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "is_verified": true
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
