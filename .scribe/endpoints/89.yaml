name: 'Provider Dashboard'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/provider/dashboard
    metadata:
      groupName: 'Provider Dashboard'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get provider dashboard data.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      period:
        name: period
        description: 'Period for dashboard data (today, week, month, year).'
        required: false
        example: today
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      period: today
    bodyParameters:
      period:
        name: period
        description: ''
        required: false
        example: week
        type: string
        enumValues:
          - today
          - week
          - month
          - year
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      period: week
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Provider dashboard data retrieved successfully",
            "data": {
              "overview": {
                "total_deliveries": 45,
                "completed_deliveries": 42,
                "total_earnings": 15750,
                "average_delivery_time": 28,
                "success_rate": 93.33,
                "rating": 4.7,
                "total_ratings": 38
              },
              "today": {
                "deliveries": 8,
                "earnings": 2400,
                "hours_worked": 6.5,
                "average_per_delivery": 300
              },
              "recent_deliveries": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "order_reference": "ORD-2024-001",
                  "business_name": "Tasty Kitchen",
                  "customer_name": "John Doe",
                  "delivery_fee": 500,
                  "status": "completed",
                  "completed_at": "2024-01-15T14:30:00Z",
                  "delivery_time_minutes": 25
                }
              ],
              "earnings_trend": [
                {
                  "date": "2024-01-15",
                  "earnings": 2400,
                  "deliveries": 8
                }
              ],
              "performance": {
                "on_time_deliveries": 40,
                "late_deliveries": 2,
                "cancelled_deliveries": 3,
                "average_rating": 4.7
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
