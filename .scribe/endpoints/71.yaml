name: 'Business Payouts'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/business/payouts
    metadata:
      groupName: 'Business Payouts'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get payout history.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      limit:
        name: limit
        description: 'The number of payouts to retrieve (default: 50).'
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanQueryParameters:
      limit: 16
    bodyParameters:
      limit:
        name: limit
        description: 'Must be at least 1. Must not be greater than 100.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      limit: 1
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "data": {
              "payouts": [
                {
                  "id": "payout-uuid",
                  "amount": 10000.00,
                  "currency": "NGN",
                  "transaction_fee": 200.00,
                  "status": "paid",
                  "payment_method": "bank_transfer",
                  "reference": "PO_ABC123_20241201",
                  "processed_at": "2024-12-01T14:00:00.000000Z",
                  "created_at": "2024-12-01T12:00:00.000000Z"
                }
              ],
              "summary": {
                "total_payouts": 5,
                "total_amount": 50000.00,
                "total_fees": 1000.00,
                "successful_payouts": 4,
                "pending_payouts": 1,
                "failed_payouts": 0
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/business/payouts
    metadata:
      groupName: 'Business Payouts'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Request a payout.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      amount:
        name: amount
        description: 'The payout amount.'
        required: true
        example: 4326.41688
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      payment_method:
        name: payment_method
        description: 'The payment method (bank_transfer, wallet).'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      bank_details:
        name: bank_details
        description: 'Bank account details for bank transfers.'
        required: true
        example: []
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      bank_details.account_number:
        name: bank_details.account_number
        description: 'Bank account number.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      bank_details.bank_code:
        name: bank_details.bank_code
        description: 'Bank code.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      bank_details.account_name:
        name: bank_details.account_name
        description: 'Account holder name.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notes:
        name: notes
        description: 'optional Additional notes for the payout.'
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      amount: 4326.41688
      payment_method: architecto
      bank_details:
        account_number: architecto
        bank_code: architecto
        account_name: architecto
      notes: architecto
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "success": true,
            "data": {
              "id": "payout-uuid",
              "amount": 10000.00,
              "currency": "NGN",
              "transaction_fee": 200.00,
              "status": "pending",
              "payment_method": "bank_transfer",
              "reference": "PO_ABC123_20241201",
              "balance_before": 15000.00,
              "balance_after": 5000.00,
              "created_at": "2024-12-01T12:00:00.000000Z"
            },
            "message": "Payout requested successfully"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/business/payouts/{id}'
    metadata:
      groupName: 'Business Payouts'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get specific payout details.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the payout.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      payout:
        name: payout
        description: 'The payout ID.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
      payout: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "data": {
              "id": "payout-uuid",
              "amount": 10000.00,
              "currency": "NGN",
              "transaction_fee": 200.00,
              "status": "paid",
              "payment_method": "bank_transfer",
              "bank_details": {
                "account_number": "**********",
                "bank_code": "044",
                "account_name": "Business Account"
              },
              "reference": "PO_ABC123_20241201",
              "balance_before": 15000.00,
              "balance_after": 5000.00,
              "notes": "Monthly payout",
              "processed_at": "2024-12-01T14:00:00.000000Z",
              "created_at": "2024-12-01T12:00:00.000000Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
