name: 'Business Delivery Assignment'
description: ''
endpoints:
  -
    httpMethods:
      - POST
    uri: 'api/v1/business/orders/{orderId}/first-to-accept'
    metadata:
      groupName: 'Business Delivery Assignment'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Initiate first-to-accept assignment for an order.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      orderId:
        name: orderId
        description: 'The order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      orderId: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      max_providers:
        name: max_providers
        description: 'Maximum number of providers to notify.'
        required: false
        example: 5
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      timeout_minutes:
        name: timeout_minutes
        description: 'Timeout in minutes for provider responses.'
        required: false
        example: 10
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      priority:
        name: priority
        description: 'Assignment priority (normal, high, urgent).'
        required: false
        example: normal
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      max_providers: 5
      timeout_minutes: 10
      priority: normal
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "First-to-accept assignment initiated successfully",
            "data": {
              "assignment_type": "first_to_accept",
              "broadcast_id": "broadcast_019723aa_1640995200",
              "providers_notified": 5,
              "timeout_at": "2024-01-15T12:30:00Z",
              "status": "waiting_for_acceptance"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/business/orders/{orderId}/assignment-status'
    metadata:
      groupName: 'Business Delivery Assignment'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get assignment status for an order.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      orderId:
        name: orderId
        description: 'The order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      orderId: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Assignment status retrieved successfully",
            "data": {
              "order_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "status": "waiting_for_acceptance",
              "broadcast_id": "broadcast_019723aa_1640995200",
              "providers_count": 5,
              "timeout_at": "2024-01-15T12:30:00Z",
              "time_remaining_seconds": 450,
              "requests": [
                {
                  "provider_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                  "provider_name": "FastDelivery Lagos",
                  "status": "pending",
                  "sent_at": "2024-01-15T12:20:00Z"
                }
              ]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/business/broadcasts/{broadcastId}/stats'
    metadata:
      groupName: 'Business Delivery Assignment'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get broadcast statistics for a specific broadcast.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      broadcastId:
        name: broadcastId
        description: 'The broadcast ID.'
        required: true
        example: broadcast_019723aa_1640995200
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      broadcastId: broadcast_019723aa_1640995200
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Broadcast statistics retrieved successfully",
            "data": {
              "broadcast_id": "broadcast_019723aa_1640995200",
              "total_requests": 5,
              "accepted_count": 1,
              "cancelled_count": 4,
              "expired_count": 0,
              "response_time_seconds": 67,
              "accepted_provider": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                "name": "FastDelivery Lagos"
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/business/first-to-accept/stats
    metadata:
      groupName: 'Business Delivery Assignment'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get first-to-accept assignment statistics for the business.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      days:
        name: days
        description: 'Number of days to include in statistics.'
        required: false
        example: 30
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      days: 30
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "First-to-accept statistics retrieved successfully",
            "data": {
              "period_days": 30,
              "total_broadcasts": 25,
              "successful_assignments": 22,
              "success_rate": 88.0,
              "average_response_time_seconds": 45.5,
              "timeout_count": 3,
              "timeout_rate": 12.0
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/business/orders/{orderId}/cancel-delivery-requests'
    metadata:
      groupName: 'Business Delivery Assignment'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Cancel pending delivery requests for an order.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      orderId:
        name: orderId
        description: 'The order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      orderId: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      reason:
        name: reason
        description: 'Reason for cancellation.'
        required: false
        example: 'Customer cancelled order'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      reason: 'Customer cancelled order'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Delivery requests cancelled successfully",
            "data": {
              "cancelled_requests": 3
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
