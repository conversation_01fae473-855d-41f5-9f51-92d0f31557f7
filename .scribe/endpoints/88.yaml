name: 'Provider Delivery Requests'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/provider/delivery-requests
    metadata:
      groupName: 'Provider Delivery Requests'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get delivery requests for the current provider.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      status:
        name: status
        description: 'Filter by request status (pending, accepted, cancelled, expired).'
        required: false
        example: pending
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      page:
        name: page
        description: 'Page number for pagination.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Number of items per page.'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      status: pending
      page: 1
      per_page: 15
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Delivery requests retrieved successfully",
            "data": [
              {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "order_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                "broadcast_id": "broadcast_019723aa_1640995200",
                "status": "pending",
                "expires_at": "2024-01-15T12:30:00Z",
                "time_remaining_seconds": 450,
                "order_details": {
                  "order_reference": "ORD-2024-001",
                  "pickup_address": {...},
                  "delivery_address": {...},
                  "estimated_distance": 5.2,
                  "estimated_duration": 25,
                  "order_value": 2500,
                  "delivery_fee": 500
                }
              }
            ],
            "pagination": {...}
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/provider/delivery-requests/pending
    metadata:
      groupName: 'Provider Delivery Requests'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get pending delivery requests for the current provider.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Pending delivery requests retrieved successfully",
            "data": [
              {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "order_reference": "ORD-2024-001",
                "expires_at": "2024-01-15T12:30:00Z",
                "time_remaining_seconds": 450,
                "estimated_distance": 5.2,
                "estimated_duration": 25,
                "delivery_fee": 500,
                "priority": "normal"
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/provider/delivery-requests/statistics
    metadata:
      groupName: 'Provider Delivery Requests'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get delivery request statistics for the current provider.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      days:
        name: days
        description: 'Number of days to include in statistics.'
        required: false
        example: 30
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      days: 30
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Delivery request statistics retrieved successfully",
            "data": {
              "period_days": 30,
              "total_requests": 45,
              "accepted_requests": 38,
              "acceptance_rate": 84.44,
              "average_response_time_seconds": 67.5,
              "fastest_response_time_seconds": 12,
              "expired_requests": 5,
              "cancelled_requests": 2
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/provider/delivery-requests/{requestId}'
    metadata:
      groupName: 'Provider Delivery Requests'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get a specific delivery request.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      requestId:
        name: requestId
        description: 'The delivery request ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      requestId: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Delivery request retrieved successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "order_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
              "broadcast_id": "broadcast_019723aa_1640995200",
              "status": "pending",
              "expires_at": "2024-01-15T12:30:00Z",
              "time_remaining_seconds": 450,
              "order_details": {...},
              "order": {...}
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/provider/delivery-requests/{requestId}/accept'
    metadata:
      groupName: 'Provider Delivery Requests'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Accept a delivery request (first-to-accept).'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      requestId:
        name: requestId
        description: 'The delivery request ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      requestId: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Congratulations! You won the delivery assignment",
            "data": {
              "delivery_id": "019723aa-3202-70dd-a0c1-3565681dd87c",
              "response_time_seconds": 45,
              "assignment_type": "first_to_accept",
              "race_winner": true
            }
          }
        headers: []
        description: ''
        custom: []
      -
        status: 422
        content: |-
          {
            "success": false,
            "message": "This delivery request is no longer available",
            "error": {
              "code": "REQUEST_UNAVAILABLE",
              "current_status": "accepted"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
