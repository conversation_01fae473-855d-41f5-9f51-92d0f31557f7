name: 'Central Customer - Order Search'
description: ''
endpoints:
  -
    httpMethods:
      - POST
    uri: api/v1/customer/search/orders
    metadata:
      groupName: 'Central Customer - Order Search'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Search customer's orders."
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      query:
        name: query
        description: 'Search query for order reference, business name, etc.'
        required: false
        example: ORD123
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: 'Filter by order status.'
        required: false
        example: delivered
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      payment_status:
        name: payment_status
        description: 'Filter by payment status.'
        required: false
        example: paid
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      order_type:
        name: order_type
        description: 'Filter by order type.'
        required: false
        example: delivery
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      source:
        name: source
        description: 'Filter by order source.'
        required: false
        example: mobile_app
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      business_id:
        name: business_id
        description: 'Filter by specific business.'
        required: false
        example: uuid
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      min_amount:
        name: min_amount
        description: 'Minimum order amount.'
        required: false
        example: 1000.0
        type: number
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      max_amount:
        name: max_amount
        description: 'Maximum order amount.'
        required: false
        example: 10000.0
        type: number
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      date_from:
        name: date_from
        description: 'Filter orders from date (Y-m-d).'
        required: false
        example: '2024-01-01'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      date_to:
        name: date_to
        description: 'Filter orders to date (Y-m-d).'
        required: false
        example: '2024-01-31'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort:
        name: sort
        description: 'Sort results (relevance, newest, oldest, amount_desc, amount_asc, completed_desc, completed_asc).'
        required: false
        example: newest
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (max 50).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      page:
        name: page
        description: 'Page number.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      query: ORD123
      status: delivered
      payment_status: paid
      order_type: delivery
      source: mobile_app
      business_id: uuid
      min_amount: 1000.0
      max_amount: 10000.0
      date_from: '2024-01-01'
      date_to: '2024-01-31'
      sort: newest
      per_page: 15
      page: 1
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Orders found successfully",
            "data": {
              "data": [
                {
                  "id": "uuid",
                  "order_reference": "ORD123456",
                  "business_name": "Sample Restaurant",
                  "status": "delivered",
                  "total_amount": 2500.00,
                  "created_at": "2024-01-15T10:30:00Z"
                }
              ],
              "current_page": 1,
              "per_page": 15,
              "total": 25,
              "last_page": 2
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/customer/search/orders/suggestions
    metadata:
      groupName: 'Central Customer - Order Search'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get order search suggestions.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      query:
        name: query
        description: 'Search query.'
        required: true
        example: ORD
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      limit:
        name: limit
        description: 'Number of suggestions (max 20).'
        required: false
        example: 10
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      query: ORD
      limit: 10
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Search suggestions retrieved successfully",
            "data": [
              "ORD123456",
              "ORD123789",
              "ORD124000"
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
