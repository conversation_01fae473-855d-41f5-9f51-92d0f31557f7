name: 'Product Collections'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/business/product-collections
    metadata:
      groupName: 'Product Collections'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get product collections.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 'Page number for pagination.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (max 100).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      search:
        name: search
        description: 'Search collections by name or description.'
        required: false
        example: summer
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      type:
        name: type
        description: 'Filter by collection type.'
        required: false
        example: seasonal
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_active:
        name: is_active
        description: 'Filter by active status.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: 'Sort by field.'
        required: false
        example: display_order
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_direction:
        name: sort_direction
        description: 'Sort direction (asc, desc).'
        required: false
        example: asc
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 15
      search: summer
      type: seasonal
      is_active: true
      sort_by: display_order
      sort_direction: asc
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Product collections retrieved successfully",
            "data": {
              "data": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "name": "Summer Collection",
                  "description": "Summer specials available until August",
                  "type": "time_based",
                  "is_active": true,
                  "display_order": 1,
                  "products_count": 12,
                  "created_at": "2024-01-15T10:30:00Z"
                }
              ],
              "current_page": 1,
              "per_page": 15,
              "total": 5
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/business/product-collections
    metadata:
      groupName: 'Product Collections'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Create new product collection.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Collection name.'
        required: true
        example: 'Summer Collection'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      description:
        name: description
        description: 'Collection description.'
        required: false
        example: 'Summer specials available until August'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      type:
        name: type
        description: 'Collection type.'
        required: true
        example: time_based
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_active:
        name: is_active
        description: 'Collection active status.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      active_start_time:
        name: active_start_time
        description: 'Start time for time-based collections.'
        required: false
        example: '2024-01-15T06:00:00Z'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      active_end_time:
        name: active_end_time
        description: 'End time for time-based collections.'
        required: false
        example: '2024-01-15T11:00:00Z'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      display_order:
        name: display_order
        description: 'Display order.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      image_url:
        name: image_url
        description: 'Collection image URL.'
        required: false
        example: 'https://example.com/image.jpg'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      products:
        name: products
        description: 'Array of product IDs to add to collection.'
        required: false
        example:
          - 019723aa-3202-70dd-a0c1-3565681dd87b
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 'Summer Collection'
      description: 'Summer specials available until August'
      type: time_based
      is_active: true
      active_start_time: '2024-01-15T06:00:00Z'
      active_end_time: '2024-01-15T11:00:00Z'
      display_order: 1
      image_url: 'https://example.com/image.jpg'
      products:
        - 019723aa-3202-70dd-a0c1-3565681dd87b
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "success": true,
            "message": "Product collection created successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "Summer Collection",
              "description": "Summer specials available until August",
              "type": "time_based",
              "is_active": true,
              "display_order": 1,
              "products_count": 5,
              "created_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/business/product-collections/{id}'
    metadata:
      groupName: 'Product Collections'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get specific product collection details.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the product collection.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      collection:
        name: collection
        description: 'Collection ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
      collection: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Product collection retrieved successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "Summer Collection",
              "description": "Summer specials available until August",
              "type": "time_based",
              "is_active": true,
              "active_start_time": "2024-01-15T06:00:00Z",
              "active_end_time": "2024-01-15T11:00:00Z",
              "display_order": 1,
              "products": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                  "name": "Pancakes",
                  "price": 800,
                  "display_order": 1
                }
              ],
              "created_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
