name: 'Admin Notifications'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/admin/notifications
    metadata:
      groupName: 'Admin Notifications'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get all notifications across platform.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:14.087967Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 808d9596-b9c8-468b-9251-c07b2691d0f6
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-EbPP8RsGdkGrzBMEqviEhg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/notifications/analytics
    metadata:
      groupName: 'Admin Notifications'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get notification analytics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      period:
        name: period
        description: ''
        required: false
        example: month
        type: string
        enumValues:
          - today
          - week
          - month
          - quarter
          - year
        exampleWasSpecified: false
        nullable: false
        custom: []
      type:
        name: type
        description: ''
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      channel:
        name: channel
        description: ''
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      period: month
      type: architecto
      channel: architecto
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:14.094173Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 01735687-9746-4ae8-bbfd-4de5e70978c8
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-BaztOpUeUlxZmz+sHV9htg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/admin/notifications/broadcast
    metadata:
      groupName: 'Admin Notifications'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Send broadcast notification.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      title:
        name: title
        description: 'Must not be greater than 255 characters.'
        required: true
        example: b
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      message:
        name: message
        description: 'Must not be greater than 1000 characters.'
        required: true
        example: 'n'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      type:
        name: type
        description: ''
        required: true
        example: maintenance
        type: string
        enumValues:
          - announcement
          - maintenance
          - promotion
          - alert
        exampleWasSpecified: false
        nullable: false
        custom: []
      priority:
        name: priority
        description: ''
        required: true
        example: medium
        type: string
        enumValues:
          - low
          - medium
          - high
          - urgent
        exampleWasSpecified: false
        nullable: false
        custom: []
      channels:
        name: channels
        description: ''
        required: false
        example:
          - database
        type: 'string[]'
        enumValues:
          - database
          - email
          - sms
          - push
          - whatsapp
        exampleWasSpecified: false
        nullable: false
        custom: []
      target_audience:
        name: target_audience
        description: ''
        required: true
        example: providers
        type: string
        enumValues:
          - all
          - businesses
          - providers
          - customers
        exampleWasSpecified: false
        nullable: false
        custom: []
      tenant_ids:
        name: tenant_ids
        description: 'The <code>id</code> of an existing record in the tenants table.'
        required: false
        example:
          - architecto
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      schedule_at:
        name: schedule_at
        description: 'Must be a valid date. Must be a date after <code>now</code>.'
        required: false
        example: '2051-07-04'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      title: b
      message: 'n'
      type: maintenance
      priority: medium
      channels:
        - database
      target_audience: providers
      tenant_ids:
        - architecto
      schedule_at: '2051-07-04'
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/notifications/{notification}'
    metadata:
      groupName: 'Admin Notifications'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get notification details.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      notification:
        name: notification
        description: 'The notification.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      notification: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:14.100545Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 40305988-a466-4e3f-978e-b946e5a3351e
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-qdkEgkE4X/wINvOrmt/09A==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
