## Autogenerated by Scribe. DO NOT MODIFY.

name: 'Provider Vehicle Management'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/provider/vehicles
    metadata:
      groupName: 'Provider Vehicle Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get list of vehicles for the provider.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      search:
        name: search
        description: 'Search by license plate or type.'
        required: false
        example: ABC123
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: 'Filter by vehicle status (available, in_use, maintenance).'
        required: false
        example: available
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      type:
        name: type
        description: 'Filter by vehicle type (bicycle, motorcycle, car, van, truck).'
        required: false
        example: car
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      driver_id:
        name: driver_id
        description: 'Filter by assigned driver ID.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: 'Sort by field (license_plate, type, status, created_at).'
        required: false
        example: license_plate
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_direction:
        name: sort_direction
        description: 'Sort direction (asc, desc).'
        required: false
        example: asc
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (1-100).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      search: ABC123
      status: available
      type: car
      driver_id: 019723aa-3202-70dd-a0c1-3565681dd87a
      sort_by: license_plate
      sort_direction: asc
      per_page: 15
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Vehicles retrieved successfully",
            "data": [
              {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "type": "car",
                "license_plate": "ABC123",
                "status": "available",
                "capacity": "500.00",
                "driver": {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                  "name": "John Doe",
                  "phone": "+2348012345678"
                },
                "current_deliveries": 0,
                "needs_maintenance": false,
                "days_since_maintenance": 15,
                "created_at": "2024-01-22T10:30:00Z"
              }
            ],
            "meta": {
              "current_page": 1,
              "per_page": 15,
              "total": 25,
              "last_page": 2
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/provider/vehicles
    metadata:
      groupName: 'Provider Vehicle Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Create a new vehicle.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      type:
        name: type
        description: 'Vehicle type (bicycle, motorcycle, car, van, truck).'
        required: true
        example: car
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      license_plate:
        name: license_plate
        description: 'Vehicle license plate.'
        required: true
        example: ABC123
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      capacity:
        name: capacity
        description: 'Vehicle capacity in kg.'
        required: false
        example: '500.00'
        type: numeric
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      driver_id:
        name: driver_id
        description: 'Optional driver to assign.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      type: car
      license_plate: ABC123
      capacity: '500.00'
      driver_id: 019723aa-3202-70dd-a0c1-3565681dd87a
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "success": true,
            "message": "Vehicle created successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "type": "car",
              "license_plate": "ABC123",
              "status": "available",
              "capacity": "500.00",
              "driver": null,
              "created_at": "2024-01-22T15:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/provider/vehicles/{id}'
    metadata:
      groupName: 'Provider Vehicle Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get vehicle details.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the vehicle.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      vehicle:
        name: vehicle
        description: 'Vehicle ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
      vehicle: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Vehicle retrieved successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "type": "car",
              "license_plate": "ABC123",
              "status": "available",
              "capacity": "500.00",
              "last_maintenance_date": "2024-01-07T00:00:00Z",
              "driver": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                "name": "John Doe",
                "phone": "+2348012345678"
              },
              "current_deliveries": 0,
              "needs_maintenance": false,
              "days_since_maintenance": 15,
              "recent_deliveries": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
                  "tracking_id": "DN123456",
                  "status": "delivered",
                  "completed_at": "2024-01-21T16:30:00Z"
                }
              ],
              "created_at": "2024-01-01T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/provider/vehicles/{vehicle}/assign-driver'
    metadata:
      groupName: 'Provider Vehicle Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Assign vehicle to driver.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      vehicle:
        name: vehicle
        description: 'Vehicle ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      vehicle: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      driver_id:
        name: driver_id
        description: 'Driver ID to assign.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87b
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      driver_id: 019723aa-3202-70dd-a0c1-3565681dd87b
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Vehicle assigned to driver successfully",
            "data": {
              "vehicle_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "driver_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
              "driver_name": "John Doe",
              "assigned_at": "2024-01-22T15:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/provider/vehicles/{vehicle}/unassign-driver'
    metadata:
      groupName: 'Provider Vehicle Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Unassign vehicle from driver.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      vehicle:
        name: vehicle
        description: 'Vehicle ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      vehicle: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Vehicle unassigned from driver successfully",
            "data": {
              "vehicle_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "unassigned_at": "2024-01-22T15:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/provider/vehicles/utilization
    metadata:
      groupName: 'Provider Vehicle Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get vehicle utilization statistics.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Vehicle utilization statistics retrieved successfully",
            "data": {
              "total_vehicles": 10,
              "available_vehicles": 6,
              "in_use_vehicles": 3,
              "maintenance_vehicles": 1,
              "assigned_vehicles": 8,
              "unassigned_vehicles": 2,
              "utilization_rate": 30.0,
              "assignment_rate": 80.0
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
