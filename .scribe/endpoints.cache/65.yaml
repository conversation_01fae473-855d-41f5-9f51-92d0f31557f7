## Autogenerated by Scribe. DO NOT MODIFY.

name: 'Business Products'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/business/products
    metadata:
      groupName: 'Business Products'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display a listing of products for the business.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 'Page number for pagination.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Number of items per page (max 100).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      search:
        name: search
        description: 'Search in product name, description, SKU.'
        required: false
        example: Jollof
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      category_id:
        name: category_id
        description: 'Filter by category ID.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_available:
        name: is_available
        description: 'Filter by availability.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      in_stock:
        name: in_stock
        description: 'Filter by stock status.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      price_min:
        name: price_min
        description: 'Minimum price filter.'
        required: false
        example: 1000.0
        type: number
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      price_max:
        name: price_max
        description: 'Maximum price filter.'
        required: false
        example: 5000.0
        type: number
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: 'Sort by field (name, price, quantity, created_at).'
        required: false
        example: name
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_direction:
        name: sort_direction
        description: 'Sort direction (asc, desc).'
        required: false
        example: asc
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 15
      search: Jollof
      category_id: 019723aa-3202-70dd-a0c1-3565681dd87a
      is_available: true
      in_stock: true
      price_min: 1000.0
      price_max: 5000.0
      sort_by: name
      sort_direction: asc
    bodyParameters:
      page:
        name: page
        description: 'Must be at least 1.'
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Must be at least 1. Must not be greater than 100.'
        required: false
        example: 22
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      search:
        name: search
        description: 'Must not be greater than 255 characters.'
        required: false
        example: g
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      category_id:
        name: category_id
        description: 'The <code>id</code> of an existing record in the product_categories table.'
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      is_available:
        name: is_available
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      in_stock:
        name: in_stock
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      price_min:
        name: price_min
        description: 'Must be at least 0.'
        required: false
        example: 39
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      price_max:
        name: price_max
        description: 'Must be at least 0.'
        required: false
        example: 84
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: ''
        required: false
        example: name
        type: string
        enumValues:
          - name
          - price
          - quantity
          - created_at
        exampleWasSpecified: false
        nullable: false
        custom: []
      sort_direction:
        name: sort_direction
        description: ''
        required: false
        example: desc
        type: string
        enumValues:
          - asc
          - desc
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      page: 16
      per_page: 22
      search: g
      category_id: architecto
      is_available: true
      in_stock: false
      price_min: 39
      price_max: 84
      sort_by: name
      sort_direction: desc
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Products retrieved successfully",
            "data": {
              "products": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "name": "Jollof Rice",
                  "slug": "jollof-rice",
                  "description": "Delicious Nigerian jollof rice",
                  "price": 2500,
                  "sale_price": null,
                  "effective_price": 2500,
                  "sku": "JR001",
                  "quantity": 50,
                  "is_available": true,
                  "is_in_stock": true,
                  "main_image_url": "https://example.com/jollof.jpg",
                  "category": {
                    "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                    "name": "Main Dishes"
                  },
                  "average_rating": 4.5,
                  "total_ratings": 25,
                  "created_at": "2024-01-15T10:30:00Z"
                }
              ],
              "pagination": {
                "current_page": 1,
                "per_page": 15,
                "total": 50,
                "last_page": 4
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/business/products
    metadata:
      groupName: 'Business Products'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Store a newly created product.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: multipart/form-data
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Product name.'
        required: true
        example: 'Jollof Rice'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      description:
        name: description
        description: 'Product description.'
        required: false
        example: 'Delicious Nigerian jollof rice'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      price:
        name: price
        description: 'Product price.'
        required: true
        example: '2500'
        type: numeric
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      cost_price:
        name: cost_price
        description: 'Cost price for profit calculation.'
        required: false
        example: '1500'
        type: numeric
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sale_price:
        name: sale_price
        description: 'Sale price if on discount.'
        required: false
        example: '2000'
        type: numeric
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sku:
        name: sku
        description: 'Product SKU.'
        required: false
        example: JR001
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      barcode:
        name: barcode
        description: 'Product barcode.'
        required: false
        example: '1234567890123'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      quantity:
        name: quantity
        description: 'Stock quantity (-1 for unlimited).'
        required: false
        example: 50
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      weight:
        name: weight
        description: 'Product weight in kg.'
        required: false
        example: '0.5'
        type: numeric
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      category_id:
        name: category_id
        description: 'Category ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_available:
        name: is_available
        description: 'Product availability.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      allows_pickup:
        name: allows_pickup
        description: 'Allow pickup orders.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      preparation_time_minutes:
        name: preparation_time_minutes
        description: 'Preparation time in minutes.'
        required: false
        example: 30
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      main_image_url:
        name: main_image_url
        description: 'Main product image URL.'
        required: false
        example: 'https://example.com/image.jpg'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      main_image:
        name: main_image
        description: 'Must be an image. Must not be greater than 10240 kilobytes.'
        required: false
        example: null
        type: file
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      additional_images:
        name: additional_images
        description: 'Additional image URLs.'
        required: false
        example:
          - 'https://example.com/img1.jpg'
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      tags:
        name: tags
        description: 'Product tags.'
        required: false
        example:
          - spicy
          - popular
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      attributes:
        name: attributes
        description: 'Product attributes.'
        required: false
        example:
          spice_level: medium
          serves: 2
        type: object
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 'Jollof Rice'
      description: 'Delicious Nigerian jollof rice'
      price: '2500'
      cost_price: '1500'
      sale_price: '2000'
      sku: JR001
      barcode: '1234567890123'
      quantity: 50
      weight: '0.5'
      category_id: 019723aa-3202-70dd-a0c1-3565681dd87a
      is_available: true
      allows_pickup: true
      preparation_time_minutes: 30
      main_image_url: 'https://example.com/image.jpg'
      additional_images:
        - 'https://example.com/img1.jpg'
      tags:
        - spicy
        - popular
      attributes:
        spice_level: medium
        serves: 2
    fileParameters:
      main_image: null
    responses:
      -
        status: 201
        content: |-
          {
            "success": true,
            "message": "Product created successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "Jollof Rice",
              "slug": "jollof-rice",
              "description": "Delicious Nigerian jollof rice",
              "price": 2500,
              "effective_price": 2500,
              "sku": "JR001",
              "quantity": 50,
              "is_available": true,
              "created_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/business/products/{id}'
    metadata:
      groupName: 'Business Products'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Display the specified product.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The product ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Product retrieved successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "Jollof Rice",
              "slug": "jollof-rice",
              "description": "Delicious Nigerian jollof rice",
              "price": 2500,
              "cost_price": 1500,
              "sale_price": null,
              "effective_price": 2500,
              "profit_margin": 40.0,
              "sku": "JR001",
              "barcode": "1234567890123",
              "quantity": 50,
              "weight": 0.5,
              "is_available": true,
              "is_in_stock": true,
              "is_on_sale": false,
              "allows_pickup": true,
              "preparation_time_minutes": 30,
              "main_image_url": "https://example.com/jollof.jpg",
              "additional_images": ["https://example.com/img1.jpg"],
              "all_images": ["https://example.com/jollof.jpg", "https://example.com/img1.jpg"],
              "category": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                "name": "Main Dishes",
                "slug": "main-dishes"
              },
              "tags": ["spicy", "popular"],
              "attributes": {"spice_level": "medium", "serves": 2},
              "average_rating": 4.5,
              "total_ratings": 25,
              "created_at": "2024-01-15T10:30:00Z",
              "updated_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/v1/business/products/{id}'
    metadata:
      groupName: 'Business Products'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update the specified product.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: multipart/form-data
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The product ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Product name.'
        required: false
        example: 'Jollof Rice Special'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      description:
        name: description
        description: 'Product description.'
        required: false
        example: 'Updated description'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      price:
        name: price
        description: 'Product price.'
        required: false
        example: '2800'
        type: numeric
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      cost_price:
        name: cost_price
        description: 'Cost price.'
        required: false
        example: '1600'
        type: numeric
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sale_price:
        name: sale_price
        description: 'Sale price.'
        required: false
        example: '2400'
        type: numeric
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sku:
        name: sku
        description: ''
        required: false
        example: null
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      barcode:
        name: barcode
        description: ''
        required: false
        example: null
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      quantity:
        name: quantity
        description: 'Stock quantity.'
        required: false
        example: 75
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      weight:
        name: weight
        description: 'Must be at least 0.'
        required: false
        example: 8
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      category_id:
        name: category_id
        description: 'The <code>id</code> of an existing record in the product_categories table.'
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      is_available:
        name: is_available
        description: 'Product availability.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      allows_pickup:
        name: allows_pickup
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      preparation_time_minutes:
        name: preparation_time_minutes
        description: 'Must be at least 1. Must not be greater than 300.'
        required: false
        example: 22
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      main_image_url:
        name: main_image_url
        description: 'Must be a valid URL. Must not be greater than 500 characters.'
        required: false
        example: 'http://crooks.biz/et-fugiat-sunt-nihil-accusantium'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      main_image:
        name: main_image
        description: 'Must be an image. Must not be greater than 10240 kilobytes.'
        required: false
        example: null
        type: file
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      additional_images:
        name: additional_images
        description: 'Must be a valid URL. Must not be greater than 500 characters.'
        required: false
        example:
          - 'n'
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      tags:
        name: tags
        description: 'Must not be greater than 50 characters.'
        required: false
        example:
          - i
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      attributes:
        name: attributes
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 'Jollof Rice Special'
      description: 'Updated description'
      price: '2800'
      cost_price: '1600'
      sale_price: '2400'
      quantity: 75
      weight: 8
      category_id: architecto
      is_available: false
      allows_pickup: false
      preparation_time_minutes: 22
      main_image_url: 'http://crooks.biz/et-fugiat-sunt-nihil-accusantium'
      additional_images:
        - 'n'
      tags:
        - i
    fileParameters:
      main_image: null
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Product updated successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "Jollof Rice Special",
              "price": 2800,
              "updated_at": "2024-01-15T11:00:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/business/products/{id}'
    metadata:
      groupName: 'Business Products'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove the specified product.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The product ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Product deleted successfully",
            "data": null
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/business/products/{product}/upload-image'
    metadata:
      groupName: 'Business Products'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Upload product image.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: multipart/form-data
      Accept: application/json
    urlParameters:
      product:
        name: product
        description: 'The product.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      id:
        name: id
        description: 'The product ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      product: architecto
      id: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      image:
        name: image
        description: 'Product image (max 10MB, JPEG/PNG/GIF/WebP/HEIF/HEIC)'
        required: true
        example: null
        type: file
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      type:
        name: type
        description: 'Image type (main or additional).'
        required: false
        example: main
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      type: main
    fileParameters:
      image: null
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Product image uploaded successfully",
            "data": {
              "image_url": "https://cdn.example.com/products/large/image.webp",
              "variants": {
                "thumbnail": "https://cdn.example.com/products/thumbnail/image.webp",
                "small": "https://cdn.example.com/products/small/image.webp",
                "medium": "https://cdn.example.com/products/medium/image.webp",
                "large": "https://cdn.example.com/products/large/image.webp"
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
