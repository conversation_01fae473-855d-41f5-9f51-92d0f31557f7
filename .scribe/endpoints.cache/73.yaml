## Autogenerated by Scribe. DO NOT MODIFY.

name: 'Menu Branch Management'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: 'api/v1/business/branches/{branchId}/products'
    metadata:
      groupName: 'Menu Branch Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get products for a specific branch with overrides applied.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      branchId:
        name: branchId
        description: 'The branch ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      branchId: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters:
      menu_collection_id:
        name: menu_collection_id
        description: 'Filter by menu collection ID.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87b
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      menu_collection_id: 019723aa-3202-70dd-a0c1-3565681dd87b
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Branch products retrieved successfully",
            "data": [
              {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
                "name": "Grilled Chicken",
                "description": "Tender grilled chicken breast",
                "price": 1500,
                "stock_quantity": 50,
                "is_available": true,
                "branch_price": 1400,
                "branch_stock": 45,
                "branch_is_available": true,
                "branch_description": "Special branch description",
                "has_branch_override": true
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/business/branches/{branchId}/catalog-customizations'
    metadata:
      groupName: 'Menu Branch Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Create a branch-specific menu customization.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      branchId:
        name: branchId
        description: 'The branch ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      branchId: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      menu_collection_id:
        name: menu_collection_id
        description: 'Menu collection ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87b
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_active:
        name: is_active
        description: 'Customization active status.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      active_start_time:
        name: active_start_time
        description: 'Start time for customization.'
        required: false
        example: '2024-01-15T06:00:00Z'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      active_end_time:
        name: active_end_time
        description: 'End time for customization.'
        required: false
        example: '2024-01-15T11:00:00Z'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      display_order:
        name: display_order
        description: 'Display order override.'
        required: false
        example: 2
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      custom_settings:
        name: custom_settings
        description: 'Custom settings for the branch.'
        required: false
        example:
          pricing_adjustment: 0.1
        type: object
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      menu_collection_id: 019723aa-3202-70dd-a0c1-3565681dd87b
      is_active: true
      active_start_time: '2024-01-15T06:00:00Z'
      active_end_time: '2024-01-15T11:00:00Z'
      display_order: 2
      custom_settings:
        pricing_adjustment: 0.1
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "success": true,
            "message": "Branch menu customization created successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87d",
              "menu_collection_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
              "is_active": true,
              "active_start_time": "2024-01-15T06:00:00Z",
              "active_end_time": "2024-01-15T11:00:00Z",
              "display_order": 2,
              "custom_settings": {"pricing_adjustment": 0.1}
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: 'api/v1/business/catalog-customizations/{customizationId}'
    metadata:
      groupName: 'Menu Branch Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update a branch-specific menu customization.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      customizationId:
        name: customizationId
        description: 'The customization ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87d
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      customizationId: 019723aa-3202-70dd-a0c1-3565681dd87d
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      is_active:
        name: is_active
        description: 'Customization active status.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      active_start_time:
        name: active_start_time
        description: 'Start time for customization.'
        required: false
        example: '2024-01-15T07:00:00Z'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      active_end_time:
        name: active_end_time
        description: 'End time for customization.'
        required: false
        example: '2024-01-15T12:00:00Z'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      display_order:
        name: display_order
        description: 'Display order override.'
        required: false
        example: 3
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      custom_settings:
        name: custom_settings
        description: 'Custom settings for the branch.'
        required: false
        example:
          pricing_adjustment: 0.15
        type: object
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      is_active: false
      active_start_time: '2024-01-15T07:00:00Z'
      active_end_time: '2024-01-15T12:00:00Z'
      display_order: 3
      custom_settings:
        pricing_adjustment: 0.15
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Branch menu customization updated successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87d",
              "menu_collection_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
              "is_active": false,
              "active_start_time": "2024-01-15T07:00:00Z",
              "active_end_time": "2024-01-15T12:00:00Z",
              "display_order": 3,
              "custom_settings": {"pricing_adjustment": 0.15}
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/business/catalog-customizations/{customizationId}'
    metadata:
      groupName: 'Menu Branch Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Delete a branch-specific menu customization.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      customizationId:
        name: customizationId
        description: 'The customization ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87d
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      customizationId: 019723aa-3202-70dd-a0c1-3565681dd87d
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Branch menu customization deleted successfully"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/business/branches/{branchId}/product-overrides'
    metadata:
      groupName: 'Menu Branch Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Create a branch-specific product override.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      branchId:
        name: branchId
        description: 'The branch ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      branchId: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      product_id:
        name: product_id
        description: 'Product ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87c
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_available:
        name: is_available
        description: 'Product availability override.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      price_override:
        name: price_override
        description: 'Product price override.'
        required: false
        example: 1400.5
        type: number
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      stock_override:
        name: stock_override
        description: 'Product stock override.'
        required: false
        example: 45
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      description_override:
        name: description_override
        description: 'Product description override.'
        required: false
        example: '"Special branch description"'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      custom_attributes:
        name: custom_attributes
        description: 'Custom attributes for the product.'
        required: false
        example:
          special_note: 'Limited time offer'
        type: object
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      product_id: 019723aa-3202-70dd-a0c1-3565681dd87c
      is_available: false
      price_override: 1400.5
      stock_override: 45
      description_override: '"Special branch description"'
      custom_attributes:
        special_note: 'Limited time offer'
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "success": true,
            "message": "Branch product override created successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87e",
              "product_id": "019723aa-3202-70dd-a0c1-3565681dd87c",
              "is_available": false,
              "price_override": 1400.50,
              "stock_override": 45,
              "description_override": "Special branch description",
              "custom_attributes": {"special_note": "Limited time offer"}
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/business/branches/{sourceBranchId}/copy-to/{targetBranchId}'
    metadata:
      groupName: 'Menu Branch Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Copy menu customizations from one branch to another.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      sourceBranchId:
        name: sourceBranchId
        description: 'The source branch ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      targetBranchId:
        name: targetBranchId
        description: 'The target branch ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87b
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      sourceBranchId: 019723aa-3202-70dd-a0c1-3565681dd87a
      targetBranchId: 019723aa-3202-70dd-a0c1-3565681dd87b
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Menu customizations copied successfully",
            "data": {
              "copied_count": 5
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
