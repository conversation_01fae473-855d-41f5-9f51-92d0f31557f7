## Autogenerated by <PERSON>ribe. DO NOT MODIFY.

name: 'Provider Drivers'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/provider/drivers
    metadata:
      groupName: 'Provider Drivers'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get provider's drivers."
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 'Page number for pagination.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (max 100).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      search:
        name: search
        description: 'Search drivers by name or email.'
        required: false
        example: john
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: 'Filter by status (active, inactive).'
        required: false
        example: active
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: 'Sort by field (name, email, created_at).'
        required: false
        example: name
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_direction:
        name: sort_direction
        description: 'Sort direction (asc, desc).'
        required: false
        example: asc
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 15
      search: john
      status: active
      sort_by: name
      sort_direction: asc
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Drivers retrieved successfully",
            "data": {
              "data": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "name": "John Driver",
                  "email": "<EMAIL>",
                  "phone": "08012345678",
                  "is_active": true,
                  "is_available": true,
                  "vehicle_type": "motorcycle",
                  "license_number": "ABC123456",
                  "created_at": "2024-01-15T10:30:00Z"
                }
              ],
              "current_page": 1,
              "per_page": 15,
              "total": 10
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/provider/drivers
    metadata:
      groupName: 'Provider Drivers'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Add new driver.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      first_name:
        name: first_name
        description: "Driver's first name."
        required: true
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: "Driver's last name."
        required: true
        example: Doe
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      email:
        name: email
        description: "Driver's email address."
        required: true
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: "Driver's phone number."
        required: true
        example: '08012345678'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      license_number:
        name: license_number
        description: "Driver's license number."
        required: true
        example: ABC123456
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      vehicle_type:
        name: vehicle_type
        description: 'Vehicle type.'
        required: true
        example: motorcycle
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      password:
        name: password
        description: "Driver's password."
        required: true
        example: password123
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      first_name: John
      last_name: Doe
      email: <EMAIL>
      phone_number: '08012345678'
      license_number: ABC123456
      vehicle_type: motorcycle
      password: password123
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "success": true,
            "message": "Driver added successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "John Doe",
              "email": "<EMAIL>",
              "phone": "08012345678",
              "is_active": true,
              "is_available": true,
              "vehicle_type": "motorcycle",
              "license_number": "ABC123456",
              "created_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/provider/drivers/{id}'
    metadata:
      groupName: 'Provider Drivers'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get specific driver details.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the driver.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      driver:
        name: driver
        description: 'Driver ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
      driver: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Driver retrieved successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "John Driver",
              "email": "<EMAIL>",
              "phone": "08012345678",
              "is_active": true,
              "is_available": true,
              "vehicle_type": "motorcycle",
              "license_number": "ABC123456",
              "total_deliveries": 150,
              "completed_deliveries": 145,
              "average_rating": 4.7,
              "created_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/v1/provider/drivers/{id}'
    metadata:
      groupName: 'Provider Drivers'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update driver.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the driver.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      first_name:
        name: first_name
        description: "Driver's first name."
        required: false
        example: John
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      last_name:
        name: last_name
        description: "Driver's last name."
        required: false
        example: Doe
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      phone_number:
        name: phone_number
        description: "Driver's phone number."
        required: false
        example: '08012345678'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      license_number:
        name: license_number
        description: "Driver's license number."
        required: false
        example: ABC123456
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      vehicle_type:
        name: vehicle_type
        description: 'Vehicle type.'
        required: false
        example: motorcycle
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_active:
        name: is_active
        description: "Driver's active status."
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      first_name: John
      last_name: Doe
      phone_number: '08012345678'
      license_number: ABC123456
      vehicle_type: motorcycle
      is_active: true
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Driver updated successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "John Doe",
              "email": "<EMAIL>",
              "phone": "08012345678",
              "is_active": true,
              "is_available": true,
              "vehicle_type": "motorcycle",
              "license_number": "ABC123456",
              "created_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/provider/drivers/{id}'
    metadata:
      groupName: 'Provider Drivers'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove driver.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the driver.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Driver removed successfully"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
