## Autogenerated by Scribe. DO NOT MODIFY.

name: 'Admin Real-Time Tracking'
description: |-

  APIs for admins to monitor and manage real-time delivery tracking across the platform
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/admin/tracking/dashboard
    metadata:
      groupName: 'Admin Real-Time Tracking'
      groupDescription: |-

        APIs for admins to monitor and manage real-time delivery tracking across the platform
      subgroup: ''
      subgroupDescription: ''
      title: 'Get Real-Time Dashboard'
      description: 'Get comprehensive real-time tracking dashboard for all active deliveries.'
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Real-time dashboard retrieved successfully",
            "data": {
              "summary": {
                "total_active_deliveries": 45,
                "deliveries_with_tracking": 38,
                "average_delivery_time": "32 minutes",
                "on_time_percentage": 87.5
              },
              "active_deliveries": [
                {
                  "delivery_id": "uuid",
                  "order_reference": "ORD-2024-ABC123",
                  "customer_name": "<PERSON>e",
                  "business_name": "<PERSON>'s Pizza",
                  "driver_name": "<PERSON> <PERSON>",
                  "status": "in_transit",
                  "current_location": {
                    "latitude": 6.5244,
                    "longitude": 3.3792,
                    "timestamp": "2024-01-15T10:30:00Z"
                  },
                  "estimated_delivery_time": "2024-01-15T11:15:00Z",
                  "is_delayed": false
                }
              ]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/admin/tracking/deliveries/{delivery_id}'
    metadata:
      groupName: 'Admin Real-Time Tracking'
      groupDescription: |-

        APIs for admins to monitor and manage real-time delivery tracking across the platform
      subgroup: ''
      subgroupDescription: ''
      title: 'Track Specific Delivery'
      description: 'Get detailed real-time tracking information for a specific delivery.'
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      delivery_id:
        name: delivery_id
        description: 'The delivery ID to track.'
        required: true
        example: uuid
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      delivery_id: uuid
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Delivery tracking retrieved successfully",
            "data": {
              "delivery": {
                "id": "uuid",
                "tracking_number": "TRK-2024-ABC123",
                "status": "in_transit",
                "order_reference": "ORD-2024-ABC123"
              },
              "current_location": {
                "latitude": 6.5244,
                "longitude": 3.3792,
                "accuracy": 5.0,
                "speed": 25.5,
                "heading": 180.0,
                "timestamp": "2024-01-15T10:30:00Z"
              },
              "tracking_history": [
                {
                  "latitude": 6.5200,
                  "longitude": 3.3750,
                  "timestamp": "2024-01-15T10:25:00Z"
                }
              ],
              "driver": {
                "id": "uuid",
                "name": "Jane Driver",
                "phone": "+2348987654321"
              },
              "customer": {
                "id": "uuid",
                "name": "John Doe",
                "phone": "+2348123456789"
              },
              "business": {
                "id": "uuid",
                "name": "Mario's Pizza",
                "address": "123 Business St, Lagos"
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/tracking/active-deliveries
    metadata:
      groupName: 'Admin Real-Time Tracking'
      groupDescription: |-

        APIs for admins to monitor and manage real-time delivery tracking across the platform
      subgroup: ''
      subgroupDescription: ''
      title: 'Get All Active Trackable Deliveries'
      description: 'Get all deliveries that are currently being tracked in real-time.'
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      status:
        name: status
        description: 'Filter by delivery status.'
        required: false
        example: in_transit
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      driver_id:
        name: driver_id
        description: 'Filter by driver ID.'
        required: false
        example: uuid
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      business_id:
        name: business_id
        description: 'Filter by business ID.'
        required: false
        example: uuid
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      delayed:
        name: delayed
        description: 'Filter delayed deliveries only.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      status: in_transit
      driver_id: uuid
      business_id: uuid
      delayed: true
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Active trackable deliveries retrieved successfully",
            "data": [
              {
                "delivery_id": "uuid",
                "order_reference": "ORD-2024-ABC123",
                "customer_name": "John Doe",
                "business_name": "Mario's Pizza",
                "driver_name": "Jane Driver",
                "status": "in_transit",
                "current_location": {
                  "latitude": 6.5244,
                  "longitude": 3.3792,
                  "timestamp": "2024-01-15T10:30:00Z"
                },
                "estimated_delivery_time": "2024-01-15T11:15:00Z",
                "is_delayed": false,
                "tracking_active": true
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
