## Autogenerated by Scribe. DO NOT MODIFY.

name: 'Business Delivery'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/business/deliveries
    metadata:
      groupName: 'Business Delivery'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get deliveries for the business'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      search:
        name: search
        description: 'Search in order reference, customer name, or delivery address.'
        required: false
        example: ORD-2024
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: 'Filter by delivery status.'
        required: false
        example: in_transit
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      driver_id:
        name: driver_id
        description: 'Filter by driver ID.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      date_from:
        name: date_from
        description: 'Filter deliveries from date (Y-m-d).'
        required: false
        example: '2024-01-01'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      date_to:
        name: date_to
        description: 'Filter deliveries to date (Y-m-d).'
        required: false
        example: '2024-01-31'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort:
        name: sort
        description: 'Sort field.'
        required: false
        example: created_at
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      direction:
        name: direction
        description: 'Sort direction (asc/desc).'
        required: false
        example: desc
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      page:
        name: page
        description: 'Page number.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (max 50).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      search: ORD-2024
      status: in_transit
      driver_id: 019723aa-3202-70dd-a0c1-3565681dd87a
      date_from: '2024-01-01'
      date_to: '2024-01-31'
      sort: created_at
      direction: desc
      page: 1
      per_page: 15
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Deliveries retrieved successfully",
            "data": [
              {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "order_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                "order_reference": "ORD-2024-001",
                "status": "in_transit",
                "customer_name": "John Doe",
                "delivery_address": {
                  "street": "123 Main St",
                  "city": "Lagos",
                  "state": "Lagos",
                  "latitude": 6.5244,
                  "longitude": 3.3792
                },
                "driver": {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
                  "name": "Driver Name",
                  "phone": "+234801234567"
                },
                "estimated_delivery_time": "2024-01-22T15:30:00Z",
                "delivery_fee": 800,
                "distance_km": 5.2,
                "created_at": "2024-01-22T14:00:00Z"
              }
            ],
            "meta": {
              "current_page": 1,
              "per_page": 15,
              "total": 25,
              "last_page": 2
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/business/deliveries
    metadata:
      groupName: 'Business Delivery'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Create a delivery for an order'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      order_id:
        name: order_id
        description: 'Order ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87b
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      provider_id:
        name: provider_id
        description: 'Provider ID (optional - will auto-match if not provided).'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87c
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      priority:
        name: priority
        description: 'Delivery priority (normal, urgent, economy).'
        required: false
        example: normal
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      delivery_instructions:
        name: delivery_instructions
        description: 'Special delivery instructions.'
        required: false
        example: 'Leave at front door'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      order_id: 019723aa-3202-70dd-a0c1-3565681dd87b
      provider_id: 019723aa-3202-70dd-a0c1-3565681dd87c
      priority: normal
      delivery_instructions: 'Leave at front door'
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "success": true,
            "message": "Delivery created successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "order_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
              "provider_id": "019723aa-3202-70dd-a0c1-3565681dd87c",
              "status": "pending",
              "estimated_pickup_time": "2024-01-22T14:20:00Z",
              "estimated_delivery_time": "2024-01-22T15:30:00Z",
              "delivery_fee": 800,
              "distance_km": 5.2
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/business/deliveries/{id}'
    metadata:
      groupName: 'Business Delivery'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get delivery details'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the delivery.'
        required: true
        example: 01974ede-f930-7382-94c9-bcd83c5b3c58
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      delivery:
        name: delivery
        description: 'The delivery ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 01974ede-f930-7382-94c9-bcd83c5b3c58
      delivery: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Delivery details retrieved successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "order": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                "order_reference": "ORD-2024-001",
                "total_amount": 2500
              },
              "customer": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
                "name": "John Doe",
                "phone": "+234801234567"
              },
              "provider": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87d",
                "business_name": "FastDelivery Ltd"
              },
              "driver": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87e",
                "name": "Driver Name",
                "phone": "+234801234568",
                "vehicle_info": "Honda CRV - ABC123"
              },
              "status": "in_transit",
              "pickup_address": {
                "street": "456 Business St",
                "city": "Lagos",
                "state": "Lagos"
              },
              "delivery_address": {
                "street": "123 Main St",
                "city": "Lagos",
                "state": "Lagos"
              },
              "estimated_pickup_time": "2024-01-22T14:20:00Z",
              "estimated_delivery_time": "2024-01-22T15:30:00Z",
              "picked_up_at": "2024-01-22T14:25:00Z",
              "delivery_fee": 800,
              "distance_km": 5.2,
              "driver_location": {
                "latitude": 6.5244,
                "longitude": 3.3792,
                "updated_at": "2024-01-22T15:00:00Z"
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/business/deliveries/{delivery_id}/cancel'
    metadata:
      groupName: 'Business Delivery'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Cancel a delivery'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      delivery_id:
        name: delivery_id
        description: 'The ID of the delivery.'
        required: true
        example: 01974ede-f930-7382-94c9-bcd83c5b3c58
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      delivery:
        name: delivery
        description: 'The delivery ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      delivery_id: 01974ede-f930-7382-94c9-bcd83c5b3c58
      delivery: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      reason:
        name: reason
        description: 'Cancellation reason.'
        required: true
        example: 'Customer requested cancellation'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      reason: 'Customer requested cancellation'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Delivery cancelled successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "status": "cancelled",
              "cancelled_at": "2024-01-22T15:00:00Z",
              "cancellation_reason": "Customer requested cancellation"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
