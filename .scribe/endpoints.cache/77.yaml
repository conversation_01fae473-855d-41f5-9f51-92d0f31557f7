## Autogenerated by Scribe. DO NOT MODIFY.

name: 'Business Dashboard'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/business/dashboard
    metadata:
      groupName: 'Business Dashboard'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get business dashboard overview.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      period:
        name: period
        description: 'Period for analytics (today, week, month, year).'
        required: false
        example: month
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      timezone:
        name: timezone
        description: 'Timezone for date calculations.'
        required: false
        example: Africa/Lagos
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      period: month
      timezone: Africa/Lagos
    bodyParameters:
      period:
        name: period
        description: ''
        required: false
        example: week
        type: string
        enumValues:
          - today
          - week
          - month
          - year
        exampleWasSpecified: false
        nullable: false
        custom: []
      timezone:
        name: timezone
        description: 'Must not be greater than 50 characters.'
        required: false
        example: Asia/Yekaterinburg
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      period: week
      timezone: Asia/Yekaterinburg
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Dashboard data retrieved successfully",
            "data": {
              "overview": {
                "total_orders": 150,
                "total_revenue": 750000,
                "average_order_value": 5000,
                "active_products": 25,
                "total_categories": 8,
                "business_status": "active",
                "auto_accept_enabled": true
              },
              "period_stats": {
                "period": "month",
                "orders_count": 45,
                "revenue": 225000,
                "growth_percentage": 15.5,
                "completed_orders": 42,
                "cancelled_orders": 3,
                "completion_rate": 93.3
              },
              "recent_orders": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "order_reference": "ORD-2024-001",
                  "customer_name": "John Doe",
                  "total_amount": 5500,
                  "status": "completed",
                  "created_at": "2024-01-15T10:30:00Z"
                }
              ],
              "top_products": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "name": "Jollof Rice",
                  "orders_count": 25,
                  "revenue": 125000
                }
              ],
              "performance_trends": {
                "daily_orders": [10, 15, 12, 18, 20, 16, 14],
                "daily_revenue": [50000, 75000, 60000, 90000, 100000, 80000, 70000],
                "labels": ["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"]
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
