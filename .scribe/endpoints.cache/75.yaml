## Autogenerated by Scribe. DO NOT MODIFY.

name: 'WhatsApp Configuration'
description: |-

  APIs for managing WhatsApp Business API configuration for tenant businesses
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/business/whatsapp/config
    metadata:
      groupName: 'WhatsApp Configuration'
      groupDescription: |-

        APIs for managing WhatsApp Business API configuration for tenant businesses
      subgroup: ''
      subgroupDescription: ''
      title: 'Get WhatsApp Configuration'
      description: 'Get the current WhatsApp Business API configuration for the tenant.'
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "WhatsApp configuration retrieved successfully",
            "data": {
              "config": {
                "id": "uuid",
                "phone_number": "+*************",
                "display_phone_number": "+234 ************",
                "is_verified": true,
                "verification_status": "verified",
                "business_profile": {
                  "name": "My Business",
                  "description": "We deliver great food"
                },
                "settings": {
                  "auto_reply": {
                    "enabled": true,
                    "message": "Thank you for contacting us!"
                  }
                }
              },
              "setup_required": false,
              "webhook_url": "https://api.deliverynexus.com/api/v1/whatsapp/webhook"
            }
          }
        headers: []
        description: ''
        custom: []
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "WhatsApp configuration retrieved successfully",
            "data": {
              "config": null,
              "setup_required": true,
              "webhook_url": "https://api.deliverynexus.com/api/v1/whatsapp/webhook"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/business/whatsapp/config
    metadata:
      groupName: 'WhatsApp Configuration'
      groupDescription: |-

        APIs for managing WhatsApp Business API configuration for tenant businesses
      subgroup: ''
      subgroupDescription: ''
      title: 'Create/Update WhatsApp Configuration'
      description: 'Create or update WhatsApp Business API configuration for the tenant.'
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      phone_number:
        name: phone_number
        description: 'The WhatsApp Business phone number.'
        required: true
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      display_phone_number:
        name: display_phone_number
        description: 'optional Formatted display phone number.'
        required: false
        example: '+234 ************'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      business_account_id:
        name: business_account_id
        description: 'WhatsApp Business Account ID.'
        required: true
        example: '***************'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      access_token:
        name: access_token
        description: 'WhatsApp Business API access token.'
        required: true
        example: EAABsBCS1234...
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      api_version:
        name: api_version
        description: 'optional API version to use.'
        required: false
        example: v18.0
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      business_profile:
        name: business_profile
        description: 'optional Business profile information.'
        required: false
        example: []
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_profile.name:
        name: business_profile.name
        description: 'optional Business name.'
        required: false
        example: 'My Restaurant'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      business_profile.description:
        name: business_profile.description
        description: 'optional Business description.'
        required: false
        example: 'We deliver great food'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      business_profile.email:
        name: business_profile.email
        description: 'Must be a valid email address. Must not be greater than 100 characters.'
        required: false
        example: <EMAIL>
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      business_profile.websites:
        name: business_profile.websites
        description: 'Must be a valid URL. Must not be greater than 255 characters.'
        required: false
        example:
          - k
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings:
        name: settings
        description: 'optional WhatsApp settings configuration.'
        required: false
        example: []
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.auto_reply:
        name: settings.auto_reply
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.auto_reply.enabled:
        name: settings.auto_reply.enabled
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.auto_reply.message:
        name: settings.auto_reply.message
        description: 'Must not be greater than 1000 characters.'
        required: false
        example: f
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.auto_reply.business_hours_only:
        name: settings.auto_reply.business_hours_only
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.order_notifications:
        name: settings.order_notifications
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.order_notifications.enabled:
        name: settings.order_notifications.enabled
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.order_notifications.statuses:
        name: settings.order_notifications.statuses
        description: ''
        required: false
        example:
          - confirmed
        type: 'string[]'
        enumValues:
          - confirmed
          - preparing
          - ready
          - out_for_delivery
          - delivered
          - cancelled
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.catalog_sharing:
        name: settings.catalog_sharing
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.catalog_sharing.enabled:
        name: settings.catalog_sharing.enabled
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.catalog_sharing.include_prices:
        name: settings.catalog_sharing.include_prices
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.catalog_sharing.include_images:
        name: settings.catalog_sharing.include_images
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.customer_support:
        name: settings.customer_support
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.customer_support.enabled:
        name: settings.customer_support.enabled
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.customer_support.escalation_keywords:
        name: settings.customer_support.escalation_keywords
        description: 'Must not be greater than 50 characters.'
        required: false
        example:
          - t
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.customer_support.business_hours:
        name: settings.customer_support.business_hours
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      phone_number: '+*************'
      display_phone_number: '+234 ************'
      business_account_id: '***************'
      access_token: EAABsBCS1234...
      api_version: v18.0
      business_profile:
        name: 'My Restaurant'
        description: 'We deliver great food'
        email: <EMAIL>
        websites:
          - k
      settings:
        auto_reply:
          enabled: false
          message: f
          business_hours_only: true
        order_notifications:
          enabled: true
          statuses:
            - confirmed
        catalog_sharing:
          enabled: true
          include_prices: false
          include_images: false
        customer_support:
          enabled: true
          escalation_keywords:
            - t
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "success": true,
            "message": "WhatsApp configuration saved. Verification process initiated.",
            "data": {
              "id": "uuid",
              "phone_number": "+*************",
              "display_phone_number": "+234 ************",
              "is_verified": false,
              "verification_status": "pending",
              "webhook_verify_token": "abc123def456",
              "webhook_url": "https://api.deliverynexus.com/api/v1/whatsapp/webhook"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/business/whatsapp/config/settings
    metadata:
      groupName: 'WhatsApp Configuration'
      groupDescription: |-

        APIs for managing WhatsApp Business API configuration for tenant businesses
      subgroup: ''
      subgroupDescription: ''
      title: 'Update WhatsApp Settings'
      description: 'Update WhatsApp settings and preferences for the tenant.'
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      settings:
        name: settings
        description: 'WhatsApp settings configuration.'
        required: true
        example: []
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.auto_reply:
        name: settings.auto_reply
        description: 'optional Auto-reply settings.'
        required: false
        example: []
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.auto_reply.enabled:
        name: settings.auto_reply.enabled
        description: 'optional Enable auto-reply.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      settings.auto_reply.message:
        name: settings.auto_reply.message
        description: 'optional Auto-reply message.'
        required: false
        example: 'Thank you for contacting us!'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      settings.auto_reply.business_hours_only:
        name: settings.auto_reply.business_hours_only
        description: 'optional Only auto-reply during business hours.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      settings.order_notifications:
        name: settings.order_notifications
        description: 'optional Order notification settings.'
        required: false
        example: []
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.order_notifications.enabled:
        name: settings.order_notifications.enabled
        description: 'optional Enable order notifications.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      settings.order_notifications.statuses:
        name: settings.order_notifications.statuses
        description: 'optional Order statuses to notify about.'
        required: false
        example:
          - confirmed
          - ready
          - delivered
        type: 'string[]'
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      settings.customer_support:
        name: settings.customer_support
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      settings.catalog_sharing:
        name: settings.catalog_sharing
        description: ''
        required: false
        example: null
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      settings:
        auto_reply:
          enabled: true
          message: 'Thank you for contacting us!'
          business_hours_only: true
        order_notifications:
          enabled: true
          statuses:
            - confirmed
            - ready
            - delivered
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "WhatsApp settings updated successfully",
            "data": {
              "settings": {
                "auto_reply": {
                  "enabled": true,
                  "message": "Thank you for contacting us!",
                  "business_hours_only": true
                },
                "order_notifications": {
                  "enabled": true,
                  "statuses": ["confirmed", "ready", "delivered"]
                }
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/business/whatsapp/config/test
    metadata:
      groupName: 'WhatsApp Configuration'
      groupDescription: |-

        APIs for managing WhatsApp Business API configuration for tenant businesses
      subgroup: ''
      subgroupDescription: ''
      title: 'Test WhatsApp Configuration'
      description: 'Send a test message to verify WhatsApp configuration is working.'
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      phone_number:
        name: phone_number
        description: 'Phone number to send test message to.'
        required: true
        example: '+*************'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      message:
        name: message
        description: 'optional Custom test message.'
        required: false
        example: 'This is a test message from our WhatsApp integration.'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      phone_number: '+*************'
      message: 'This is a test message from our WhatsApp integration.'
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Test message sent successfully",
            "data": {
              "sent_to": "+*************",
              "message": "Test message from My Business",
              "timestamp": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: api/v1/business/whatsapp/config
    metadata:
      groupName: 'WhatsApp Configuration'
      groupDescription: |-

        APIs for managing WhatsApp Business API configuration for tenant businesses
      subgroup: ''
      subgroupDescription: ''
      title: 'Delete WhatsApp Configuration'
      description: 'Remove WhatsApp configuration for the tenant.'
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "WhatsApp configuration deleted successfully"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
