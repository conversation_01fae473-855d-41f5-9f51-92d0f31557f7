## Autogenerated by Scribe. DO NOT MODIFY.

name: 'Business Pickup Management'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/business/pickup-slots
    metadata:
      groupName: 'Business Pickup Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get pickup slots list'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 'Page number for pagination.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (max 100).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      search:
        name: search
        description: 'Search pickup slots by day name.'
        required: false
        example: monday
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      day_of_week:
        name: day_of_week
        description: 'Filter by day of week (0-6).'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_active:
        name: is_active
        description: 'Filter by active status.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      branch_id:
        name: branch_id
        description: 'Filter by business branch ID.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87b
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort:
        name: sort
        description: 'Sort field (day_of_week, start_time, created_at).'
        required: false
        example: day_of_week
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      direction:
        name: direction
        description: 'Sort direction (asc, desc).'
        required: false
        example: asc
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 15
      search: monday
      day_of_week: 1
      is_active: true
      branch_id: 019723aa-3202-70dd-a0c1-3565681dd87b
      sort: day_of_week
      direction: asc
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Pickup slots retrieved successfully",
            "data": [
              {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "day_of_week": 1,
                "day_name": "Monday",
                "start_time": "09:00",
                "end_time": "12:00",
                "time_range": "09:00 - 12:00",
                "display_name": "Monday 09:00 - 12:00",
                "max_orders": 20,
                "has_limit": true,
                "is_active": true,
                "current_orders_count": 5,
                "remaining_capacity": 15,
                "is_currently_open": false,
                "next_occurrence": "2024-01-22T09:00:00Z",
                "business_branch": {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                  "name": "Main Branch"
                }
              }
            ],
            "meta": {
              "current_page": 1,
              "per_page": 15,
              "total": 25,
              "last_page": 2
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/business/pickup-slots
    metadata:
      groupName: 'Business Pickup Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Create new pickup slot'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      business_branch_id:
        name: business_branch_id
        description: 'Business branch ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87b
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      day_of_week:
        name: day_of_week
        description: 'Day of week (0=Sunday, 1=Monday, etc.).'
        required: true
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      start_time:
        name: start_time
        description: 'Start time in HH:MM format.'
        required: true
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      end_time:
        name: end_time
        description: 'End time in HH:MM format.'
        required: true
        example: '12:00'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      max_orders:
        name: max_orders
        description: 'Maximum number of orders for this slot.'
        required: false
        example: 20
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      is_active:
        name: is_active
        description: 'Whether the slot is active.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      business_branch_id: 019723aa-3202-70dd-a0c1-3565681dd87b
      day_of_week: 1
      start_time: '09:00'
      end_time: '12:00'
      max_orders: 20
      is_active: true
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "success": true,
            "message": "Pickup slot created successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "day_of_week": 1,
              "day_name": "Monday",
              "start_time": "09:00",
              "end_time": "12:00",
              "time_range": "09:00 - 12:00",
              "display_name": "Monday 09:00 - 12:00",
              "max_orders": 20,
              "has_limit": true,
              "is_active": true,
              "current_orders_count": 0,
              "remaining_capacity": 20,
              "is_currently_open": false,
              "next_occurrence": "2024-01-22T09:00:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/business/pickup-slots/{id}'
    metadata:
      groupName: 'Business Pickup Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get specific pickup slot'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the pickup slot.'
        required: true
        example: 01974edf-089a-717b-8f2e-bd793f9b2669
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      pickupSlot:
        name: pickupSlot
        description: 'The pickup slot ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 01974edf-089a-717b-8f2e-bd793f9b2669
      pickupSlot: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Pickup slot retrieved successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "day_of_week": 1,
              "day_name": "Monday",
              "start_time": "09:00",
              "end_time": "12:00",
              "time_range": "09:00 - 12:00",
              "display_name": "Monday 09:00 - 12:00",
              "max_orders": 20,
              "has_limit": true,
              "is_active": true,
              "current_orders_count": 5,
              "remaining_capacity": 15,
              "is_currently_open": false,
              "next_occurrence": "2024-01-22T09:00:00Z",
              "business_branch": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
                "name": "Main Branch"
              },
              "recent_orders": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
                  "order_reference": "ORD-2024-001",
                  "customer_name": "John Doe",
                  "scheduled_pickup_time": "2024-01-22T09:30:00Z",
                  "status": "ready_for_pickup"
                }
              ]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/v1/business/pickup-slots/{id}'
    metadata:
      groupName: 'Business Pickup Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update pickup slot'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the pickup slot.'
        required: true
        example: 01974edf-089a-717b-8f2e-bd793f9b2669
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      pickupSlot:
        name: pickupSlot
        description: 'The pickup slot ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 01974edf-089a-717b-8f2e-bd793f9b2669
      pickupSlot: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      day_of_week:
        name: day_of_week
        description: 'Day of week (0=Sunday, 1=Monday, etc.).'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      start_time:
        name: start_time
        description: 'Start time in HH:MM format.'
        required: false
        example: '09:00'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      end_time:
        name: end_time
        description: 'End time in HH:MM format.'
        required: false
        example: '12:00'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      max_orders:
        name: max_orders
        description: 'Maximum number of orders for this slot.'
        required: false
        example: 20
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: true
        custom: []
      is_active:
        name: is_active
        description: 'Whether the slot is active.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      day_of_week: 1
      start_time: '09:00'
      end_time: '12:00'
      max_orders: 20
      is_active: true
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Pickup slot updated successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "day_of_week": 1,
              "day_name": "Monday",
              "start_time": "09:00",
              "end_time": "12:00",
              "time_range": "09:00 - 12:00",
              "display_name": "Monday 09:00 - 12:00",
              "max_orders": 25,
              "has_limit": true,
              "is_active": true,
              "current_orders_count": 5,
              "remaining_capacity": 20,
              "is_currently_open": false,
              "next_occurrence": "2024-01-22T09:00:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/business/pickup-slots/{id}'
    metadata:
      groupName: 'Business Pickup Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Delete pickup slot'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the pickup slot.'
        required: true
        example: 01974edf-089a-717b-8f2e-bd793f9b2669
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      pickupSlot:
        name: pickupSlot
        description: 'The pickup slot ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: 01974edf-089a-717b-8f2e-bd793f9b2669
      pickupSlot: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Pickup slot deleted successfully"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/business/pickup-slots-schedule/weekly
    metadata:
      groupName: 'Business Pickup Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get weekly schedule for business'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      branch_id:
        name: branch_id
        description: 'Filter by business branch ID.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87b
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      branch_id: 019723aa-3202-70dd-a0c1-3565681dd87b
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Weekly schedule retrieved successfully",
            "data": {
              "0": {
                "day_name": "Sunday",
                "slots": []
              },
              "1": {
                "day_name": "Monday",
                "slots": [
                  {
                    "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                    "day_of_week": 1,
                    "day_name": "Monday",
                    "start_time": "09:00",
                    "end_time": "12:00",
                    "time_range": "09:00 - 12:00",
                    "display_name": "Monday 09:00 - 12:00",
                    "max_orders": 20,
                    "has_limit": true,
                    "is_active": true,
                    "current_orders_count": 5,
                    "remaining_capacity": 15,
                    "is_currently_open": false,
                    "next_occurrence": "2024-01-22T09:00:00Z"
                  }
                ]
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/business/pickup-slots-schedule/available
    metadata:
      groupName: 'Business Pickup Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get available pickup slots for a specific date'
      description: ''
      authenticated: true
      custom: []
    headers:
      Authorization: 'Bearer {token}'
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      date:
        name: date
        description: 'Date in Y-m-d format.'
        required: true
        example: '2024-01-22'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      branch_id:
        name: branch_id
        description: 'Filter by business branch ID.'
        required: false
        example: 019723aa-3202-70dd-a0c1-3565681dd87b
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      date: '2024-01-22'
      branch_id: 019723aa-3202-70dd-a0c1-3565681dd87b
    bodyParameters:
      date:
        name: date
        description: 'Must be a valid date. Must be a date after or equal to <code>today</code>.'
        required: true
        example: '2051-07-03'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      branch_id:
        name: branch_id
        description: 'The <code>id</code> of an existing record in the business_branches table.'
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      date: '2051-07-03'
      branch_id: architecto
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Available pickup slots retrieved successfully",
            "data": [
              {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "day_of_week": 1,
                "day_name": "Monday",
                "start_time": "09:00",
                "end_time": "12:00",
                "time_range": "09:00 - 12:00",
                "display_name": "Monday 09:00 - 12:00",
                "max_orders": 20,
                "has_limit": true,
                "is_active": true,
                "current_orders_count": 5,
                "remaining_capacity": 15,
                "is_currently_open": false,
                "next_occurrence": "2024-01-22T09:00:00Z"
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
