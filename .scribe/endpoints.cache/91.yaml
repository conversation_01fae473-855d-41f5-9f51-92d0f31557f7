## Autogenerated by Scribe. DO NOT MODIFY.

name: 'Real-Time Tracking (Provider)'
description: |-

  APIs for providers/drivers to update their location and manage real-time delivery tracking
endpoints:
  -
    httpMethods:
      - POST
    uri: api/v1/driver/tracking/location
    metadata:
      groupName: 'Real-Time Tracking (Provider)'
      groupDescription: |-

        APIs for providers/drivers to update their location and manage real-time delivery tracking
      subgroup: ''
      subgroupDescription: ''
      title: 'Update Driver Location'
      description: "Update the driver's current location for real-time delivery tracking."
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      delivery_id:
        name: delivery_id
        description: 'The delivery ID being tracked.'
        required: true
        example: uuid
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      latitude:
        name: latitude
        description: 'Current latitude.'
        required: true
        example: 6.5244
        type: number
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      longitude:
        name: longitude
        description: 'Current longitude.'
        required: true
        example: 3.3792
        type: number
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      accuracy:
        name: accuracy
        description: 'optional Location accuracy in meters.'
        required: false
        example: 5.0
        type: number
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      speed:
        name: speed
        description: 'optional Current speed in km/h.'
        required: false
        example: 25.5
        type: number
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      heading:
        name: heading
        description: 'optional Direction heading in degrees (0-360).'
        required: false
        example: 180.0
        type: number
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      delivery_id: uuid
      latitude: 6.5244
      longitude: 3.3792
      accuracy: 5.0
      speed: 25.5
      heading: 180.0
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Location updated successfully",
            "data": {
              "delivery_id": "uuid",
              "location": {
                "latitude": 6.5244,
                "longitude": 3.3792,
                "accuracy": 5.0,
                "speed": 25.5,
                "heading": 180.0,
                "timestamp": "2024-01-15T10:30:00Z"
              },
              "estimated_arrival": "2024-01-15T11:15:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/driver/tracking/start
    metadata:
      groupName: 'Real-Time Tracking (Provider)'
      groupDescription: |-

        APIs for providers/drivers to update their location and manage real-time delivery tracking
      subgroup: ''
      subgroupDescription: ''
      title: 'Start Delivery Tracking'
      description: 'Start real-time tracking for a delivery.'
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      delivery_id:
        name: delivery_id
        description: 'The delivery ID to start tracking.'
        required: true
        example: uuid
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      delivery_id: uuid
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Delivery tracking started successfully",
            "data": {
              "delivery_id": "uuid",
              "tracking_started": true,
              "started_at": "2024-01-15T10:00:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/driver/tracking/stop
    metadata:
      groupName: 'Real-Time Tracking (Provider)'
      groupDescription: |-

        APIs for providers/drivers to update their location and manage real-time delivery tracking
      subgroup: ''
      subgroupDescription: ''
      title: 'Stop Delivery Tracking'
      description: 'Stop real-time tracking for a delivery.'
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      delivery_id:
        name: delivery_id
        description: 'The delivery ID to stop tracking.'
        required: true
        example: uuid
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      delivery_id: uuid
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Delivery tracking stopped successfully",
            "data": {
              "delivery_id": "uuid",
              "tracking_stopped": true,
              "stopped_at": "2024-01-15T11:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/driver/tracking/deliveries/{delivery_id}/location'
    metadata:
      groupName: 'Real-Time Tracking (Provider)'
      groupDescription: |-

        APIs for providers/drivers to update their location and manage real-time delivery tracking
      subgroup: ''
      subgroupDescription: ''
      title: 'Get Current Location'
      description: 'Get the current location for a delivery being tracked.'
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      delivery_id:
        name: delivery_id
        description: 'The delivery ID.'
        required: true
        example: uuid
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      delivery_id: uuid
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Current location retrieved successfully",
            "data": {
              "delivery_id": "uuid",
              "current_location": {
                "latitude": 6.5244,
                "longitude": 3.3792,
                "accuracy": 5.0,
                "speed": 25.5,
                "heading": 180.0,
                "timestamp": "2024-01-15T10:30:00Z"
              },
              "tracking_history": [
                {
                  "latitude": 6.5200,
                  "longitude": 3.3750,
                  "timestamp": "2024-01-15T10:25:00Z"
                }
              ]
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/driver/tracking/active-deliveries
    metadata:
      groupName: 'Real-Time Tracking (Provider)'
      groupDescription: |-

        APIs for providers/drivers to update their location and manage real-time delivery tracking
      subgroup: ''
      subgroupDescription: ''
      title: "Get Driver's Active Deliveries"
      description: 'Get all active deliveries assigned to the current driver.'
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Active deliveries retrieved successfully",
            "data": [
              {
                "delivery_id": "uuid",
                "order_id": "uuid",
                "status": "in_transit",
                "pickup_address": "123 Business St, Lagos",
                "delivery_address": "456 Customer Ave, Lagos",
                "estimated_delivery_time": "2024-01-15T11:15:00Z",
                "is_tracking_active": true
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
