## Autogenerated by <PERSON>ribe. DO NOT MODIFY.

name: 'Provider Deliveries'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/provider/deliveries
    metadata:
      groupName: 'Provider Deliveries'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: "Get provider's deliveries."
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 'Page number for pagination.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (max 100).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      status:
        name: status
        description: 'Filter by delivery status.'
        required: false
        example: pending
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      date_from:
        name: date_from
        description: 'Filter deliveries from date (Y-m-d).'
        required: false
        example: '2024-01-01'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      date_to:
        name: date_to
        description: 'Filter deliveries to date (Y-m-d).'
        required: false
        example: '2024-01-31'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: 'Sort by field (created_at, delivery_fee, estimated_delivery_time).'
        required: false
        example: created_at
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      sort_direction:
        name: sort_direction
        description: 'Sort direction (asc, desc).'
        required: false
        example: desc
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 15
      status: pending
      date_from: '2024-01-01'
      date_to: '2024-01-31'
      sort_by: created_at
      sort_direction: desc
    bodyParameters:
      page:
        name: page
        description: 'Must be at least 1.'
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Must be at least 1. Must not be greater than 100.'
        required: false
        example: 22
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      status:
        name: status
        description: ''
        required: false
        example: accepted
        type: string
        enumValues:
          - pending
          - accepted
          - picked_up
          - in_transit
          - delivered
          - cancelled
        exampleWasSpecified: false
        nullable: false
        custom: []
      date_from:
        name: date_from
        description: 'Must be a valid date in the format <code>Y-m-d</code>.'
        required: false
        example: '2025-06-09'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      date_to:
        name: date_to
        description: 'Must be a valid date in the format <code>Y-m-d</code>.'
        required: false
        example: '2025-06-09'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      sort_by:
        name: sort_by
        description: ''
        required: false
        example: estimated_delivery_time
        type: string
        enumValues:
          - created_at
          - delivery_fee
          - estimated_delivery_time
        exampleWasSpecified: false
        nullable: false
        custom: []
      sort_direction:
        name: sort_direction
        description: ''
        required: false
        example: desc
        type: string
        enumValues:
          - asc
          - desc
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      page: 16
      per_page: 22
      status: accepted
      date_from: '2025-06-09'
      date_to: '2025-06-09'
      sort_by: estimated_delivery_time
      sort_direction: desc
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Deliveries retrieved successfully",
            "data": {
              "data": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "order_reference": "ORD-2024-001",
                  "business_name": "Tasty Kitchen",
                  "customer_name": "John Doe",
                  "pickup_address": "123 Business St, Lagos",
                  "delivery_address": "456 Customer Ave, Lagos",
                  "delivery_fee": 500,
                  "status": "pending",
                  "estimated_delivery_time": "2024-01-15T15:30:00Z",
                  "created_at": "2024-01-15T14:00:00Z",
                  "distance_km": 5.2
                }
              ],
              "current_page": 1,
              "per_page": 15,
              "total": 25
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/provider/deliveries/{id}'
    metadata:
      groupName: 'Provider Deliveries'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get specific delivery details.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the delivery.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      delivery:
        name: delivery
        description: 'Delivery ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
      delivery: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Delivery retrieved successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "order_reference": "ORD-2024-001",
              "business": {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "business_name": "Tasty Kitchen",
                "contact_phone": "***********"
              },
              "customer": {
                "name": "John Doe",
                "phone": "***********"
              },
              "pickup_address": "123 Business St, Lagos",
              "delivery_address": "456 Customer Ave, Lagos",
              "delivery_fee": 500,
              "status": "pending",
              "estimated_delivery_time": "2024-01-15T15:30:00Z",
              "special_instructions": "Handle with care",
              "distance_km": 5.2,
              "created_at": "2024-01-15T14:00:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: 'api/v1/provider/deliveries/{delivery}/accept'
    metadata:
      groupName: 'Provider Deliveries'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Accept a delivery request.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      delivery:
        name: delivery
        description: 'Delivery ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      delivery: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Delivery accepted successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "status": "accepted",
              "accepted_at": "2024-01-15T14:05:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
