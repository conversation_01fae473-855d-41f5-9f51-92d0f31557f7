## Autogenerated by <PERSON>ribe. DO NOT MODIFY.

name: 'File Management'
description: ''
endpoints:
  -
    httpMethods:
      - POST
    uri: api/v1/files/upload
    metadata:
      groupName: 'File Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Upload a file.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: multipart/form-data
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      file:
        name: file
        description: 'The file to upload (max 50MB for documents, 10MB for CSV)'
        required: true
        example: null
        type: file
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      category:
        name: category
        description: 'File category (documents, verification, exports, reports, etc.)'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      entity_id:
        name: entity_id
        description: 'Optional entity ID for organization'
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      visibility:
        name: visibility
        description: 'Visibility setting (private or public). Default: private'
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      attachable_type:
        name: attachable_type
        description: 'Optional polymorphic type for attachment'
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      attachable_id:
        name: attachable_id
        description: 'Optional polymorphic ID for attachment'
        required: false
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      category: architecto
      entity_id: architecto
      visibility: architecto
      attachable_type: architecto
      attachable_id: architecto
    fileParameters:
      file: null
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "File uploaded successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "file_name": "document.pdf",
              "file_size": 1024000,
              "file_type": "application/pdf",
              "url": "https://example.com/signed-url",
              "category": "documents",
              "created_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/files/download/{path}'
    metadata:
      groupName: 'File Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Download a file.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      path:
        name: path
        description: 'The file path to download'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      path: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: 'Binary file content'
        headers: []
        description: ''
        custom: []
      -
        status: 404
        content: |-
          {
            "success": false,
            "message": "File not found"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/files/{path}'
    metadata:
      groupName: 'File Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Delete a file.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      path:
        name: path
        description: 'The file path to delete'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      path: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "File deleted successfully"
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/files/category/{category}'
    metadata:
      groupName: 'File Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'List files by category.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      category:
        name: category
        description: 'File category'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanUrlParameters:
      category: architecto
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "data": [
              {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "file_name": "document.pdf",
                "file_size": 1024000,
                "file_type": "application/pdf",
                "url": "https://example.com/signed-url",
                "created_at": "2024-01-15T10:30:00Z"
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
