## Autogenerated by Scribe. DO NOT MODIFY.

name: 'Business Settings'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/business/settings
    metadata:
      groupName: 'Business Settings'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get current business settings.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Business settings retrieved successfully",
            "data": {
              "operating_hours": {
                "monday": {"open": "08:00", "close": "22:00"},
                "tuesday": {"open": "08:00", "close": "22:00"}
              },
              "global_auto_accept_orders": true,
              "auto_acceptance_criteria": {
                "min_value": 500,
                "max_distance": 5,
                "business_hours_only": true
              },
              "accepts_cash_on_delivery": true,
              "allows_pickup": false
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
    uri: api/v1/business/settings
    metadata:
      groupName: 'Business Settings'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update business settings.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      auto_accept_orders:
        name: auto_accept_orders
        description: 'Automatically accept orders that meet criteria.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      order_notification_sound:
        name: order_notification_sound
        description: 'Play sound for new order notifications.'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      delivery_radius_km:
        name: delivery_radius_km
        description: 'Maximum delivery radius in kilometers. Must be at least 1. Must not be greater than 100.'
        required: false
        example: 10.5
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      minimum_order_value:
        name: minimum_order_value
        description: 'Minimum order value to accept. Must be at least 0.'
        required: false
        example: 500.0
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      preparation_time_minutes:
        name: preparation_time_minutes
        description: 'Average preparation time in minutes. Must be at least 5. Must not be greater than 180.'
        required: false
        example: 30
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      operating_hours:
        name: operating_hours
        description: 'optional Operating hours for each day'
        required: false
        example: []
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      auto_acceptance_criteria:
        name: auto_acceptance_criteria
        description: 'optional Criteria for auto-accepting orders'
        required: false
        example: []
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      auto_acceptance_criteria.min_value:
        name: auto_acceptance_criteria.min_value
        description: 'optional Minimum order value'
        required: false
        example: 4326.41688
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      auto_acceptance_criteria.max_distance:
        name: auto_acceptance_criteria.max_distance
        description: 'optional Maximum delivery distance'
        required: false
        example: 4326.41688
        type: number
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      auto_acceptance_criteria.business_hours_only:
        name: auto_acceptance_criteria.business_hours_only
        description: 'optional Only during business hours'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      payment_settings:
        name: payment_settings
        description: 'Payment method preferences.'
        required: false
        example:
          accept_cash: true
          accept_card: true
          accept_transfer: true
          require_payment_before_preparation: false
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      payment_settings.accept_cash:
        name: payment_settings.accept_cash
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      payment_settings.accept_card:
        name: payment_settings.accept_card
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      payment_settings.accept_transfer:
        name: payment_settings.accept_transfer
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      payment_settings.require_payment_before_preparation:
        name: payment_settings.require_payment_before_preparation
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notification_settings:
        name: notification_settings
        description: 'Business notification preferences.'
        required: false
        example:
          new_order_sound: true
          low_stock_alerts: true
          daily_summary: true
          weekly_report: false
        type: object
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notification_settings.new_order_sound:
        name: notification_settings.new_order_sound
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notification_settings.low_stock_alerts:
        name: notification_settings.low_stock_alerts
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notification_settings.daily_summary:
        name: notification_settings.daily_summary
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      notification_settings.weekly_report:
        name: notification_settings.weekly_report
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'operating_hours[].day':
        name: 'operating_hours[].day'
        description: 'This field is required when <code>operating_hours</code> is present.'
        required: false
        example: thursday
        type: string
        enumValues:
          - monday
          - tuesday
          - wednesday
          - thursday
          - friday
          - saturday
          - sunday
        exampleWasSpecified: false
        nullable: false
        custom: []
      'operating_hours[].open_time':
        name: 'operating_hours[].open_time'
        description: 'This field is required when <code>operating_hours</code> is present. Must be a valid date in the format <code>H:i</code>.'
        required: false
        example: '13:55'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'operating_hours[].close_time':
        name: 'operating_hours[].close_time'
        description: 'This field is required when <code>operating_hours</code> is present. Must be a valid date in the format <code>H:i</code>.'
        required: false
        example: '13:55'
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      'operating_hours[].is_closed':
        name: 'operating_hours[].is_closed'
        description: ''
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      global_auto_accept_orders:
        name: global_auto_accept_orders
        description: 'optional Enable auto-accept orders'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      accepts_cash_on_delivery:
        name: accepts_cash_on_delivery
        description: 'optional Accept cash on delivery'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      allows_pickup:
        name: allows_pickup
        description: 'optional Allow customer pickup'
        required: false
        example: false
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      auto_accept_orders: false
      order_notification_sound: false
      delivery_radius_km: 10.5
      minimum_order_value: 500.0
      preparation_time_minutes: 30
      operating_hours:
        -
          day: thursday
          open_time: '13:55'
          close_time: '13:55'
          is_closed: false
      auto_acceptance_criteria:
        min_value: 4326.41688
        max_distance: 4326.41688
        business_hours_only: false
      payment_settings:
        accept_cash: true
        accept_card: true
        accept_transfer: true
        require_payment_before_preparation: false
      notification_settings:
        new_order_sound: true
        low_stock_alerts: true
        daily_summary: true
        weekly_report: false
      global_auto_accept_orders: false
      accepts_cash_on_delivery: false
      allows_pickup: false
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Business settings updated successfully",
            "data": {
              "global_auto_accept_orders": true,
              "auto_acceptance_criteria": {
                "min_value": 1000,
                "max_distance": 10
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
