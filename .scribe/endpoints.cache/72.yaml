## Autogenerated by Scribe. DO NOT MODIFY.

name: 'Product Catalog Management'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: 'api/v1/business/branches/{branchId}/product-collections'
    metadata:
      groupName: 'Product Catalog Management'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get product collections for a specific branch.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      branchId:
        name: branchId
        description: 'The branch ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      branchId: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Branch product collections retrieved successfully",
            "data": [
              {
                "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                "name": "Breakfast Menu",
                "description": "Morning specials available until 11 AM",
                "type": "time_based",
                "is_active": true,
                "display_order": 1,
                "is_branch_specific": false,
                "branch_customization": {
                  "is_active": true,
                  "active_start_time": "2024-01-15T06:00:00Z",
                  "active_end_time": "2024-01-15T11:00:00Z",
                  "display_order": 2,
                  "custom_settings": {}
                }
              }
            ]
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
