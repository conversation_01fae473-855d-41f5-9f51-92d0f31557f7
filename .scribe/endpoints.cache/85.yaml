## Autogenerated by Scribe. DO NOT MODIFY.

name: 'Provider Service Areas'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/provider/service-areas
    metadata:
      groupName: 'Provider Service Areas'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get provider service areas.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters:
      page:
        name: page
        description: 'Page number for pagination.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Items per page (max 50).'
        required: false
        example: 15
        type: integer
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      search:
        name: search
        description: 'Search service areas by name.'
        required: false
        example: lagos
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      is_active:
        name: is_active
        description: 'Filter by active status.'
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanQueryParameters:
      page: 1
      per_page: 15
      search: lagos
      is_active: true
    bodyParameters:
      page:
        name: page
        description: 'Must be at least 1.'
        required: false
        example: 16
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      per_page:
        name: per_page
        description: 'Must be at least 1. Must not be greater than 50.'
        required: false
        example: 22
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      search:
        name: search
        description: 'Must not be greater than 255 characters.'
        required: false
        example: g
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      is_active:
        name: is_active
        description: ''
        required: false
        example: true
        type: boolean
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      page: 16
      per_page: 22
      search: g
      is_active: true
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Service areas retrieved successfully",
            "data": {
              "data": [
                {
                  "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
                  "name": "Lagos Island",
                  "description": "Lagos Island and surrounding areas",
                  "is_active": true,
                  "delivery_fee": 1000,
                  "minimum_order": 500,
                  "estimated_time": "30-45 minutes",
                  "coverage_radius": 15,
                  "coordinates": {
                    "center": {"lat": 6.4541, "lng": 3.3947},
                    "bounds": [
                      {"lat": 6.4400, "lng": 3.3800},
                      {"lat": 6.4700, "lng": 3.4100}
                    ]
                  },
                  "created_at": "2024-01-15T10:30:00Z"
                }
              ],
              "current_page": 1,
              "per_page": 15,
              "total": 5
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/provider/service-areas
    metadata:
      groupName: 'Provider Service Areas'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Add service area.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Service area name.'
        required: true
        example: 'Lagos Island'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      polygon_coordinates:
        name: polygon_coordinates
        description: 'Polygon coordinates as GeoJSON or simple coordinates.'
        required: true
        example: '[[6.4400,3.3800],[6.4700,3.4100]]'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      price_multiplier:
        name: price_multiplier
        description: 'Price multiplier for this area.'
        required: false
        example: 1.2
        type: number
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 'Lagos Island'
      polygon_coordinates: '[[6.4400,3.3800],[6.4700,3.4100]]'
      price_multiplier: 1.2
    fileParameters: []
    responses:
      -
        status: 201
        content: |-
          {
            "success": true,
            "message": "Service area created successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "Lagos Island",
              "polygon_coordinates": "[[6.4400,3.3800],[6.4700,3.4100]]",
              "price_multiplier": 1.2,
              "is_active": true,
              "created_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: 'api/v1/provider/service-areas/{id}'
    metadata:
      groupName: 'Provider Service Areas'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get specific service area details.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the service area.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      area:
        name: area
        description: 'Service area ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
      area: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Service area retrieved successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "Lagos Island",
              "description": "Lagos Island and surrounding areas",
              "is_active": true,
              "delivery_fee": 1000,
              "minimum_order": 500,
              "estimated_time": "30-45 minutes",
              "coverage_radius": 15,
              "coordinates": {
                "center": {"lat": 6.4541, "lng": 3.3947},
                "bounds": [
                  {"lat": 6.4400, "lng": 3.3800},
                  {"lat": 6.4700, "lng": 3.4100}
                ]
              },
              "statistics": {
                "total_deliveries": 1250,
                "completed_deliveries": 1200,
                "average_delivery_time": "35 minutes",
                "success_rate": 96.0
              },
              "created_at": "2024-01-15T10:30:00Z",
              "updated_at": "2024-01-20T14:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - PUT
      - PATCH
    uri: 'api/v1/provider/service-areas/{id}'
    metadata:
      groupName: 'Provider Service Areas'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Update service area.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the service area.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      area:
        name: area
        description: 'Service area ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
      area: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      name:
        name: name
        description: 'Service area name.'
        required: false
        example: 'Lagos Island Updated'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      polygon_coordinates:
        name: polygon_coordinates
        description: 'Polygon coordinates.'
        required: false
        example: '[[6.4400,3.3800],[6.4700,3.4100]]'
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
      price_multiplier:
        name: price_multiplier
        description: 'Price multiplier for this area.'
        required: false
        example: 1.5
        type: number
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanBodyParameters:
      name: 'Lagos Island Updated'
      polygon_coordinates: '[[6.4400,3.3800],[6.4700,3.4100]]'
      price_multiplier: 1.5
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Service area updated successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "Lagos Island Updated",
              "polygon_coordinates": "[[6.4400,3.3800],[6.4700,3.4100]]",
              "price_multiplier": 1.5,
              "is_active": true,
              "updated_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - DELETE
    uri: 'api/v1/provider/service-areas/{id}'
    metadata:
      groupName: 'Provider Service Areas'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Remove service area.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters:
      id:
        name: id
        description: 'The ID of the service area.'
        required: true
        example: architecto
        type: string
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
      area:
        name: area
        description: 'Service area ID.'
        required: true
        example: 019723aa-3202-70dd-a0c1-3565681dd87a
        type: string
        enumValues: []
        exampleWasSpecified: true
        nullable: false
        custom: []
    cleanUrlParameters:
      id: architecto
      area: 019723aa-3202-70dd-a0c1-3565681dd87a
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "Service area removed successfully",
            "data": {
              "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
              "name": "Lagos Island",
              "removed_at": "2024-01-15T10:30:00Z"
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
