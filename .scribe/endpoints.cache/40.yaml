## Autogenerated by Scribe. DO NOT MODIFY.

name: 'Admin System'
description: ''
endpoints:
  -
    httpMethods:
      - GET
    uri: api/v1/admin/system/health
    metadata:
      groupName: 'Admin System'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get system health overview.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 200
        content: |-
          {
            "success": true,
            "message": "System health retrieved successfully",
            "data": {
              "overall_status": "healthy",
              "services": {
                "database": {
                  "status": "healthy",
                  "response_time": 15,
                  "connections": 25
                },
                "redis": {
                  "status": "healthy",
                  "memory_usage": "45%",
                  "connected_clients": 12
                },
                "queue": {
                  "status": "healthy",
                  "pending_jobs": 5,
                  "failed_jobs": 0
                }
              },
              "performance": {
                "avg_response_time": 120,
                "memory_usage": "65%",
                "cpu_usage": "35%",
                "disk_usage": "45%"
              }
            }
          }
        headers: []
        description: ''
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/system/configuration
    metadata:
      groupName: 'Admin System'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get system configuration.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.928438Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: d3ce5ae0-955d-4da2-b12a-92c80f878c41
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-Hf/iXev2D+NQRBOAkTTPwQ==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/admin/system/clear-cache
    metadata:
      groupName: 'Admin System'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Clear system caches.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      cache_types:
        name: cache_types
        description: ''
        required: false
        example:
          - view
        type: 'string[]'
        enumValues:
          - application
          - config
          - route
          - view
          - event
          - tenant
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      cache_types:
        - view
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/admin/system/maintenance
    metadata:
      groupName: 'Admin System'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Run system maintenance.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      operations:
        name: operations
        description: ''
        required: false
        example:
          - migrate
        type: 'string[]'
        enumValues:
          - optimize
          - cleanup
          - backup
          - migrate
          - seed
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      operations:
        - migrate
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/system/logs
    metadata:
      groupName: 'Admin System'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get system logs.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters:
      log_type:
        name: log_type
        description: ''
        required: false
        example: access
        type: string
        enumValues:
          - application
          - error
          - access
          - security
        exampleWasSpecified: false
        nullable: false
        custom: []
      level:
        name: level
        description: ''
        required: false
        example: warning
        type: string
        enumValues:
          - debug
          - info
          - warning
          - error
          - critical
        exampleWasSpecified: false
        nullable: false
        custom: []
      lines:
        name: lines
        description: 'Must be at least 10. Must not be greater than 1000.'
        required: false
        example: 1
        type: integer
        enumValues: []
        exampleWasSpecified: false
        nullable: false
        custom: []
    cleanBodyParameters:
      log_type: access
      level: warning
      lines: 1
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.937142Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: 6048ecb9-a882-4e0e-a78b-3c6fb11f32fa
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-jLg5fMoEW1vJwSLXY2IkGA==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - GET
    uri: api/v1/admin/system/queue-status
    metadata:
      groupName: 'Admin System'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Get queue status.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses:
      -
        status: 401
        content: '{"success":false,"message":"Unauthenticated","error_code":"UNAUTHENTICATED","timestamp":"2025-06-09T13:55:13.943226Z"}'
        headers:
          cache-control: 'must-revalidate, no-cache, no-store, private'
          content-type: application/json
          x-content-type-options: nosniff
          x-frame-options: DENY
          x-xss-protection: '1; mode=block'
          referrer-policy: strict-origin-when-cross-origin
          x-api-version: v1
          x-request-id: b3dda7fd-34b6-44a3-82b3-9573c78f7cbe
          x-download-options: noopen
          x-permitted-cross-domain-policies: none
          pragma: no-cache
          expires: '0'
          permissions-policy: 'geolocation=(self), camera=(), microphone=(), payment=(self), usb=(), magnetometer=(), gyroscope=(), accelerometer=(), ambient-light-sensor=(), autoplay=(), encrypted-media=(), fullscreen=(self), picture-in-picture=()'
          content-security-policy: "default-src 'self'; script-src 'self' 'nonce-ktIA0JV+fuK/yYl4jONcCg==' 'unsafe-eval'; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self' https://api.twilio.com https://api.zeptomail.com https://api.paystack.co; frame-src 'none'; object-src 'none'; base-uri 'self'; form-action 'self'; connect-src 'self' wss://http://deliverynexus.test: https://api.twilio.com https://api.zeptomail.com https://api.paystack.co"
          access-control-allow-origin: '*'
        description: null
        custom: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
  -
    httpMethods:
      - POST
    uri: api/v1/admin/system/restart-queue
    metadata:
      groupName: 'Admin System'
      groupDescription: ''
      subgroup: ''
      subgroupDescription: ''
      title: 'Restart queue workers.'
      description: ''
      authenticated: true
      custom: []
    headers:
      Content-Type: application/json
      Accept: application/json
    urlParameters: []
    cleanUrlParameters: []
    queryParameters: []
    cleanQueryParameters: []
    bodyParameters: []
    cleanBodyParameters: []
    fileParameters: []
    responses: []
    responseFields: []
    auth: []
    controller: null
    method: null
    route: null
    custom: []
