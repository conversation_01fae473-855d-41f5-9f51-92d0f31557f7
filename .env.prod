# Production Environment Configuration for DeliveryNexus
# Copy this file to .env and update with your production values

# Application
APP_NAME="DeliveryNexus"
APP_ENV=production
APP_KEY=base64:GENERATE_NEW_KEY_FOR_PRODUCTION
APP_DEBUG=false
APP_TIMEZONE=Africa/Lagos
APP_URL=https://api.deliverynexus.com.ng
APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

# Maintenance Mode
APP_MAINTENANCE_DRIVER=file

# Logging
LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=warning

# Database
DB_CONNECTION=pgsql
DB_HOST=your-production-db-host
DB_PORT=5432
DB_DATABASE=deliverynexus_prod
DB_USERNAME=your-db-username
DB_PASSWORD=your-secure-db-password

# Session
SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=true
SESSION_PATH=/
SESSION_DOMAIN=.deliverynexus.com.ng
SESSION_SECURE_COOKIE=true
SESSION_HTTP_ONLY=true
SESSION_SAME_SITE=lax

# Cache
CACHE_STORE=redis
CACHE_PREFIX=deliverynexus_prod

# Queue
QUEUE_CONNECTION=redis

# Redis
REDIS_CLIENT=phpredis
REDIS_HOST=your-redis-host
REDIS_PASSWORD=your-redis-password
REDIS_PORT=6379
REDIS_DB=0

# Mail
MAIL_MAILER=smtp
MAIL_HOST=smtp.zoho.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-email-password
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=<EMAIL>
MAIL_FROM_NAME="DeliveryNexus"

# ZeptoMail Configuration
ZEPTO_MAIL_TOKEN=your-zepto-mail-token
ZEPTO_MAIL_DOMAIN=deliverynexus.com.ng

# Broadcasting
BROADCAST_CONNECTION=reverb

# Reverb
REVERB_APP_ID=your-reverb-app-id
REVERB_APP_KEY=your-reverb-app-key
REVERB_APP_SECRET=your-reverb-app-secret
REVERB_HOST=ws.deliverynexus.com.ng
REVERB_PORT=443
REVERB_SCHEME=https
REVERB_SERVER_HOST=0.0.0.0
REVERB_SERVER_PORT=8080

# Filesystem
FILESYSTEM_DISK=r2

# Cloudflare R2
CLOUDFLARE_R2_ACCESS_KEY_ID=your-r2-access-key
CLOUDFLARE_R2_SECRET_ACCESS_KEY=your-r2-secret-key
CLOUDFLARE_R2_BUCKET=deliverynexus-prod
CLOUDFLARE_R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
CLOUDFLARE_R2_URL=https://cdn.deliverynexus.com.ng

# Meilisearch
MEILISEARCH_HOST=https://search.deliverynexus.com.ng
MEILISEARCH_KEY=your-meilisearch-master-key

# Laravel Sanctum
SANCTUM_STATEFUL_DOMAINS=deliverynexus.com.ng,*.deliverynexus.com.ng,app.deliverynexus.com.ng

# Rate Limiting
API_RATE_LIMIT=120
API_RATE_LIMIT_WINDOW=1

# Security Headers
SECURE_HEADERS_ENABLED=true

# Monitoring & Debugging
TELESCOPE_ENABLED=false
PULSE_ENABLED=true
PULSE_DOMAIN=pulse.deliverynexus.com.ng

# External Services
PAYSTACK_PUBLIC_KEY=pk_live_your_paystack_public_key
PAYSTACK_SECRET_KEY=sk_live_your_paystack_secret_key
PAYSTACK_WEBHOOK_SECRET=your_paystack_webhook_secret

# Twilio
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_VERIFY_SERVICE_SID=your_twilio_verify_service_sid
TWILIO_FROM_NUMBER=+234XXXXXXXXXX

# Google Services
GOOGLE_MAPS_API_KEY=your_google_maps_api_key
GOOGLE_CLIENT_ID=your_google_oauth_client_id
GOOGLE_CLIENT_SECRET=your_google_oauth_client_secret

# Apple OAuth
APPLE_CLIENT_ID=your_apple_client_id
APPLE_CLIENT_SECRET=your_apple_client_secret
APPLE_REDIRECT_URI=https://api.deliverynexus.com.ng/api/v1/auth/apple/callback

# QoreID KYC
QOREID_SECRET_KEY=your_qoreid_secret_key
QOREID_PUBLIC_KEY=your_qoreid_public_key

# WhatsApp Business API
WHATSAPP_BUSINESS_ACCOUNT_ID=your_whatsapp_business_account_id
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token
WHATSAPP_WEBHOOK_VERIFY_TOKEN=your_whatsapp_webhook_verify_token

# Firebase Cloud Messaging
FCM_SERVER_KEY=your_fcm_server_key
FCM_SENDER_ID=your_fcm_sender_id

# Sentry (Error Tracking)
SENTRY_LARAVEL_DSN=https://<EMAIL>/project-id

# Performance Monitoring
NEW_RELIC_LICENSE_KEY=your_new_relic_license_key

# SSL/TLS
FORCE_HTTPS=true

# CORS
CORS_ALLOWED_ORIGINS=https://deliverynexus.com.ng,https://app.deliverynexus.com.ng,https://business.deliverynexus.com.ng,https://provider.deliverynexus.com.ng

# Tenant Configuration
CENTRAL_DOMAIN=deliverynexus.com.ng
TENANT_DOMAIN_SUFFIX=.deliverynexus.com.ng

# Backup Configuration
BACKUP_DISK=r2
BACKUP_NOTIFICATION_MAIL=<EMAIL>

# Health Check
HEALTH_CHECK_ENABLED=true
HEALTH_CHECK_SECRET=your_health_check_secret

# Feature Flags
FEATURE_AI_ENABLED=true
FEATURE_WHATSAPP_ENABLED=true
FEATURE_ADVANCED_ANALYTICS_ENABLED=true

# Nigerian Specific
NIGERIA_TIMEZONE=Africa/Lagos
NIGERIA_CURRENCY=NGN
NIGERIA_COUNTRY_CODE=NG

# Multi-tenancy
TENANCY_CACHE_STORE=redis
TENANCY_QUEUE_CONNECTION=redis

# Image Processing
IMAGE_OPTIMIZATION_ENABLED=true
IMAGE_WEBP_CONVERSION=true
IMAGE_MAX_SIZE=2048

# API Documentation
API_DOCS_ENABLED=false
API_DOCS_PASSWORD=your_api_docs_password

# Maintenance
MAINTENANCE_SECRET=your_maintenance_secret

# Analytics
ANALYTICS_ENABLED=true
ANALYTICS_PROVIDER=mixpanel
MIXPANEL_TOKEN=your_mixpanel_token

# Content Delivery Network
CDN_URL=https://cdn.deliverynexus.com.ng

# Database Optimization
DB_SLOW_QUERY_LOG=true
DB_SLOW_QUERY_TIME=2

# Queue Workers
QUEUE_WORKERS=4
QUEUE_MAX_JOBS=1000
QUEUE_MAX_TIME=3600

# Memory Limits
MEMORY_LIMIT=512M
MAX_EXECUTION_TIME=300

# File Upload Limits
MAX_FILE_SIZE=10240
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx

# Security
BCRYPT_ROUNDS=12
PASSWORD_TIMEOUT=10800

# Two-Factor Authentication
TWO_FACTOR_ENABLED=true
TWO_FACTOR_ISSUER="DeliveryNexus"

# Audit Logging
AUDIT_LOG_ENABLED=true
AUDIT_LOG_RETENTION_DAYS=365

# Fraud Detection
FRAUD_DETECTION_ENABLED=true
FRAUD_DETECTION_THRESHOLD=0.8

# Dynamic Pricing
DYNAMIC_PRICING_ENABLED=true
DYNAMIC_PRICING_MAX_INCREASE=0.5
DYNAMIC_PRICING_MAX_DECREASE=0.3

# AI Features
AI_PROVIDER_MATCHING_ENABLED=true
AI_DEMAND_FORECASTING_ENABLED=true
AI_ROUTE_OPTIMIZATION_ENABLED=true

# Compliance
GDPR_COMPLIANCE_ENABLED=true
NDPR_COMPLIANCE_ENABLED=true
DATA_RETENTION_DAYS=2555

# Localization
SUPPORTED_LOCALES=en,ha,yo,ig
DEFAULT_CURRENCY=NGN
SUPPORTED_CURRENCIES=NGN,USD

# Performance
OPCACHE_ENABLED=true
REDIS_CLUSTER_ENABLED=false

# Load Balancing
LOAD_BALANCER_ENABLED=true
STICKY_SESSIONS=true

# Auto Scaling
AUTO_SCALING_ENABLED=true
MIN_INSTANCES=2
MAX_INSTANCES=10

# Backup & Recovery
AUTOMATED_BACKUPS=true
BACKUP_FREQUENCY=daily
BACKUP_RETENTION_DAYS=30

# Disaster Recovery
DR_ENABLED=true
DR_REGION=eu-west-1

# Compliance Reporting
COMPLIANCE_REPORTING_ENABLED=true
COMPLIANCE_REPORT_FREQUENCY=monthly
