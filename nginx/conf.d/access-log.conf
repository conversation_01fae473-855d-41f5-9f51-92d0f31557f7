# If Fly-Client-Ip is empty, set $forwarded_header_value to
# value of X-Forwarded-For, else set
# $forwarded_header_value to value of Fly-Client-Ip
map $http_fly_client_ip $forwarded_header_value {
    default $http_fly_client_ip;
    "" $http_x_forwarded_for;
}

# Use variable $forwarded_header_value in place where we'd normally
# see $http_x_forwarded_For. We prefer the value of Fly-Client-Ip
# if it's available
log_format  fly  '$remote_addr - $remote_user [$time_local] "$request" '
                 '$status $body_bytes_sent "$http_referer" '
                 '"$http_user_agent" "$forwarded_header_value"';
