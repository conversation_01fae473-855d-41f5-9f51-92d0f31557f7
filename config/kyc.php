<?php

return [

    /*
    |--------------------------------------------------------------------------
    | KYC Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Know Your Customer (KYC) verification system.
    | Controls which verification method to use and related settings.
    |
    */

    /**
     * Enable unified KYC system using QoreID.
     * When true, uses QoreID for all verifications.
     * When false, falls back to legacy three-tier system.
     */
    'unified_kyc_enabled' => env('UNIFIED_KYC_ENABLED', true),

    /**
     * Default verification method.
     * Options: 'qoreid', 'legacy'
     */
    'default_verification_method' => env('KYC_DEFAULT_METHOD', 'qoreid'),

    /**
     * Verification score thresholds for KYC levels.
     */
    'score_thresholds' => [
        'basic' => 50,
        'intermediate' => 70,
        'advanced' => 90,
    ],

    /**
     * Risk level thresholds.
     */
    'risk_thresholds' => [
        'low' => 85,
        'medium' => 65,
        'high' => 0,
    ],

    /**
     * Cache settings for verification results.
     */
    'cache' => [
        'enabled' => env('KYC_CACHE_ENABLED', true),
        'ttl' => env('KYC_CACHE_TTL', 86400), // 24 hours
        'prefix' => 'kyc_verification:',
    ],

    /**
     * Rate limiting for verification requests.
     */
    'rate_limiting' => [
        'enabled' => env('KYC_RATE_LIMITING_ENABLED', true),
        'max_attempts' => env('KYC_MAX_ATTEMPTS', 10),
        'window_minutes' => env('KYC_RATE_WINDOW', 60),
    ],

    /**
     * Verification expiry settings.
     */
    'expiry' => [
        'bvn' => env('KYC_BVN_EXPIRY_DAYS', 365), // 1 year
        'nin' => env('KYC_NIN_EXPIRY_DAYS', 365), // 1 year
        'bank_account' => env('KYC_BANK_EXPIRY_DAYS', 90), // 3 months
    ],

    /**
     * Notification settings.
     */
    'notifications' => [
        'enabled' => env('KYC_NOTIFICATIONS_ENABLED', true),
        'channels' => ['mail', 'database', 'push'],
        'send_on_completion' => true,
        'send_on_failure' => true,
        'send_on_expiry' => true,
    ],

    /**
     * Audit and compliance settings.
     */
    'audit' => [
        'enabled' => env('KYC_AUDIT_ENABLED', true),
        'log_all_attempts' => true,
        'log_sensitive_data' => false,
        'retention_days' => env('KYC_AUDIT_RETENTION_DAYS', 2555), // 7 years
    ],

    /**
     * Legacy system settings (deprecated).
     */
    'legacy' => [
        'enabled' => env('LEGACY_KYC_ENABLED', false),
        'fallback_on_qoreid_failure' => env('KYC_FALLBACK_TO_LEGACY', false),
    ],

    /**
     * Development and testing settings.
     */
    'testing' => [
        'mock_responses' => env('KYC_MOCK_RESPONSES', false),
        'skip_verification' => env('KYC_SKIP_VERIFICATION', false),
        'auto_approve' => env('KYC_AUTO_APPROVE', false),
    ],

];
