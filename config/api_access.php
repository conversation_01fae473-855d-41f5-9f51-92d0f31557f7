<?php

declare(strict_types=1);

return [
    /*
    |--------------------------------------------------------------------------
    | API Access Tiers Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration defines the API access tiers and their limits
    | based on subscription plans. Each tier has specific request limits,
    | feature access, and endpoint permissions.
    |
    */

    'tiers' => [
        'free' => [
            'name' => 'Free Tier',
            'description' => 'Dashboard access only, no API access',
            'daily_requests' => 0,
            'monthly_requests' => 0,
            'features' => [
                'dashboard_access' => true,
                'api_access' => false,
                'bulk_operations' => false,
                'advanced_analytics' => false,
                'webhook_subscriptions' => false,
                'real_time_tracking' => false,
                'data_export' => false,
                'priority_support' => false,
                'custom_branding' => false,
            ],
            'allowed_endpoints' => [
                // Only dashboard-related endpoints
                'GET:/api/v1/user/profile',
                'PUT:/api/v1/user/profile',
                'GET:/api/v1/dashboard/stats',
            ],
        ],

        'starter_business' => [
            'name' => 'Business Starter',
            'description' => 'Basic API access with read-only operations',
            'daily_requests' => 1000,
            'monthly_requests' => 30000,
            'features' => [
                'dashboard_access' => true,
                'api_access' => true,
                'bulk_operations' => false,
                'advanced_analytics' => false,
                'webhook_subscriptions' => false,
                'real_time_tracking' => false,
                'data_export' => false,
                'priority_support' => false,
                'custom_branding' => false,
            ],
            'allowed_endpoints' => [
                'GET:/api/v1/orders',
                'GET:/api/v1/orders/*',
                'GET:/api/v1/products',
                'GET:/api/v1/products/*',
                'GET:/api/v1/analytics/basic',
                'GET:/api/v1/delivery/tracking/*',
            ],
        ],

        'starter_provider' => [
            'name' => 'Provider Starter',
            'description' => 'Basic API access for delivery providers',
            'daily_requests' => 2000,
            'monthly_requests' => 60000,
            'features' => [
                'dashboard_access' => true,
                'api_access' => true,
                'bulk_operations' => false,
                'advanced_analytics' => false,
                'webhook_subscriptions' => false,
                'real_time_tracking' => true,
                'data_export' => false,
                'priority_support' => false,
                'custom_branding' => false,
            ],
            'allowed_endpoints' => [
                'GET:/api/v1/deliveries',
                'GET:/api/v1/deliveries/*',
                'PUT:/api/v1/deliveries/*/status',
                'POST:/api/v1/location/update',
                'GET:/api/v1/analytics/basic',
            ],
        ],

        'business_business' => [
            'name' => 'Business Pro',
            'description' => 'Full CRUD operations with enhanced features',
            'daily_requests' => 10000,
            'monthly_requests' => 300000,
            'features' => [
                'dashboard_access' => true,
                'api_access' => true,
                'bulk_operations' => false,
                'advanced_analytics' => true,
                'webhook_subscriptions' => true,
                'real_time_tracking' => true,
                'data_export' => true,
                'priority_support' => false,
                'custom_branding' => false,
            ],
            'allowed_endpoints' => [
                'GET:/api/v1/orders',
                'POST:/api/v1/orders',
                'PUT:/api/v1/orders/*',
                'DELETE:/api/v1/orders/*',
                'GET:/api/v1/products',
                'POST:/api/v1/products',
                'PUT:/api/v1/products/*',
                'DELETE:/api/v1/products/*',
                'GET:/api/v1/analytics/advanced',
                'GET:/api/v1/delivery/tracking/*',
                'POST:/api/v1/webhooks/subscribe',
                'GET:/api/v1/export/orders',
            ],
        ],

        'business_provider' => [
            'name' => 'Provider Pro',
            'description' => 'Full delivery provider API access',
            'daily_requests' => 10000,
            'monthly_requests' => 300000,
            'features' => [
                'dashboard_access' => true,
                'api_access' => true,
                'bulk_operations' => false,
                'advanced_analytics' => true,
                'webhook_subscriptions' => true,
                'real_time_tracking' => true,
                'data_export' => true,
                'priority_support' => false,
                'custom_branding' => false,
            ],
            'allowed_endpoints' => [
                'GET:/api/v1/deliveries',
                'POST:/api/v1/deliveries',
                'PUT:/api/v1/deliveries/*',
                'DELETE:/api/v1/deliveries/*',
                'POST:/api/v1/location/update',
                'GET:/api/v1/analytics/advanced',
                'POST:/api/v1/webhooks/subscribe',
                'GET:/api/v1/export/deliveries',
            ],
        ],

        'enterprise_business' => [
            'name' => 'Business Enterprise',
            'description' => 'Extended access with bulk operations and premium features',
            'daily_requests' => 50000,
            'monthly_requests' => 1500000,
            'features' => [
                'dashboard_access' => true,
                'api_access' => true,
                'bulk_operations' => true,
                'advanced_analytics' => true,
                'webhook_subscriptions' => true,
                'real_time_tracking' => true,
                'data_export' => true,
                'priority_support' => true,
                'custom_branding' => true,
            ],
            'allowed_endpoints' => [
                '*', // All endpoints allowed
            ],
        ],

        'enterprise_provider' => [
            'name' => 'Provider Enterprise',
            'description' => 'Full enterprise provider access with all features',
            'daily_requests' => 50000,
            'monthly_requests' => 1500000,
            'features' => [
                'dashboard_access' => true,
                'api_access' => true,
                'bulk_operations' => true,
                'advanced_analytics' => true,
                'webhook_subscriptions' => true,
                'real_time_tracking' => true,
                'data_export' => true,
                'priority_support' => true,
                'custom_branding' => true,
            ],
            'allowed_endpoints' => [
                '*', // All endpoints allowed
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Feature Access Mapping
    |--------------------------------------------------------------------------
    |
    | Maps specific features to their required subscription tiers.
    |
    */

    'feature_requirements' => [
        'bulk_create_orders' => ['enterprise_business', 'enterprise_provider'],
        'bulk_update_orders' => ['enterprise_business', 'enterprise_provider'],
        'advanced_analytics' => ['business_business', 'business_provider', 'enterprise_business', 'enterprise_provider'],
        'real_time_tracking' => ['starter_provider', 'business_business', 'business_provider', 'enterprise_business', 'enterprise_provider'],
        'webhook_subscriptions' => ['business_business', 'business_provider', 'enterprise_business', 'enterprise_provider'],
        'data_export' => ['business_business', 'business_provider', 'enterprise_business', 'enterprise_provider'],
        'priority_support' => ['enterprise_business', 'enterprise_provider'],
        'custom_branding' => ['enterprise_business', 'enterprise_provider'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Endpoint Access Mapping
    |--------------------------------------------------------------------------
    |
    | Maps API endpoints to their minimum required subscription tiers.
    |
    */

    'endpoint_requirements' => [
        'POST:/api/v1/orders/bulk' => 'enterprise_business',
        'PUT:/api/v1/orders/bulk' => 'enterprise_business',
        'GET:/api/v1/analytics/advanced' => 'business_business',
        'GET:/api/v1/analytics/custom' => 'enterprise_business',
        'POST:/api/v1/webhooks/subscribe' => 'business_business',
        'GET:/api/v1/export/*' => 'business_business',
        'GET:/api/v1/delivery/real-time/*' => 'starter_provider',
        'POST:/api/v1/deliveries/bulk' => 'enterprise_provider',
    ],

    /*
    |--------------------------------------------------------------------------
    | Rate Limiting Configuration
    |--------------------------------------------------------------------------
    |
    | Rate limiting settings per tier.
    |
    */

    'rate_limits' => [
        'free' => [
            'requests_per_minute' => 0,
            'burst_allowance' => 0,
        ],
        'starter_business' => [
            'requests_per_minute' => 50,
            'burst_allowance' => 10,
        ],
        'starter_provider' => [
            'requests_per_minute' => 100,
            'burst_allowance' => 20,
        ],
        'business_business' => [
            'requests_per_minute' => 200,
            'burst_allowance' => 50,
        ],
        'business_provider' => [
            'requests_per_minute' => 200,
            'burst_allowance' => 50,
        ],
        'enterprise_business' => [
            'requests_per_minute' => 1000,
            'burst_allowance' => 200,
        ],
        'enterprise_provider' => [
            'requests_per_minute' => 1000,
            'burst_allowance' => 200,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Default Settings
    |--------------------------------------------------------------------------
    |
    | Default settings for API access.
    |
    */

    'defaults' => [
        'tier' => 'free',
        'daily_requests' => 0,
        'rate_limit_per_minute' => 0,
        'features' => [],
    ],
];
