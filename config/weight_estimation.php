<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Package Weight Estimation Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration file defines default weights, limits, and validation
    | rules for package weight estimation across different product categories.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Category Default Weights (in kg)
    |--------------------------------------------------------------------------
    |
    | Default weight ranges for different product categories. These are used
    | when specific product weights are not available.
    |
    */
    'category_weights' => [
        'electronics' => [
            'min' => 0.1,
            'max' => 10.0,
            'average' => 2.0,
            'description' => 'Phones, laptops, gadgets, and electronic devices',
        ],
        'clothing' => [
            'min' => 0.1,
            'max' => 3.0,
            'average' => 0.8,
            'description' => 'Apparel, shoes, accessories, and textiles',
        ],
        'food' => [
            'min' => 0.1,
            'max' => 5.0,
            'average' => 1.5,
            'description' => 'Packaged food items, beverages, and perishables',
        ],
        'documents' => [
            'min' => 0.01,
            'max' => 2.0,
            'average' => 0.3,
            'description' => 'Papers, books, files, and printed materials',
        ],
        'furniture' => [
            'min' => 5.0,
            'max' => 100.0,
            'average' => 25.0,
            'description' => 'Home and office furniture, large items',
        ],
        'cosmetics' => [
            'min' => 0.05,
            'max' => 2.0,
            'average' => 0.4,
            'description' => 'Beauty products, skincare, and personal care items',
        ],
        'books' => [
            'min' => 0.1,
            'max' => 5.0,
            'average' => 1.0,
            'description' => 'Books, magazines, and printed publications',
        ],
        'toys' => [
            'min' => 0.1,
            'max' => 10.0,
            'average' => 2.5,
            'description' => 'Children\'s toys, games, and recreational items',
        ],
        'automotive' => [
            'min' => 0.5,
            'max' => 50.0,
            'average' => 8.0,
            'description' => 'Car parts, accessories, and automotive supplies',
        ],
        'sports' => [
            'min' => 0.2,
            'max' => 20.0,
            'average' => 5.0,
            'description' => 'Sports equipment, fitness gear, and outdoor items',
        ],
        'jewelry' => [
            'min' => 0.01,
            'max' => 1.0,
            'average' => 0.1,
            'description' => 'Precious items, watches, and valuable accessories',
        ],
        'medical' => [
            'min' => 0.05,
            'max' => 5.0,
            'average' => 1.2,
            'description' => 'Medical supplies, pharmaceuticals, and health products',
        ],
        'general' => [
            'min' => 0.1,
            'max' => 10.0,
            'average' => 2.0,
            'description' => 'Miscellaneous items and general packages',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Weight Validation Limits (in kg)
    |--------------------------------------------------------------------------
    |
    | Validation limits for customer-provided weights. These are more lenient
    | than category defaults to allow for variations in actual products.
    |
    */
    'weight_limits' => [
        'electronics' => ['min' => 0.01, 'max' => 25.0],
        'clothing' => ['min' => 0.01, 'max' => 10.0],
        'food' => ['min' => 0.01, 'max' => 20.0],
        'documents' => ['min' => 0.001, 'max' => 5.0],
        'furniture' => ['min' => 1.0, 'max' => 200.0],
        'cosmetics' => ['min' => 0.01, 'max' => 5.0],
        'books' => ['min' => 0.01, 'max' => 15.0],
        'toys' => ['min' => 0.01, 'max' => 25.0],
        'automotive' => ['min' => 0.1, 'max' => 100.0],
        'sports' => ['min' => 0.05, 'max' => 50.0],
        'jewelry' => ['min' => 0.001, 'max' => 2.0],
        'medical' => ['min' => 0.01, 'max' => 15.0],
        'general' => ['min' => 0.01, 'max' => 50.0],
    ],

    /*
    |--------------------------------------------------------------------------
    | Vehicle Weight Capacities (in kg)
    |--------------------------------------------------------------------------
    |
    | Maximum weight and dimension limits for different vehicle types.
    | Used for vehicle recommendation based on package weight and size.
    |
    */
    'vehicle_capacities' => [
        'bicycle' => [
            'max_weight' => 15.0,
            'max_dimension' => 100, // cm
            'description' => 'Bicycle delivery for small, light packages',
            'suitable_for' => ['documents', 'jewelry', 'small electronics'],
        ],
        'motorcycle' => [
            'max_weight' => 50.0,
            'max_dimension' => 150, // cm
            'description' => 'Motorcycle delivery for medium packages',
            'suitable_for' => ['electronics', 'clothing', 'food', 'books'],
        ],
        'car' => [
            'max_weight' => 200.0,
            'max_dimension' => 200, // cm
            'description' => 'Car delivery for larger packages',
            'suitable_for' => ['electronics', 'clothing', 'toys', 'sports equipment'],
        ],
        'van' => [
            'max_weight' => 1000.0,
            'max_dimension' => 300, // cm
            'description' => 'Van delivery for bulk items',
            'suitable_for' => ['furniture', 'automotive', 'bulk orders'],
        ],
        'truck' => [
            'max_weight' => 5000.0,
            'max_dimension' => 500, // cm
            'description' => 'Truck delivery for heavy freight',
            'suitable_for' => ['furniture', 'automotive', 'industrial equipment'],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Dimensional Weight Calculation
    |--------------------------------------------------------------------------
    |
    | Factors used for calculating dimensional weight when package dimensions
    | are provided. Dimensional weight = (L × W × H) × density_factor
    |
    */
    'dimensional_factors' => [
        'density_factor' => 200.0, // kg per cubic meter
        'minimum_weight' => 0.1, // minimum dimensional weight in kg
        'unit_conversion' => [
            'cm_to_m' => 0.01,
            'inches_to_m' => 0.0254,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Weight Categories for Pricing
    |--------------------------------------------------------------------------
    |
    | Weight categories used for pricing calculations and delivery planning.
    |
    */
    'weight_categories' => [
        'light' => [
            'max_weight' => 1.0,
            'description' => 'Light packages (up to 1kg)',
            'pricing_multiplier' => 1.0,
        ],
        'medium' => [
            'max_weight' => 5.0,
            'description' => 'Medium packages (1-5kg)',
            'pricing_multiplier' => 1.2,
        ],
        'heavy' => [
            'max_weight' => 20.0,
            'description' => 'Heavy packages (5-20kg)',
            'pricing_multiplier' => 1.5,
        ],
        'bulk' => [
            'max_weight' => 50.0,
            'description' => 'Bulk packages (20-50kg)',
            'pricing_multiplier' => 2.0,
        ],
        'freight' => [
            'max_weight' => PHP_FLOAT_MAX,
            'description' => 'Freight packages (50kg+)',
            'pricing_multiplier' => 3.0,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Validation Rules
    |--------------------------------------------------------------------------
    |
    | Validation rules for weight and dimension inputs.
    |
    */
    'validation_rules' => [
        'weight' => [
            'min' => 0.001, // 1 gram minimum
            'max' => 5000.0, // 5 tons maximum
            'precision' => 3, // decimal places
        ],
        'dimensions' => [
            'min' => 0.1, // 1mm minimum
            'max' => 500.0, // 5 meters maximum (in cm)
            'precision' => 1, // decimal places
        ],
        'quantity' => [
            'min' => 1,
            'max' => 1000,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Special Handling Categories
    |--------------------------------------------------------------------------
    |
    | Categories that require special handling or have specific restrictions.
    |
    */
    'special_handling' => [
        'fragile' => [
            'categories' => ['electronics', 'jewelry', 'medical'],
            'weight_multiplier' => 1.1,
            'packaging_requirements' => 'Extra padding and careful handling required',
        ],
        'perishable' => [
            'categories' => ['food'],
            'weight_multiplier' => 1.0,
            'packaging_requirements' => 'Temperature-controlled delivery may be required',
        ],
        'hazardous' => [
            'categories' => ['automotive', 'medical'],
            'weight_multiplier' => 1.3,
            'packaging_requirements' => 'Special handling and documentation required',
        ],
        'valuable' => [
            'categories' => ['jewelry', 'electronics'],
            'weight_multiplier' => 1.2,
            'packaging_requirements' => 'Insurance and signature confirmation required',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Regional Adjustments
    |--------------------------------------------------------------------------
    |
    | Regional adjustments for weight estimation based on local preferences
    | and market conditions.
    |
    */
    'regional_adjustments' => [
        'lagos' => [
            'weight_multiplier' => 1.0,
            'popular_categories' => ['electronics', 'clothing', 'food'],
        ],
        'abuja' => [
            'weight_multiplier' => 1.0,
            'popular_categories' => ['electronics', 'documents', 'clothing'],
        ],
        'kano' => [
            'weight_multiplier' => 0.95,
            'popular_categories' => ['clothing', 'food', 'general'],
        ],
        'port_harcourt' => [
            'weight_multiplier' => 1.05,
            'popular_categories' => ['automotive', 'electronics', 'general'],
        ],
        'default' => [
            'weight_multiplier' => 1.0,
            'popular_categories' => ['general', 'electronics', 'clothing'],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Configuration
    |--------------------------------------------------------------------------
    |
    | Caching settings for weight estimation calculations.
    |
    */
    'cache' => [
        'enabled' => true,
        'ttl' => 3600, // 1 hour
        'prefix' => 'weight_estimation',
        'tags' => ['weight', 'estimation', 'pricing'],
    ],
];
