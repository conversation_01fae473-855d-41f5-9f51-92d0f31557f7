<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Third Party Services
    |--------------------------------------------------------------------------
    |
    | This file is for storing the credentials for third party services such
    | as Mailgun, Postmark, AWS and more. This file provides the de facto
    | location for this type of information, allowing packages to have
    | a conventional file to locate the various service credentials.
    |
    */

    'postmark' => [
        'token' => env('POSTMARK_TOKEN'),
    ],

    'ses' => [
        'key' => env('AWS_ACCESS_KEY_ID'),
        'secret' => env('AWS_SECRET_ACCESS_KEY'),
        'region' => env('AWS_DEFAULT_REGION', 'us-east-1'),
    ],

    'resend' => [
        'key' => env('RESEND_KEY'),
    ],

    'slack' => [
        'notifications' => [
            'bot_user_oauth_token' => env('SLACK_BOT_USER_OAUTH_TOKEN'),
            'channel' => env('SLACK_BOT_USER_DEFAULT_CHANNEL'),
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | DeliveryNexus Services
    |--------------------------------------------------------------------------
    */

    // Twilio SMS is now handled by laravel-notification-channels/twilio
    // Configuration is in config/twilio-notification-channel.php

    'zeptomail' => [
        'enabled' => env('ZEPTOMAIL_ENABLED', false),
        'api_key' => env('ZEPTOMAIL_API_KEY'),
        'from_email' => env('ZEPTOMAIL_FROM_EMAIL', 'noreply@' . env('EMAIL_DOMAIN', 'deliverynexus.test')),
        'from_name' => env('ZEPTOMAIL_FROM_NAME', 'DeliveryNexus'),
        'base_url' => env('ZEPTOMAIL_BASE_URL', 'https://api.zeptomail.com/v1.1'),
    ],

    // Email delivery options (choose one)
    'email_provider' => env('EMAIL_PROVIDER', 'smtp'), // smtp, zeptomail, sendgrid, mailgun

    // Firebase Configuration (Modern Setup with kreait/laravel-firebase)
    'firebase' => [
        'credentials' => env('FIREBASE_CREDENTIALS'),
        'database_url' => env('FIREBASE_DATABASE_URL'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Google Services Configuration
    |--------------------------------------------------------------------------
    */

    'google' => [
        // OAuth Configuration
        'client_id' => env('GOOGLE_CLIENT_ID'),
        'client_secret' => env('GOOGLE_CLIENT_SECRET'),
        'redirect' => env('GOOGLE_REDIRECT_URI', env('APP_URL').'/auth/google/callback'),

        // Google Maps Platform Configuration
        'maps_api_key' => env('GOOGLE_MAPS_API_KEY'),
        'maps_base_url' => 'https://maps.googleapis.com/maps/api',

        // API Configuration
        'geocoding_enabled' => env('GOOGLE_GEOCODING_ENABLED', true),
        'directions_enabled' => env('GOOGLE_DIRECTIONS_ENABLED', true),
        'distance_matrix_enabled' => env('GOOGLE_DISTANCE_MATRIX_ENABLED', true),
        'places_enabled' => env('GOOGLE_PLACES_ENABLED', false),

        // Rate Limiting & Optimization
        'cache_geocoding_results' => env('GOOGLE_CACHE_GEOCODING', true),
        'cache_ttl_hours' => env('GOOGLE_CACHE_TTL_HOURS', 24),
        'request_timeout' => env('GOOGLE_REQUEST_TIMEOUT', 10),

        // Nigerian-specific defaults
        'default_country' => 'NG',
        'default_region' => 'ng',
        'default_language' => 'en',
    ],

    'apple' => [
        'client_id' => env('APPLE_CLIENT_ID'),
        'client_secret' => env('APPLE_CLIENT_SECRET'),
        'redirect' => env('APPLE_REDIRECT_URI', env('APP_URL').'/auth/apple/callback'),
        'key_id' => env('APPLE_KEY_ID'),
        'team_id' => env('APPLE_TEAM_ID'),
        'private_key' => env('APPLE_PRIVATE_KEY'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Frontend Application Configuration
    |--------------------------------------------------------------------------
    */

    'frontend' => [
        'web_url' => env('FRONTEND_WEB_URL', 'http://localhost:3000'),
        'mobile_deep_link' => env('FRONTEND_MOBILE_DEEP_LINK', 'deliverynexus://'),
        'oauth_success_redirect' => env('OAUTH_SUCCESS_REDIRECT', '/auth/success'),
        'oauth_error_redirect' => env('OAUTH_ERROR_REDIRECT', '/auth/error'),
    ],

    'paystack' => [
        'enabled' => env('PAYSTACK_ENABLED', false),
        'public_key' => env('PAYSTACK_PUBLIC_KEY'),
        'secret_key' => env('PAYSTACK_SECRET_KEY'),
        'base_url' => env('PAYSTACK_BASE_URL', 'https://api.paystack.co'),
        'webhook_secret' => env('PAYSTACK_WEBHOOK_SECRET'),
    ],

    /*
    |--------------------------------------------------------------------------
    | KYC Verification Services Configuration
    |--------------------------------------------------------------------------
    */

    'qoreid' => [
        'enabled' => env('QOREID_ENABLED', false),
        'api_key' => env('QOREID_API_KEY'),
        'secret_key' => env('QOREID_SECRET_KEY'),
        'base_url' => env('QOREID_BASE_URL', 'https://api.qoreid.com/v1'),
        'workflow_id' => env('QOREID_WORKFLOW_ID'),
        'webhook_secret' => env('QOREID_WEBHOOK_SECRET'),
    ],

    // Legacy KYC services (deprecated - use QoreID instead)
    'nibss' => [
        'enabled' => env('NIBSS_ENABLED', false),
        'api_key' => env('NIBSS_API_KEY'),
        'secret_key' => env('NIBSS_SECRET_KEY'),
        'base_url' => env('NIBSS_BASE_URL', 'https://api.nibss-plc.com.ng'),
    ],

    'nimc' => [
        'enabled' => env('NIMC_ENABLED', false),
        'api_key' => env('NIMC_API_KEY'),
        'secret_key' => env('NIMC_SECRET_KEY'),
        'base_url' => env('NIMC_BASE_URL', 'https://api.nimc.gov.ng'),
    ],

    /*
    |--------------------------------------------------------------------------
    | IP Geolocation Services Configuration
    |--------------------------------------------------------------------------
    */

    'ipinfo' => [
        'token' => env('IPINFO_TOKEN'), // Optional - for higher rate limits
        'base_url' => 'https://ipinfo.io',
        'timeout' => env('IPINFO_TIMEOUT', 5),
    ],

];
