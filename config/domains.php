<?php

return [

    /*
    |--------------------------------------------------------------------------
    | Domain Configuration
    |--------------------------------------------------------------------------
    |
    | This configuration manages all domain-related settings for the
    | DeliveryNexus platform, ensuring consistent domain usage across
    | different environments without hardcoding.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Base Domain
    |--------------------------------------------------------------------------
    |
    | The base domain for the platform. This changes based on environment:
    | - Local: deliverynexus.test
    | - Production: deliverynexus.com.ng
    |
    */

    'base_domain' => env('BASE_DOMAIN', 'deliverynexus.test'),

    /*
    |--------------------------------------------------------------------------
    | Central Domain
    |--------------------------------------------------------------------------
    |
    | The main domain where customers and platform admins access the system.
    | This is extracted from APP_URL to ensure consistency.
    |
    */

    'central_domain' => str(env('APP_URL', 'http://deliverynexus.test'))
        ->after('://')
        ->before('/')
        ->toString(),

    /*
    |--------------------------------------------------------------------------
    | Frontend Domains
    |--------------------------------------------------------------------------
    |
    | Frontend application domains for different user types.
    | These are dynamically generated based on the base domain.
    |
    */

    'frontend' => [
        'customer_app' => env('FRONTEND_CUSTOMER_URL', 'https://app.' . env('BASE_DOMAIN', 'deliverynexus.test')),
        'business_portal' => env('FRONTEND_BUSINESS_URL', 'https://business.' . env('BASE_DOMAIN', 'deliverynexus.test')),
        'provider_portal' => env('FRONTEND_PROVIDER_URL', 'https://provider.' . env('BASE_DOMAIN', 'deliverynexus.test')),
        'admin_portal' => env('FRONTEND_ADMIN_URL', 'https://admin.' . env('BASE_DOMAIN', 'deliverynexus.test')),
        'marketing_site' => env('FRONTEND_MARKETING_URL', 'https://' . env('BASE_DOMAIN', 'deliverynexus.test')),
    ],

    /*
    |--------------------------------------------------------------------------
    | Tenant Domain Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for tenant subdomains.
    |
    */

    'tenant' => [
        'subdomain_suffix' => '.' . env('BASE_DOMAIN', 'deliverynexus.test'),
        'protocol' => env('TENANT_PROTOCOL', 'https'),
    ],

    /*
    |--------------------------------------------------------------------------
    | API Domain Configuration
    |--------------------------------------------------------------------------
    |
    | API endpoint configurations.
    |
    */

    'api' => [
        'base_url' => env('APP_URL', 'http://deliverynexus.test'),
        'version' => env('API_VERSION', 'v1'),
    ],

    /*
    |--------------------------------------------------------------------------
    | Email Domain Configuration
    |--------------------------------------------------------------------------
    |
    | Domain used for email addresses and support contacts.
    |
    */

    'email' => [
        'domain' => env('EMAIL_DOMAIN', env('BASE_DOMAIN', 'deliverynexus.test')),
        'support' => env('SUPPORT_EMAIL', 'support@' . env('BASE_DOMAIN', 'deliverynexus.test')),
        'noreply' => env('NOREPLY_EMAIL', 'noreply@' . env('BASE_DOMAIN', 'deliverynexus.test')),
        'admin' => env('ADMIN_EMAIL', 'admin@' . env('BASE_DOMAIN', 'deliverynexus.test')),
    ],

    /*
    |--------------------------------------------------------------------------
    | OAuth Redirect URLs
    |--------------------------------------------------------------------------
    |
    | OAuth callback URLs for social authentication.
    |
    */

    'oauth' => [
        'google_redirect' => env('GOOGLE_REDIRECT_URI', env('APP_URL', 'http://deliverynexus.test') . '/api/v1/auth/oauth/callback/google'),
        'apple_redirect' => env('APPLE_REDIRECT_URI', env('APP_URL', 'http://deliverynexus.test') . '/api/v1/auth/oauth/callback/apple'),
    ],

    /*
    |--------------------------------------------------------------------------
    | WebSocket Configuration
    |--------------------------------------------------------------------------
    |
    | Real-time communication domain configuration.
    |
    */

    'websocket' => [
        'host' => env('REVERB_HOST', 'localhost'),
        'port' => env('REVERB_PORT', 8080),
        'scheme' => env('REVERB_SCHEME', 'http'),
    ],

    /*
    |--------------------------------------------------------------------------
    | CDN Configuration
    |--------------------------------------------------------------------------
    |
    | Content delivery network domain configuration.
    |
    */

    'cdn' => [
        'url' => env('CDN_URL', 'https://cdn.' . env('BASE_DOMAIN', 'deliverynexus.test')),
        'assets_url' => env('ASSETS_URL', env('APP_URL', 'http://deliverynexus.test')),
    ],

    /*
    |--------------------------------------------------------------------------
    | Environment-Specific Configurations
    |--------------------------------------------------------------------------
    |
    | Different configurations based on environment.
    |
    */

    'environments' => [
        'local' => [
            'base_domain' => 'deliverynexus.test',
            'protocol' => 'http',
            'port' => ':8000',
        ],
        'staging' => [
            'base_domain' => 'staging.deliverynexus.com.ng',
            'protocol' => 'https',
            'port' => '',
        ],
        'production' => [
            'base_domain' => 'deliverynexus.com.ng',
            'protocol' => 'https',
            'port' => '',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Helper Methods
    |--------------------------------------------------------------------------
    |
    | Helper configurations for common domain operations.
    |
    */

    'helpers' => [
        'generate_tenant_url' => function (string $subdomain): string {
            $protocol = config('domains.tenant.protocol');
            $suffix = config('domains.tenant.subdomain_suffix');
            return "{$protocol}://{$subdomain}{$suffix}";
        },
        
        'generate_menu_url' => function (string $subdomain, array $params = []): string {
            $baseUrl = config('domains.helpers.generate_tenant_url')($subdomain);
            $url = "{$baseUrl}/menu";
            return empty($params) ? $url : $url . '?' . http_build_query($params);
        },
    ],

];
