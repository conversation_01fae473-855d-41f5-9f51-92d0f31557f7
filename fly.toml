app = 'deliverynexus'
primary_region = 'ams'

[build]
[build.args]
NODE_VERSION = '18'
PHP_VERSION = '8.3'

[env]
APP_ENV = 'production'
LOG_CHANNEL = 'stderr'
LOG_LEVEL = 'info'
LOG_STDERR_FORMATTER = 'Monolog\Formatter\JsonFormatter'
SESSION_DRIVER = 'cookie'
SESSION_SECURE_COOKIE = 'true'
DB_CONNECTION = "pgsql"

[http_service]
internal_port = 8080
force_https = true
auto_stop_machines = "suspend"
auto_start_machines = true
min_machines_running = 1
processes = ['app']

[[vm]]
memory = '512mb'
cpu_kind = 'shared'
cpus = 2
