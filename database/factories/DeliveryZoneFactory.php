<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Enums\Delivery\ZoneType;
use App\Models\Core\State;
use App\Models\Delivery\DeliveryZone;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Delivery\DeliveryZone>
 */
class DeliveryZoneFactory extends Factory
{
    protected $model = DeliveryZone::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->city().' Zone',
            'zone_type' => $this->faker->randomElement(ZoneType::cases()),
            'states' => null,
            'cities' => null,
            'polygon_coordinates' => null,
            'base_multiplier' => $this->faker->randomFloat(2, 1.0, 2.0),
            'is_active' => true,
            'description' => $this->faker->optional()->sentence(),
            'metadata' => null,
        ];
    }

    /**
     * Create a city zone.
     */
    public function cityZone(): static
    {
        return $this->state(fn () => [
            'zone_type' => ZoneType::CITY,
            'cities' => $this->faker->randomElements([
                'Lagos Island',
                'Victoria Island',
                'Ikoyi',
                'Lekki',
                'Ikeja',
                'Surulere',
                'Yaba',
                'Gbagada',
            ], $this->faker->numberBetween(1, 4)),
        ]);
    }

    /**
     * Create a state zone.
     */
    public function stateZone(): static
    {
        return $this->state(fn () => [
            'zone_type' => ZoneType::STATE,
            'states' => [State::factory()->create()->id],
        ]);
    }

    /**
     * Create an interstate zone.
     */
    public function interstateZone(): static
    {
        return $this->state(fn () => [
            'zone_type' => ZoneType::INTERSTATE,
            'states' => [
                State::factory()->create()->id,
                State::factory()->create()->id,
            ],
        ]);
    }

    /**
     * Create a polygon zone.
     */
    public function polygonZone(): static
    {
        return $this->state(fn () => [
            'zone_type' => ZoneType::POLYGON,
            'polygon_coordinates' => [
                [6.4281, 3.4219], // Lagos coordinates
                [6.4381, 3.4319],
                [6.4181, 3.4319],
                [6.4181, 3.4119],
                [6.4281, 3.4219], // Close the polygon
            ],
        ]);
    }

    /**
     * Create an active zone.
     */
    public function active(): static
    {
        return $this->state(fn () => [
            'is_active' => true,
        ]);
    }

    /**
     * Create an inactive zone.
     */
    public function inactive(): static
    {
        return $this->state(fn () => [
            'is_active' => false,
        ]);
    }

    /**
     * Create a zone with high pricing multiplier.
     */
    public function premium(): static
    {
        return $this->state(fn () => [
            'base_multiplier' => $this->faker->randomFloat(2, 1.5, 2.5),
            'name' => 'Premium '.$this->faker->city().' Zone',
        ]);
    }

    /**
     * Create a zone with low pricing multiplier.
     */
    public function economy(): static
    {
        return $this->state(fn () => [
            'base_multiplier' => $this->faker->randomFloat(2, 0.8, 1.2),
            'name' => 'Economy '.$this->faker->city().' Zone',
        ]);
    }

    /**
     * Create a zone for Lagos.
     */
    public function lagos(): static
    {
        return $this->state(fn () => [
            'name' => 'Lagos '.$this->faker->randomElement(['Island', 'Mainland', 'Central']).' Zone',
            'zone_type' => ZoneType::CITY,
            'cities' => $this->faker->randomElements([
                'Lagos Island',
                'Victoria Island',
                'Ikoyi',
                'Lekki',
                'Ikeja',
                'Surulere',
                'Yaba',
                'Gbagada',
                'Apapa',
                'Mushin',
            ], $this->faker->numberBetween(2, 5)),
        ]);
    }

    /**
     * Create a zone for Abuja.
     */
    public function abuja(): static
    {
        return $this->state(fn () => [
            'name' => 'Abuja '.$this->faker->randomElement(['Central', 'Municipal', 'Metro']).' Zone',
            'zone_type' => ZoneType::CITY,
            'cities' => $this->faker->randomElements([
                'Wuse',
                'Garki',
                'Asokoro',
                'Maitama',
                'Gwarinpa',
                'Kubwa',
                'Nyanya',
                'Karu',
            ], $this->faker->numberBetween(2, 4)),
        ]);
    }
}
