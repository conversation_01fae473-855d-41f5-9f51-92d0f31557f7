<?php

declare(strict_types=1);

namespace Database\Factories;

use App\Enums\Delivery\VehicleType;
use App\Models\Core\State;
use App\Models\Delivery\InterstatePricing;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Delivery\InterstatePricing>
 */
class InterstatePricingFactory extends Factory
{
    protected $model = InterstatePricing::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $basePrice = $this->faker->numberBetween(5000, 50000);
        $pricePerKm = $this->faker->numberBetween(20, 100);
        $estimatedDistance = $this->faker->numberBetween(50, 800);

        return [
            'from_state_id' => State::factory(),
            'to_state_id' => State::factory(),
            'vehicle_type' => $this->faker->randomElement(VehicleType::cases()),
            'base_price' => $basePrice,
            'price_per_km' => $pricePerKm,
            'minimum_price' => $basePrice * 0.8, // 20% below base price
            'maximum_price' => $basePrice * 3, // 3x base price
            'estimated_distance_km' => $estimatedDistance,
            'estimated_duration_minutes' => $this->calculateEstimatedDuration($estimatedDistance),
            'delivery_count' => $this->faker->numberBetween(0, 500),
            'average_actual_distance' => $estimatedDistance + $this->faker->numberBetween(-20, 50),
            'average_actual_duration' => null,
            'is_active' => true,
        ];
    }

    /**
     * Calculate estimated duration based on distance.
     */
    private function calculateEstimatedDuration(int $distance): int
    {
        // Assume average speed of 60 km/h for interstate travel
        $hours = $distance / 60;

        return (int) ($hours * 60); // Convert to minutes
    }

    /**
     * Create pricing for motorcycle.
     */
    public function motorcycle(): static
    {
        return $this->state(fn () => [
            'vehicle_type' => VehicleType::MOTORCYCLE,
            'base_price' => $this->faker->numberBetween(3000, 15000),
            'price_per_km' => $this->faker->numberBetween(20, 50),
        ]);
    }

    /**
     * Create pricing for van.
     */
    public function van(): static
    {
        return $this->state(fn () => [
            'vehicle_type' => VehicleType::VAN,
            'base_price' => $this->faker->numberBetween(8000, 25000),
            'price_per_km' => $this->faker->numberBetween(40, 80),
        ]);
    }

    /**
     * Create pricing for truck.
     */
    public function truck(): static
    {
        return $this->state(fn () => [
            'vehicle_type' => VehicleType::TRUCK,
            'base_price' => $this->faker->numberBetween(15000, 50000),
            'price_per_km' => $this->faker->numberBetween(60, 120),
        ]);
    }

    /**
     * Create active pricing.
     */
    public function active(): static
    {
        return $this->state(fn () => [
            'is_active' => true,
        ]);
    }

    /**
     * Create inactive pricing.
     */
    public function inactive(): static
    {
        return $this->state(fn () => [
            'is_active' => false,
        ]);
    }

    /**
     * Create popular route (high delivery count).
     */
    public function popular(): static
    {
        return $this->state(fn () => [
            'delivery_count' => $this->faker->numberBetween(100, 1000),
        ]);
    }

    /**
     * Create new route (low delivery count).
     */
    public function newRoute(): static
    {
        return $this->state(fn () => [
            'delivery_count' => $this->faker->numberBetween(0, 10),
        ]);
    }

    /**
     * Create short distance route.
     */
    public function shortDistance(): static
    {
        $distance = $this->faker->numberBetween(50, 200);

        return $this->state(fn () => [
            'estimated_distance_km' => $distance,
            'estimated_duration_minutes' => $this->calculateEstimatedDuration($distance),
            'base_price' => $this->faker->numberBetween(3000, 12000),
        ]);
    }

    /**
     * Create long distance route.
     */
    public function longDistance(): static
    {
        $distance = $this->faker->numberBetween(400, 800);

        return $this->state(fn () => [
            'estimated_distance_km' => $distance,
            'estimated_duration_minutes' => $this->calculateEstimatedDuration($distance),
            'base_price' => $this->faker->numberBetween(20000, 60000),
        ]);
    }

    /**
     * Create Lagos to Abuja route.
     */
    public function lagosToAbuja(): static
    {
        return $this->state(function () {
            $lagos = State::firstOrCreate(['code' => 'LA'], ['name' => 'Lagos']);
            $abuja = State::firstOrCreate(['code' => 'FC'], ['name' => 'FCT']);

            return [
                'from_state_id' => $lagos->id,
                'to_state_id' => $abuja->id,
                'estimated_distance_km' => 750,
                'estimated_duration_minutes' => 750, // ~12.5 hours
                'base_price' => $this->faker->numberBetween(15000, 35000),
            ];
        });
    }

    /**
     * Create Lagos to Kano route.
     */
    public function lagosToKano(): static
    {
        return $this->state(function () {
            $lagos = State::firstOrCreate(['code' => 'LA'], ['name' => 'Lagos']);
            $kano = State::firstOrCreate(['code' => 'KN'], ['name' => 'Kano']);

            return [
                'from_state_id' => $lagos->id,
                'to_state_id' => $kano->id,
                'estimated_distance_km' => 1100,
                'estimated_duration_minutes' => 1100, // ~18 hours
                'base_price' => $this->faker->numberBetween(25000, 50000),
            ];
        });
    }

    /**
     * Create bidirectional pricing (both directions).
     */
    public function bidirectional(): static
    {
        return $this->afterCreating(function (InterstatePricing $pricing) {
            // Create the reverse route with similar pricing
            InterstatePricing::factory()->create([
                'from_state_id' => $pricing->to_state_id,
                'to_state_id' => $pricing->from_state_id,
                'vehicle_type' => $pricing->vehicle_type,
                'base_price' => $pricing->base_price,
                'price_per_km' => $pricing->price_per_km,
                'minimum_price' => $pricing->minimum_price,
                'maximum_price' => $pricing->maximum_price,
                'estimated_distance_km' => $pricing->estimated_distance_km,
                'estimated_duration_minutes' => $pricing->estimated_duration_minutes,
                'is_active' => $pricing->is_active,
            ]);
        });
    }
}
