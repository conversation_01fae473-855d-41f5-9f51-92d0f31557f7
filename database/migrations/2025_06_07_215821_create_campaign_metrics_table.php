<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('campaign_metrics', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('campaign_id');
            $table->integer('total_sent')->default(0);
            $table->integer('total_delivered')->default(0);
            $table->integer('total_bounced')->default(0);
            $table->integer('total_opened')->default(0);
            $table->integer('total_clicked')->default(0);
            $table->integer('total_conversions')->default(0);
            $table->integer('total_unsubscribed')->default(0);
            $table->decimal('revenue_generated', 15, 2)->default(0);
            $table->decimal('cost_spent', 15, 2)->default(0);
            $table->json('channel_breakdown')->nullable(); // Performance by channel
            $table->json('hourly_breakdown')->nullable(); // Performance by hour
            $table->json('daily_breakdown')->nullable(); // Performance by day
            $table->timestamps();

            // Indexes
            $table->index('campaign_id');
            $table->unique('campaign_id'); // One metric record per campaign

            // Foreign keys
            $table->foreign('campaign_id')->references('id')->on('campaigns')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campaign_metrics');
    }
};
