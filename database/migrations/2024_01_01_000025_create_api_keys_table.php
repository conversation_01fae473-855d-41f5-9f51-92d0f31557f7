<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('api_keys', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('key_hash'); // Hashed API key
            $table->string('key_prefix', 8); // First 8 characters for identification
            $table->uuid('user_id')->nullable();
            $table->string('tenant_id')->nullable();
            $table->string('type')->default('general'); // general, webhook, integration, etc.
            $table->json('permissions')->nullable(); // Specific permissions for this key
            $table->json('rate_limits')->nullable(); // Custom rate limits
            $table->json('allowed_ips')->nullable(); // IP whitelist
            $table->string('environment')->default('production'); // production, sandbox
            $table->boolean('is_active')->default(true);
            $table->timestamp('last_used_at')->nullable();
            $table->timestamp('expires_at')->nullable();
            $table->uuid('created_by')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['key_prefix', 'is_active']);
            $table->index(['user_id', 'is_active']);
            $table->index(['tenant_id', 'is_active']);
            $table->index(['type', 'environment']);
            $table->index('expires_at');

            // Foreign keys
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('api_keys');
    }
};
