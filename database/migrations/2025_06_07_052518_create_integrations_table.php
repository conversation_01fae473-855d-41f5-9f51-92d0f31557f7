<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('integrations', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->string('slug')->unique();
            $table->string('type'); // payment, sms, email, whatsapp, maps, etc.
            $table->string('status')->default('draft'); // draft, configuring, testing, connected, error, disabled, deprecated
            $table->text('description')->nullable();
            $table->string('provider')->nullable();
            $table->string('version')->nullable();
            $table->json('configuration')->nullable();
            $table->json('credentials')->nullable();
            $table->json('settings')->nullable();
            $table->json('endpoints')->nullable();
            $table->boolean('is_active')->default(false);
            $table->boolean('is_sandbox')->default(true);
            $table->timestamp('last_tested_at')->nullable();
            $table->text('last_error')->nullable();
            $table->uuid('created_by')->nullable();
            $table->uuid('updated_by')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['type', 'status']);
            $table->index(['provider', 'is_active']);
            $table->index(['is_active', 'is_sandbox']);
            $table->index('slug');

            // Foreign keys
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('integrations');
    }
};
