<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('configuration_settings', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('group_id')->nullable();
            $table->string('key')->unique();
            $table->string('name');
            $table->text('description')->nullable();
            $table->string('type'); // string, integer, boolean, json, etc.
            $table->json('value')->nullable();
            $table->json('default_value')->nullable();
            $table->json('validation_rules')->nullable();
            $table->json('options')->nullable(); // For select/multiselect types
            $table->boolean('is_required')->default(false);
            $table->boolean('is_active')->default(true);
            $table->boolean('is_sensitive')->default(false);
            $table->uuid('updated_by')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['group_id', 'is_active']);
            $table->index(['key', 'is_active']);
            $table->index(['type', 'is_active']);
            $table->index('is_required');
            $table->index('is_sensitive');

            // Foreign keys
            $table->foreign('group_id')->references('id')->on('configuration_groups')->onDelete('set null');
            $table->foreign('updated_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('configuration_settings');
    }
};
