<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('campaigns', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('name');
            $table->text('description')->nullable();
            $table->enum('type', ['email', 'sms', 'push', 'whatsapp', 'multi_channel']);
            $table->enum('status', ['draft', 'active', 'paused', 'completed', 'cancelled'])->default('draft');
            $table->json('channels'); // Array of channels: email, sms, push, whatsapp
            $table->json('content'); // Subject, message, template_id, variables
            $table->json('settings'); // send_immediately, scheduled_at, timezone, frequency, ab_test_enabled
            $table->json('budget')->nullable(); // total_budget, daily_budget, currency
            $table->uuid('created_by');
            $table->timestamp('launched_at')->nullable();
            $table->uuid('launched_by')->nullable();
            $table->timestamp('paused_at')->nullable();
            $table->text('pause_reason')->nullable();
            $table->timestamp('completed_at')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['status', 'created_at']);
            $table->index(['type', 'status']);
            $table->index('created_by');
            $table->index('launched_by');

            // Foreign keys
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('launched_by')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campaigns');
    }
};
