<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->text('two_factor_secret')->nullable()->comment('Encrypted TOTP secret key');
            $table->timestamp('two_factor_confirmed_at')->nullable()->comment('When 2FA was confirmed/enabled');
            $table->text('two_factor_backup_codes')->nullable()->comment('Encrypted backup codes for 2FA recovery');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'two_factor_secret',
                'two_factor_confirmed_at',
                'two_factor_backup_codes',
            ]);
        });
    }
};
