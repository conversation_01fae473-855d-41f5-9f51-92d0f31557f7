<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('campaign_audiences', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('campaign_id');
            $table->string('name');
            $table->json('criteria'); // Array of targeting criteria
            $table->integer('estimated_size')->default(0);
            $table->integer('actual_size')->nullable();
            $table->timestamps();

            // Indexes
            $table->index('campaign_id');
            $table->index(['campaign_id', 'name']);

            // Foreign keys
            $table->foreign('campaign_id')->references('id')->on('campaigns')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('campaign_audiences');
    }
};
