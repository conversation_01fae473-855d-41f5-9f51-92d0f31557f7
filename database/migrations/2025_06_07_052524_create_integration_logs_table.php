<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('integration_logs', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('integration_id');
            $table->string('method'); // GET, POST, PUT, DELETE, etc.
            $table->string('endpoint');
            $table->json('request_data')->nullable();
            $table->json('response_data')->nullable();
            $table->integer('response_code');
            $table->string('status'); // success, error
            $table->float('duration_ms');
            $table->text('error_message')->nullable();
            $table->uuid('user_id')->nullable();
            $table->timestamps();

            // Indexes
            $table->index(['integration_id', 'created_at']);
            $table->index(['status', 'created_at']);
            $table->index(['response_code', 'created_at']);
            $table->index('user_id');

            // Foreign keys
            $table->foreign('integration_id')->references('id')->on('integrations')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('integration_logs');
    }
};
