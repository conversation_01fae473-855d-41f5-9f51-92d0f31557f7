<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add QoreID fields to identity_verifications table
        Schema::table('identity_verifications', function (Blueprint $table) {
            $table->string('qoreid_reference')->nullable()->after('external_reference');
            $table->string('risk_level')->nullable()->after('verification_score');
            $table->index(['qoreid_reference']);
        });

        // Add QoreID fields to bank_account_verifications table
        Schema::table('bank_account_verifications', function (Blueprint $table) {
            $table->string('qoreid_reference')->nullable()->after('paystack_reference');
            $table->string('risk_level')->nullable()->after('verification_score');
            $table->index(['qoreid_reference']);
        });

        // Create new table for QoreID comprehensive verifications
        Schema::create('qoreid_verifications', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('user_id');
            $table->string('verification_type'); // 'comprehensive', 'bvn', 'nin', 'bank', etc.
            $table->string('qoreid_reference')->unique();
            $table->string('our_reference');
            $table->json('verification_data');
            $table->integer('verification_score')->nullable();
            $table->string('risk_level')->nullable();
            $table->enum('status', ['pending', 'completed', 'failed'])->default('pending');
            $table->timestamp('verified_at')->nullable();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->index(['user_id', 'verification_type']);
            $table->index(['status']);
            $table->index(['qoreid_reference']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Drop the new table
        Schema::dropIfExists('qoreid_verifications');

        // Remove QoreID fields from bank_account_verifications table
        Schema::table('bank_account_verifications', function (Blueprint $table) {
            $table->dropIndex(['qoreid_reference']);
            $table->dropColumn(['qoreid_reference', 'risk_level']);
        });

        // Remove QoreID fields from identity_verifications table
        Schema::table('identity_verifications', function (Blueprint $table) {
            $table->dropIndex(['qoreid_reference']);
            $table->dropColumn(['qoreid_reference', 'risk_level']);
        });
    }
};
