<?php

declare(strict_types=1);

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('team_invitations', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->string('tenant_id');
            $table->string('email');
            $table->string('role')->comment('Role to be assigned when invitation is accepted');
            $table->string('token', 64)->unique()->comment('Unique invitation token');
            $table->enum('status', ['pending', 'accepted', 'expired', 'cancelled'])->default('pending');
            $table->timestamp('expires_at')->comment('When the invitation expires');
            $table->timestamp('accepted_at')->nullable()->comment('When the invitation was accepted');
            $table->foreignUuid('user_id')->nullable()->constrained('users')->onDelete('set null')->comment('User who accepted the invitation');
            $table->foreignUuid('invited_by_id')->nullable()->constrained('users')->onDelete('set null')->comment('User who sent the invitation');
            $table->json('metadata')->nullable()->comment('Additional invitation metadata (business_id, provider_id, etc.)');
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('tenant_id')->references('id')->on('tenants')->onDelete('cascade');

            // Indexes for performance
            $table->index(['tenant_id', 'status']);
            $table->index(['email', 'status']);
            $table->index(['token', 'status']);
            $table->index('expires_at');

            // Unique constraint to prevent duplicate pending invitations
            $table->unique(['tenant_id', 'email', 'status'], 'unique_pending_invitation');

            $table->comment('Team invitations for businesses and delivery providers. Tracks invitation status and enables registration from tokens.');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('team_invitations');
    }
};
