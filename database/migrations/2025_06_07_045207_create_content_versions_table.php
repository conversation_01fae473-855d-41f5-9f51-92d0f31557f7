<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('content_versions', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('content_id');
            $table->integer('version_number');
            $table->string('title');
            $table->text('excerpt')->nullable();
            $table->longText('content');
            $table->json('changes')->nullable();
            $table->string('change_summary')->nullable();
            $table->uuid('created_by');
            $table->timestamps();

            // Indexes
            $table->index(['content_id', 'version_number']);
            $table->index('created_by');

            // Foreign keys
            $table->foreign('content_id')->references('id')->on('contents')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');

            // Unique constraint
            $table->unique(['content_id', 'version_number']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('content_versions');
    }
};
