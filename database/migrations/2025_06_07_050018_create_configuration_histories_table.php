<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('configuration_histories', function (Blueprint $table) {
            $table->uuid('id')->primary();
            $table->uuid('setting_id');
            $table->json('previous_value')->nullable();
            $table->json('new_value')->nullable();
            $table->string('change_reason')->nullable();
            $table->uuid('changed_by');
            $table->timestamps();

            // Indexes
            $table->index(['setting_id', 'created_at']);
            $table->index('changed_by');

            // Foreign keys
            $table->foreign('setting_id')->references('id')->on('configuration_settings')->onDelete('cascade');
            $table->foreign('changed_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('configuration_histories');
    }
};
