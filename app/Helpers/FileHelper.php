<?php

declare(strict_types=1);

namespace App\Helpers;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

/**
 * Unified file operations helper
 *
 * Consolidates common file handling operations across the application
 * to eliminate duplication and ensure consistency.
 */
class FileHelper
{
    /**
     * Supported image MIME types
     */
    public const IMAGE_MIME_TYPES = [
        'image/jpeg',
        'image/jpg',
        'image/png',
        'image/gif',
        'image/webp',
        'image/heic',
        'image/heif',
    ];

    /**
     * Supported document MIME types
     */
    public const DOCUMENT_MIME_TYPES = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'text/plain',
    ];

    /**
     * Maximum file sizes (in bytes)
     */
    public const MAX_IMAGE_SIZE = 5 * 1024 * 1024; // 5MB

    public const MAX_DOCUMENT_SIZE = 10 * 1024 * 1024; // 10MB

    public const MAX_AVATAR_SIZE = 2 * 1024 * 1024; // 2MB

    /**
     * Generate a unique filename with timestamp and random string
     */
    public static function generateUniqueFilename(string $originalName, ?string $prefix = null): string
    {
        $extension = pathinfo($originalName, PATHINFO_EXTENSION);
        $timestamp = now()->format('YmdHis');
        $random = Str::random(8);

        $filename = $timestamp.'_'.$random;

        if ($prefix) {
            $filename = $prefix.'_'.$filename;
        }

        return $filename.($extension ? '.'.$extension : '');
    }

    /**
     * Generate file hash for deduplication
     */
    public static function generateFileHash(UploadedFile $file): string
    {
        return hash_file('sha256', $file->getRealPath());
    }

    /**
     * Validate uploaded image file
     */
    public static function validateImage(UploadedFile $file, int $maxSize = self::MAX_IMAGE_SIZE): array
    {
        $errors = [];

        // Check file size
        if ($file->getSize() > $maxSize) {
            $errors[] = 'File size exceeds maximum allowed size of '.FormatHelper::formatFileSize($maxSize);
        }

        // Check MIME type
        if (! in_array($file->getMimeType(), self::IMAGE_MIME_TYPES)) {
            $errors[] = 'Invalid file type. Allowed types: '.implode(', ', self::getImageExtensions());
        }

        // Check if file is actually an image
        if (! getimagesize($file->getRealPath())) {
            $errors[] = 'File is not a valid image';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Validate uploaded document file
     */
    public static function validateDocument(UploadedFile $file, int $maxSize = self::MAX_DOCUMENT_SIZE): array
    {
        $errors = [];

        // Check file size
        if ($file->getSize() > $maxSize) {
            $errors[] = 'File size exceeds maximum allowed size of '.FormatHelper::formatFileSize($maxSize);
        }

        // Check MIME type
        if (! in_array($file->getMimeType(), self::DOCUMENT_MIME_TYPES)) {
            $errors[] = 'Invalid file type. Allowed types: '.implode(', ', self::getDocumentExtensions());
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Get allowed image file extensions
     */
    public static function getImageExtensions(): array
    {
        return ['jpg', 'jpeg', 'png', 'gif', 'webp', 'heic', 'heif'];
    }

    /**
     * Get allowed document file extensions
     */
    public static function getDocumentExtensions(): array
    {
        return ['pdf', 'doc', 'docx', 'txt'];
    }

    /**
     * Generate storage path for file category
     */
    public static function generateStoragePath(string $category, ?string $entityId = null, ?string $subcategory = null): string
    {
        $path = $category;

        if ($subcategory) {
            $path .= '/'.$subcategory;
        }

        if ($entityId) {
            $path .= '/'.$entityId;
        }

        return $path;
    }

    /**
     * Store file with automatic path generation
     */
    public static function storeFile(
        UploadedFile $file,
        string $category,
        ?string $entityId = null,
        string $disk = 'public',
        ?string $filename = null
    ): array {
        $filename = $filename ?: self::generateUniqueFilename($file->getClientOriginalName(), $category);
        $path = self::generateStoragePath($category, $entityId);

        $storedPath = $file->storeAs($path, $filename, $disk);

        return [
            'path' => $storedPath,
            'filename' => $filename,
            'original_name' => $file->getClientOriginalName(),
            'size' => $file->getSize(),
            'mime_type' => $file->getMimeType(),
            'url' => Storage::disk($disk)->url($storedPath),
        ];
    }

    /**
     * Delete file from storage
     */
    public static function deleteFile(string $path, string $disk = 'public'): bool
    {
        if (Storage::disk($disk)->exists($path)) {
            return Storage::disk($disk)->delete($path);
        }

        return true; // Consider non-existent files as successfully deleted
    }

    /**
     * Delete file by URL
     */
    public static function deleteFileByUrl(string $url, string $disk = 'public'): bool
    {
        // Extract path from URL
        $baseUrl = Storage::disk($disk)->url('');
        $path = str_replace($baseUrl, '', $url);
        $path = ltrim($path, '/');

        return self::deleteFile($path, $disk);
    }

    /**
     * Copy file to new location
     */
    public static function copyFile(string $sourcePath, string $destinationPath, string $disk = 'public'): bool
    {
        if (! Storage::disk($disk)->exists($sourcePath)) {
            return false;
        }

        return Storage::disk($disk)->copy($sourcePath, $destinationPath);
    }

    /**
     * Move file to new location
     */
    public static function moveFile(string $sourcePath, string $destinationPath, string $disk = 'public'): bool
    {
        if (! Storage::disk($disk)->exists($sourcePath)) {
            return false;
        }

        return Storage::disk($disk)->move($sourcePath, $destinationPath);
    }

    /**
     * Get file info
     */
    public static function getFileInfo(string $path, string $disk = 'public'): ?array
    {
        if (! Storage::disk($disk)->exists($path)) {
            return null;
        }

        return [
            'path' => $path,
            'size' => Storage::disk($disk)->size($path),
            'last_modified' => Storage::disk($disk)->lastModified($path),
            'url' => Storage::disk($disk)->url($path),
            'exists' => true,
        ];
    }

    /**
     * Generate thumbnail filename
     */
    public static function generateThumbnailFilename(string $originalFilename, string $size = 'thumb'): string
    {
        $pathInfo = pathinfo($originalFilename);
        $name = $pathInfo['filename'];
        $extension = $pathInfo['extension'] ?? '';

        return $name.'_'.$size.($extension ? '.'.$extension : '');
    }

    /**
     * Check if file is an image
     */
    public static function isImage(string $mimeType): bool
    {
        return in_array($mimeType, self::IMAGE_MIME_TYPES);
    }

    /**
     * Check if file is a document
     */
    public static function isDocument(string $mimeType): bool
    {
        return in_array($mimeType, self::DOCUMENT_MIME_TYPES);
    }

    /**
     * Get file extension from MIME type
     */
    public static function getExtensionFromMimeType(string $mimeType): string
    {
        $mimeToExt = [
            'image/jpeg' => 'jpg',
            'image/jpg' => 'jpg',
            'image/png' => 'png',
            'image/gif' => 'gif',
            'image/webp' => 'webp',
            'image/heic' => 'heic',
            'image/heif' => 'heif',
            'application/pdf' => 'pdf',
            'application/msword' => 'doc',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document' => 'docx',
            'text/plain' => 'txt',
        ];

        return $mimeToExt[$mimeType] ?? 'bin';
    }

    /**
     * Clean up old files in a directory
     */
    public static function cleanupOldFiles(string $directory, int $daysOld = 30, string $disk = 'public'): int
    {
        $files = Storage::disk($disk)->files($directory);
        $cutoffTime = now()->subDays($daysOld)->timestamp;
        $deletedCount = 0;

        foreach ($files as $file) {
            $lastModified = Storage::disk($disk)->lastModified($file);

            if ($lastModified < $cutoffTime) {
                if (Storage::disk($disk)->delete($file)) {
                    $deletedCount++;
                }
            }
        }

        return $deletedCount;
    }

    /**
     * Get directory size
     */
    public static function getDirectorySize(string $directory, string $disk = 'public'): int
    {
        $files = Storage::disk($disk)->allFiles($directory);
        $totalSize = 0;

        foreach ($files as $file) {
            $totalSize += Storage::disk($disk)->size($file);
        }

        return $totalSize;
    }

    /**
     * Create directory if it doesn't exist
     */
    public static function ensureDirectoryExists(string $directory, string $disk = 'public'): bool
    {
        if (! Storage::disk($disk)->exists($directory)) {
            return Storage::disk($disk)->makeDirectory($directory);
        }

        return true;
    }

    /**
     * Generate secure download URL with expiration
     */
    public static function generateSecureUrl(string $path, int $expirationMinutes = 60, string $disk = 'private'): string
    {
        return Storage::disk($disk)->temporaryUrl($path, now()->addMinutes($expirationMinutes));
    }

    /**
     * Validate file upload limits
     */
    public static function validateUploadLimits(UploadedFile $file, array $config = []): array
    {
        $maxSize = $config['max_size'] ?? self::MAX_IMAGE_SIZE;
        $allowedTypes = $config['allowed_types'] ?? self::IMAGE_MIME_TYPES;

        $errors = [];

        if ($file->getSize() > $maxSize) {
            $errors[] = 'File size exceeds limit of '.FormatHelper::formatFileSize($maxSize);
        }

        if (! in_array($file->getMimeType(), $allowedTypes)) {
            $errors[] = 'File type not allowed';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }
}
