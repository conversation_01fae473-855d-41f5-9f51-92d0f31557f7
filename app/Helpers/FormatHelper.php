<?php

declare(strict_types=1);

namespace App\Helpers;

use Carbon\Carbon;

/**
 * Unified formatting helper
 *
 * Consolidates common formatting operations across the application
 * to eliminate duplication and ensure consistency.
 */
class FormatHelper
{
    /**
     * Format Nigerian phone number to international format
     */
    public static function formatNigerianPhone(string $phone): string
    {
        // Remove all non-numeric characters except +
        $phone = preg_replace('/[^0-9+]/', '', $phone);

        // Convert Nigerian local format to international
        if (str_starts_with($phone, '0')) {
            return '+234'.substr($phone, 1);
        } elseif (str_starts_with($phone, '234')) {
            return '+'.$phone;
        } elseif (str_starts_with($phone, '+234')) {
            return $phone;
        }

        // If it's already in international format or unknown format, return as is
        return $phone;
    }

    /**
     * Format phone number for display (with spaces)
     */
    public static function formatPhoneForDisplay(string $phone): string
    {
        $formatted = self::formatNigerianPhone($phone);

        // Format +234XXXXXXXXXX to +234 XXX XXX XXXX
        if (preg_match('/^\+234(\d{10})$/', $formatted, $matches)) {
            $number = $matches[1];

            return '+234 '.substr($number, 0, 3).' '.substr($number, 3, 3).' '.substr($number, 6);
        }

        return $formatted;
    }

    /**
     * Format currency amount (Nigerian Naira)
     */
    public static function formatCurrency(float $amount, string $currency = 'NGN', bool $showSymbol = true): string
    {
        $symbols = [
            'NGN' => '₦',
            'USD' => '$',
            'EUR' => '€',
            'GBP' => '£',
        ];

        $symbol = $showSymbol ? ($symbols[$currency] ?? $currency.' ') : '';

        return $symbol.number_format($amount, 2);
    }

    /**
     * Format currency for API responses (no symbol, consistent decimals)
     */
    public static function formatCurrencyForApi(float $amount): string
    {
        return number_format($amount, 2, '.', '');
    }

    /**
     * Format file size in human readable format
     */
    public static function formatFileSize(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, 2).' '.$units[$i];
    }

    /**
     * Format duration in human readable format
     */
    public static function formatDuration(int $seconds): string
    {
        if ($seconds < 60) {
            return $seconds.' second'.($seconds !== 1 ? 's' : '');
        }

        if ($seconds < 3600) {
            $minutes = floor($seconds / 60);

            return $minutes.' minute'.($minutes !== 1 ? 's' : '');
        }

        if ($seconds < 86400) {
            $hours = floor($seconds / 3600);

            return $hours.' hour'.($hours !== 1 ? 's' : '');
        }

        $days = floor($seconds / 86400);

        return $days.' day'.($days !== 1 ? 's' : '');
    }

    /**
     * Format distance in human readable format
     */
    public static function formatDistance(float $meters): string
    {
        if ($meters < 1000) {
            return round($meters).' m';
        }

        return round($meters / 1000, 1).' km';
    }

    /**
     * Format percentage
     */
    public static function formatPercentage(float $value, int $decimals = 1): string
    {
        return number_format($value, $decimals).'%';
    }

    /**
     * Format business name for URL slug
     */
    public static function formatBusinessSlug(string $name): string
    {
        // Convert to lowercase and replace spaces/special chars with hyphens
        $slug = strtolower($name);
        $slug = preg_replace('/[^a-z0-9]+/', '-', $slug);
        $slug = trim($slug, '-');

        return $slug;
    }

    /**
     * Format address for display
     */
    public static function formatAddress(array $address): string
    {
        $parts = array_filter([
            $address['street_address'] ?? '',
            $address['city'] ?? '',
            $address['state'] ?? '',
            $address['postal_code'] ?? '',
        ]);

        return implode(', ', $parts);
    }

    /**
     * Format coordinates for display
     */
    public static function formatCoordinates(float $latitude, float $longitude, int $precision = 6): string
    {
        return number_format($latitude, $precision).', '.number_format($longitude, $precision);
    }

    /**
     * Format date for API responses (ISO 8601)
     */
    public static function formatDateForApi(?Carbon $date): ?string
    {
        return $date?->toISOString();
    }

    /**
     * Format date for display (human readable)
     */
    public static function formatDateForDisplay(?Carbon $date, string $format = 'M j, Y'): ?string
    {
        return $date?->format($format);
    }

    /**
     * Format datetime for display with timezone
     */
    public static function formatDateTimeForDisplay(?Carbon $date, string $timezone = 'Africa/Lagos'): ?string
    {
        if (! $date) {
            return null;
        }

        return $date->setTimezone($timezone)->format('M j, Y g:i A T');
    }

    /**
     * Format time ago (relative time)
     */
    public static function formatTimeAgo(?Carbon $date): ?string
    {
        return $date?->diffForHumans();
    }

    /**
     * Format order number with prefix
     */
    public static function formatOrderNumber(string $id): string
    {
        return 'DN-'.strtoupper(substr($id, 0, 8));
    }

    /**
     * Format tracking number
     */
    public static function formatTrackingNumber(string $id): string
    {
        return 'TRK-'.strtoupper(substr($id, 0, 10));
    }

    /**
     * Format business registration number
     */
    public static function formatBusinessRegNumber(string $number): string
    {
        // Remove spaces and format as XXX-XXX-XXX
        $clean = preg_replace('/[^0-9]/', '', $number);

        if (strlen($clean) >= 9) {
            return substr($clean, 0, 3).'-'.substr($clean, 3, 3).'-'.substr($clean, 6, 3);
        }

        return $number;
    }

    /**
     * Format tax identification number
     */
    public static function formatTaxNumber(string $number): string
    {
        // Remove spaces and format as XXXX-XXXX-XXXX
        $clean = preg_replace('/[^0-9A-Z]/', '', strtoupper($number));

        if (strlen($clean) >= 12) {
            return substr($clean, 0, 4).'-'.substr($clean, 4, 4).'-'.substr($clean, 8, 4);
        }

        return $number;
    }

    /**
     * Truncate text with ellipsis
     */
    public static function truncateText(string $text, int $length = 100, string $suffix = '...'): string
    {
        if (strlen($text) <= $length) {
            return $text;
        }

        return substr($text, 0, $length - strlen($suffix)).$suffix;
    }

    /**
     * Format array as comma-separated list
     */
    public static function formatList(array $items, string $conjunction = 'and'): string
    {
        if (empty($items)) {
            return '';
        }

        if (count($items) === 1) {
            return $items[0];
        }

        if (count($items) === 2) {
            return implode(' '.$conjunction.' ', $items);
        }

        $last = array_pop($items);

        return implode(', ', $items).', '.$conjunction.' '.$last;
    }

    /**
     * Format status for display (capitalize and replace underscores)
     */
    public static function formatStatus(string $status): string
    {
        return ucwords(str_replace('_', ' ', $status));
    }

    /**
     * Format API response timestamp
     */
    public static function formatApiTimestamp(): string
    {
        return now()->toISOString();
    }

    /**
     * Sanitize filename for storage
     */
    public static function sanitizeFilename(string $filename): string
    {
        // Remove path traversal attempts
        $filename = basename($filename);

        // Replace spaces and special characters
        $filename = preg_replace('/[^a-zA-Z0-9._-]/', '_', $filename);

        // Remove multiple consecutive underscores
        $filename = preg_replace('/_+/', '_', $filename);

        // Trim underscores from start and end
        return trim($filename, '_');
    }

    /**
     * Format version number
     */
    public static function formatVersion(string $version): string
    {
        // Ensure version follows semantic versioning
        if (! preg_match('/^\d+\.\d+\.\d+/', $version)) {
            return '1.0.0';
        }

        return $version;
    }
}
