<?php

declare(strict_types=1);

namespace App\Helpers;

use Illuminate\Support\Str;
use Illuminate\Validation\Rules\Password;

/**
 * Unified password generation and validation helper
 *
 * Consolidates password-related functionality across the application
 * to eliminate duplication and ensure consistency.
 */
class PasswordHelper
{
    /**
     * Generate a secure temporary password for admin-created users
     */
    public static function generateTemporaryPassword(int $length = 12): string
    {
        // Character sets for password complexity
        $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        $lowercase = 'abcdefghijklmnopqrstuvwxyz';
        $numbers = '0123456789';
        $symbols = '!@#$%^&*';

        $password = '';

        // Ensure at least one character from each set
        $password .= $uppercase[random_int(0, strlen($uppercase) - 1)];
        $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        $password .= $numbers[random_int(0, strlen($numbers) - 1)];
        $password .= $symbols[random_int(0, strlen($symbols) - 1)];

        // Fill the rest with random characters
        $allChars = $uppercase.$lowercase.$numbers.$symbols;
        for ($i = 4; $i < $length; $i++) {
            $password .= $allChars[random_int(0, strlen($allChars) - 1)];
        }

        // Shuffle to randomize character positions
        return str_shuffle($password);
    }

    /**
     * Generate a branded temporary password with DeliveryNexus prefix
     */
    public static function generateBrandedTemporaryPassword(): string
    {
        return 'DN'.Str::upper(Str::random(2)).rand(1000, 9999).'!';
    }

    /**
     * Generate a simple numeric password for quick access
     */
    public static function generateNumericPassword(int $length = 8): string
    {
        return str_pad((string) random_int(10000000, 99999999), $length, '0', STR_PAD_LEFT);
    }

    /**
     * Get standard password validation rules for user registration
     */
    public static function getRegistrationRules(): array
    {
        return [
            'required',
            'confirmed',
            Password::min(8)
                ->mixedCase()
                ->numbers()
                ->symbols()
                ->uncompromised(),
        ];
    }

    /**
     * Get basic password validation rules (less strict)
     */
    public static function getBasicRules(): array
    {
        return [
            'required',
            'confirmed',
            Password::min(8)
                ->mixedCase()
                ->numbers(),
        ];
    }

    /**
     * Get admin password validation rules (more strict)
     */
    public static function getAdminRules(): array
    {
        return [
            'required',
            'confirmed',
            Password::min(12)
                ->mixedCase()
                ->numbers()
                ->symbols()
                ->uncompromised(),
        ];
    }

    /**
     * Get password change validation rules
     */
    public static function getChangePasswordRules(): array
    {
        return [
            'current_password' => 'required|string',
            'new_password' => [
                'required',
                'confirmed',
                Password::min(8)
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
                    ->uncompromised(),
            ],
        ];
    }

    /**
     * Get password reset validation rules
     */
    public static function getResetRules(): array
    {
        return [
            'email' => 'required|email|max:255',
            'otp' => 'required|string|size:6|regex:/^[0-9]+$/',
            'password' => [
                'required',
                'confirmed',
                Password::min(8)
                    ->mixedCase()
                    ->numbers(),
            ],
        ];
    }

    /**
     * Validate password strength manually
     */
    public static function validateStrength(string $password): array
    {
        $errors = [];

        if (strlen($password) < 8) {
            $errors[] = 'Password must be at least 8 characters long';
        }

        if (! preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter';
        }

        if (! preg_match('/[a-z]/', $password)) {
            $errors[] = 'Password must contain at least one lowercase letter';
        }

        if (! preg_match('/[0-9]/', $password)) {
            $errors[] = 'Password must contain at least one number';
        }

        if (! preg_match('/[!@#$%^&*(),.?":{}|<>]/', $password)) {
            $errors[] = 'Password must contain at least one special character';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'strength' => self::calculateStrength($password),
        ];
    }

    /**
     * Calculate password strength score (0-100)
     */
    private static function calculateStrength(string $password): int
    {
        $score = 0;

        // Length bonus
        $score += min(25, strlen($password) * 2);

        // Character variety bonus
        if (preg_match('/[a-z]/', $password)) {
            $score += 15;
        }
        if (preg_match('/[A-Z]/', $password)) {
            $score += 15;
        }
        if (preg_match('/[0-9]/', $password)) {
            $score += 15;
        }
        if (preg_match('/[!@#$%^&*(),.?":{}|<>]/', $password)) {
            $score += 20;
        }

        // Complexity bonus
        if (preg_match('/[a-z].*[A-Z]|[A-Z].*[a-z]/', $password)) {
            $score += 5;
        }
        if (preg_match('/[a-zA-Z].*[0-9]|[0-9].*[a-zA-Z]/', $password)) {
            $score += 5;
        }

        return min(100, $score);
    }

    /**
     * Generate a password that meets specific requirements
     */
    public static function generateCompliantPassword(array $requirements = []): string
    {
        $length = $requirements['length'] ?? 12;
        $requireUppercase = $requirements['uppercase'] ?? true;
        $requireLowercase = $requirements['lowercase'] ?? true;
        $requireNumbers = $requirements['numbers'] ?? true;
        $requireSymbols = $requirements['symbols'] ?? true;

        $chars = '';
        $password = '';

        if ($requireUppercase) {
            $uppercase = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
            $chars .= $uppercase;
            $password .= $uppercase[random_int(0, strlen($uppercase) - 1)];
        }

        if ($requireLowercase) {
            $lowercase = 'abcdefghijklmnopqrstuvwxyz';
            $chars .= $lowercase;
            $password .= $lowercase[random_int(0, strlen($lowercase) - 1)];
        }

        if ($requireNumbers) {
            $numbers = '0123456789';
            $chars .= $numbers;
            $password .= $numbers[random_int(0, strlen($numbers) - 1)];
        }

        if ($requireSymbols) {
            $symbols = '!@#$%^&*';
            $chars .= $symbols;
            $password .= $symbols[random_int(0, strlen($symbols) - 1)];
        }

        // Fill remaining length
        while (strlen($password) < $length) {
            $password .= $chars[random_int(0, strlen($chars) - 1)];
        }

        return str_shuffle($password);
    }
}
