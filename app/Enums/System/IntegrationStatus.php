<?php

declare(strict_types=1);

namespace App\Enums\System;

enum IntegrationStatus: string
{
    case DRAFT = 'draft';
    case CONFIGURING = 'configuring';
    case TESTING = 'testing';
    case CONNECTED = 'connected';
    case ERROR = 'error';
    case DISABLED = 'disabled';
    case DEPRECATED = 'deprecated';

    /**
     * Get human-readable label.
     */
    public function label(): string
    {
        return match ($this) {
            self::DRAFT => 'Draft',
            self::CONFIGURING => 'Configuring',
            self::TESTING => 'Testing',
            self::CONNECTED => 'Connected',
            self::ERROR => 'Error',
            self::DISABLED => 'Disabled',
            self::DEPRECATED => 'Deprecated',
        };
    }

    /**
     * Get color for UI display.
     */
    public function color(): string
    {
        return match ($this) {
            self::DRAFT => 'gray',
            self::CONFIGURING => 'blue',
            self::TESTING => 'yellow',
            self::CONNECTED => 'green',
            self::ERROR => 'red',
            self::DISABLED => 'orange',
            self::DEPRECATED => 'purple',
        };
    }

    /**
     * Get description for the status.
     */
    public function description(): string
    {
        return match ($this) {
            self::DRAFT => 'Integration is being set up',
            self::CONFIGURING => 'Configuration is in progress',
            self::TESTING => 'Integration is being tested',
            self::CONNECTED => 'Integration is active and working',
            self::ERROR => 'Integration has errors and needs attention',
            self::DISABLED => 'Integration is temporarily disabled',
            self::DEPRECATED => 'Integration is deprecated and should be replaced',
        };
    }

    /**
     * Get statuses that are considered active.
     */
    public static function activeStatuses(): array
    {
        return [
            self::CONNECTED,
            self::TESTING,
        ];
    }

    /**
     * Get statuses that require attention.
     */
    public static function attentionStatuses(): array
    {
        return [
            self::ERROR,
            self::DEPRECATED,
        ];
    }

    /**
     * Check if status is active.
     */
    public function isActive(): bool
    {
        return in_array($this, self::activeStatuses());
    }

    /**
     * Check if status requires attention.
     */
    public function requiresAttention(): bool
    {
        return in_array($this, self::attentionStatuses());
    }

    /**
     * Check if integration can be tested.
     */
    public function canBeTested(): bool
    {
        return in_array($this, [
            self::CONFIGURING,
            self::TESTING,
            self::CONNECTED,
            self::ERROR,
        ]);
    }

    /**
     * Check if integration can be used.
     */
    public function canBeUsed(): bool
    {
        return $this === self::CONNECTED;
    }
}
