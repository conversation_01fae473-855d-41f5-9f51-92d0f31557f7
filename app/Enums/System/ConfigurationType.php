<?php

declare(strict_types=1);

namespace App\Enums\System;

enum ConfigurationType: string
{
    case STRING = 'string';
    case TEXT = 'text';
    case INTEGER = 'integer';
    case FLOAT = 'float';
    case BOOLEAN = 'boolean';
    case EMAIL = 'email';
    case URL = 'url';
    case PASSWORD = 'password';
    case JSON = 'json';
    case ARRAY = 'array';
    case SELECT = 'select';
    case MULTISELECT = 'multiselect';
    case FILE = 'file';
    case IMAGE = 'image';
    case COLOR = 'color';
    case DATE = 'date';
    case DATETIME = 'datetime';
    case TIME = 'time';

    /**
     * Get human-readable label.
     */
    public function label(): string
    {
        return match ($this) {
            self::STRING => 'Text',
            self::TEXT => 'Long Text',
            self::INTEGER => 'Number (Integer)',
            self::FLOAT => 'Number (Decimal)',
            self::BOOLEAN => 'Yes/No',
            self::EMAIL => 'Email Address',
            self::URL => 'URL',
            self::PASSWORD => 'Password',
            self::JSON => 'JSON Object',
            self::ARRAY => 'Array/List',
            self::SELECT => 'Dropdown',
            self::MULTISELECT => 'Multiple Selection',
            self::FILE => 'File Upload',
            self::IMAGE => 'Image Upload',
            self::COLOR => 'Color Picker',
            self::DATE => 'Date',
            self::DATETIME => 'Date & Time',
            self::TIME => 'Time',
        };
    }

    /**
     * Get description for the type.
     */
    public function description(): string
    {
        return match ($this) {
            self::STRING => 'Single line text input',
            self::TEXT => 'Multi-line text area',
            self::INTEGER => 'Whole numbers only',
            self::FLOAT => 'Numbers with decimal places',
            self::BOOLEAN => 'True/false or yes/no values',
            self::EMAIL => 'Valid email address format',
            self::URL => 'Valid URL format',
            self::PASSWORD => 'Sensitive text (masked)',
            self::JSON => 'JSON formatted object',
            self::ARRAY => 'List of values',
            self::SELECT => 'Single selection from options',
            self::MULTISELECT => 'Multiple selections from options',
            self::FILE => 'File upload field',
            self::IMAGE => 'Image file upload',
            self::COLOR => 'Color value picker',
            self::DATE => 'Date selection',
            self::DATETIME => 'Date and time selection',
            self::TIME => 'Time selection',
        };
    }

    /**
     * Get validation rules for this type.
     */
    public function getValidationRules(): array
    {
        return match ($this) {
            self::STRING => ['string', 'max:255'],
            self::TEXT => ['string'],
            self::INTEGER => ['integer'],
            self::FLOAT => ['numeric'],
            self::BOOLEAN => ['boolean'],
            self::EMAIL => ['email'],
            self::URL => ['url'],
            self::PASSWORD => ['string', 'min:8'],
            self::JSON => ['json'],
            self::ARRAY => ['array'],
            self::SELECT => ['string'],
            self::MULTISELECT => ['array'],
            self::FILE => ['file'],
            self::IMAGE => ['image'],
            self::COLOR => ['string', 'regex:/^#[0-9A-Fa-f]{6}$/'],
            self::DATE => ['date'],
            self::DATETIME => ['date'],
            self::TIME => ['date_format:H:i'],
        };
    }

    /**
     * Get HTML input type.
     */
    public function getHtmlInputType(): string
    {
        return match ($this) {
            self::STRING => 'text',
            self::TEXT => 'textarea',
            self::INTEGER => 'number',
            self::FLOAT => 'number',
            self::BOOLEAN => 'checkbox',
            self::EMAIL => 'email',
            self::URL => 'url',
            self::PASSWORD => 'password',
            self::JSON, self::ARRAY => 'textarea',
            self::SELECT => 'select',
            self::MULTISELECT => 'select',
            self::FILE => 'file',
            self::IMAGE => 'file',
            self::COLOR => 'color',
            self::DATE => 'date',
            self::DATETIME => 'datetime-local',
            self::TIME => 'time',
        };
    }

    /**
     * Check if this type requires options.
     */
    public function requiresOptions(): bool
    {
        return in_array($this, [
            self::SELECT,
            self::MULTISELECT,
        ]);
    }

    /**
     * Check if this type is sensitive.
     */
    public function isSensitive(): bool
    {
        return in_array($this, [
            self::PASSWORD,
        ]);
    }

    /**
     * Check if this type supports multiple values.
     */
    public function isMultiple(): bool
    {
        return in_array($this, [
            self::ARRAY,
            self::MULTISELECT,
        ]);
    }

    /**
     * Get default value for this type.
     */
    public function getDefaultValue()
    {
        return match ($this) {
            self::STRING, self::TEXT, self::EMAIL, self::URL, self::PASSWORD => '',
            self::INTEGER => 0,
            self::FLOAT => 0.0,
            self::BOOLEAN => false,
            self::JSON => [],
            self::ARRAY, self::MULTISELECT => [],
            self::SELECT => null,
            self::COLOR => '#000000',
            default => null,
        };
    }
}
