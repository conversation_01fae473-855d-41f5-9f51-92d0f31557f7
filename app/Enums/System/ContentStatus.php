<?php

declare(strict_types=1);

namespace App\Enums\System;

enum ContentStatus: string
{
    case DRAFT = 'draft';
    case PENDING_REVIEW = 'pending_review';
    case APPROVED = 'approved';
    case PUBLISHED = 'published';
    case SCHEDULED = 'scheduled';
    case ARCHIVED = 'archived';
    case EXPIRED = 'expired';

    /**
     * Get human-readable label.
     */
    public function label(): string
    {
        return match ($this) {
            self::DRAFT => 'Draft',
            self::PENDING_REVIEW => 'Pending Review',
            self::APPROVED => 'Approved',
            self::PUBLISHED => 'Published',
            self::SCHEDULED => 'Scheduled',
            self::ARCHIVED => 'Archived',
            self::EXPIRED => 'Expired',
        };
    }

    /**
     * Get color for UI display.
     */
    public function color(): string
    {
        return match ($this) {
            self::DRAFT => 'gray',
            self::PENDING_REVIEW => 'yellow',
            self::APPROVED => 'blue',
            self::PUBLISHED => 'green',
            self::SCHEDULED => 'purple',
            self::ARCHIVED => 'orange',
            self::EXPIRED => 'red',
        };
    }

    /**
     * Get description for the status.
     */
    public function description(): string
    {
        return match ($this) {
            self::DRAFT => 'Content is being worked on and not ready for review',
            self::PENDING_REVIEW => 'Content is ready and waiting for review',
            self::APPROVED => 'Content has been approved and ready for publishing',
            self::PUBLISHED => 'Content is live and visible to users',
            self::SCHEDULED => 'Content is scheduled for future publication',
            self::ARCHIVED => 'Content has been archived and is no longer active',
            self::EXPIRED => 'Content has passed its expiration date',
        };
    }

    /**
     * Get statuses that are visible to public.
     */
    public static function publicVisible(): array
    {
        return [
            self::PUBLISHED,
        ];
    }

    /**
     * Get statuses that can be edited.
     */
    public static function editable(): array
    {
        return [
            self::DRAFT,
            self::PENDING_REVIEW,
            self::APPROVED,
            self::SCHEDULED,
        ];
    }

    /**
     * Get statuses that can be published.
     */
    public static function publishable(): array
    {
        return [
            self::APPROVED,
            self::SCHEDULED,
        ];
    }

    /**
     * Check if content with this status is visible to public.
     */
    public function isPublicVisible(): bool
    {
        return in_array($this, self::publicVisible());
    }

    /**
     * Check if content with this status can be edited.
     */
    public function isEditable(): bool
    {
        return in_array($this, self::editable());
    }

    /**
     * Check if content with this status can be published.
     */
    public function isPublishable(): bool
    {
        return in_array($this, self::publishable());
    }
}
