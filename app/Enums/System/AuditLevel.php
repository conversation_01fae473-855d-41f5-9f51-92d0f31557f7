<?php

declare(strict_types=1);

namespace App\Enums\System;

enum AuditLevel: string
{
    case CRITICAL = 'critical';
    case HIGH = 'high';
    case MEDIUM = 'medium';
    case LOW = 'low';
    case INFO = 'info';

    /**
     * Get human-readable label.
     */
    public function label(): string
    {
        return match ($this) {
            self::CRITICAL => 'Critical',
            self::HIGH => 'High',
            self::MEDIUM => 'Medium',
            self::LOW => 'Low',
            self::INFO => 'Info',
        };
    }

    /**
     * Get color for UI display.
     */
    public function color(): string
    {
        return match ($this) {
            self::CRITICAL => 'red',
            self::HIGH => 'orange',
            self::MEDIUM => 'yellow',
            self::LOW => 'blue',
            self::INFO => 'gray',
        };
    }

    /**
     * Get numeric priority value.
     */
    public function priority(): int
    {
        return match ($this) {
            self::CRITICAL => 5,
            self::HIGH => 4,
            self::MEDIUM => 3,
            self::LOW => 2,
            self::INFO => 1,
        };
    }

    /**
     * Get levels that require immediate attention.
     */
    public static function alertLevels(): array
    {
        return [
            self::CRITICAL,
            self::HIGH,
        ];
    }

    /**
     * Check if this level requires immediate attention.
     */
    public function requiresAlert(): bool
    {
        return in_array($this, self::alertLevels());
    }

    /**
     * Get description for the level.
     */
    public function description(): string
    {
        return match ($this) {
            self::CRITICAL => 'Critical security or system events requiring immediate attention',
            self::HIGH => 'High-priority events that should be reviewed promptly',
            self::MEDIUM => 'Medium-priority events for regular monitoring',
            self::LOW => 'Low-priority events for general awareness',
            self::INFO => 'Informational events for audit trail purposes',
        };
    }
}
