<?php

declare(strict_types=1);

namespace App\Enums\System;

enum IntegrationType: string
{
    case PAYMENT = 'payment';
    case SMS = 'sms';
    case EMAIL = 'email';
    case WHATSAPP = 'whatsapp';
    case MAPS = 'maps';
    case ANALYTICS = 'analytics';
    case STORAGE = 'storage';
    case KYC = 'kyc';
    case LOGISTICS = 'logistics';
    case SOCIAL_AUTH = 'social_auth';
    case PUSH_NOTIFICATION = 'push_notification';
    case WEBHOOK = 'webhook';
    case API = 'api';
    case DATABASE = 'database';
    case MONITORING = 'monitoring';

    /**
     * Get human-readable label.
     */
    public function label(): string
    {
        return match ($this) {
            self::PAYMENT => 'Payment Gateway',
            self::SMS => 'SMS Provider',
            self::EMAIL => 'Email Service',
            self::WHATSAPP => 'WhatsApp Business',
            self::MAPS => 'Maps & Geocoding',
            self::ANALYTICS => 'Analytics Platform',
            self::STORAGE => 'Cloud Storage',
            self::KYC => 'KYC Verification',
            self::LOGISTICS => 'Logistics & Shipping',
            self::SOCIAL_AUTH => 'Social Authentication',
            self::PUSH_NOTIFICATION => 'Push Notifications',
            self::WEBHOOK => 'Webhook Service',
            self::API => 'External API',
            self::DATABASE => 'External Database',
            self::MONITORING => 'Monitoring Service',
        };
    }

    /**
     * Get description for the integration type.
     */
    public function description(): string
    {
        return match ($this) {
            self::PAYMENT => 'Payment processing and gateway integrations',
            self::SMS => 'SMS messaging and verification services',
            self::EMAIL => 'Email delivery and marketing services',
            self::WHATSAPP => 'WhatsApp Business API for messaging',
            self::MAPS => 'Maps, geocoding, and location services',
            self::ANALYTICS => 'Analytics and tracking platforms',
            self::STORAGE => 'Cloud storage and file management',
            self::KYC => 'Know Your Customer verification services',
            self::LOGISTICS => 'Shipping and logistics providers',
            self::SOCIAL_AUTH => 'Social media authentication providers',
            self::PUSH_NOTIFICATION => 'Mobile and web push notifications',
            self::WEBHOOK => 'Webhook delivery and management',
            self::API => 'Third-party API integrations',
            self::DATABASE => 'External database connections',
            self::MONITORING => 'Application monitoring and alerting',
        };
    }

    /**
     * Get common providers for this integration type.
     */
    public function getCommonProviders(): array
    {
        return match ($this) {
            self::PAYMENT => ['Paystack', 'Flutterwave', 'Stripe', 'PayPal', 'Razorpay'],
            self::SMS => ['Twilio', 'Termii', 'Nexmo', 'AWS SNS', 'Africa\'s Talking'],
            self::EMAIL => ['SendGrid', 'Mailgun', 'AWS SES', 'Postmark', 'ZeptoMail'],
            self::WHATSAPP => ['WhatsApp Business API', 'Twilio WhatsApp', 'MessageBird'],
            self::MAPS => ['Google Maps', 'Mapbox', 'HERE Maps', 'OpenStreetMap'],
            self::ANALYTICS => ['Google Analytics', 'Mixpanel', 'Amplitude', 'Segment'],
            self::STORAGE => ['AWS S3', 'Cloudflare R2', 'Google Cloud Storage', 'Azure Blob'],
            self::KYC => ['QoreID', 'Smile Identity', 'Jumio', 'Onfido', 'Veriff'],
            self::LOGISTICS => ['DHL', 'FedEx', 'UPS', 'GIG Logistics', 'Kwik Delivery'],
            self::SOCIAL_AUTH => ['Google', 'Facebook', 'Apple', 'Twitter', 'LinkedIn'],
            self::PUSH_NOTIFICATION => ['Firebase FCM', 'OneSignal', 'Pusher', 'AWS SNS'],
            self::WEBHOOK => ['Webhook.site', 'ngrok', 'Zapier', 'Custom'],
            self::API => ['REST API', 'GraphQL', 'SOAP', 'Custom'],
            self::DATABASE => ['PostgreSQL', 'MySQL', 'MongoDB', 'Redis'],
            self::MONITORING => ['Sentry', 'Bugsnag', 'New Relic', 'DataDog'],
        };
    }

    /**
     * Get required configuration fields.
     */
    public function getRequiredFields(): array
    {
        return match ($this) {
            self::PAYMENT => ['api_key', 'secret_key', 'webhook_url'],
            self::SMS => ['api_key', 'sender_id'],
            self::EMAIL => ['api_key', 'from_email', 'from_name'],
            self::WHATSAPP => ['phone_number', 'access_token', 'webhook_verify_token'],
            self::MAPS => ['api_key'],
            self::ANALYTICS => ['tracking_id', 'api_key'],
            self::STORAGE => ['access_key', 'secret_key', 'bucket', 'region'],
            self::KYC => ['api_key', 'secret_key'],
            self::LOGISTICS => ['api_key', 'account_number'],
            self::SOCIAL_AUTH => ['client_id', 'client_secret'],
            self::PUSH_NOTIFICATION => ['server_key', 'sender_id'],
            self::WEBHOOK => ['url', 'secret'],
            self::API => ['base_url', 'api_key'],
            self::DATABASE => ['host', 'database', 'username', 'password'],
            self::MONITORING => ['dsn', 'api_key'],
        };
    }

    /**
     * Get optional configuration fields.
     */
    public function getOptionalFields(): array
    {
        return match ($this) {
            self::PAYMENT => ['merchant_id', 'callback_url', 'currency'],
            self::SMS => ['callback_url', 'unicode_support'],
            self::EMAIL => ['reply_to', 'tracking_enabled'],
            self::WHATSAPP => ['business_account_id', 'display_phone_number'],
            self::MAPS => ['language', 'region'],
            self::ANALYTICS => ['custom_dimensions', 'enhanced_ecommerce'],
            self::STORAGE => ['endpoint', 'path_style'],
            self::KYC => ['webhook_url', 'callback_url'],
            self::LOGISTICS => ['webhook_url', 'test_mode'],
            self::SOCIAL_AUTH => ['scope', 'redirect_uri'],
            self::PUSH_NOTIFICATION => ['icon', 'sound'],
            self::WEBHOOK => ['headers', 'retry_attempts'],
            self::API => ['headers', 'timeout'],
            self::DATABASE => ['port', 'ssl_mode'],
            self::MONITORING => ['environment', 'release'],
        };
    }

    /**
     * Check if this integration type supports webhooks.
     */
    public function supportsWebhooks(): bool
    {
        return in_array($this, [
            self::PAYMENT,
            self::SMS,
            self::EMAIL,
            self::WHATSAPP,
            self::KYC,
            self::LOGISTICS,
        ]);
    }

    /**
     * Check if this integration type requires sandbox testing.
     */
    public function requiresSandbox(): bool
    {
        return in_array($this, [
            self::PAYMENT,
            self::KYC,
            self::LOGISTICS,
        ]);
    }
}
