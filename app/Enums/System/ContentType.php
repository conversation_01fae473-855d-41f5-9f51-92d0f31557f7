<?php

declare(strict_types=1);

namespace App\Enums\System;

enum ContentType: string
{
    case PAGE = 'page';
    case ANNOUNCEMENT = 'announcement';
    case FAQ = 'faq';
    case LEGAL = 'legal';
    case HELP = 'help';
    case NEWS = 'news';
    case BLOG = 'blog';
    case LANDING_PAGE = 'landing_page';
    case EMAIL_TEMPLATE = 'email_template';
    case SMS_TEMPLATE = 'sms_template';

    /**
     * Get human-readable label.
     */
    public function label(): string
    {
        return match ($this) {
            self::PAGE => 'Page',
            self::ANNOUNCEMENT => 'Announcement',
            self::FAQ => 'FAQ',
            self::LEGAL => 'Legal Document',
            self::HELP => 'Help Article',
            self::NEWS => 'News Article',
            self::BLOG => 'Blog Post',
            self::LANDING_PAGE => 'Landing Page',
            self::EMAIL_TEMPLATE => 'Email Template',
            self::SMS_TEMPLATE => 'SMS Template',
        };
    }

    /**
     * Get description for the content type.
     */
    public function description(): string
    {
        return match ($this) {
            self::PAGE => 'Static pages like About Us, Contact, etc.',
            self::ANNOUNCEMENT => 'Platform announcements and updates',
            self::FAQ => 'Frequently asked questions',
            self::LEGAL => 'Terms of service, privacy policy, etc.',
            self::HELP => 'Help documentation and guides',
            self::NEWS => 'News articles and updates',
            self::BLOG => 'Blog posts and articles',
            self::LANDING_PAGE => 'Marketing landing pages',
            self::EMAIL_TEMPLATE => 'Email notification templates',
            self::SMS_TEMPLATE => 'SMS notification templates',
        };
    }

    /**
     * Get content types that support versioning.
     */
    public static function versionable(): array
    {
        return [
            self::PAGE,
            self::LEGAL,
            self::HELP,
            self::FAQ,
            self::LANDING_PAGE,
        ];
    }

    /**
     * Get content types that can be featured.
     */
    public static function featurable(): array
    {
        return [
            self::ANNOUNCEMENT,
            self::NEWS,
            self::BLOG,
            self::HELP,
        ];
    }

    /**
     * Get content types that support comments.
     */
    public static function commentable(): array
    {
        return [
            self::NEWS,
            self::BLOG,
            self::ANNOUNCEMENT,
        ];
    }

    /**
     * Check if this content type supports versioning.
     */
    public function isVersionable(): bool
    {
        return in_array($this, self::versionable());
    }

    /**
     * Check if this content type can be featured.
     */
    public function isFeaturable(): bool
    {
        return in_array($this, self::featurable());
    }

    /**
     * Check if this content type supports comments.
     */
    public function isCommentable(): bool
    {
        return in_array($this, self::commentable());
    }
}
