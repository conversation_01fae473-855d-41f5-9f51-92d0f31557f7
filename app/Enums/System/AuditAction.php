<?php

declare(strict_types=1);

namespace App\Enums\System;

enum AuditAction: string
{
    case CREATE = 'create';
    case READ = 'read';
    case UPDATE = 'update';
    case DELETE = 'delete';
    case LOGIN = 'login';
    case LOGOUT = 'logout';
    case EXPORT = 'export';
    case IMPORT = 'import';
    case APPROVE = 'approve';
    case REJECT = 'reject';
    case SUSPEND = 'suspend';
    case ACTIVATE = 'activate';
    case DEACTIVATE = 'deactivate';
    case ASSIGN = 'assign';
    case UNASSIGN = 'unassign';
    case PUBLISH = 'publish';
    case UNPUBLISH = 'unpublish';
    case ARCHIVE = 'archive';
    case RESTORE = 'restore';
    case BACKUP = 'backup';
    case SYNC = 'sync';

    /**
     * Get human-readable label.
     */
    public function label(): string
    {
        return match ($this) {
            self::CREATE => 'Create',
            self::READ => 'Read',
            self::UPDATE => 'Update',
            self::DELETE => 'Delete',
            self::LOGIN => 'Login',
            self::LOGOUT => 'Logout',
            self::EXPORT => 'Export',
            self::IMPORT => 'Import',
            self::APPROVE => 'Approve',
            self::REJECT => 'Reject',
            self::SUSPEND => 'Suspend',
            self::ACTIVATE => 'Activate',
            self::DEACTIVATE => 'Deactivate',
            self::ASSIGN => 'Assign',
            self::UNASSIGN => 'Unassign',
            self::PUBLISH => 'Publish',
            self::UNPUBLISH => 'Unpublish',
            self::ARCHIVE => 'Archive',
            self::RESTORE => 'Restore',
            self::BACKUP => 'Backup',
            self::SYNC => 'Sync',
        };
    }

    /**
     * Get color for UI display.
     */
    public function color(): string
    {
        return match ($this) {
            self::CREATE => 'green',
            self::READ => 'blue',
            self::UPDATE => 'yellow',
            self::DELETE => 'red',
            self::LOGIN => 'green',
            self::LOGOUT => 'gray',
            self::EXPORT => 'purple',
            self::IMPORT => 'purple',
            self::APPROVE => 'green',
            self::REJECT => 'red',
            self::SUSPEND => 'orange',
            self::ACTIVATE => 'green',
            self::DEACTIVATE => 'orange',
            self::ASSIGN => 'blue',
            self::UNASSIGN => 'gray',
            self::PUBLISH => 'green',
            self::UNPUBLISH => 'orange',
            self::ARCHIVE => 'gray',
            self::RESTORE => 'blue',
            self::BACKUP => 'purple',
            self::SYNC => 'blue',
        };
    }

    /**
     * Get actions that modify data.
     */
    public static function dataModifyingActions(): array
    {
        return [
            self::CREATE,
            self::UPDATE,
            self::DELETE,
            self::APPROVE,
            self::REJECT,
            self::SUSPEND,
            self::ACTIVATE,
            self::DEACTIVATE,
            self::ASSIGN,
            self::UNASSIGN,
            self::PUBLISH,
            self::UNPUBLISH,
            self::ARCHIVE,
            self::RESTORE,
        ];
    }

    /**
     * Get security-related actions.
     */
    public static function securityActions(): array
    {
        return [
            self::LOGIN,
            self::LOGOUT,
            self::ASSIGN,
            self::UNASSIGN,
            self::SUSPEND,
            self::ACTIVATE,
            self::DEACTIVATE,
        ];
    }

    /**
     * Check if this action modifies data.
     */
    public function isDataModifying(): bool
    {
        return in_array($this, self::dataModifyingActions());
    }

    /**
     * Check if this is a security-related action.
     */
    public function isSecurityAction(): bool
    {
        return in_array($this, self::securityActions());
    }

    /**
     * Get risk level for this action.
     */
    public function getRiskLevel(): int
    {
        return match ($this) {
            self::DELETE, self::SUSPEND => 5,
            self::UPDATE, self::REJECT => 4,
            self::CREATE, self::APPROVE, self::ACTIVATE => 3,
            self::ASSIGN, self::UNASSIGN, self::PUBLISH, self::UNPUBLISH => 2,
            self::READ, self::EXPORT, self::LOGIN, self::LOGOUT => 1,
            default => 2,
        };
    }
}
