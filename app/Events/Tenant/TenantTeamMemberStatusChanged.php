<?php

declare(strict_types=1);

namespace App\Events\Tenant;

use App\Models\Business\Business;
use App\Models\Delivery\DeliveryProvider;
use App\Models\User\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Unified Tenant Team Member Status Changed Event
 *
 * Handles team member status changes for both businesses and delivery providers.
 * Replaces separate TeamMemberStatusChanged and ProviderTeamMemberStatusChanged events.
 */
class TenantTeamMemberStatusChanged implements ShouldBroadcast
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public readonly User $teamMember,
        public readonly Business|DeliveryProvider $tenant,
        public readonly User $changedBy,
        public readonly string $action, // 'activated', 'deactivated', 'role_assigned', 'ownership_transferred', 'removed'
        public readonly ?string $newRole = null,
        public readonly ?string $message = null,
        public readonly array $metadata = []
    ) {}

    /**
     * Get the tenant type (business or provider)
     */
    public function getTenantType(): string
    {
        return $this->tenant instanceof Business ? 'business' : 'provider';
    }

    /**
     * Get the tenant ID
     */
    public function getTenantId(): string
    {
        return $this->tenant->id;
    }

    /**
     * Check if this is a business team member change
     */
    public function isBusinessTeamMember(): bool
    {
        return $this->tenant instanceof Business;
    }

    /**
     * Check if this is a provider team member change
     */
    public function isProviderTeamMember(): bool
    {
        return $this->tenant instanceof DeliveryProvider;
    }

    /**
     * Get action title for notifications
     */
    public function getActionTitle(): string
    {
        return match ($this->action) {
            'activated' => 'Account Activated',
            'deactivated' => 'Account Deactivated',
            'role_assigned' => 'Role Updated',
            'ownership_transferred' => 'Ownership Transferred',
            'removed' => 'Team Membership Ended',
            default => 'Account Status Changed'
        };
    }

    /**
     * Get notification type for styling
     */
    public function getNotificationType(): string
    {
        return match ($this->action) {
            'activated', 'ownership_transferred' => 'success',
            'deactivated', 'removed' => 'warning',
            'role_assigned' => 'info',
            default => 'info'
        };
    }

    /**
     * Get broadcast channels
     */
    public function broadcastOn(): array
    {
        $tenantType = $this->getTenantType();
        $tenantId = $this->getTenantId();

        return [
            new PrivateChannel("user.{$this->teamMember->id}"),
            new PrivateChannel("{$tenantType}.{$tenantId}"),
            new PrivateChannel('admin.tenant.management'),
        ];
    }

    /**
     * Get broadcast data
     */
    public function broadcastWith(): array
    {
        return [
            'team_member_id' => $this->teamMember->id,
            'team_member_name' => $this->teamMember->full_name,
            'team_member_email' => $this->teamMember->email,
            'tenant_type' => $this->getTenantType(),
            'tenant_id' => $this->getTenantId(),
            'tenant_name' => $this->tenant->name ?? $this->tenant->company_name ?? $this->tenant->business_name,
            'action' => $this->action,
            'action_title' => $this->getActionTitle(),
            'notification_type' => $this->getNotificationType(),
            'new_role' => $this->newRole,
            'message' => $this->message,
            'changed_by_id' => $this->changedBy->id,
            'changed_by_name' => $this->changedBy->full_name,
            'changed_at' => now()->toISOString(),
            'metadata' => $this->metadata,
        ];
    }

    /**
     * Get broadcast event name
     */
    public function broadcastAs(): string
    {
        return 'tenant.team.member.status.changed';
    }
}
