<?php

declare(strict_types=1);

namespace App\Events\User;

use App\Models\User\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * User Created By Admin Event
 *
 * Dispatched when an admin manually creates a user account.
 */
class UserCreatedByAdmin
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public readonly User $user,
        public readonly User $createdBy,
        public readonly string $temporaryPassword
    ) {}
}
