<?php

declare(strict_types=1);

namespace App\Events\Business;

use App\Models\Business\Business;
use App\Models\User\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Team Member Status Changed Event
 *
 * Dispatched when a team member's status changes (activated/deactivated).
 */
class TeamMemberStatusChanged
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public readonly User $teamMember,
        public readonly Business $business,
        public readonly User $changedBy,
        public readonly string $action, // 'activated', 'deactivated', 'role_assigned', 'ownership_transferred'
        public readonly ?string $newRole = null,
        public readonly ?string $message = null
    ) {}
}
