<?php

declare(strict_types=1);

namespace App\Events\Delivery;

use App\Models\Delivery\DeliveryProvider;
use App\Models\User\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Provider Team Member Status Changed Event
 *
 * Dispatched when a provider team member's status changes.
 */
class ProviderTeamMemberStatusChanged
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public readonly User $teamMember,
        public readonly DeliveryProvider $provider,
        public readonly User $changedBy,
        public readonly string $action, // 'removed', 'activated', 'deactivated', 'role_changed'
        public readonly ?string $newRole = null,
        public readonly ?string $message = null
    ) {}
}
