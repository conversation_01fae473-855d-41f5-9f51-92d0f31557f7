<?php

declare(strict_types=1);

namespace App\Events\Delivery;

use App\Models\Delivery\DeliveryProvider;
use App\Models\User\User;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

/**
 * Provider Staff Added Event
 *
 * Dispatched when a new staff member is added to a delivery provider.
 */
class ProviderStaffAdded
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public function __construct(
        public readonly User $staffMember,
        public readonly DeliveryProvider $provider,
        public readonly User $addedBy,
        public readonly string $role,
        public readonly string $temporaryPassword
    ) {}
}
