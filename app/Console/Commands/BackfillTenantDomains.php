<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Enums\System\DomainVerificationStatus;
use App\Models\System\Domain;
use App\Models\System\Tenant;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

/**
 * Backfill domains for existing tenants that don't have domains.
 *
 * This command creates domains for tenants that were created before
 * the domain creation process was properly implemented.
 */
class BackfillTenantDomains extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'tenants:backfill-domains 
                            {--dry-run : Show what would be created without actually creating}
                            {--force : Force creation even if domains exist}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create domains for existing tenants that don\'t have any domains';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $dryRun = $this->option('dry-run');
        $force = $this->option('force');

        $this->info('Starting tenant domain backfill process...');

        // Get base domain from config
        $baseDomain = config('domains.base_domain', 'deliverynexus.test');
        $this->info("Using base domain: {$baseDomain}");

        // Get tenants without domains or all if force
        $query = Tenant::with('domains');

        if (! $force) {
            $query->whereDoesntHave('domains');
        }

        $tenants = $query->get();

        if ($tenants->isEmpty()) {
            $this->info('No tenants found that need domain creation.');

            return self::SUCCESS;
        }

        $this->info("Found {$tenants->count()} tenants that need domains.");

        if ($dryRun) {
            $this->warn('DRY RUN MODE - No domains will be created');
        }

        $created = 0;
        $skipped = 0;
        $errors = 0;

        foreach ($tenants as $tenant) {
            try {
                // Skip if tenant already has domains and not forcing
                if (! $force && $tenant->domains->isNotEmpty()) {
                    $this->line("Skipping {$tenant->name} - already has domains");
                    $skipped++;

                    continue;
                }

                // Generate subdomain from tenant name or ID
                $subdomain = $this->generateSubdomain($tenant);
                $fullDomain = $subdomain.'.'.$baseDomain;

                // Check if domain already exists
                $existingDomain = Domain::where('domain', $fullDomain)->first();
                if ($existingDomain && ! $force) {
                    $this->warn("Domain {$fullDomain} already exists for another tenant");
                    $skipped++;

                    continue;
                }

                if ($dryRun) {
                    $this->line("Would create domain: {$fullDomain} for tenant: {$tenant->name}");
                    $created++;

                    continue;
                }

                // Create the domain
                $domain = $tenant->domains()->create([
                    'domain' => $fullDomain,
                    'is_primary' => true,
                    'ssl_enabled' => true,
                    'verification_status' => DomainVerificationStatus::VERIFIED,
                ]);

                $this->info("✓ Created domain {$fullDomain} for tenant: {$tenant->name}");
                $created++;

            } catch (\Exception $e) {
                $this->error("✗ Failed to create domain for tenant {$tenant->name}: {$e->getMessage()}");
                $errors++;
            }
        }

        // Summary
        $this->newLine();
        $this->info('=== SUMMARY ===');
        $this->info("Domains created: {$created}");
        $this->info("Tenants skipped: {$skipped}");
        $this->info("Errors: {$errors}");

        if ($dryRun) {
            $this->warn('This was a dry run. Use --force to actually create the domains.');
        }

        return self::SUCCESS;
    }

    /**
     * Generate a subdomain from tenant name or ID.
     */
    private function generateSubdomain(Tenant $tenant): string
    {
        // For platform tenant, use 'platform'
        if ($tenant->id === 'platform') {
            return 'platform';
        }

        // For tenants with simple IDs (like restaurant_1, provider_1), use the ID
        if (preg_match('/^[a-z_]+_\d+$/', $tenant->id)) {
            return str_replace('_', '-', $tenant->id);
        }

        // For UUID-based tenants, generate from name
        $subdomain = Str::slug($tenant->name);

        // Ensure it's not too long (max 63 chars for subdomain)
        if (strlen($subdomain) > 50) {
            $subdomain = substr($subdomain, 0, 50);
        }

        // If subdomain is empty or invalid, use first 8 chars of tenant ID
        if (empty($subdomain) || ! preg_match('/^[a-z0-9-]+$/', $subdomain)) {
            $subdomain = substr($tenant->id, 0, 8);
        }

        return $subdomain;
    }
}
