<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Analytics Export Request
 *
 * Validates analytics export parameters for business+ users.
 * Supports exporting analytics data in various formats.
 */
class AnalyticsExportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() && $this->user()->can('export-analytics');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Export format and options
            'format' => 'required|string|in:csv,excel,pdf,json',
            'filename' => 'sometimes|string|max:255|regex:/^[a-zA-Z0-9_\-\.]+$/',

            // Analytics type
            'analytics_type' => 'required|string|in:revenue,products,customers,orders,comprehensive',

            // Date range filters
            'date_from' => 'sometimes|date|date_format:Y-m-d',
            'date_to' => 'sometimes|date|date_format:Y-m-d|after_or_equal:date_from',
            'period' => 'sometimes|string|in:week,month,quarter,year',
            'timezone' => 'sometimes|string|max:50',

            // Data selection
            'include_trends' => 'sometimes|boolean',
            'include_comparisons' => 'sometimes|boolean',
            'include_breakdowns' => 'sometimes|boolean',
            'include_forecasts' => 'sometimes|boolean',

            // Specific filters
            'product_categories' => 'sometimes|array',
            'product_categories.*' => 'string|max:255',
            'customer_segments' => 'sometimes|array',
            'customer_segments.*' => 'string|in:high_value,medium_value,low_value,new,returning',
            'order_statuses' => 'sometimes|array',
            'order_statuses.*' => 'string',

            // Aggregation options
            'group_by' => 'sometimes|string|in:day,week,month,quarter,category,product,customer',
            'metrics' => 'sometimes|array',
            'metrics.*' => 'string|in:revenue,orders,customers,average_order_value,conversion_rate,retention_rate',

            // Limits and pagination
            'limit' => 'sometimes|integer|min:1|max:10000',
            'top_n' => 'sometimes|integer|min:1|max:100', // For top products/customers

            // Export options
            'compress' => 'sometimes|boolean',
            'email_when_ready' => 'sometimes|boolean',
            'include_charts' => 'sometimes|boolean', // For PDF exports
            'chart_types' => 'sometimes|array',
            'chart_types.*' => 'string|in:line,bar,pie,area',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'format.required' => 'Export format is required.',
            'format.in' => 'Export format must be one of: csv, excel, pdf, json.',
            'analytics_type.required' => 'Analytics type is required.',
            'analytics_type.in' => 'Analytics type must be one of: revenue, products, customers, orders, comprehensive.',
            'filename.regex' => 'Filename can only contain letters, numbers, underscores, hyphens, and dots.',
            'date_from.date_format' => 'Date from must be in Y-m-d format.',
            'date_to.date_format' => 'Date to must be in Y-m-d format.',
            'date_to.after_or_equal' => 'Date to must be after or equal to date from.',
            'period.in' => 'Period must be one of: week, month, quarter, year.',
            'limit.max' => 'Maximum 10,000 records allowed per export.',
            'top_n.max' => 'Maximum 100 items allowed for top N analysis.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'analytics_type' => 'analytics type',
            'date_from' => 'start date',
            'date_to' => 'end date',
            'top_n' => 'top N limit',
            'chart_types' => 'chart types',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate date range is not too large (max 2 years)
            if ($this->has('date_from') && $this->has('date_to')) {
                $dateFrom = \Carbon\Carbon::parse($this->input('date_from'));
                $dateTo = \Carbon\Carbon::parse($this->input('date_to'));

                if ($dateFrom->diffInDays($dateTo) > 730) {
                    $validator->errors()->add(
                        'date_to',
                        'Date range cannot exceed 2 years.'
                    );
                }
            }

            // Validate filename extension matches format
            if ($this->has('filename') && $this->has('format')) {
                $filename = $this->input('filename');
                $format = $this->input('format');

                $expectedExtensions = [
                    'csv' => 'csv',
                    'excel' => ['xlsx', 'xls'],
                    'pdf' => 'pdf',
                    'json' => 'json',
                ];

                $extension = pathinfo($filename, PATHINFO_EXTENSION);
                $expected = $expectedExtensions[$format];

                if (is_array($expected)) {
                    if (! in_array($extension, $expected)) {
                        $validator->errors()->add(
                            'filename',
                            'Filename extension must be one of: '.implode(', ', $expected)." for {$format} format."
                        );
                    }
                } else {
                    if ($extension !== $expected) {
                        $validator->errors()->add(
                            'filename',
                            "Filename extension must be .{$expected} for {$format} format."
                        );
                    }
                }
            }

            // Validate chart types are only allowed for PDF format
            if ($this->has('chart_types') && $this->input('format') !== 'pdf') {
                $validator->errors()->add(
                    'chart_types',
                    'Chart types are only supported for PDF exports.'
                );
            }

            // Validate include_charts is only for PDF format
            if ($this->input('include_charts', false) && $this->input('format') !== 'pdf') {
                $validator->errors()->add(
                    'include_charts',
                    'Charts are only supported for PDF exports.'
                );
            }
        });
    }

    /**
     * Get the default filename if none provided.
     */
    public function getFilename(): string
    {
        if ($this->has('filename')) {
            return $this->input('filename');
        }

        $format = $this->input('format', 'csv');
        $analyticsType = $this->input('analytics_type', 'analytics');
        $timestamp = now()->format('Y-m-d_H-i-s');

        $extensions = [
            'csv' => 'csv',
            'excel' => 'xlsx',
            'pdf' => 'pdf',
            'json' => 'json',
        ];

        $extension = $extensions[$format] ?? 'csv';

        return "{$analyticsType}_analytics_export_{$timestamp}.{$extension}";
    }

    /**
     * Get the period dates based on input.
     */
    public function getPeriodDates(): array
    {
        if ($this->has('date_from') && $this->has('date_to')) {
            return [
                'start' => \Carbon\Carbon::parse($this->input('date_from'))->startOfDay(),
                'end' => \Carbon\Carbon::parse($this->input('date_to'))->endOfDay(),
            ];
        }

        $period = $this->input('period', 'month');
        $timezone = $this->input('timezone', 'Africa/Lagos');
        $now = \Carbon\Carbon::now($timezone);

        return match ($period) {
            'week' => [
                'start' => $now->copy()->startOfWeek(),
                'end' => $now->copy()->endOfWeek(),
            ],
            'month' => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
            'quarter' => [
                'start' => $now->copy()->startOfQuarter(),
                'end' => $now->copy()->endOfQuarter(),
            ],
            'year' => [
                'start' => $now->copy()->startOfYear(),
                'end' => $now->copy()->endOfYear(),
            ],
            default => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ]
        };
    }
}
