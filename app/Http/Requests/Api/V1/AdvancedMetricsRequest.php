<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Advanced Metrics Request
 *
 * Validates advanced metrics parameters for business+ users.
 * Supports complex analytics queries with multiple dimensions.
 */
class AdvancedMetricsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() && $this->user()->can('view-advanced-metrics');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Time period
            'date_from' => 'sometimes|date|date_format:Y-m-d',
            'date_to' => 'sometimes|date|date_format:Y-m-d|after_or_equal:date_from',
            'period' => 'sometimes|string|in:week,month,quarter,year',
            'timezone' => 'sometimes|string|max:50',
            'compare_period' => 'sometimes|string|in:previous_period,previous_year,custom',
            'compare_date_from' => 'required_if:compare_period,custom|date|date_format:Y-m-d',
            'compare_date_to' => 'required_if:compare_period,custom|date|date_format:Y-m-d|after_or_equal:compare_date_from',

            // Metrics selection
            'metrics' => 'required|array|min:1',
            'metrics.*' => 'string|in:revenue,orders,customers,average_order_value,conversion_rate,retention_rate,customer_lifetime_value,churn_rate,repeat_purchase_rate,order_frequency,revenue_per_customer,profit_margin,inventory_turnover,fulfillment_rate,delivery_time,customer_satisfaction',

            // Dimensions and grouping
            'group_by' => 'sometimes|array',
            'group_by.*' => 'string|in:day,week,month,quarter,year,hour,day_of_week,product_category,customer_segment,order_source,payment_method,delivery_method,geographic_region',
            'segment_by' => 'sometimes|string|in:customer_type,order_value,product_category,geographic_location,acquisition_channel',

            // Filters
            'filters' => 'sometimes|array',
            'filters.product_categories' => 'sometimes|array',
            'filters.product_categories.*' => 'string|max:255',
            'filters.customer_segments' => 'sometimes|array',
            'filters.customer_segments.*' => 'string|in:new,returning,high_value,medium_value,low_value,at_risk,loyal',
            'filters.order_sources' => 'sometimes|array',
            'filters.order_sources.*' => 'string|in:web,mobile,api,pos,phone',
            'filters.payment_methods' => 'sometimes|array',
            'filters.payment_methods.*' => 'string',
            'filters.min_order_value' => 'sometimes|numeric|min:0',
            'filters.max_order_value' => 'sometimes|numeric|min:0|gte:filters.min_order_value',
            'filters.geographic_regions' => 'sometimes|array',
            'filters.geographic_regions.*' => 'string|max:255',

            // Advanced options
            'include_forecasts' => 'sometimes|boolean',
            'forecast_periods' => 'required_if:include_forecasts,true|integer|min:1|max:12',
            'include_anomalies' => 'sometimes|boolean',
            'include_correlations' => 'sometimes|boolean',
            'include_cohort_analysis' => 'sometimes|boolean',
            'cohort_type' => 'required_if:include_cohort_analysis,true|string|in:acquisition,behavioral,revenue',

            // Statistical options
            'confidence_level' => 'sometimes|numeric|min:0.8|max:0.99',
            'statistical_significance' => 'sometimes|boolean',
            'trend_analysis' => 'sometimes|boolean',
            'seasonality_analysis' => 'sometimes|boolean',

            // Output options
            'aggregation_level' => 'sometimes|string|in:raw,daily,weekly,monthly,quarterly,yearly',
            'include_percentiles' => 'sometimes|boolean',
            'percentiles' => 'required_if:include_percentiles,true|array',
            'percentiles.*' => 'integer|min:1|max:99',
            'include_moving_averages' => 'sometimes|boolean',
            'moving_average_periods' => 'required_if:include_moving_averages,true|array',
            'moving_average_periods.*' => 'integer|min:3|max:30',

            // Limits
            'limit' => 'sometimes|integer|min:1|max:10000',
            'top_n' => 'sometimes|integer|min:1|max:100',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'metrics.required' => 'At least one metric is required.',
            'metrics.*.in' => 'Invalid metric selected.',
            'date_from.date_format' => 'Date from must be in Y-m-d format.',
            'date_to.date_format' => 'Date to must be in Y-m-d format.',
            'date_to.after_or_equal' => 'Date to must be after or equal to date from.',
            'compare_date_from.required_if' => 'Comparison start date is required for custom comparison period.',
            'compare_date_to.required_if' => 'Comparison end date is required for custom comparison period.',
            'forecast_periods.required_if' => 'Forecast periods is required when forecasts are enabled.',
            'cohort_type.required_if' => 'Cohort type is required when cohort analysis is enabled.',
            'percentiles.required_if' => 'Percentiles array is required when percentiles are enabled.',
            'moving_average_periods.required_if' => 'Moving average periods are required when moving averages are enabled.',
            'confidence_level.min' => 'Confidence level must be at least 0.8 (80%).',
            'confidence_level.max' => 'Confidence level cannot exceed 0.99 (99%).',
            'limit.max' => 'Maximum 10,000 records allowed.',
            'top_n.max' => 'Maximum 100 items allowed for top N analysis.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'date_from' => 'start date',
            'date_to' => 'end date',
            'compare_date_from' => 'comparison start date',
            'compare_date_to' => 'comparison end date',
            'forecast_periods' => 'forecast periods',
            'cohort_type' => 'cohort type',
            'confidence_level' => 'confidence level',
            'top_n' => 'top N limit',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate date range is not too large (max 2 years)
            if ($this->has('date_from') && $this->has('date_to')) {
                $dateFrom = \Carbon\Carbon::parse($this->input('date_from'));
                $dateTo = \Carbon\Carbon::parse($this->input('date_to'));

                if ($dateFrom->diffInDays($dateTo) > 730) {
                    $validator->errors()->add(
                        'date_to',
                        'Date range cannot exceed 2 years.'
                    );
                }
            }

            // Validate comparison date range
            if ($this->has('compare_date_from') && $this->has('compare_date_to')) {
                $compareFrom = \Carbon\Carbon::parse($this->input('compare_date_from'));
                $compareTo = \Carbon\Carbon::parse($this->input('compare_date_to'));

                if ($compareFrom->diffInDays($compareTo) > 730) {
                    $validator->errors()->add(
                        'compare_date_to',
                        'Comparison date range cannot exceed 2 years.'
                    );
                }
            }

            // Validate percentiles are unique and sorted
            if ($this->has('percentiles')) {
                $percentiles = $this->input('percentiles');
                if (count($percentiles) !== count(array_unique($percentiles))) {
                    $validator->errors()->add(
                        'percentiles',
                        'Percentiles must be unique values.'
                    );
                }
            }

            // Validate moving average periods are reasonable
            if ($this->has('moving_average_periods')) {
                $periods = $this->input('moving_average_periods');
                if (count($periods) > 5) {
                    $validator->errors()->add(
                        'moving_average_periods',
                        'Maximum 5 moving average periods allowed.'
                    );
                }
            }

            // Validate metric combinations
            $metrics = $this->input('metrics', []);
            $complexMetrics = ['customer_lifetime_value', 'churn_rate', 'retention_rate'];
            $hasComplexMetrics = ! empty(array_intersect($metrics, $complexMetrics));

            if ($hasComplexMetrics && count($metrics) > 3) {
                $validator->errors()->add(
                    'metrics',
                    'Complex metrics (CLV, churn rate, retention rate) can only be combined with up to 2 other metrics.'
                );
            }
        });
    }

    /**
     * Get the period dates based on input.
     */
    public function getPeriodDates(): array
    {
        if ($this->has('date_from') && $this->has('date_to')) {
            return [
                'start' => \Carbon\Carbon::parse($this->input('date_from'))->startOfDay(),
                'end' => \Carbon\Carbon::parse($this->input('date_to'))->endOfDay(),
            ];
        }

        $period = $this->input('period', 'month');
        $timezone = $this->input('timezone', 'Africa/Lagos');
        $now = \Carbon\Carbon::now($timezone);

        return match ($period) {
            'week' => [
                'start' => $now->copy()->startOfWeek(),
                'end' => $now->copy()->endOfWeek(),
            ],
            'month' => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
            'quarter' => [
                'start' => $now->copy()->startOfQuarter(),
                'end' => $now->copy()->endOfQuarter(),
            ],
            'year' => [
                'start' => $now->copy()->startOfYear(),
                'end' => $now->copy()->endOfYear(),
            ],
            default => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ]
        };
    }

    /**
     * Get comparison period dates.
     */
    public function getComparisonPeriodDates(): ?array
    {
        $comparePeriod = $this->input('compare_period');

        if (! $comparePeriod) {
            return null;
        }

        if ($comparePeriod === 'custom') {
            return [
                'start' => \Carbon\Carbon::parse($this->input('compare_date_from'))->startOfDay(),
                'end' => \Carbon\Carbon::parse($this->input('compare_date_to'))->endOfDay(),
            ];
        }

        $currentDates = $this->getPeriodDates();
        $start = $currentDates['start'];
        $end = $currentDates['end'];
        $duration = $start->diffInDays($end);

        return match ($comparePeriod) {
            'previous_period' => [
                'start' => $start->copy()->subDays($duration + 1),
                'end' => $start->copy()->subDay(),
            ],
            'previous_year' => [
                'start' => $start->copy()->subYear(),
                'end' => $end->copy()->subYear(),
            ],
            default => null,
        };
    }
}
