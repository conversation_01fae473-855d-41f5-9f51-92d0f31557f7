<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use App\Enums\Delivery\OrderStatus;
use App\Enums\Financial\PaymentStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Order Export Request
 *
 * Validates order export parameters for business+ users.
 * Supports exporting orders in various formats with filtering options.
 */
class OrderExportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() && $this->user()->can('export-orders');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Export format and options
            'format' => 'required|string|in:csv,excel,pdf,json',
            'filename' => 'sometimes|string|max:255|regex:/^[a-zA-Z0-9_\-\.]+$/',

            // Date range filters
            'date_from' => 'sometimes|date|date_format:Y-m-d',
            'date_to' => 'sometimes|date|date_format:Y-m-d|after_or_equal:date_from',
            'date_field' => 'sometimes|string|in:created_at,updated_at,delivered_at,cancelled_at',

            // Status filters
            'status' => ['sometimes', 'array'],
            'status.*' => ['string', Rule::in(array_column(OrderStatus::cases(), 'value'))],
            'payment_status' => ['sometimes', 'array'],
            'payment_status.*' => ['string', Rule::in(array_column(PaymentStatus::cases(), 'value'))],

            // Business and customer filters
            'business_id' => 'sometimes|uuid|exists:businesses,id',
            'customer_id' => 'sometimes|uuid|exists:users,id',
            'delivery_provider_id' => 'sometimes|uuid|exists:delivery_providers,id',

            // Amount filters
            'min_amount' => 'sometimes|numeric|min:0',
            'max_amount' => 'sometimes|numeric|min:0|gte:min_amount',

            // Search filters
            'search' => 'sometimes|string|max:255',
            'order_reference' => 'sometimes|string|max:255',

            // Data selection
            'fields' => 'sometimes|array',
            'fields.*' => 'string|in:id,order_reference,customer_name,customer_email,business_name,status,payment_status,total_amount,delivery_fee,created_at,updated_at,delivered_at,items,delivery_address,pickup_address',
            'include_items' => 'sometimes|boolean',
            'include_addresses' => 'sometimes|boolean',
            'include_delivery_details' => 'sometimes|boolean',
            'include_payment_details' => 'sometimes|boolean',

            // Pagination and limits
            'limit' => 'sometimes|integer|min:1|max:10000', // Max 10k records per export
            'offset' => 'sometimes|integer|min:0',

            // Sorting
            'sort_by' => 'sometimes|string|in:created_at,updated_at,total_amount,status,order_reference',
            'sort_direction' => 'sometimes|string|in:asc,desc',

            // Export options
            'compress' => 'sometimes|boolean', // Compress large exports
            'email_when_ready' => 'sometimes|boolean', // Email download link when ready
            'include_summary' => 'sometimes|boolean', // Include summary statistics
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'format.required' => 'Export format is required.',
            'format.in' => 'Export format must be one of: csv, excel, pdf, json.',
            'filename.regex' => 'Filename can only contain letters, numbers, underscores, hyphens, and dots.',
            'date_from.date_format' => 'Date from must be in Y-m-d format.',
            'date_to.date_format' => 'Date to must be in Y-m-d format.',
            'date_to.after_or_equal' => 'Date to must be after or equal to date from.',
            'status.*.in' => 'Invalid order status.',
            'payment_status.*.in' => 'Invalid payment status.',
            'min_amount.min' => 'Minimum amount must be greater than or equal to 0.',
            'max_amount.gte' => 'Maximum amount must be greater than or equal to minimum amount.',
            'limit.max' => 'Maximum 10,000 records allowed per export.',
            'fields.*.in' => 'Invalid field selection.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'date_from' => 'start date',
            'date_to' => 'end date',
            'min_amount' => 'minimum amount',
            'max_amount' => 'maximum amount',
            'sort_by' => 'sort field',
            'sort_direction' => 'sort direction',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate date range is not too large (max 1 year)
            if ($this->has('date_from') && $this->has('date_to')) {
                $dateFrom = \Carbon\Carbon::parse($this->input('date_from'));
                $dateTo = \Carbon\Carbon::parse($this->input('date_to'));

                if ($dateFrom->diffInDays($dateTo) > 365) {
                    $validator->errors()->add(
                        'date_to',
                        'Date range cannot exceed 365 days.'
                    );
                }
            }

            // Validate filename extension matches format
            if ($this->has('filename') && $this->has('format')) {
                $filename = $this->input('filename');
                $format = $this->input('format');

                $expectedExtensions = [
                    'csv' => 'csv',
                    'excel' => ['xlsx', 'xls'],
                    'pdf' => 'pdf',
                    'json' => 'json',
                ];

                $extension = pathinfo($filename, PATHINFO_EXTENSION);
                $expected = $expectedExtensions[$format];

                if (is_array($expected)) {
                    if (! in_array($extension, $expected)) {
                        $validator->errors()->add(
                            'filename',
                            'Filename extension must be one of: '.implode(', ', $expected)." for {$format} format."
                        );
                    }
                } else {
                    if ($extension !== $expected) {
                        $validator->errors()->add(
                            'filename',
                            "Filename extension must be .{$expected} for {$format} format."
                        );
                    }
                }
            }
        });
    }

    /**
     * Get the default filename if none provided.
     */
    public function getFilename(): string
    {
        if ($this->has('filename')) {
            return $this->input('filename');
        }

        $format = $this->input('format', 'csv');
        $timestamp = now()->format('Y-m-d_H-i-s');

        $extensions = [
            'csv' => 'csv',
            'excel' => 'xlsx',
            'pdf' => 'pdf',
            'json' => 'json',
        ];

        $extension = $extensions[$format] ?? 'csv';

        return "orders_export_{$timestamp}.{$extension}";
    }
}
