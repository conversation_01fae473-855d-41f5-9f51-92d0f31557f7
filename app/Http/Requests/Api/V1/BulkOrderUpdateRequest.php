<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use App\Enums\Delivery\OrderStatus;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Bulk Order Update Request
 *
 * Validates bulk order update data for enterprise users.
 * Supports updating multiple orders in a single API call.
 */
class BulkOrderUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() && $this->user()->can('update-bulk-orders');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'updates' => 'required|array|min:1|max:100', // Limit to 100 orders per batch
            'updates.*.order_id' => 'required|uuid|exists:orders,id',
            'updates.*.status' => ['sometimes', 'string', Rule::in(array_column(OrderStatus::cases(), 'value'))],
            'updates.*.business_notes' => 'sometimes|string|max:1000',
            'updates.*.estimated_preparation_time' => 'sometimes|integer|min:1|max:300',
            'updates.*.cancellation_reason' => 'required_if:updates.*.status,cancelled|string|max:500',
            'updates.*.priority' => 'sometimes|string|in:low,normal,high,urgent',
            'updates.*.special_instructions' => 'sometimes|string|max:1000',
            'updates.*.scheduled_for' => 'sometimes|date|after:now',
            'updates.*.delivery_provider_id' => 'sometimes|uuid|exists:delivery_providers,id',
            'updates.*.metadata' => 'sometimes|array',

            // Batch options
            'batch_options' => 'sometimes|array',
            'batch_options.send_notifications' => 'sometimes|boolean',
            'batch_options.validate_transitions' => 'sometimes|boolean',
            'batch_options.fail_on_error' => 'sometimes|boolean', // Stop processing on first error
            'batch_options.update_reason' => 'sometimes|string|max:255',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'updates.required' => 'At least one order update is required.',
            'updates.max' => 'Maximum 100 order updates allowed per batch.',
            'updates.*.order_id.required' => 'Order ID is required for each update.',
            'updates.*.order_id.exists' => 'Order does not exist.',
            'updates.*.status.in' => 'Invalid order status.',
            'updates.*.cancellation_reason.required_if' => 'Cancellation reason is required when status is cancelled.',
            'updates.*.estimated_preparation_time.min' => 'Estimated preparation time must be at least 1 minute.',
            'updates.*.estimated_preparation_time.max' => 'Estimated preparation time cannot exceed 300 minutes.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'updates.*.order_id' => 'order ID',
            'updates.*.status' => 'status',
            'updates.*.business_notes' => 'business notes',
            'updates.*.estimated_preparation_time' => 'estimated preparation time',
            'updates.*.cancellation_reason' => 'cancellation reason',
            'updates.*.priority' => 'priority',
            'updates.*.special_instructions' => 'special instructions',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $updates = $this->input('updates', []);

            foreach ($updates as $index => $update) {
                // Validate status transitions if validation is enabled
                if ($this->input('batch_options.validate_transitions', true) && isset($update['status'])) {
                    $this->validateStatusTransition($validator, $index, $update);
                }

                // Validate required fields based on status
                $this->validateStatusRequiredFields($validator, $index, $update);
            }
        });
    }

    /**
     * Validate status transition for an order update.
     */
    private function validateStatusTransition($validator, int $index, array $update): void
    {
        try {
            $order = \App\Models\Delivery\Order::find($update['order_id']);
            if (! $order) {
                return; // Order existence already validated
            }

            $currentStatus = $order->status;
            $newStatus = OrderStatus::from($update['status']);

            // Define valid transitions
            $validTransitions = [
                OrderStatus::PENDING->value => [OrderStatus::CONFIRMED->value, OrderStatus::CANCELLED->value],
                OrderStatus::CONFIRMED->value => [OrderStatus::PREPARING->value, OrderStatus::CANCELLED->value],
                OrderStatus::PREPARING->value => [OrderStatus::READY->value, OrderStatus::CANCELLED->value],
                OrderStatus::READY->value => [OrderStatus::PICKED_UP->value, OrderStatus::CANCELLED->value],
                OrderStatus::PICKED_UP->value => [OrderStatus::IN_TRANSIT->value, OrderStatus::CANCELLED->value],
                OrderStatus::IN_TRANSIT->value => [OrderStatus::DELIVERED->value, OrderStatus::CANCELLED->value],
                OrderStatus::DELIVERED->value => [], // Final state
                OrderStatus::CANCELLED->value => [], // Final state
            ];

            $allowedStatuses = $validTransitions[$currentStatus->value] ?? [];

            if (! in_array($newStatus->value, $allowedStatuses)) {
                $validator->errors()->add(
                    "updates.{$index}.status",
                    "Invalid status transition from {$currentStatus->value} to {$newStatus->value}."
                );
            }

        } catch (\Exception $e) {
            $validator->errors()->add(
                "updates.{$index}.status",
                'Invalid status value.'
            );
        }
    }

    /**
     * Validate required fields based on status.
     */
    private function validateStatusRequiredFields($validator, int $index, array $update): void
    {
        $status = $update['status'] ?? null;

        if ($status === OrderStatus::CANCELLED->value) {
            if (empty($update['cancellation_reason'])) {
                $validator->errors()->add(
                    "updates.{$index}.cancellation_reason",
                    'Cancellation reason is required when cancelling an order.'
                );
            }
        }

        if ($status === OrderStatus::PREPARING->value) {
            if (empty($update['estimated_preparation_time'])) {
                $validator->errors()->add(
                    "updates.{$index}.estimated_preparation_time",
                    'Estimated preparation time is required when setting status to preparing.'
                );
            }
        }
    }
}
