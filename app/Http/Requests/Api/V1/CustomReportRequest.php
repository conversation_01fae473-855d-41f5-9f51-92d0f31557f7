<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Custom Report Request
 *
 * Validates custom report parameters for enterprise users.
 * Supports building custom analytics reports with flexible configurations.
 */
class CustomReportRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() && $this->user()->can('create-custom-reports');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Report configuration
            'report_name' => 'required|string|max:255',
            'report_description' => 'sometimes|string|max:1000',
            'report_type' => 'required|string|in:dashboard,detailed,summary,comparison,trend_analysis,cohort,funnel',

            // Data sources
            'data_sources' => 'required|array|min:1',
            'data_sources.*' => 'string|in:orders,products,customers,payments,deliveries,inventory,reviews,promotions,staff,analytics_events',

            // Time configuration
            'date_from' => 'sometimes|date|date_format:Y-m-d',
            'date_to' => 'sometimes|date|date_format:Y-m-d|after_or_equal:date_from',
            'period' => 'sometimes|string|in:day,week,month,quarter,year',
            'timezone' => 'sometimes|string|max:50',
            'auto_refresh' => 'sometimes|boolean',
            'refresh_interval' => 'required_if:auto_refresh,true|string|in:hourly,daily,weekly,monthly',

            // Metrics and dimensions
            'metrics' => 'required|array|min:1|max:20',
            'metrics.*.name' => 'required|string|max:100',
            'metrics.*.type' => 'required|string|in:sum,count,avg,min,max,distinct_count,percentage,ratio,growth_rate,conversion_rate',
            'metrics.*.field' => 'required|string|max:100',
            'metrics.*.label' => 'sometimes|string|max:100',
            'metrics.*.format' => 'sometimes|string|in:number,currency,percentage,decimal,integer',
            'metrics.*.aggregation_level' => 'sometimes|string|in:raw,daily,weekly,monthly,quarterly,yearly',

            'dimensions' => 'sometimes|array|max:10',
            'dimensions.*.name' => 'required|string|max:100',
            'dimensions.*.field' => 'required|string|max:100',
            'dimensions.*.label' => 'sometimes|string|max:100',
            'dimensions.*.type' => 'required|string|in:categorical,temporal,numerical,geographical',
            'dimensions.*.grouping' => 'sometimes|string|in:day,week,month,quarter,year,category,region,segment',

            // Filters
            'filters' => 'sometimes|array|max:20',
            'filters.*.field' => 'required|string|max:100',
            'filters.*.operator' => 'required|string|in:equals,not_equals,greater_than,less_than,greater_equal,less_equal,contains,not_contains,in,not_in,between,is_null,is_not_null',
            'filters.*.value' => 'required_unless:filters.*.operator,is_null,is_not_null',
            'filters.*.label' => 'sometimes|string|max:100',
            'filters.*.type' => 'required|string|in:string,number,date,boolean,array',

            // Sorting and limits
            'sort_by' => 'sometimes|array|max:5',
            'sort_by.*.field' => 'required|string|max:100',
            'sort_by.*.direction' => 'required|string|in:asc,desc',
            'limit' => 'sometimes|integer|min:1|max:10000',
            'offset' => 'sometimes|integer|min:0',

            // Visualization
            'visualizations' => 'sometimes|array|max:10',
            'visualizations.*.type' => 'required|string|in:table,line_chart,bar_chart,pie_chart,area_chart,scatter_plot,heatmap,gauge,funnel_chart,cohort_table',
            'visualizations.*.title' => 'required|string|max:255',
            'visualizations.*.metrics' => 'required|array|min:1',
            'visualizations.*.metrics.*' => 'string|max:100',
            'visualizations.*.dimensions' => 'sometimes|array',
            'visualizations.*.dimensions.*' => 'string|max:100',
            'visualizations.*.config' => 'sometimes|array',

            // Output options
            'output_format' => 'sometimes|string|in:json,csv,excel,pdf',
            'include_raw_data' => 'sometimes|boolean',
            'include_summary' => 'sometimes|boolean',
            'include_metadata' => 'sometimes|boolean',

            // Sharing and permissions
            'is_public' => 'sometimes|boolean',
            'shared_with' => 'sometimes|array',
            'shared_with.*' => 'uuid|exists:users,id',
            'access_level' => 'sometimes|string|in:view,edit,admin',

            // Scheduling
            'schedule_enabled' => 'sometimes|boolean',
            'schedule_frequency' => 'required_if:schedule_enabled,true|string|in:daily,weekly,monthly,quarterly',
            'schedule_time' => 'required_if:schedule_enabled,true|date_format:H:i',
            'schedule_recipients' => 'required_if:schedule_enabled,true|array',
            'schedule_recipients.*' => 'email',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'report_name.required' => 'Report name is required.',
            'report_type.required' => 'Report type is required.',
            'data_sources.required' => 'At least one data source is required.',
            'data_sources.min' => 'At least one data source is required.',
            'metrics.required' => 'At least one metric is required.',
            'metrics.min' => 'At least one metric is required.',
            'metrics.max' => 'Maximum 20 metrics allowed per report.',
            'dimensions.max' => 'Maximum 10 dimensions allowed per report.',
            'filters.max' => 'Maximum 20 filters allowed per report.',
            'sort_by.max' => 'Maximum 5 sort fields allowed.',
            'visualizations.max' => 'Maximum 10 visualizations allowed per report.',
            'limit.max' => 'Maximum 10,000 records allowed per report.',
            'refresh_interval.required_if' => 'Refresh interval is required when auto-refresh is enabled.',
            'schedule_frequency.required_if' => 'Schedule frequency is required when scheduling is enabled.',
            'schedule_time.required_if' => 'Schedule time is required when scheduling is enabled.',
            'schedule_recipients.required_if' => 'Schedule recipients are required when scheduling is enabled.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'report_name' => 'report name',
            'report_type' => 'report type',
            'data_sources' => 'data sources',
            'date_from' => 'start date',
            'date_to' => 'end date',
            'refresh_interval' => 'refresh interval',
            'schedule_frequency' => 'schedule frequency',
            'schedule_time' => 'schedule time',
            'schedule_recipients' => 'schedule recipients',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate date range is not too large (max 2 years)
            if ($this->has('date_from') && $this->has('date_to')) {
                $dateFrom = \Carbon\Carbon::parse($this->input('date_from'));
                $dateTo = \Carbon\Carbon::parse($this->input('date_to'));

                if ($dateFrom->diffInDays($dateTo) > 730) {
                    $validator->errors()->add(
                        'date_to',
                        'Date range cannot exceed 2 years.'
                    );
                }
            }

            // Validate metric names are unique
            $metrics = $this->input('metrics', []);
            $metricNames = array_column($metrics, 'name');
            if (count($metricNames) !== count(array_unique($metricNames))) {
                $validator->errors()->add(
                    'metrics',
                    'Metric names must be unique within the report.'
                );
            }

            // Validate dimension names are unique
            $dimensions = $this->input('dimensions', []);
            $dimensionNames = array_column($dimensions, 'name');
            if (count($dimensionNames) !== count(array_unique($dimensionNames))) {
                $validator->errors()->add(
                    'dimensions',
                    'Dimension names must be unique within the report.'
                );
            }

            // Validate visualization metrics exist in report metrics
            $visualizations = $this->input('visualizations', []);
            $reportMetricNames = array_column($metrics, 'name');

            foreach ($visualizations as $index => $viz) {
                $vizMetrics = $viz['metrics'] ?? [];
                $invalidMetrics = array_diff($vizMetrics, $reportMetricNames);

                if (! empty($invalidMetrics)) {
                    $validator->errors()->add(
                        "visualizations.{$index}.metrics",
                        'Visualization metrics must be defined in the report metrics: '.implode(', ', $invalidMetrics)
                    );
                }
            }

            // Validate report complexity (prevent overly complex reports)
            $complexity = count($metrics) + count($dimensions) + count($this->input('filters', []));
            if ($complexity > 50) {
                $validator->errors()->add(
                    'metrics',
                    'Report is too complex. Total metrics, dimensions, and filters cannot exceed 50.'
                );
            }
        });
    }

    /**
     * Get the period dates based on input.
     */
    public function getPeriodDates(): array
    {
        if ($this->has('date_from') && $this->has('date_to')) {
            return [
                'start' => \Carbon\Carbon::parse($this->input('date_from'))->startOfDay(),
                'end' => \Carbon\Carbon::parse($this->input('date_to'))->endOfDay(),
            ];
        }

        $period = $this->input('period', 'month');
        $timezone = $this->input('timezone', 'Africa/Lagos');
        $now = \Carbon\Carbon::now($timezone);

        return match ($period) {
            'day' => [
                'start' => $now->copy()->startOfDay(),
                'end' => $now->copy()->endOfDay(),
            ],
            'week' => [
                'start' => $now->copy()->startOfWeek(),
                'end' => $now->copy()->endOfWeek(),
            ],
            'month' => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
            'quarter' => [
                'start' => $now->copy()->startOfQuarter(),
                'end' => $now->copy()->endOfQuarter(),
            ],
            'year' => [
                'start' => $now->copy()->startOfYear(),
                'end' => $now->copy()->endOfYear(),
            ],
            default => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ]
        };
    }
}
