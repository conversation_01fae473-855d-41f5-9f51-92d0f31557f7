<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1\User;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Request for deactivating user account.
 */
class DeactivateAccountRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'password' => 'required|string|min:8',
            'reason' => 'sometimes|nullable|string|max:500',
        ];
    }

    /**
     * Get the body parameters for API documentation.
     */
    public function bodyParameters(): array
    {
        return [
            'password' => [
                'description' => 'Current password for verification',
                'example' => 'password123',
            ],
            'reason' => [
                'description' => 'Optional reason for account deactivation',
                'example' => 'No longer needed',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'password.required' => 'Password is required to deactivate your account.',
            'password.min' => 'Password must be at least 8 characters long.',
            'reason.max' => 'Reason cannot exceed 500 characters.',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Trim reason if provided
        if ($this->has('reason')) {
            $this->merge(['reason' => trim($this->input('reason'))]);
        }
    }
}
