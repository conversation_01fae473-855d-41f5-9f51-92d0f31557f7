<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Tracking Subscription Request
 *
 * Validates webhook subscription parameters for enterprise users.
 * Supports subscribing to delivery tracking updates via webhooks.
 */
class TrackingSubscriptionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() && $this->user()->can('subscribe-to-tracking-webhooks');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            // Webhook configuration
            'webhook_url' => 'required|url|max:2048',
            'webhook_secret' => 'sometimes|string|min:16|max:255',
            'webhook_name' => 'sometimes|string|max:255',
            'webhook_description' => 'sometimes|string|max:1000',

            // Subscription settings
            'events' => 'required|array|min:1',
            'events.*' => 'string|in:location_updated,status_changed,delivery_started,delivery_completed,delivery_failed,eta_updated,driver_assigned,driver_unassigned,route_optimized,exception_occurred',

            // Delivery filters
            'delivery_filters' => 'sometimes|array',
            'delivery_filters.delivery_ids' => 'sometimes|array',
            'delivery_filters.delivery_ids.*' => 'uuid|exists:deliveries,id',
            'delivery_filters.order_ids' => 'sometimes|array',
            'delivery_filters.order_ids.*' => 'uuid|exists:orders,id',
            'delivery_filters.driver_ids' => 'sometimes|array',
            'delivery_filters.driver_ids.*' => 'uuid|exists:users,id',
            'delivery_filters.provider_ids' => 'sometimes|array',
            'delivery_filters.provider_ids.*' => 'uuid|exists:delivery_providers,id',
            'delivery_filters.statuses' => 'sometimes|array',
            'delivery_filters.statuses.*' => 'string',
            'delivery_filters.priority_levels' => 'sometimes|array',
            'delivery_filters.priority_levels.*' => 'string|in:low,normal,high,urgent',

            // Geographic filters
            'geographic_filters' => 'sometimes|array',
            'geographic_filters.states' => 'sometimes|array',
            'geographic_filters.states.*' => 'string|max:255',
            'geographic_filters.cities' => 'sometimes|array',
            'geographic_filters.cities.*' => 'string|max:255',
            'geographic_filters.zones' => 'sometimes|array',
            'geographic_filters.zones.*' => 'uuid|exists:delivery_zones,id',
            'geographic_filters.radius_km' => 'sometimes|numeric|min:0.1|max:1000',
            'geographic_filters.center_lat' => 'required_with:geographic_filters.radius_km|numeric|between:-90,90',
            'geographic_filters.center_lng' => 'required_with:geographic_filters.radius_km|numeric|between:-180,180',

            // Update frequency and batching
            'update_frequency' => 'sometimes|string|in:real_time,every_30_seconds,every_minute,every_5_minutes,every_15_minutes',
            'batch_updates' => 'sometimes|boolean',
            'batch_size' => 'required_if:batch_updates,true|integer|min:1|max:100',
            'batch_timeout_seconds' => 'required_if:batch_updates,true|integer|min:1|max:300',

            // Retry and reliability settings
            'retry_attempts' => 'sometimes|integer|min:0|max:10',
            'retry_delay_seconds' => 'sometimes|integer|min:1|max:3600',
            'timeout_seconds' => 'sometimes|integer|min:5|max:120',
            'verify_ssl' => 'sometimes|boolean',

            // Data inclusion settings
            'include_location_history' => 'sometimes|boolean',
            'include_delivery_details' => 'sometimes|boolean',
            'include_customer_info' => 'sometimes|boolean',
            'include_driver_info' => 'sometimes|boolean',
            'include_eta_calculations' => 'sometimes|boolean',
            'include_route_information' => 'sometimes|boolean',

            // Security and authentication
            'auth_type' => 'sometimes|string|in:none,bearer_token,api_key,hmac_signature',
            'auth_token' => 'required_if:auth_type,bearer_token|string|max:500',
            'auth_api_key' => 'required_if:auth_type,api_key|string|max:255',
            'auth_header_name' => 'required_if:auth_type,api_key|string|max:100',

            // Subscription management
            'is_active' => 'sometimes|boolean',
            'expires_at' => 'sometimes|date|after:now',
            'max_deliveries_per_day' => 'sometimes|integer|min:1|max:10000',
            'max_events_per_hour' => 'sometimes|integer|min:1|max:1000',

            // Testing and validation
            'test_webhook' => 'sometimes|boolean',
            'send_test_event' => 'sometimes|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'webhook_url.required' => 'Webhook URL is required.',
            'webhook_url.url' => 'Webhook URL must be a valid URL.',
            'webhook_secret.min' => 'Webhook secret must be at least 16 characters.',
            'events.required' => 'At least one event type is required.',
            'events.min' => 'At least one event type is required.',
            'events.*.in' => 'Invalid event type selected.',
            'delivery_filters.delivery_ids.*.exists' => 'One or more delivery IDs do not exist.',
            'delivery_filters.order_ids.*.exists' => 'One or more order IDs do not exist.',
            'delivery_filters.driver_ids.*.exists' => 'One or more driver IDs do not exist.',
            'delivery_filters.provider_ids.*.exists' => 'One or more provider IDs do not exist.',
            'geographic_filters.radius_km.max' => 'Geographic radius cannot exceed 1000 km.',
            'geographic_filters.center_lat.required_with' => 'Center latitude is required when radius is specified.',
            'geographic_filters.center_lng.required_with' => 'Center longitude is required when radius is specified.',
            'batch_size.required_if' => 'Batch size is required when batch updates are enabled.',
            'batch_timeout_seconds.required_if' => 'Batch timeout is required when batch updates are enabled.',
            'retry_attempts.max' => 'Maximum 10 retry attempts allowed.',
            'timeout_seconds.max' => 'Maximum timeout is 120 seconds.',
            'auth_token.required_if' => 'Auth token is required for bearer token authentication.',
            'auth_api_key.required_if' => 'API key is required for API key authentication.',
            'auth_header_name.required_if' => 'Header name is required for API key authentication.',
            'expires_at.after' => 'Expiration date must be in the future.',
            'max_deliveries_per_day.max' => 'Maximum 10,000 deliveries per day allowed.',
            'max_events_per_hour.max' => 'Maximum 1,000 events per hour allowed.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'webhook_url' => 'webhook URL',
            'webhook_secret' => 'webhook secret',
            'webhook_name' => 'webhook name',
            'update_frequency' => 'update frequency',
            'batch_size' => 'batch size',
            'batch_timeout_seconds' => 'batch timeout',
            'retry_attempts' => 'retry attempts',
            'retry_delay_seconds' => 'retry delay',
            'timeout_seconds' => 'timeout',
            'auth_type' => 'authentication type',
            'auth_token' => 'authentication token',
            'auth_api_key' => 'API key',
            'auth_header_name' => 'header name',
            'expires_at' => 'expiration date',
            'max_deliveries_per_day' => 'daily delivery limit',
            'max_events_per_hour' => 'hourly event limit',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Validate webhook URL is accessible (basic check)
            $webhookUrl = $this->input('webhook_url');
            if ($webhookUrl && ! $this->isValidWebhookUrl($webhookUrl)) {
                $validator->errors()->add(
                    'webhook_url',
                    'Webhook URL appears to be invalid or unreachable.'
                );
            }

            // Validate event combinations
            $events = $this->input('events', []);
            if (in_array('location_updated', $events) && $this->input('update_frequency') === 'real_time') {
                if (count($events) > 3) {
                    $validator->errors()->add(
                        'events',
                        'Real-time location updates can only be combined with up to 2 other event types.'
                    );
                }
            }

            // Validate geographic filters
            if ($this->has('geographic_filters.radius_km')) {
                if (! $this->has('geographic_filters.center_lat') || ! $this->has('geographic_filters.center_lng')) {
                    $validator->errors()->add(
                        'geographic_filters.radius_km',
                        'Center coordinates are required when using radius filtering.'
                    );
                }
            }

            // Validate batch settings
            if ($this->input('batch_updates', false)) {
                $batchSize = $this->input('batch_size', 0);
                $updateFrequency = $this->input('update_frequency', 'real_time');

                if ($updateFrequency === 'real_time' && $batchSize > 10) {
                    $validator->errors()->add(
                        'batch_size',
                        'Batch size cannot exceed 10 for real-time updates.'
                    );
                }
            }

            // Validate subscription limits based on user tier
            $user = $this->user();
            if ($user) {
                $this->validateSubscriptionLimits($validator, $user);
            }
        });
    }

    /**
     * Validate webhook URL accessibility.
     */
    private function isValidWebhookUrl(string $url): bool
    {
        // Basic URL validation
        $parsedUrl = parse_url($url);

        // Must be HTTPS for production
        if (app()->environment('production') && ($parsedUrl['scheme'] ?? '') !== 'https') {
            return false;
        }

        // Must have valid host
        if (empty($parsedUrl['host'])) {
            return false;
        }

        // Block localhost/private IPs in production
        if (app()->environment('production')) {
            $ip = gethostbyname($parsedUrl['host']);
            if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE) === false) {
                return false;
            }
        }

        return true;
    }

    /**
     * Validate subscription limits based on user tier.
     */
    private function validateSubscriptionLimits($validator, $user): void
    {// TODO
        // This would check user's subscription tier and apply appropriate limits
        // For now, basic validation

        $maxDeliveriesPerDay = $this->input('max_deliveries_per_day', 100);
        $maxEventsPerHour = $this->input('max_events_per_hour', 100);

        // Example tier-based limits (would be fetched from subscription service)
        $tierLimits = [
            'starter' => ['daily_deliveries' => 100, 'hourly_events' => 50],
            'business' => ['daily_deliveries' => 1000, 'hourly_events' => 200],
            'enterprise' => ['daily_deliveries' => 10000, 'hourly_events' => 1000],
        ];

        // Would implement actual tier checking here
    }
}
