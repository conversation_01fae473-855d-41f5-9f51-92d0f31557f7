<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1\Admin;

use App\Enums\Financial\SubscriptionTargetType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class UpdatePlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('manage-subscriptions');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $planId = $this->route('plan');

        return [
            'name' => 'sometimes|string|max:255',
            'slug' => [
                'sometimes',
                'string',
                'max:255',
                Rule::unique('subscription_plans', 'slug')->ignore($planId),
            ],
            'description' => 'sometimes|string|max:1000',
            'target_type' => [
                'sometimes',
                'string',
                Rule::enum(SubscriptionTargetType::class),
            ],
            'is_active' => 'sometimes|boolean',

            // Optional pricing data
            'prices' => 'sometimes|array|min:1',
            'prices.*.billing_interval' => 'required_with:prices|string|in:monthly,quarterly,annually',
            'prices.*.price' => 'required_with:prices|numeric|min:0',
            'prices.*.currency' => 'required_with:prices|string|in:NGN,USD',
            'prices.*.is_active' => 'sometimes|boolean',

            // Optional feature data
            'features' => 'sometimes|array',
            'features.*.feature_id' => 'required_with:features|uuid|exists:features,id',
            'features.*.limit' => 'nullable|integer|min:0',
            'features.*.is_enabled' => 'required_with:features|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'slug.unique' => 'Plan slug must be unique',
            'prices.*.billing_interval.required_with' => 'Billing interval is required for each price',
            'prices.*.price.required_with' => 'Price is required for each pricing tier',
            'features.*.feature_id.required_with' => 'Feature ID is required for each feature',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'target_type' => 'target type',
            'prices.*.billing_interval' => 'billing interval',
            'prices.*.price' => 'price',
            'prices.*.currency' => 'currency',
            'features.*.feature_id' => 'feature',
            'features.*.limit' => 'feature limit',
        ];
    }
}
