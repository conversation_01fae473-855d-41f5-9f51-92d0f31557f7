<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1\Admin;

use App\Enums\Financial\SubscriptionTargetType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CreatePlanRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user()->can('manage-subscriptions');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:subscription_plans,slug',
            'description' => 'required|string|max:1000',
            'target_type' => [
                'required',
                'string',
                Rule::enum(SubscriptionTargetType::class),
            ],
            'is_active' => 'sometimes|boolean',

            // Optional pricing data
            'prices' => 'sometimes|array|min:1',
            'prices.*.billing_interval' => 'required_with:prices|string|in:monthly,quarterly,annually',
            'prices.*.price' => 'required_with:prices|numeric|min:0',
            'prices.*.currency' => 'required_with:prices|string|in:NGN,USD',
            'prices.*.is_active' => 'sometimes|boolean',

            // Optional feature data
            'features' => 'sometimes|array',
            'features.*.feature_id' => 'required_with:features|uuid|exists:features,id',
            'features.*.limit' => 'nullable|integer|min:0',
            'features.*.is_enabled' => 'required_with:features|boolean',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Plan name is required',
            'slug.required' => 'Plan slug is required',
            'slug.unique' => 'Plan slug must be unique',
            'description.required' => 'Plan description is required',
            'target_type.required' => 'Target type is required',
            'prices.*.billing_interval.required_with' => 'Billing interval is required for each price',
            'prices.*.price.required_with' => 'Price is required for each pricing tier',
            'features.*.feature_id.required_with' => 'Feature ID is required for each feature',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'target_type' => 'target type',
            'prices.*.billing_interval' => 'billing interval',
            'prices.*.price' => 'price',
            'prices.*.currency' => 'currency',
            'features.*.feature_id' => 'feature',
            'features.*.limit' => 'feature limit',
        ];
    }
}
