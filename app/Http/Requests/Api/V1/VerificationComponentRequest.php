<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use Illuminate\Foundation\Http\FormRequest;

/**
 * Verification Component Request
 *
 * Validates individual verification component data (BVN, NIN, Bank Account).
 */
class VerificationComponentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'type' => ['required', 'string', 'in:bvn,nin,bank_account'],
            'bvn' => ['required_if:type,bvn', 'string', 'regex:/^\d{11}$/'],
            'nin' => ['required_if:type,nin', 'string', 'regex:/^\d{11}$/'],
            'account_number' => ['required_if:type,bank_account', 'string', 'min:10', 'max:10'],
            'bank_code' => ['required_if:type,bank_account', 'string', 'min:3', 'max:6'],
            'customer_data' => ['sometimes', 'array'],
            'customer_data.first_name' => ['sometimes', 'string', 'max:255'],
            'customer_data.last_name' => ['sometimes', 'string', 'max:255'],
            'customer_data.phone_number' => ['sometimes', 'string', 'max:20'],
            'customer_data.date_of_birth' => ['sometimes', 'date_format:Y-m-d'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'type.required' => 'Verification type is required.',
            'type.in' => 'Verification type must be one of: bvn, nin, bank_account.',
            'bvn.required_if' => 'BVN is required for BVN verification.',
            'bvn.regex' => 'BVN must be exactly 11 digits.',
            'nin.required_if' => 'NIN is required for NIN verification.',
            'nin.regex' => 'NIN must be exactly 11 digits.',
            'account_number.required_if' => 'Account number is required for bank account verification.',
            'account_number.min' => 'Account number must be exactly 10 digits.',
            'account_number.max' => 'Account number must be exactly 10 digits.',
            'bank_code.required_if' => 'Bank code is required for bank account verification.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'type' => 'verification type',
            'bvn' => 'Bank Verification Number',
            'nin' => 'National Identification Number',
            'account_number' => 'bank account number',
            'bank_code' => 'bank code',
            'customer_data.first_name' => 'first name',
            'customer_data.last_name' => 'last name',
            'customer_data.phone_number' => 'phone number',
            'customer_data.date_of_birth' => 'date of birth',
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        // Clean and format the data
        if ($this->has('bvn')) {
            $this->merge([
                'bvn' => preg_replace('/\D/', '', $this->bvn), // Remove non-digits
            ]);
        }

        if ($this->has('nin')) {
            $this->merge([
                'nin' => preg_replace('/\D/', '', $this->nin), // Remove non-digits
            ]);
        }

        if ($this->has('account_number')) {
            $this->merge([
                'account_number' => preg_replace('/\D/', '', $this->account_number), // Remove non-digits
            ]);
        }

        if ($this->has('bank_code')) {
            $this->merge([
                'bank_code' => trim($this->bank_code),
            ]);
        }
    }
}
