<?php

declare(strict_types=1);

namespace App\Http\Requests\Api\V1;

use App\Enums\Financial\PaymentMethod;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

/**
 * Bulk Order Creation Request
 *
 * Validates bulk order creation data for enterprise users.
 * Supports creating multiple orders in a single API call.
 */
class BulkOrderRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() && $this->user()->can('create-bulk-orders');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'orders' => 'required|array|min:1|max:100', // Limit to 100 orders per batch
            'orders.*.customer_id' => 'required|uuid|exists:users,id',
            'orders.*.business_id' => 'required|uuid|exists:businesses,id',
            'orders.*.pickup_address_id' => 'required|uuid|exists:addresses,id',
            'orders.*.delivery_address_id' => 'required|uuid|exists:addresses,id',
            'orders.*.payment_method' => ['required', 'string', Rule::in(array_column(PaymentMethod::cases(), 'value'))],
            'orders.*.special_instructions' => 'sometimes|string|max:1000',
            'orders.*.scheduled_for' => 'sometimes|date|after:now',
            'orders.*.priority' => 'sometimes|string|in:low,normal,high,urgent',

            // Order items
            'orders.*.items' => 'required|array|min:1',
            'orders.*.items.*.product_id' => 'required|uuid|exists:products,id',
            'orders.*.items.*.quantity' => 'required|integer|min:1|max:100',
            'orders.*.items.*.unit_price' => 'required|numeric|min:0',
            'orders.*.items.*.special_instructions' => 'sometimes|string|max:500',
            'orders.*.items.*.customizations' => 'sometimes|array',

            // Pricing
            'orders.*.subtotal' => 'required|numeric|min:0',
            'orders.*.tax_amount' => 'sometimes|numeric|min:0',
            'orders.*.delivery_fee' => 'sometimes|numeric|min:0',
            'orders.*.service_fee' => 'sometimes|numeric|min:0',
            'orders.*.discount_amount' => 'sometimes|numeric|min:0',
            'orders.*.total_amount' => 'required|numeric|min:0',

            // Delivery preferences
            'orders.*.preferred_delivery_time' => 'sometimes|date|after:now',
            'orders.*.delivery_window_start' => 'sometimes|date_format:H:i',
            'orders.*.delivery_window_end' => 'sometimes|date_format:H:i|after:orders.*.delivery_window_start',
            'orders.*.delivery_provider_id' => 'sometimes|uuid|exists:delivery_providers,id',

            // Metadata
            'orders.*.external_reference' => 'sometimes|string|max:255',
            'orders.*.source' => 'sometimes|string|max:100',
            'orders.*.metadata' => 'sometimes|array',

            // Batch options
            'batch_options' => 'sometimes|array',
            'batch_options.auto_assign_providers' => 'sometimes|boolean',
            'batch_options.send_notifications' => 'sometimes|boolean',
            'batch_options.validate_inventory' => 'sometimes|boolean',
            'batch_options.fail_on_error' => 'sometimes|boolean', // Stop processing on first error
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'orders.required' => 'At least one order is required.',
            'orders.max' => 'Maximum 100 orders allowed per batch.',
            'orders.*.customer_id.required' => 'Customer ID is required for each order.',
            'orders.*.customer_id.exists' => 'Customer does not exist.',
            'orders.*.business_id.required' => 'Business ID is required for each order.',
            'orders.*.business_id.exists' => 'Business does not exist.',
            'orders.*.items.required' => 'At least one item is required for each order.',
            'orders.*.items.*.product_id.exists' => 'Product does not exist.',
            'orders.*.items.*.quantity.min' => 'Quantity must be at least 1.',
            'orders.*.items.*.quantity.max' => 'Maximum quantity per item is 100.',
            'orders.*.total_amount.required' => 'Total amount is required for each order.',
            'orders.*.total_amount.min' => 'Total amount must be greater than or equal to 0.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'orders.*.customer_id' => 'customer',
            'orders.*.business_id' => 'business',
            'orders.*.pickup_address_id' => 'pickup address',
            'orders.*.delivery_address_id' => 'delivery address',
            'orders.*.payment_method' => 'payment method',
            'orders.*.items.*.product_id' => 'product',
            'orders.*.items.*.quantity' => 'quantity',
            'orders.*.items.*.unit_price' => 'unit price',
            'orders.*.total_amount' => 'total amount',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            $orders = $this->input('orders', []);

            foreach ($orders as $index => $order) {
                // Validate total amount calculation
                $calculatedTotal = $this->calculateOrderTotal($order);
                $providedTotal = $order['total_amount'] ?? 0;

                if (abs($calculatedTotal - $providedTotal) > 0.01) {
                    $validator->errors()->add(
                        "orders.{$index}.total_amount",
                        "Total amount does not match calculated total. Expected: {$calculatedTotal}, Provided: {$providedTotal}"
                    );
                }

                // Validate delivery window
                if (isset($order['delivery_window_start']) && isset($order['delivery_window_end'])) {
                    $start = \Carbon\Carbon::createFromFormat('H:i', $order['delivery_window_start']);
                    $end = \Carbon\Carbon::createFromFormat('H:i', $order['delivery_window_end']);

                    if ($start->gte($end)) {
                        $validator->errors()->add(
                            "orders.{$index}.delivery_window_end",
                            'Delivery window end time must be after start time.'
                        );
                    }
                }
            }
        });
    }

    /**
     * Calculate order total for validation.
     */
    private function calculateOrderTotal(array $order): float
    {
        $subtotal = $order['subtotal'] ?? 0;
        $taxAmount = $order['tax_amount'] ?? 0;
        $deliveryFee = $order['delivery_fee'] ?? 0;
        $serviceFee = $order['service_fee'] ?? 0;
        $discountAmount = $order['discount_amount'] ?? 0;

        return $subtotal + $taxAmount + $deliveryFee + $serviceFee - $discountAmount;
    }
}
