<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use App\Models\User\User;
use App\Services\Financial\ProviderSubscriptionService;
use Closure;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Stancl\Tenancy\Facades\Tenancy;

/**
 * API Access Gate Middleware
 *
 * Gates API access based on subscription tier and endpoint requirements.
 * Ensures users can only access endpoints allowed by their subscription.
 */
class ApiAccessGate
{
    public function __construct(
        private ProviderSubscriptionService $subscriptionService
    ) {}

    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, ?string $requiredTier = null): Response
    {
        $user = $request->user();

        if (! $user) {
            return $this->handleAccessDenied($user, 'Authentication required');
        }

        // Check if API access is enabled for user's subscription
        if (! $this->checkApiAccess($user, $request->path())) {
            return $this->handleAccessDenied($user, 'API access not available for your subscription tier');
        }

        // Check specific tier requirement if provided
        if ($requiredTier && ! $this->checkTierRequirement($user, $requiredTier)) {
            return $this->handleAccessDenied($user, "Subscription tier '{$requiredTier}' required for this endpoint");
        }

        // Check endpoint-specific requirements
        $endpoint = $request->method().':'.$request->path();
        $requiredTierForEndpoint = $this->getRequiredTier($endpoint);

        if ($requiredTierForEndpoint && ! $this->checkTierRequirement($user, $requiredTierForEndpoint)) {
            return $this->handleAccessDenied($user, 'Higher subscription tier required for this endpoint');
        }

        return $next($request);
    }

    /**
     * Check if user has API access for the endpoint.
     */
    public function checkApiAccess(User $user, string $endpoint): bool
    {
        try {
            $userTier = $this->getUserTier($user);
            $tierConfig = config("api_access.tiers.{$userTier}");

            if (! $tierConfig) {
                return false;
            }

            // Check if API access is enabled for this tier
            if (! ($tierConfig['features']['api_access'] ?? false)) {
                return false;
            }

            // Check if endpoint is allowed
            $allowedEndpoints = $tierConfig['allowed_endpoints'] ?? [];

            // If '*' is in allowed endpoints, all endpoints are allowed
            if (in_array('*', $allowedEndpoints)) {
                return true;
            }

            // Check specific endpoint patterns
            $requestMethod = request()->method();
            $fullEndpoint = $requestMethod.':'.$endpoint;

            foreach ($allowedEndpoints as $allowedEndpoint) {
                if ($this->matchesEndpointPattern($fullEndpoint, $allowedEndpoint)) {
                    return true;
                }
            }

            return false;

        } catch (\Exception $e) {
            Log::error('Failed to check API access', [
                'user_id' => $user->id,
                'endpoint' => $endpoint,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }

    /**
     * Get required tier for an endpoint.
     */
    public function getRequiredTier(string $endpoint): ?string
    {
        $endpointRequirements = config('api_access.endpoint_requirements', []);

        foreach ($endpointRequirements as $pattern => $requiredTier) {
            if ($this->matchesEndpointPattern($endpoint, $pattern)) {
                return $requiredTier;
            }
        }

        return null;
    }

    /**
     * Handle access denied response.
     */
    public function handleAccessDenied(?User $user, string $reason): JsonResponse
    {
        $response = [
            'success' => false,
            'message' => 'Access denied',
            'error_code' => 'API_ACCESS_DENIED',
            'reason' => $reason,
            'timestamp' => now()->toISOString(),
        ];

        if ($user) {
            $userTier = $this->getUserTier($user);
            $tierConfig = config("api_access.tiers.{$userTier}");

            $response['current_tier'] = $userTier;
            $response['current_features'] = $tierConfig['features'] ?? [];
            $response['upgrade_options'] = $this->getUpgradeOptions($user);
        }

        Log::warning('API access denied', [
            'user_id' => $user?->id,
            'reason' => $reason,
            'endpoint' => request()->path(),
            'method' => request()->method(),
        ]);

        return response()->json($response, 403);
    }

    /**
     * Check if user meets tier requirement.
     */
    private function checkTierRequirement(User $user, string $requiredTier): bool
    {
        $userTier = $this->getUserTier($user);
        $tierHierarchy = $this->getTierHierarchy();

        $userTierLevel = $tierHierarchy[$userTier] ?? 0;
        $requiredTierLevel = $tierHierarchy[$requiredTier] ?? 999;

        return $userTierLevel >= $requiredTierLevel;
    }

    /**
     * Get user's subscription tier.
     */
    private function getUserTier(User $user): string
    {
        try {
            $tenant = Tenancy::tenant();
            $targetType = 'business'; // Default

            if ($tenant) {
                $targetType = match ($tenant->tenant_type) {
                    'business' => 'business',
                    'delivery_provider' => 'provider',
                    default => 'business',
                };
            } elseif ($user->hasRole(['delivery_provider', 'delivery_driver', 'delivery_rider'])) {
                $targetType = 'provider';
            }

            $subscription = $this->subscriptionService->getCurrentSubscription($user, $targetType);

            if (! $subscription || ! $subscription->isActive()) {
                return 'free';
            }

            $planName = $subscription->subscriptionPlan->name ?? '';

            // Map plan names to API access tiers
            return match (true) {
                str_contains($planName, 'enterprise') => $targetType === 'business' ? 'enterprise_business' : 'enterprise_provider',
                str_contains($planName, 'business') || str_contains($planName, 'pro') => $targetType === 'business' ? 'business_business' : 'business_provider',
                str_contains($planName, 'starter') => $targetType === 'business' ? 'starter_business' : 'starter_provider',
                default => 'free',
            };

        } catch (\Exception $e) {
            Log::error('Failed to get user tier', [
                'user_id' => $user->id,
                'error' => $e->getMessage(),
            ]);

            return 'free';
        }
    }

    /**
     * Get tier hierarchy for comparison.
     */
    private function getTierHierarchy(): array
    {
        return [
            'free' => 0,
            'starter_business' => 1,
            'starter_provider' => 1,
            'business_business' => 2,
            'business_provider' => 2,
            'enterprise_business' => 3,
            'enterprise_provider' => 3,
        ];
    }

    /**
     * Check if endpoint matches pattern.
     */
    private function matchesEndpointPattern(string $endpoint, string $pattern): bool
    {
        // Convert pattern to regex
        $regex = str_replace(['*', '/'], ['.*', '\/'], $pattern);
        $regex = '/^'.$regex.'$/';

        return preg_match($regex, $endpoint) === 1;
    }

    /**
     * Get upgrade options for user.
     */
    private function getUpgradeOptions(User $user): array
    {
        $currentTier = $this->getUserTier($user);
        $tierHierarchy = $this->getTierHierarchy();
        $currentLevel = $tierHierarchy[$currentTier] ?? 0;

        $upgradeOptions = [];

        foreach ($tierHierarchy as $tier => $level) {
            if ($level > $currentLevel) {
                $tierConfig = config("api_access.tiers.{$tier}");
                if ($tierConfig) {
                    $upgradeOptions[] = [
                        'tier' => $tier,
                        'name' => $tierConfig['name'],
                        'description' => $tierConfig['description'],
                        'daily_requests' => $tierConfig['daily_requests'],
                        'features' => array_keys(array_filter($tierConfig['features'])),
                    ];
                }
            }
        }

        return $upgradeOptions;
    }
}
