<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Customer;

use App\Http\Controllers\Controller;
use App\Http\Resources\BusinessResource;
use App\Http\Resources\ProductResource;
use App\Models\Business\Business;
use App\Models\Business\Product;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Business Browsing Controller for customers.
 * Handles customer business discovery and browsing functionality.
 */
class CustomerBusinessBrowsingController extends Controller
{
    use \App\Traits\ApiResponseTrait, \App\Traits\QueryHandlerTrait;

    /**
     * Browse businesses (customer discovery).
     */
    public function index(Request $request): JsonResponse
    {
        $query = Business::query()
            ->where('status', 'active')
            ->where('is_verified', true)
            ->with([
                'owner:id,first_name,last_name,email,phone_number',
                'primaryAddress:id,address_line_1,city,state,country',
                'country:id,name,code',
                'state:id,name,code',
            ]);

        return $this->handleQuery($query, $request, [
            'resource' => BusinessResource::class,
            'message' => 'Businesses retrieved successfully',
            'searchFields' => ['business_name', 'description', 'business_type'],
            'sortFields' => ['business_name', 'created_at', 'rating'],
            'filters' => [
                'business_type' => ['type' => 'exact'],
                'country_id' => ['type' => 'exact'],
                'state_id' => ['type' => 'exact'],
            ],
        ]);
    }

    /**
     * Show business details for customers.
     */
    public function show(Request $request, string $id): JsonResponse
    {
        $query = Business::query()
            ->where('status', 'active')
            ->where('is_verified', true)
            ->with([
                'owner:id,first_name,last_name,email,phone_number',
                'primaryAddress',
                'country:id,name,code',
                'state:id,name,code',
                'operatingHours',
                'categories.products' => function ($q) {
                    $q->where('is_available', true)
                        ->where('stock_quantity', '>', 0);
                },
            ]);

        return $this->handleShow($query, $id, [
            'resource' => BusinessResource::class,
            'message' => 'Business details retrieved successfully',
            'notFoundMessage' => 'Business not found or not available',
        ]);
    }

    /**
     * Get business products for customers.
     */
    public function products(Request $request, string $id): JsonResponse
    {
        $business = Business::where('id', $id)
            ->where('status', 'active')
            ->where('is_verified', true)
            ->first();

        if (! $business) {
            return $this->notFoundResponse('Business not found or not available');
        }

        $query = Product::query()
            ->where('business_id', $id)
            ->where('is_available', true)
            ->where('stock_quantity', '>', 0)
            ->with(['category:id,name', 'business:id,business_name']);

        return $this->handleQuery($query, $request, [
            'resource' => ProductResource::class,
            'message' => 'Business products retrieved successfully',
            'searchFields' => ['name', 'description'],
            'sortFields' => ['name', 'price', 'created_at'],
            'filters' => [
                'category_id' => ['type' => 'exact'],
            ],
        ]);
    }

    /**
     * Get business menu (organized by categories).
     */
    public function menu(Request $request, string $id): JsonResponse
    {
        $business = Business::where('id', $id)
            ->where('status', 'active')
            ->where('is_verified', true)
            ->with([
                'categories' => function ($q) {
                    $q->orderBy('sort_order', 'asc')
                        ->orderBy('name', 'asc');
                },
                'categories.products' => function ($q) {
                    $q->where('is_available', true)
                        ->where('stock_quantity', '>', 0)
                        ->orderBy('sort_order', 'asc')
                        ->orderBy('name', 'asc');
                },
            ])
            ->first();

        if (! $business) {
            return $this->notFoundResponse('Business not found or not available');
        }

        return $this->successResponse([
            'business' => [
                'id' => $business->id,
                'name' => $business->business_name,
                'description' => $business->description,
                'logo_url' => $business->logo_url,
            ],
            'menu' => $business->categories->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name,
                    'description' => $category->description,
                    'products' => ProductResource::collection($category->products),
                ];
            }),
        ], 'Business menu retrieved successfully');
    }
}
