<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Customer;

use App\Http\Controllers\Controller;
use App\Models\Delivery\Order;
use App\Services\Delivery\RealTimeTrackingService;
use Illuminate\Http\JsonResponse;

/**
 * @group Order Tracking (Customer)
 *
 * APIs for customers to track their orders in real-time
 */
class CustomerOrderTrackingController extends Controller
{
    use \App\Traits\ApiResponseTrait;

    public function __construct(
        private readonly RealTimeTrackingService $trackingService
    ) {}

    /**
     * Track Order
     *
     * Get real-time tracking information for a customer's order.
     *
     * @authenticated
     *
     * @urlParam order_id string required The order ID to track. Example: uuid
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Order tracking information retrieved successfully",
     *   "data": {
     *     "order": {
     *       "id": "uuid",
     *       "order_reference": "ORD-2024-ABC123",
     *       "status": "in_transit",
     *       "status_display": "Out for Delivery",
     *       "created_at": "2024-01-15T09:00:00Z",
     *       "estimated_delivery_time": "2024-01-15T11:15:00Z"
     *     },
     *     "business": {
     *       "id": "uuid",
     *       "name": "Mario's Pizza",
     *       "phone": "+2348123456789",
     *       "address": "123 Business St, Lagos"
     *     },
     *     "delivery": {
     *       "id": "uuid",
     *       "status": "in_transit",
     *       "driver": {
     *         "id": "uuid",
     *         "name": "John Driver",
     *         "phone": "+2348987654321"
     *       },
     *       "current_location": {
     *         "latitude": 6.5244,
     *         "longitude": 3.3792,
     *         "timestamp": "2024-01-15T10:30:00Z"
     *       },
     *       "pickup_address": "123 Business St, Lagos",
     *       "delivery_address": "456 Customer Ave, Lagos"
     *     },
     *     "timeline": [
     *       {
     *         "status": "pending",
     *         "timestamp": "2024-01-15T09:00:00Z",
     *         "description": "Order placed"
     *       },
     *       {
     *         "status": "confirmed",
     *         "timestamp": "2024-01-15T09:05:00Z",
     *         "description": "Order confirmed by restaurant"
     *       }
     *     ]
     *   }
     * }
     */
    public function trackOrder(string $orderId): JsonResponse
    {
        // Validate UUID format
        if (! preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $orderId)) {
            return $this->errorResponse('Order not found', 404);
        }

        $order = Order::with([
            'business',
            'delivery.assignedDriver',
        ])->where('id', $orderId)
            ->where('customer_id', auth()->id())
            ->first();

        if (! $order) {
            return $this->errorResponse('Order not found', 404);
        }

        $trackingData = [
            'order' => [
                'id' => $order->id,
                'order_reference' => $order->order_reference,
                'status' => $order->status->value,
                'status_display' => $order->status->getDisplayName(),
                'created_at' => $order->created_at?->toISOString(),
                'estimated_delivery_time' => $order->delivery?->estimated_delivery_time?->toISOString(),
            ],
            'business' => [
                'id' => $order->business_id,
                'name' => $order->business?->business_name,
                'phone' => $order->business?->contact_phone,
                'address' => $order->business?->address?->formatted_address,
            ],
            'timeline' => $this->generateOrderTimeline($order),
        ];

        // Add delivery tracking if order has delivery
        if ($order->delivery) {
            $currentLocation = $this->trackingService->getCurrentDeliveryLocation($order->delivery->id);

            $trackingData['delivery'] = [
                'id' => $order->delivery->id,
                'status' => $order->delivery->status->value,
                'driver' => $order->delivery->assignedDriver ? [
                    'id' => $order->delivery->assignedDriver->id,
                    'name' => $order->delivery->assignedDriver->name,
                    'phone' => $order->delivery->assignedDriver->phone_number,
                ] : null,
                'current_location' => $currentLocation,
                'estimated_delivery_time' => $order->delivery->estimated_delivery_time?->toISOString(),
            ];
        }

        return $this->successResponse(
            $trackingData,
            'Order tracking information retrieved successfully'
        );
    }

    /**
     * Get Order Timeline
     *
     * Get the timeline of status changes for an order.
     *
     * @authenticated
     *
     * @urlParam order_id string required The order ID. Example: uuid
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Order timeline retrieved successfully",
     *   "data": [
     *     {
     *       "status": "pending",
     *       "timestamp": "2024-01-15T09:00:00Z",
     *       "description": "Order placed",
     *       "icon": "clock"
     *     },
     *     {
     *       "status": "confirmed",
     *       "timestamp": "2024-01-15T09:05:00Z",
     *       "description": "Order confirmed by restaurant",
     *       "icon": "check"
     *     },
     *     {
     *       "status": "preparing",
     *       "timestamp": "2024-01-15T09:10:00Z",
     *       "description": "Order is being prepared",
     *       "icon": "cooking"
     *     }
     *   ]
     * }
     */
    public function getOrderTimeline(string $orderId): JsonResponse
    {
        // Validate UUID format
        if (! preg_match('/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i', $orderId)) {
            return $this->errorResponse('Order not found', 404);
        }

        $order = Order::where('id', $orderId)
            ->where('customer_id', auth()->id())
            ->first();

        if (! $order) {
            return $this->errorResponse('Order not found', 404);
        }

        $timeline = $this->generateOrderTimeline($order);

        return $this->successResponse(
            $timeline,
            'Order timeline retrieved successfully'
        );
    }

    /**
     * Get Customer's Active Orders
     *
     * Get all active orders for the current customer that can be tracked.
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Active orders retrieved successfully",
     *   "data": [
     *     {
     *       "order_id": "uuid",
     *       "order_reference": "ORD-2024-ABC123",
     *       "business_name": "Mario's Pizza",
     *       "status": "in_transit",
     *       "status_display": "Out for Delivery",
     *       "estimated_delivery_time": "2024-01-15T11:15:00Z",
     *       "has_live_tracking": true,
     *       "total_amount": "2500.00"
     *     }
     *   ]
     * }
     */
    public function getActiveOrders(): JsonResponse
    {
        $activeOrders = Order::where('customer_id', auth()->id())
            ->whereNotIn('status', ['delivered', 'cancelled'])
            ->with(['business', 'delivery'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($order) {
                $hasLiveTracking = $order->delivery &&
                    $this->trackingService->getCurrentDeliveryLocation($order->delivery->id) !== null;

                return [
                    'order_id' => $order->id,
                    'order_reference' => $order->order_reference,
                    'business_name' => $order->business?->business_name,
                    'status' => $order->status->value,
                    'status_display' => $order->status->getDisplayName(),
                    'estimated_delivery_time' => $order->delivery?->estimated_delivery_time?->toISOString(),
                    'has_live_tracking' => $hasLiveTracking,
                    'total_amount' => $order->total_amount,
                    'created_at' => $order->created_at?->toISOString(),
                ];
            });

        return $this->successResponse(
            $activeOrders,
            'Active orders retrieved successfully'
        );
    }

    /**
     * Generate order timeline from order data.
     */
    private function generateOrderTimeline(Order $order): array
    {
        $timeline = [];

        // Order placed
        $timeline[] = [
            'status' => 'pending',
            'timestamp' => $order->created_at?->toISOString(),
            'description' => 'Order placed',
            'icon' => 'clock',
            'completed' => true,
        ];

        // Order confirmed (if not pending)
        if (! in_array($order->status->value, ['pending'])) {
            $timeline[] = [
                'status' => 'confirmed',
                'timestamp' => $order->updated_at?->toISOString(), // This would ideally be from status history
                'description' => 'Order confirmed by business',
                'icon' => 'check',
                'completed' => true,
            ];
        }

        // Order preparing (if applicable)
        if (in_array($order->status->value, ['preparing', 'ready', 'out_for_delivery', 'delivered'])) {
            $timeline[] = [
                'status' => 'preparing',
                'timestamp' => $order->updated_at?->toISOString(),
                'description' => 'Order is being prepared',
                'icon' => 'cooking',
                'completed' => true,
            ];
        }

        // Order ready (if applicable)
        if (in_array($order->status->value, ['ready', 'out_for_delivery', 'delivered'])) {
            $timeline[] = [
                'status' => 'ready',
                'timestamp' => $order->updated_at?->toISOString(),
                'description' => 'Order is ready',
                'icon' => 'package',
                'completed' => true,
            ];
        }

        // Out for delivery (if applicable)
        if (in_array($order->status->value, ['out_for_delivery', 'delivered'])) {
            $timeline[] = [
                'status' => 'out_for_delivery',
                'timestamp' => $order->delivery?->updated_at?->toISOString(),
                'description' => 'Out for delivery',
                'icon' => 'truck',
                'completed' => true,
            ];
        }

        // Delivered (if applicable)
        if ($order->status->value === 'delivered') {
            $timeline[] = [
                'status' => 'delivered',
                'timestamp' => $order->updated_at?->toISOString(),
                'description' => 'Order delivered',
                'icon' => 'check-circle',
                'completed' => true,
            ];
        }

        return $timeline;
    }
}
