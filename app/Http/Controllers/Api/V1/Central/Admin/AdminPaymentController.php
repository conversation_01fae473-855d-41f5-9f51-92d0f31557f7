<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Enums\Financial\PaymentStatus;
use App\Http\Controllers\Controller;
use App\Models\Financial\Payment;
use App\Services\Financial\PaymentManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Payment Controller
 *
 * Handles cross-tenant payment management for administrators.
 * Provides comprehensive payment oversight and management capabilities.
 */
class AdminPaymentController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly PaymentManagementService $paymentService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all payments across tenants.
     *
     * @group Admin Payments
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Payment::query()
                ->with([
                    'order.customer:id,first_name,last_name,email',
                    'order.business:id,business_name,tenant_id',
                    'subscription.tenant:id,name',
                ]);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'reference',
                    'gateway_reference',
                    'order.customer.first_name',
                    'order.customer.last_name',
                    'order.business.business_name',
                ],
                'sortFields' => [
                    'created_at',
                    'status',
                    'amount',
                    'payment_method',
                ],
                'filters' => [
                    'status' => ['type' => 'exact'],
                    'payment_method' => ['type' => 'exact'],
                    'currency' => ['type' => 'exact'],
                    'gateway' => ['type' => 'exact'],
                    'business_id' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->whereHas('order', function ($q) use ($value) {
                                $q->where('business_id', $value);
                            });
                        },
                    ],
                    'tenant_id' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->where(function ($q) use ($value) {
                                $q->whereHas('order.business', function ($subQ) use ($value) {
                                    $subQ->where('tenant_id', $value);
                                })->orWhereHas('subscription.tenant', function ($subQ) use ($value) {
                                    $subQ->where('id', $value);
                                });
                            });
                        },
                    ],
                    'amount_min' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->where('amount', '>=', $value);
                        },
                    ],
                    'amount_max' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->where('amount', '<=', $value);
                        },
                    ],
                    'date_from' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->where('created_at', '>=', $value);
                        },
                    ],
                    'date_to' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->where('created_at', '<=', $value);
                        },
                    ],
                ],
                'message' => 'Payments retrieved successfully',
                'entityName' => 'payments',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve payments',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve payments');
        }
    }

    /**
     * Get payment details.
     *
     * @group Admin Payments
     *
     * @authenticated
     */
    public function show(Request $request, string $paymentId): JsonResponse
    {
        try {
            $payment = $this->paymentService->getPaymentWithDetails($paymentId);

            return $this->successResponse(
                $payment,
                'Payment details retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve payment details',
                $e,
                ['payment_id' => $paymentId]
            );

            return $this->serverErrorResponse('Failed to retrieve payment details');
        }
    }

    /**
     * Process refund.
     *
     * @group Admin Payments
     *
     * @authenticated
     */
    public function refund(Request $request, string $paymentId): JsonResponse
    {
        $request->validate([
            'amount' => 'required|numeric|min:0',
            'reason' => 'required|string|max:500',
            'refund_type' => 'required|string|in:full,partial',
            'notify_customer' => 'sometimes|boolean',
        ]);

        try {
            $refund = $this->paymentService->processRefund(
                $paymentId,
                $request->input('amount'),
                $request->input('reason'),
                $request->input('refund_type'),
                $request->boolean('notify_customer', true)
            );

            return $this->successResponse(
                $refund,
                'Refund processed successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to process refund',
                $e,
                ['payment_id' => $paymentId]
            );

            return $this->serverErrorResponse('Failed to process refund');
        }
    }

    /**
     * Verify payment with gateway.
     *
     * @group Admin Payments
     *
     * @authenticated
     */
    public function verify(Request $request, string $paymentId): JsonResponse
    {
        try {
            $verification = $this->paymentService->verifyPaymentWithGateway($paymentId);

            return $this->successResponse(
                $verification,
                'Payment verification completed'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to verify payment',
                $e,
                ['payment_id' => $paymentId]
            );

            return $this->serverErrorResponse('Failed to verify payment');
        }
    }

    /**
     * Mark payment as disputed.
     *
     * @group Admin Payments
     *
     * @authenticated
     */
    public function markDisputed(Request $request, string $paymentId): JsonResponse
    {
        $request->validate([
            'dispute_reason' => 'required|string|max:500',
            'dispute_amount' => 'sometimes|numeric|min:0',
            'notes' => 'sometimes|string|max:1000',
        ]);

        try {
            $payment = $this->paymentService->markAsDisputed(
                $paymentId,
                $request->input('dispute_reason'),
                $request->input('dispute_amount'),
                $request->input('notes')
            );

            return $this->successResponse(
                $payment,
                'Payment marked as disputed successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to mark payment as disputed',
                $e,
                ['payment_id' => $paymentId]
            );

            return $this->serverErrorResponse('Failed to mark payment as disputed');
        }
    }

    /**
     * Get payment analytics.
     *
     * @group Admin Payments
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        $request->validate([
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
            'currency' => 'sometimes|string|in:NGN,USD',
            'payment_method' => 'sometimes|string',
            'gateway' => 'sometimes|string',
        ]);

        try {
            $period = $request->input('period', 'month');
            $currency = $request->input('currency', 'NGN');
            $paymentMethod = $request->input('payment_method');
            $gateway = $request->input('gateway');

            $analytics = $this->paymentService->getPaymentAnalytics([
                'period' => $period,
                'currency' => $currency,
                'payment_method' => $paymentMethod,
                'gateway' => $gateway,
            ]);

            return $this->successResponse(
                $analytics,
                'Payment analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve payment analytics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve payment analytics');
        }
    }

    /**
     * Get payment statistics.
     *
     * @group Admin Payments
     *
     * @authenticated
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $statistics = $this->paymentService->getPaymentStatistics();

            return $this->successResponse(
                $statistics,
                'Payment statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve payment statistics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve payment statistics');
        }
    }

    /**
     * Get failed payments.
     *
     * @group Admin Payments
     *
     * @authenticated
     */
    public function failed(Request $request): JsonResponse
    {
        try {
            $query = Payment::query()
                ->where('status', 'failed')
                ->with([
                    'order.customer:id,first_name,last_name,email',
                    'order.business:id,business_name',
                ]);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'reference',
                    'order.customer.first_name',
                    'order.customer.last_name',
                ],
                'sortFields' => [
                    'created_at',
                    'amount',
                ],
                'message' => 'Failed payments retrieved successfully',
                'entityName' => 'failed_payments',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve failed payments',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve failed payments');
        }
    }

    /**
     * Get disputed payments.
     *
     * @group Admin Payments
     *
     * @authenticated
     */
    public function disputed(Request $request): JsonResponse
    {
        try {
            $query = Payment::query()
                ->where('status', 'disputed')
                ->with([
                    'order.customer:id,first_name,last_name,email',
                    'order.business:id,business_name',
                ]);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'reference',
                    'order.customer.first_name',
                    'order.customer.last_name',
                ],
                'sortFields' => [
                    'created_at',
                    'amount',
                ],
                'message' => 'Disputed payments retrieved successfully',
                'entityName' => 'disputed_payments',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve disputed payments',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve disputed payments');
        }
    }

    /**
     * Export payments.
     *
     * @group Admin Payments
     *
     * @authenticated
     */
    public function export(Request $request): JsonResponse
    {
        $request->validate([
            'format' => 'required|string|in:csv,json',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'status' => 'nullable|string',
            'gateway' => 'nullable|string',
            'currency' => 'nullable|string|in:NGN,USD',
        ]);

        try {
            $filename = $this->paymentService->exportPayments(
                array_filter($request->validated(), fn ($value) => $value !== null),
                $request->input('format')
            );

            return $this->successResponse(
                [
                    'filename' => $filename,
                    'download_url' => route('admin.payments.download', ['filename' => $filename]),
                ],
                'Payments exported successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to export payments',
                $e,
                $request->validated()
            );

            return $this->serverErrorResponse('Failed to export payments');
        }
    }

    /**
     * Download exported payments.
     *
     * @group Admin Payments
     *
     * @authenticated
     */
    public function download(string $filename)
    {
        try {
            $filepath = storage_path('app/exports/'.$filename);

            if (! file_exists($filepath)) {
                return $this->notFoundResponse('Export file not found');
            }

            // Security check: ensure filename is safe
            if (! preg_match('/^payments_export_\d{4}_\d{2}_\d{2}_\d{2}_\d{2}_\d{2}\.(csv|json)$/', $filename)) {
                return $this->errorResponse('Invalid filename', 400);
            }

            return response()->download($filepath)->deleteFileAfterSend();

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to download payment export',
                $e,
                ['filename' => $filename]
            );

            return $this->serverErrorResponse('Failed to download payment export');
        }
    }

    /**
     * Get payments requiring attention.
     *
     * @group Admin Payments
     *
     * @authenticated
     */
    public function requiresAttention(): JsonResponse
    {
        try {
            $payments = $this->paymentService->getPaymentsRequiringAttention();

            return $this->successResponse(
                $payments,
                'Payments requiring attention retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve payments requiring attention',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve payments requiring attention');
        }
    }

    /**
     * Get fraud analysis.
     *
     * @group Admin Payments
     *
     * @authenticated
     */
    public function fraudAnalysis(): JsonResponse
    {
        try {
            $analysis = $this->paymentService->getFraudAnalysis();

            return $this->successResponse(
                $analysis,
                'Fraud analysis retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve fraud analysis',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve fraud analysis');
        }
    }

    /**
     * Get gateway performance.
     *
     * @group Admin Payments
     *
     * @authenticated
     */
    public function gatewayPerformance(): JsonResponse
    {
        try {
            $performance = $this->paymentService->getGatewayPerformance();

            return $this->successResponse(
                $performance,
                'Gateway performance retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve gateway performance',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve gateway performance');
        }
    }

    /**
     * Bulk update payment status.
     *
     * @group Admin Payments
     *
     * @authenticated
     */
    public function bulkUpdateStatus(Request $request): JsonResponse
    {
        $request->validate([
            'payment_ids' => 'required|array|min:1|max:100',
            'payment_ids.*' => 'uuid|exists:payments,id',
            'status' => 'required|string|in:pending,paid,failed,cancelled,refunded',
            'reason' => 'nullable|string|max:500',
        ]);

        try {
            $paymentStatus = PaymentStatus::from($request->input('status'));
            $results = $this->paymentService->bulkUpdatePaymentStatus(
                $request->input('payment_ids'),
                $paymentStatus,
                $request->input('reason')
            );

            return $this->successResponse(
                $results,
                "Bulk status update completed: {$results['updated']} updated, {$results['failed']} failed"
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to bulk update payment status',
                $e,
                $request->only(['payment_ids', 'status'])
            );

            return $this->serverErrorResponse('Failed to bulk update payment status');
        }
    }
}
