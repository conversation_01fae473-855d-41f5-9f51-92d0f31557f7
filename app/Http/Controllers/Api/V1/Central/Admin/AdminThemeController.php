<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Services\System\LoggingService;
use App\Services\System\ThemeManagementService;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Theme Controller
 *
 * Handles comprehensive theme and branding management for administrators.
 * Manages theme creation, customization, activation, and multi-tenant theming.
 */
class AdminThemeController extends Controller
{
    use ApiResponseTrait;

    public function __construct(
        private readonly ThemeManagementService $themeService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all themes with filtering.
     *
     * @group Admin Themes
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'status' => 'sometimes|string|in:draft,published,active,archived',
                'type' => 'sometimes|string|in:default,custom,tenant_specific',
                'category' => 'sometimes|string|in:general,professional,seasonal,industry_specific',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'per_page' => 'sometimes|integer|min:1|max:100',
            ]);

            $themes = $this->themeService->getThemes($validated);

            // Paginate results manually since we're working with arrays
            $perPage = $validated['per_page'] ?? 15;
            $page = $request->get('page', 1);
            $offset = ($page - 1) * $perPage;

            $paginatedThemes = array_slice($themes, $offset, $perPage);
            $total = count($themes);

            return $this->successResponse([
                'data' => array_map([$this, 'transformTheme'], $paginatedThemes),
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $total,
                    'last_page' => ceil($total / $perPage),
                    'from' => $offset + 1,
                    'to' => min($offset + $perPage, $total),
                ],
            ], 'Themes retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve themes',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve themes');
        }
    }

    /**
     * Create a new theme.
     *
     * @group Admin Themes
     *
     * @authenticated
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'sometimes|string|max:1000',
                'type' => 'required|string|in:default,custom,tenant_specific',
                'category' => 'sometimes|string|in:general,professional,seasonal,industry_specific',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'is_default' => 'sometimes|boolean',
                'colors' => 'sometimes|array',
                'colors.primary' => 'sometimes|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'colors.secondary' => 'sometimes|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'colors.background' => 'sometimes|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'colors.text' => 'sometimes|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'fonts' => 'sometimes|array',
                'layout' => 'sometimes|array',
                'components' => 'sometimes|array',
                'custom_css' => 'sometimes|string|max:10000',
                'assets' => 'sometimes|array',
                'assets.*' => 'string',
                'metadata' => 'sometimes|array',
            ]);

            $theme = $this->themeService->createTheme($validated);

            return $this->successResponse(
                $this->transformTheme($theme),
                'Theme created successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create theme',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create theme');
        }
    }

    /**
     * Get theme analytics and usage statistics.
     *
     * @group Admin Themes
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'type' => 'sometimes|string|in:default,custom,tenant_specific',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
            ]);

            $analytics = $this->themeService->getThemeAnalytics($validated);

            return $this->successResponse(
                $analytics,
                'Theme analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve theme analytics',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve theme analytics');
        }
    }

    /**
     * Update theme configuration.
     *
     * @group Admin Themes
     *
     * @authenticated
     */
    public function update(Request $request, string $themeId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'sometimes|string|max:255',
                'description' => 'sometimes|string|max:1000',
                'colors' => 'sometimes|array',
                'colors.primary' => 'sometimes|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'colors.secondary' => 'sometimes|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'colors.background' => 'sometimes|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'colors.text' => 'sometimes|string|regex:/^#[0-9A-Fa-f]{6}$/',
                'fonts' => 'sometimes|array',
                'layout' => 'sometimes|array',
                'components' => 'sometimes|array',
                'custom_css' => 'sometimes|string|max:10000',
                'assets' => 'sometimes|array',
                'assets.*' => 'string',
                'status' => 'sometimes|string|in:draft,published,active,archived',
            ]);

            $theme = $this->themeService->updateTheme($themeId, $validated);

            return $this->successResponse(
                $this->transformTheme($theme),
                'Theme updated successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->notFoundResponse('Theme not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update theme',
                $e,
                ['theme_id' => $themeId, 'updates' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update theme');
        }
    }

    /**
     * Activate theme for tenant or platform.
     *
     * @group Admin Themes
     *
     * @authenticated
     */
    public function activate(Request $request, string $themeId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
            ]);

            $theme = $this->themeService->activateTheme(
                $themeId,
                $validated['tenant_id'] ?? null
            );

            return $this->successResponse(
                $this->transformTheme($theme),
                'Theme activated successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to activate theme',
                $e,
                ['theme_id' => $themeId, 'tenant_id' => $request->get('tenant_id')]
            );

            return $this->serverErrorResponse('Failed to activate theme');
        }
    }

    /**
     * Preview theme without activating.
     *
     * @group Admin Themes
     *
     * @authenticated
     */
    public function preview(Request $request, string $themeId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'options' => 'sometimes|array',
                'device' => 'sometimes|string|in:desktop,tablet,mobile',
                'page' => 'sometimes|string|in:dashboard,orders,products,settings',
            ]);

            $preview = $this->themeService->previewTheme($themeId, $validated);

            return $this->successResponse(
                $preview,
                'Theme preview generated successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->notFoundResponse('Theme not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to generate theme preview',
                $e,
                ['theme_id' => $themeId, 'options' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to generate theme preview');
        }
    }

    /**
     * Export theme configuration.
     *
     * @group Admin Themes
     *
     * @authenticated
     */
    public function export(Request $request, string $themeId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'format' => 'sometimes|string|in:json,css,scss',
            ]);

            $export = $this->themeService->exportTheme(
                $themeId,
                $validated['format'] ?? 'json'
            );

            return $this->successResponse(
                $export,
                'Theme exported successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->notFoundResponse('Theme not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to export theme',
                $e,
                ['theme_id' => $themeId, 'format' => $request->get('format')]
            );

            return $this->serverErrorResponse('Failed to export theme');
        }
    }

    /**
     * Import theme from configuration.
     *
     * @group Admin Themes
     *
     * @authenticated
     */
    public function import(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'theme_data' => 'required|array',
                'theme_data.name' => 'required|string|max:255',
                'theme_data.colors' => 'required|array',
                'options' => 'sometimes|array',
                'overwrite_existing' => 'sometimes|boolean',
            ]);

            $theme = $this->themeService->importTheme(
                $validated['theme_data'],
                $validated['options'] ?? []
            );

            return $this->successResponse(
                $this->transformTheme($theme),
                'Theme imported successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to import theme',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to import theme');
        }
    }

    /**
     * Delete theme.
     *
     * @group Admin Themes
     *
     * @authenticated
     */
    public function destroy(Request $request, string $themeId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'reason' => 'sometimes|string|max:500',
            ]);

            $this->themeService->deleteTheme(
                $themeId,
                $validated['reason'] ?? null
            );

            return $this->successResponse(
                null,
                'Theme deleted successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to delete theme',
                $e,
                ['theme_id' => $themeId]
            );

            return $this->serverErrorResponse('Failed to delete theme');
        }
    }

    /**
     * Transform theme for API response.
     */
    public function transformTheme(array $theme): array
    {
        return [
            'id' => $theme['id'],
            'name' => $theme['name'],
            'description' => $theme['description'],
            'type' => $theme['type'],
            'category' => $theme['category'],
            'tenant_id' => $theme['tenant_id'],
            'status' => $theme['status'],
            'is_default' => $theme['is_default'],
            'colors' => $theme['colors'],
            'fonts' => $theme['fonts'],
            'layout' => $theme['layout'],
            'components' => $theme['components'],
            'custom_css' => $theme['custom_css'],
            'assets' => $theme['assets'],
            'preview_url' => $theme['preview_url'],
            'created_at' => $theme['created_at'],
            'created_by' => $theme['created_by'],
            'updated_at' => $theme['updated_at'],
            'version' => $theme['version'],
            'metadata' => $theme['metadata'],
        ];
    }
}
