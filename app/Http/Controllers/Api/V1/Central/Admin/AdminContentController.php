<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Enums\System\ContentStatus;
use App\Enums\System\ContentType;
use App\Http\Controllers\Controller;
use App\Models\System\Content;
use App\Services\System\ContentManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

/**
 * Admin Content Controller
 *
 * Handles CMS content management for administrators.
 * Manages platform content including pages, announcements, legal documents, FAQs, and help articles.
 */
class AdminContentController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly ContentManagementService $contentService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all content.
     *
     * @group Admin Content
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Content::query()
                ->with([
                    'author:id,first_name,last_name,email',
                    'publisher:id,first_name,last_name,email',
                ]);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'title',
                    'excerpt',
                    'content',
                    'meta_title',
                    'meta_description',
                ],
                'sortFields' => [
                    'title',
                    'type',
                    'status',
                    'published_at',
                    'created_at',
                    'view_count',
                    'sort_order',
                ],
                'filters' => [
                    'type' => ['type' => 'exact'],
                    'status' => ['type' => 'exact'],
                    'language' => ['type' => 'exact'],
                    'is_featured' => ['type' => 'boolean'],
                    'allow_comments' => ['type' => 'boolean'],
                    'author_id' => ['type' => 'exact'],
                ],
                'message' => 'Content retrieved successfully',
                'entityName' => 'content',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve content',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve content');
        }
    }

    /**
     * Store new content.
     *
     * @group Admin Content
     *
     * @authenticated
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'title' => 'required|string|max:255',
                'slug' => 'nullable|string|max:255|unique:contents,slug',
                'excerpt' => 'nullable|string|max:500',
                'content' => 'required|string',
                'type' => ['required', Rule::enum(ContentType::class)],
                'status' => ['nullable', Rule::enum(ContentStatus::class)],
                'meta_title' => 'nullable|string|max:255',
                'meta_description' => 'nullable|string|max:500',
                'meta_keywords' => 'nullable|array',
                'meta_keywords.*' => 'string|max:100',
                'featured_image' => 'nullable|string|max:500',
                'settings' => 'nullable|array',
                'is_featured' => 'nullable|boolean',
                'allow_comments' => 'nullable|boolean',
                'language' => 'nullable|string|size:2',
                'sort_order' => 'nullable|integer|min:0',
            ]);

            $content = $this->contentService->createContent($validated);

            return $this->successResponse(
                $content->load(['author:id,first_name,last_name,email']),
                'Content created successfully',
                201
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create content',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create content');
        }
    }

    /**
     * Show specific content.
     *
     * @group Admin Content
     *
     * @authenticated
     */
    public function show(string $id): JsonResponse
    {
        try {
            $contentDetails = $this->contentService->getContentWithDetails($id);

            return $this->successResponse(
                $contentDetails,
                'Content details retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Content not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve content details',
                $e,
                ['content_id' => $id]
            );

            return $this->serverErrorResponse('Failed to retrieve content details');
        }
    }

    /**
     * Update content.
     *
     * @group Admin Content
     *
     * @authenticated
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $validated = $request->validate([
                'title' => 'sometimes|required|string|max:255',
                'slug' => 'sometimes|string|max:255|unique:contents,slug,'.$id,
                'excerpt' => 'nullable|string|max:500',
                'content' => 'sometimes|required|string',
                'type' => ['sometimes', Rule::enum(ContentType::class)],
                'status' => ['sometimes', Rule::enum(ContentStatus::class)],
                'meta_title' => 'nullable|string|max:255',
                'meta_description' => 'nullable|string|max:500',
                'meta_keywords' => 'nullable|array',
                'meta_keywords.*' => 'string|max:100',
                'featured_image' => 'nullable|string|max:500',
                'settings' => 'nullable|array',
                'is_featured' => 'nullable|boolean',
                'allow_comments' => 'nullable|boolean',
                'language' => 'nullable|string|size:2',
                'sort_order' => 'nullable|integer|min:0',
                'change_summary' => 'nullable|string|max:255',
            ]);

            $content = $this->contentService->updateContent($id, $validated);

            return $this->successResponse(
                $content->load(['author:id,first_name,last_name,email']),
                'Content updated successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Content not found');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update content',
                $e,
                ['content_id' => $id, 'request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update content');
        }
    }

    /**
     * Delete content.
     *
     * @group Admin Content
     *
     * @authenticated
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $this->contentService->deleteContent($id);

            return $this->successResponse(
                null,
                'Content deleted successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Content not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to delete content',
                $e,
                ['content_id' => $id]
            );

            return $this->serverErrorResponse('Failed to delete content');
        }
    }

    /**
     * Publish content.
     *
     * @group Admin Content
     *
     * @authenticated
     */
    public function publish(Request $request, string $id): JsonResponse
    {
        try {
            $validated = $request->validate([
                'publish_at' => 'nullable|date|after:now',
            ]);

            $publishAt = isset($validated['publish_at'])
                ? new \DateTime($validated['publish_at'])
                : null;

            $content = $this->contentService->publishContent($id, $publishAt);

            return $this->successResponse(
                $content,
                $publishAt ? 'Content scheduled for publication' : 'Content published successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Content not found');
        } catch (\InvalidArgumentException $e) {
            return $this->badRequestResponse($e->getMessage());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to publish content',
                $e,
                ['content_id' => $id]
            );

            return $this->serverErrorResponse('Failed to publish content');
        }
    }

    /**
     * Archive content.
     *
     * @group Admin Content
     *
     * @authenticated
     */
    public function archive(string $id): JsonResponse
    {
        try {
            $content = $this->contentService->archiveContent($id);

            return $this->successResponse(
                $content,
                'Content archived successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Content not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to archive content',
                $e,
                ['content_id' => $id]
            );

            return $this->serverErrorResponse('Failed to archive content');
        }
    }

    /**
     * Get content by type.
     *
     * @group Admin Content
     *
     * @authenticated
     */
    public function byType(Request $request, string $type): JsonResponse
    {
        try {
            $contentType = ContentType::from($type);
            $limit = $request->integer('limit', 10);

            $content = $this->contentService->getContentByType($contentType, $limit);

            return $this->successResponse(
                $content,
                "Content of type '{$contentType->label()}' retrieved successfully"
            );

        } catch (\ValueError $e) {
            return $this->badRequestResponse('Invalid content type');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve content by type',
                $e,
                ['type' => $type]
            );

            return $this->serverErrorResponse('Failed to retrieve content by type');
        }
    }

    /**
     * Get content statistics.
     *
     * @group Admin Content
     *
     * @authenticated
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = [
                'total_content' => Content::count(),
                'by_status' => Content::selectRaw('status, COUNT(*) as count')
                    ->groupBy('status')
                    ->pluck('count', 'status'),
                'by_type' => Content::selectRaw('type, COUNT(*) as count')
                    ->groupBy('type')
                    ->pluck('count', 'type'),
                'published_content' => Content::where('status', ContentStatus::PUBLISHED)->count(),
                'draft_content' => Content::where('status', ContentStatus::DRAFT)->count(),
                'featured_content' => Content::where('is_featured', true)->count(),
                'total_views' => Content::sum('view_count'),
                'recent_content' => Content::latest()->limit(5)->get(['id', 'title', 'type', 'status', 'created_at']),
            ];

            return $this->successResponse(
                $stats,
                'Content statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve content statistics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve content statistics');
        }
    }
}
