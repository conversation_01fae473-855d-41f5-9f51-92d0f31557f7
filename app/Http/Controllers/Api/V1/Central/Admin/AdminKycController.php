<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Enums\User\KycLevel;
use App\Http\Controllers\Controller;
use App\Models\User\BankAccountVerification;
use App\Models\User\IdentityVerification;
use App\Models\User\KycDocument;
use App\Models\User\User;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * Admin KYC Controller
 *
 * Handles KYC verification management and manual reviews for administrators.
 * Provides comprehensive oversight of user verification processes.
 */
class AdminKycController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get KYC overview statistics.
     *
     * @group Admin KYC
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "KYC overview retrieved successfully",
     *   "data": {
     *     "overview": {
     *       "total_verifications": 1250,
     *       "pending_reviews": 45,
     *       "approved_verifications": 1100,
     *       "rejected_verifications": 105,
     *       "basic_level_users": 800,
     *       "intermediate_level_users": 350,
     *       "advanced_level_users": 100
     *     },
     *     "verification_types": {
     *       "email_verified": 1200,
     *       "phone_verified": 1150,
     *       "bank_verified": 450,
     *       "bvn_verified": 200,
     *       "nin_verified": 150
     *     }
     *   }
     * }
     */
    public function overview(Request $request): JsonResponse
    {
        try {
            $overview = [
                'overview' => $this->getKycOverviewStats(),
                'verification_types' => $this->getVerificationTypeStats(),
                'level_distribution' => $this->getKycLevelDistribution(),
                'recent_activity' => $this->getRecentKycActivity(),
            ];

            return $this->successResponse(
                $overview,
                'KYC overview retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve KYC overview',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve KYC overview');
        }
    }

    /**
     * Get pending KYC reviews.
     *
     * @group Admin KYC
     *
     * @authenticated
     */
    public function pendingReviews(Request $request): JsonResponse
    {
        try {
            $query = KycDocument::query()
                ->with([
                    'verifiable:id,first_name,last_name,email,phone_number',
                ])
                ->where('verification_status', 'pending')
                ->orderBy('created_at', 'asc');

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'document_type',
                    'verifiable.first_name',
                    'verifiable.last_name',
                    'verifiable.email',
                ],
                'sortFields' => [
                    'created_at',
                    'document_type',
                    'verification_status',
                ],
                'filters' => [
                    'document_type' => ['type' => 'exact'],
                    'verifiable_type' => ['type' => 'exact'],
                ],
                'message' => 'Pending KYC reviews retrieved successfully',
                'entityName' => 'pending_reviews',
                'transformer' => [$this, 'transformKycDocument'],
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve pending KYC reviews',
                $e,
                ['request_params' => $request->only(['search', 'document_type'])]
            );

            return $this->serverErrorResponse('Failed to retrieve pending KYC reviews');
        }
    }

    /**
     * Get KYC documents for a specific user.
     *
     * @group Admin KYC
     *
     * @authenticated
     */
    public function userDocuments(Request $request, string $userId): JsonResponse
    {
        try {
            $user = User::find($userId);
            if (! $user) {
                return $this->notFoundResponse('User not found');
            }

            $documents = KycDocument::where('verifiable_type', User::class)
                ->where('verifiable_id', $userId)
                ->with(['verifiable'])
                ->orderBy('created_at', 'desc')
                ->get();

            $bankVerifications = BankAccountVerification::where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->get();

            $identityVerifications = IdentityVerification::where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->get();

            return $this->successResponse([
                'user' => [
                    'id' => $user->id,
                    'name' => $user->first_name.' '.$user->last_name,
                    'email' => $user->email,
                    'phone_number' => $user->phone_number,
                    'kyc_level' => $user->kyc_level ?? KycLevel::BASIC,
                ],
                'documents' => $documents->map([$this, 'transformKycDocument']),
                'bank_verifications' => $bankVerifications,
                'identity_verifications' => $identityVerifications,
                'verification_summary' => $this->getUserVerificationSummary($user),
            ], 'User KYC documents retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve user KYC documents',
                $e,
                ['user_id' => $userId]
            );

            return $this->serverErrorResponse('Failed to retrieve user KYC documents');
        }
    }

    /**
     * Approve KYC document.
     *
     * @group Admin KYC
     *
     * @authenticated
     */
    public function approveDocument(Request $request, string $documentId): JsonResponse
    {
        $request->validate([
            'notes' => 'sometimes|string|max:1000',
        ]);

        try {
            $document = KycDocument::find($documentId);
            if (! $document) {
                return $this->notFoundResponse('KYC document not found');
            }

            if ($document->verification_status !== 'pending') {
                return $this->errorResponse(
                    'Document is not in pending status',
                    422,
                    null,
                    'INVALID_STATUS'
                );
            }

            DB::transaction(function () use ($document, $request) {
                $document->update([
                    'verification_status' => 'approved',
                    'verified_at' => now(),
                    'verified_by' => auth()->id(),
                    'admin_notes' => $request->input('notes'),
                ]);

                // Update user KYC level if applicable
                $this->updateUserKycLevel($document->verifiable);

                // Log the approval
                $this->loggingService->logActivity(
                    'kyc_document_approved',
                    'KYC document approved',
                    [
                        'document_id' => $document->id,
                        'document_type' => $document->document_type,
                        'user_id' => $document->verifiable_id,
                        'approved_by' => auth()->id(),
                        'notes' => $request->input('notes'),
                    ]
                );
            });

            return $this->successResponse(
                $this->transformKycDocument($document->fresh()),
                'KYC document approved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to approve KYC document',
                $e,
                ['document_id' => $documentId]
            );

            return $this->serverErrorResponse('Failed to approve KYC document');
        }
    }

    /**
     * Reject KYC document.
     *
     * @group Admin KYC
     *
     * @authenticated
     */
    public function rejectDocument(Request $request, string $documentId): JsonResponse
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:1000',
            'notes' => 'sometimes|string|max:1000',
        ]);

        try {
            $document = KycDocument::find($documentId);
            if (! $document) {
                return $this->notFoundResponse('KYC document not found');
            }

            if ($document->verification_status !== 'pending') {
                return $this->errorResponse(
                    'Document is not in pending status',
                    422,
                    null,
                    'INVALID_STATUS'
                );
            }

            DB::transaction(function () use ($document, $request) {
                $document->update([
                    'verification_status' => 'rejected',
                    'rejection_reason' => $request->input('rejection_reason'),
                    'verified_by' => auth()->id(),
                    'admin_notes' => $request->input('notes'),
                ]);

                // Log the rejection
                $this->loggingService->logActivity(
                    'kyc_document_rejected',
                    'KYC document rejected',
                    [
                        'document_id' => $document->id,
                        'document_type' => $document->document_type,
                        'user_id' => $document->verifiable_id,
                        'rejected_by' => auth()->id(),
                        'rejection_reason' => $request->input('rejection_reason'),
                        'notes' => $request->input('notes'),
                    ]
                );
            });

            return $this->successResponse(
                $this->transformKycDocument($document->fresh()),
                'KYC document rejected successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to reject KYC document',
                $e,
                ['document_id' => $documentId]
            );

            return $this->serverErrorResponse('Failed to reject KYC document');
        }
    }

    /**
     * Get KYC statistics.
     *
     * @group Admin KYC
     *
     * @authenticated
     */
    public function statistics(Request $request): JsonResponse
    {
        $request->validate([
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
        ]);

        try {
            $period = $request->input('period', 'month');
            $periodDates = $this->getPeriodDates($period);

            $statistics = [
                'verification_stats' => $this->getVerificationStats($periodDates),
                'document_stats' => $this->getDocumentStats($periodDates),
                'level_progression' => $this->getLevelProgressionStats($periodDates),
                'rejection_analysis' => $this->getRejectionAnalysis($periodDates),
            ];

            return $this->successResponse(
                $statistics,
                'KYC statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve KYC statistics',
                $e,
                ['request_params' => $request->only(['period'])]
            );

            return $this->serverErrorResponse('Failed to retrieve KYC statistics');
        }
    }

    /**
     * Transform KYC document for API response.
     */
    public function transformKycDocument(KycDocument $document): array
    {
        return [
            'id' => $document->id,
            'document_type' => $document->document_type,
            'document_number' => $document->document_number,
            'verification_status' => $document->verification_status,
            'verified_at' => $document->verified_at?->toISOString(),
            'rejection_reason' => $document->rejection_reason,
            'admin_notes' => $document->admin_notes,
            'expires_at' => $document->expires_at?->toISOString(),
            'created_at' => $document->created_at->toISOString(),
            'verifiable' => $document->verifiable ? [
                'id' => $document->verifiable->id,
                'name' => $document->verifiable->first_name.' '.$document->verifiable->last_name,
                'email' => $document->verifiable->email,
                'type' => class_basename($document->verifiable_type),
            ] : null,
        ];
    }

    /**
     * Get KYC overview statistics.
     */
    private function getKycOverviewStats(): array
    {
        return [
            'total_verifications' => KycDocument::count(),
            'pending_reviews' => KycDocument::where('verification_status', 'pending')->count(),
            'approved_verifications' => KycDocument::where('verification_status', 'approved')->count(),
            'rejected_verifications' => KycDocument::where('verification_status', 'rejected')->count(),
            'basic_level_users' => User::where('kyc_level', KycLevel::BASIC)->count(),
            'intermediate_level_users' => User::where('kyc_level', KycLevel::INTERMEDIATE)->count(),
            'advanced_level_users' => User::where('kyc_level', KycLevel::ADVANCED)->count(),
        ];
    }

    /**
     * Get verification type statistics.
     */
    private function getVerificationTypeStats(): array
    {
        return [
            'email_verified' => User::whereNotNull('email_verified_at')->count(),
            'phone_verified' => User::whereNotNull('phone_verified_at')->count(),
            'bank_verified' => BankAccountVerification::where('verification_status', 'verified')->count(),
            'bvn_verified' => IdentityVerification::where('verification_type', 'bvn')
                ->where('verification_status', 'verified')->count(),
            'nin_verified' => IdentityVerification::where('verification_type', 'nin')
                ->where('verification_status', 'verified')->count(),
        ];
    }

    /**
     * Get KYC level distribution.
     */
    private function getKycLevelDistribution(): array
    {
        return User::select('kyc_level', DB::raw('COUNT(*) as count'))
            ->groupBy('kyc_level')
            ->get()
            ->pluck('count', 'kyc_level')
            ->toArray();
    }

    /**
     * Get recent KYC activity.
     */
    private function getRecentKycActivity(): array
    {
        return KycDocument::with(['verifiable'])
            ->whereIn('verification_status', ['approved', 'rejected'])
            ->orderBy('updated_at', 'desc')
            ->limit(10)
            ->get()
            ->map([$this, 'transformKycDocument'])
            ->toArray();
    }

    /**
     * Get user verification summary.
     */
    private function getUserVerificationSummary(User $user): array
    {
        return [
            'kyc_level' => $user->kyc_level ?? KycLevel::BASIC,
            'email_verified' => ! is_null($user->email_verified_at),
            'phone_verified' => ! is_null($user->phone_verified_at),
            'documents_count' => KycDocument::where('verifiable_type', User::class)
                ->where('verifiable_id', $user->id)->count(),
            'approved_documents' => KycDocument::where('verifiable_type', User::class)
                ->where('verifiable_id', $user->id)
                ->where('verification_status', 'approved')->count(),
            'bank_verifications_count' => BankAccountVerification::where('user_id', $user->id)->count(),
            'identity_verifications_count' => IdentityVerification::where('user_id', $user->id)->count(),
        ];
    }

    /**
     * Update user KYC level based on completed verifications.
     */
    private function updateUserKycLevel(User $user): void
    {
        $approvedDocuments = KycDocument::where('verifiable_type', User::class)
            ->where('verifiable_id', $user->id)
            ->where('verification_status', 'approved')
            ->count();

        $bankVerified = BankAccountVerification::where('user_id', $user->id)
            ->where('verification_status', 'verified')
            ->exists();

        $identityVerified = IdentityVerification::where('user_id', $user->id)
            ->where('verification_status', 'verified')
            ->exists();

        // Determine KYC level based on verifications
        $newLevel = KycLevel::BASIC;

        if ($bankVerified && $approvedDocuments >= 2) {
            $newLevel = KycLevel::INTERMEDIATE;
        }

        if ($identityVerified && $bankVerified && $approvedDocuments >= 3) {
            $newLevel = KycLevel::ADVANCED;
        }

        if ($user->kyc_level !== $newLevel) {
            $user->update(['kyc_level' => $newLevel]);
        }
    }

    /**
     * Get period dates for statistics.
     */
    private function getPeriodDates(string $period): array
    {
        $now = now();

        return match ($period) {
            'today' => [
                'start' => $now->copy()->startOfDay(),
                'end' => $now->copy()->endOfDay(),
            ],
            'week' => [
                'start' => $now->copy()->startOfWeek(),
                'end' => $now->copy()->endOfWeek(),
            ],
            'month' => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
            'quarter' => [
                'start' => $now->copy()->startOfQuarter(),
                'end' => $now->copy()->endOfQuarter(),
            ],
            'year' => [
                'start' => $now->copy()->startOfYear(),
                'end' => $now->copy()->endOfYear(),
            ],
            default => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
        };
    }

    /**
     * Placeholder methods for statistics.
     */
    private function getVerificationStats(array $periodDates): array
    {
        return ['new_verifications' => 0, 'completed_verifications' => 0];
    }

    private function getDocumentStats(array $periodDates): array
    {
        return ['documents_uploaded' => 0, 'documents_processed' => 0];
    }

    private function getLevelProgressionStats(array $periodDates): array
    {
        return ['level_upgrades' => 0, 'level_downgrades' => 0];
    }

    private function getRejectionAnalysis(array $periodDates): array
    {
        return ['rejection_rate' => 0, 'common_rejection_reasons' => []];
    }
}
