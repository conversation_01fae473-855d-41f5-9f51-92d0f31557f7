<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Models\Communication\Notification;
use App\Services\Communication\NotificationManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Notification Controller
 *
 * Handles platform-wide notification management for administrators.
 * Provides comprehensive notification oversight and management capabilities.
 */
class AdminNotificationController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly NotificationManagementService $notificationService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all notifications across platform.
     *
     * @group Admin Notifications
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Notification::query()
                ->with([
                    'notifiable:id,first_name,last_name,email',
                ]);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'title',
                    'message',
                    'notifiable.first_name',
                    'notifiable.last_name',
                    'notifiable.email',
                ],
                'sortFields' => [
                    'created_at',
                    'type',
                    'priority',
                    'read_at',
                ],
                'filters' => [
                    'type' => ['type' => 'exact'],
                    'priority' => ['type' => 'exact'],
                    'channel' => ['type' => 'exact'],
                    'is_read' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            if ($value) {
                                $query->whereNotNull('read_at');
                            } else {
                                $query->whereNull('read_at');
                            }
                        },
                    ],
                    'notifiable_type' => ['type' => 'exact'],
                    'date_from' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->where('created_at', '>=', $value);
                        },
                    ],
                    'date_to' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->where('created_at', '<=', $value);
                        },
                    ],
                ],
                'message' => 'Notifications retrieved successfully',
                'entityName' => 'notifications',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve notifications',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve notifications');
        }
    }

    /**
     * Get notification details.
     *
     * @group Admin Notifications
     *
     * @authenticated
     */
    public function show(Request $request, string $notificationId): JsonResponse
    {
        try {
            $notification = $this->notificationService->getNotificationWithDetails($notificationId);

            return $this->successResponse(
                $notification,
                'Notification details retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve notification details',
                $e,
                ['notification_id' => $notificationId]
            );

            return $this->serverErrorResponse('Failed to retrieve notification details');
        }
    }

    /**
     * Send broadcast notification.
     *
     * @group Admin Notifications
     *
     * @authenticated
     */
    public function broadcast(Request $request): JsonResponse
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'message' => 'required|string|max:1000',
            'type' => 'required|string|in:announcement,maintenance,promotion,alert',
            'priority' => 'required|string|in:low,medium,high,urgent',
            'channels' => 'required|array|min:1',
            'channels.*' => 'string|in:database,email,sms,push,whatsapp',
            'target_audience' => 'required|string|in:all,businesses,providers,customers',
            'tenant_ids' => 'sometimes|array',
            'tenant_ids.*' => 'string|exists:tenants,id',
            'schedule_at' => 'sometimes|date|after:now',
        ]);

        try {
            $result = $this->notificationService->sendBroadcastNotification($request->validated());

            return $this->successResponse(
                $result,
                'Broadcast notification sent successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to send broadcast notification',
                $e,
                $request->validated()
            );

            return $this->serverErrorResponse('Failed to send broadcast notification');
        }
    }

    /**
     * Get notification templates.
     *
     * @group Admin Notifications
     *
     * @authenticated
     */
    public function templates(Request $request): JsonResponse
    {
        try {
            $templates = $this->notificationService->getNotificationTemplates();

            return $this->successResponse(
                $templates,
                'Notification templates retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve notification templates',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve notification templates');
        }
    }

    /**
     * Create notification template.
     *
     * @group Admin Notifications
     *
     * @authenticated
     */
    public function createTemplate(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:notification_templates,slug',
            'title' => 'required|string|max:255',
            'message' => 'required|string|max:1000',
            'type' => 'required|string|in:system,business,delivery,payment',
            'channels' => 'required|array|min:1',
            'channels.*' => 'string|in:database,email,sms,push,whatsapp',
            'variables' => 'sometimes|array',
        ]);

        try {
            $template = $this->notificationService->createNotificationTemplate($request->validated());

            return $this->createdResponse(
                $template,
                'Notification template created successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create notification template',
                $e,
                $request->validated()
            );

            return $this->serverErrorResponse('Failed to create notification template');
        }
    }

    /**
     * Update notification template.
     *
     * @group Admin Notifications
     *
     * @authenticated
     */
    public function updateTemplate(Request $request, string $templateId): JsonResponse
    {
        $request->validate([
            'name' => 'sometimes|string|max:255',
            'title' => 'sometimes|string|max:255',
            'message' => 'sometimes|string|max:1000',
            'channels' => 'sometimes|array|min:1',
            'channels.*' => 'string|in:database,email,sms,push,whatsapp',
            'variables' => 'sometimes|array',
            'is_active' => 'sometimes|boolean',
        ]);

        try {
            $template = $this->notificationService->updateNotificationTemplate(
                $templateId,
                $request->validated()
            );

            return $this->successResponse(
                $template,
                'Notification template updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update notification template',
                $e,
                ['template_id' => $templateId]
            );

            return $this->serverErrorResponse('Failed to update notification template');
        }
    }

    /**
     * Get notification analytics.
     *
     * @group Admin Notifications
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        $request->validate([
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
            'type' => 'sometimes|string',
            'channel' => 'sometimes|string',
        ]);

        try {
            $period = $request->input('period', 'month');
            $type = $request->input('type');
            $channel = $request->input('channel');

            $analytics = $this->notificationService->getNotificationAnalytics([
                'period' => $period,
                'type' => $type,
                'channel' => $channel,
            ]);

            return $this->successResponse(
                $analytics,
                'Notification analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve notification analytics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve notification analytics');
        }
    }

    /**
     * Get notification statistics.
     *
     * @group Admin Notifications
     *
     * @authenticated
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $statistics = $this->notificationService->getNotificationStatistics();

            return $this->successResponse(
                $statistics,
                'Notification statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve notification statistics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve notification statistics');
        }
    }

    /**
     * Get delivery status for notifications.
     *
     * @group Admin Notifications
     *
     * @authenticated
     */
    public function deliveryStatus(Request $request): JsonResponse
    {
        $request->validate([
            'notification_ids' => 'sometimes|array',
            'notification_ids.*' => 'uuid|exists:notifications,id',
            'date_from' => 'sometimes|date',
            'date_to' => 'sometimes|date|after_or_equal:date_from',
        ]);

        try {
            $deliveryStatus = $this->notificationService->getNotificationDeliveryStatus(
                $request->input('notification_ids'),
                $request->input('date_from'),
                $request->input('date_to')
            );

            return $this->successResponse(
                $deliveryStatus,
                'Notification delivery status retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve notification delivery status',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve notification delivery status');
        }
    }

    /**
     * Resend failed notifications.
     *
     * @group Admin Notifications
     *
     * @authenticated
     */
    public function resendFailed(Request $request): JsonResponse
    {
        $request->validate([
            'notification_ids' => 'required|array|min:1',
            'notification_ids.*' => 'uuid|exists:notifications,id',
            'channels' => 'sometimes|array',
            'channels.*' => 'string|in:database,email,sms,push,whatsapp',
        ]);

        try {
            $result = $this->notificationService->resendFailedNotifications(
                $request->input('notification_ids'),
                $request->input('channels')
            );

            return $this->successResponse(
                $result,
                'Failed notifications resent successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to resend notifications',
                $e,
                $request->only(['notification_ids'])
            );

            return $this->serverErrorResponse('Failed to resend notifications');
        }
    }
}
