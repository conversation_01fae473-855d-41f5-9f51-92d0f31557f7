<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Enums\Delivery\DeliveryStatus;
use App\Http\Controllers\Controller;
use App\Models\Delivery\Delivery;
use App\Services\Delivery\DeliveryManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Delivery Controller
 *
 * Handles cross-tenant delivery management for administrators.
 * Provides comprehensive delivery oversight and management capabilities.
 */
class AdminDeliveryController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly DeliveryManagementService $deliveryService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all deliveries across tenants.
     *
     * @group Admin Deliveries
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Delivery::query()
                ->with([
                    'order.customer:id,first_name,last_name,email',
                    'order.business:id,business_name,tenant_id',
                    'deliveryProvider:id,business_name,service_scope',
                    'driver:id,first_name,last_name,phone_number',
                ]);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'tracking_number',
                    'order.customer.first_name',
                    'order.customer.last_name',
                    'order.business.business_name',
                    'deliveryProvider.business_name',
                ],
                'sortFields' => [
                    'created_at',
                    'status',
                    'scheduled_pickup_time',
                    'estimated_delivery_time',
                ],
                'filters' => [
                    'status' => ['type' => 'exact'],
                    'delivery_provider_id' => ['type' => 'exact'],
                    'business_id' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->whereHas('order', function ($q) use ($value) {
                                $q->where('business_id', $value);
                            });
                        },
                    ],
                    'tenant_id' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->whereHas('order.business', function ($q) use ($value) {
                                $q->where('tenant_id', $value);
                            });
                        },
                    ],
                    'date_from' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->where('created_at', '>=', $value);
                        },
                    ],
                    'date_to' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->where('created_at', '<=', $value);
                        },
                    ],
                ],
                'message' => 'Deliveries retrieved successfully',
                'entityName' => 'deliveries',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve deliveries',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve deliveries');
        }
    }

    /**
     * Get delivery details.
     *
     * @group Admin Deliveries
     *
     * @authenticated
     */
    public function show(Request $request, string $deliveryId): JsonResponse
    {
        try {
            $delivery = $this->deliveryService->getDeliveryWithDetails($deliveryId);

            return $this->successResponse(
                $delivery,
                'Delivery details retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve delivery details',
                $e,
                ['delivery_id' => $deliveryId]
            );

            return $this->serverErrorResponse('Failed to retrieve delivery details');
        }
    }

    /**
     * Update delivery status.
     *
     * @group Admin Deliveries
     *
     * @authenticated
     */
    public function updateStatus(Request $request, string $deliveryId): JsonResponse
    {
        $request->validate([
            'status' => 'required|string|in:pending,confirmed,picked_up,in_transit,out_for_delivery,delivered,failed,cancelled,returned',
            'reason' => 'sometimes|string|max:500',
            'notes' => 'sometimes|string|max:1000',
            'location' => 'sometimes|array',
            'location.latitude' => 'required_with:location|numeric',
            'location.longitude' => 'required_with:location|numeric',
        ]);

        try {
            $delivery = $this->deliveryService->updateDeliveryStatus(
                $deliveryId,
                $request->input('status'),
                $request->input('reason'),
                $request->input('notes'),
                $request->input('location')
            );

            return $this->successResponse(
                $delivery,
                'Delivery status updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update delivery status',
                $e,
                ['delivery_id' => $deliveryId]
            );

            return $this->serverErrorResponse('Failed to update delivery status');
        }
    }

    /**
     * Assign delivery to provider.
     *
     * @group Admin Deliveries
     *
     * @authenticated
     */
    public function assignProvider(Request $request, string $deliveryId): JsonResponse
    {
        $request->validate([
            'delivery_provider_id' => 'required|uuid|exists:delivery_providers,id',
            'driver_id' => 'sometimes|uuid|exists:users,id',
            'notes' => 'sometimes|string|max:500',
        ]);

        try {
            $delivery = $this->deliveryService->assignDeliveryProvider(
                $deliveryId,
                $request->input('delivery_provider_id'),
                $request->input('driver_id'),
                $request->input('notes')
            );

            return $this->successResponse(
                $delivery,
                'Delivery provider assigned successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to assign delivery provider',
                $e,
                ['delivery_id' => $deliveryId]
            );

            return $this->serverErrorResponse('Failed to assign delivery provider');
        }
    }

    /**
     * Cancel delivery.
     *
     * @group Admin Deliveries
     *
     * @authenticated
     */
    public function cancel(Request $request, string $deliveryId): JsonResponse
    {
        $request->validate([
            'reason' => 'required|string|max:500',
            'refund_delivery_fee' => 'sometimes|boolean',
            'notify_customer' => 'sometimes|boolean',
        ]);

        try {
            $result = $this->deliveryService->cancelDelivery(
                $deliveryId,
                $request->input('reason'),
                $request->boolean('refund_delivery_fee', false),
                $request->boolean('notify_customer', true)
            );

            return $this->successResponse(
                $result,
                'Delivery cancelled successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to cancel delivery',
                $e,
                ['delivery_id' => $deliveryId]
            );

            return $this->serverErrorResponse('Failed to cancel delivery');
        }
    }

    /**
     * Get delivery analytics.
     *
     * @group Admin Deliveries
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        $request->validate([
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
            'provider_id' => 'sometimes|uuid|exists:delivery_providers,id',
            'zone_id' => 'sometimes|uuid|exists:delivery_zones,id',
        ]);

        try {
            $period = $request->input('period', 'month');
            $providerId = $request->input('provider_id');
            $zoneId = $request->input('zone_id');

            $analytics = $this->deliveryService->getDeliveryAnalytics([
                'period' => $period,
                'provider_id' => $providerId,
                'zone_id' => $zoneId,
            ]);

            return $this->successResponse(
                $analytics,
                'Delivery analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve delivery analytics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve delivery analytics');
        }
    }

    /**
     * Get delivery statistics.
     *
     * @group Admin Deliveries
     *
     * @authenticated
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $statistics = $this->deliveryService->getDeliveryStatistics();

            return $this->successResponse(
                $statistics,
                'Delivery statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve delivery statistics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve delivery statistics');
        }
    }

    /**
     * Get delivery performance metrics.
     *
     * @group Admin Deliveries
     *
     * @authenticated
     */
    public function performance(Request $request): JsonResponse
    {
        $request->validate([
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
            'group_by' => 'sometimes|string|in:provider,zone,driver,day,hour',
        ]);

        try {
            $period = $request->input('period', 'month');
            $groupBy = $request->input('group_by', 'provider');

            $performance = $this->deliveryService->getDeliveryPerformance($period, $groupBy);

            return $this->successResponse(
                $performance,
                'Delivery performance metrics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve delivery performance metrics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve delivery performance metrics');
        }
    }

    /**
     * Track delivery in real-time.
     *
     * @group Admin Deliveries
     *
     * @authenticated
     */
    public function track(Request $request, string $deliveryId): JsonResponse
    {
        try {
            $tracking = $this->deliveryService->getDeliveryTracking($deliveryId);

            return $this->successResponse(
                $tracking,
                'Delivery tracking retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve delivery tracking',
                $e,
                ['delivery_id' => $deliveryId]
            );

            return $this->serverErrorResponse('Failed to retrieve delivery tracking');
        }
    }

    /**
     * Bulk update deliveries.
     *
     * @group Admin Deliveries
     *
     * @authenticated
     */
    public function bulkUpdate(Request $request): JsonResponse
    {
        $request->validate([
            'delivery_ids' => 'required|array|min:1',
            'delivery_ids.*' => 'uuid|exists:deliveries,id',
            'action' => 'required|string|in:cancel,confirm,assign_provider',
            'delivery_provider_id' => 'required_if:action,assign_provider|uuid|exists:delivery_providers,id',
            'reason' => 'sometimes|string|max:500',
        ]);

        try {
            $result = $this->deliveryService->bulkUpdateDeliveries(
                $request->input('delivery_ids'),
                $request->input('action'),
                $request->only(['delivery_provider_id', 'reason'])
            );

            return $this->successResponse(
                $result,
                'Deliveries updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to bulk update deliveries',
                $e,
                $request->only(['delivery_ids', 'action'])
            );

            return $this->serverErrorResponse('Failed to update deliveries');
        }
    }

    /**
     * Get deliveries requiring attention.
     *
     * @group Admin Deliveries
     *
     * @authenticated
     */
    public function requiresAttention(): JsonResponse
    {
        try {
            $deliveries = $this->deliveryService->getDeliveriesRequiringAttention();

            return $this->successResponse(
                $deliveries,
                'Deliveries requiring attention retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve deliveries requiring attention',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve deliveries requiring attention');
        }
    }

    /**
     * Get provider performance.
     *
     * @group Admin Deliveries
     *
     * @authenticated
     */
    public function providerPerformance(string $providerId): JsonResponse
    {
        try {
            $performance = $this->deliveryService->getProviderPerformance($providerId);

            return $this->successResponse(
                $performance,
                'Provider performance retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve provider performance',
                $e,
                ['provider_id' => $providerId]
            );

            return $this->serverErrorResponse('Failed to retrieve provider performance');
        }
    }

    /**
     * Get route optimization suggestions.
     *
     * @group Admin Deliveries
     *
     * @authenticated
     */
    public function routeOptimization(): JsonResponse
    {
        try {
            $suggestions = $this->deliveryService->getRouteOptimizationSuggestions();

            return $this->successResponse(
                $suggestions,
                'Route optimization suggestions retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve route optimization suggestions',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve route optimization suggestions');
        }
    }

    /**
     * Export deliveries.
     *
     * @group Admin Deliveries
     *
     * @authenticated
     */
    public function export(Request $request): JsonResponse
    {
        $request->validate([
            'format' => 'required|string|in:csv,json',
            'status' => 'nullable|string',
            'provider_id' => 'nullable|uuid|exists:delivery_providers,id',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        try {
            $filename = $this->deliveryService->exportDeliveries(
                array_filter($request->validated(), fn ($value) => $value !== null),
                $request->input('format')
            );

            return $this->successResponse(
                [
                    'filename' => $filename,
                    'download_url' => route('admin.deliveries.download', ['filename' => $filename]),
                ],
                'Deliveries exported successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to export deliveries',
                $e,
                $request->validated()
            );

            return $this->serverErrorResponse('Failed to export deliveries');
        }
    }

    /**
     * Download exported deliveries.
     *
     * @group Admin Deliveries
     *
     * @authenticated
     */
    public function download(string $filename)
    {
        try {
            $filepath = storage_path('app/exports/'.$filename);

            if (! file_exists($filepath)) {
                return $this->notFoundResponse('Export file not found');
            }

            // Security check: ensure filename is safe
            if (! preg_match('/^deliveries_export_\d{4}_\d{2}_\d{2}_\d{2}_\d{2}_\d{2}\.(csv|json)$/', $filename)) {
                return $this->errorResponse('Invalid filename', 400);
            }

            return response()->download($filepath)->deleteFileAfterSend();

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to download delivery export',
                $e,
                ['filename' => $filename]
            );

            return $this->serverErrorResponse('Failed to download delivery export');
        }
    }

    /**
     * Bulk update delivery status.
     *
     * @group Admin Deliveries
     *
     * @authenticated
     */
    public function bulkUpdateStatus(Request $request): JsonResponse
    {
        $request->validate([
            'delivery_ids' => 'required|array|min:1|max:100',
            'delivery_ids.*' => 'uuid|exists:deliveries,id',
            'status' => 'required|string|in:pending,assigned,picked_up,in_transit,delivered,failed,cancelled',
            'reason' => 'nullable|string|max:500',
        ]);

        try {
            $deliveryStatus = DeliveryStatus::from($request->input('status'));
            $results = $this->deliveryService->bulkUpdateDeliveryStatus(
                $request->input('delivery_ids'),
                $deliveryStatus,
                $request->input('reason')
            );

            return $this->successResponse(
                $results,
                "Bulk status update completed: {$results['updated']} updated, {$results['failed']} failed"
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to bulk update delivery status',
                $e,
                $request->only(['delivery_ids', 'status'])
            );

            return $this->serverErrorResponse('Failed to bulk update delivery status');
        }
    }
}
