<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Models\System\ApiKey;
use App\Services\System\ApiManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin API Controller
 *
 * Handles API key and access management for administrators.
 * Manages API keys, usage monitoring, rate limiting, and API access control.
 */
class AdminApiController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly ApiManagementService $apiService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all API keys.
     *
     * @group Admin API Management
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = ApiKey::query()
                ->with(['user:id,first_name,last_name,email', 'business:id,name']);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'name',
                ],
                'sortFields' => [
                    'name',
                    'is_active',
                    'last_used_at',
                    'expires_at',
                    'created_at',
                ],
                'filters' => [
                    'user_id' => ['type' => 'exact'],
                    'business_id' => ['type' => 'exact'],
                    'is_active' => ['type' => 'boolean'],
                    'is_expired' => ['type' => 'custom', 'callback' => function ($query, $value) {
                        if ($value) {
                            $query->where('expires_at', '<', now());
                        } else {
                            $query->where(function ($q) {
                                $q->whereNull('expires_at')->orWhere('expires_at', '>=', now());
                            });
                        }
                    }],
                ],
                'message' => 'API keys retrieved successfully',
                'entityName' => 'api_keys',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve API keys',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve API keys');
        }
    }

    /**
     * Show specific API key.
     *
     * @group Admin API Management
     *
     * @authenticated
     */
    public function show(string $id): JsonResponse
    {
        try {
            $apiKey = ApiKey::with(['user:id,first_name,last_name,email', 'business:id,name'])
                ->findOrFail($id);

            $usage = $this->apiService->getApiKeyUsage($id);

            return $this->successResponse(
                [
                    'api_key' => [
                        'id' => $apiKey->id,
                        'name' => $apiKey->name,
                        'masked_key' => $apiKey->getMaskedKey(),
                        'user' => $apiKey->user,
                        'business' => $apiKey->business,
                        'abilities' => $apiKey->abilities,
                        'is_active' => $apiKey->is_active,
                        'is_expired' => $apiKey->isExpired(),
                        'last_used_at' => $apiKey->last_used_at,
                        'expires_at' => $apiKey->expires_at,
                        'created_at' => $apiKey->created_at,
                    ],
                    'usage' => $usage,
                ],
                'API key details retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('API key not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve API key details',
                $e,
                ['api_key_id' => $id]
            );

            return $this->serverErrorResponse('Failed to retrieve API key details');
        }
    }

    /**
     * Create a new API key.
     *
     * @group Admin API Management
     *
     * @authenticated
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'user_id' => 'nullable|uuid|exists:users,id',
                'business_id' => 'nullable|uuid|exists:businesses,id',
                'abilities' => 'nullable|array',
                'abilities.*' => 'string|in:'.implode(',', ApiKey::getAvailableAbilities()),
                'expires_at' => 'nullable|date|after:now',
            ]);

            $result = $this->apiService->createApiKey($validated);

            return $this->successResponse(
                [
                    'api_key' => $result['api_key'],
                    'key' => $result['key'], // Only shown once
                    'warning' => 'Store this key securely. It will not be shown again.',
                ],
                'API key created successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create API key',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create API key');
        }
    }

    /**
     * Update API key.
     *
     * @group Admin API Management
     *
     * @authenticated
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'sometimes|string|max:255',
                'abilities' => 'sometimes|array',
                'abilities.*' => 'string|in:'.implode(',', ApiKey::getAvailableAbilities()),
                'expires_at' => 'sometimes|nullable|date|after:now',
                'is_active' => 'sometimes|boolean',
            ]);

            $apiKey = $this->apiService->updateApiKey($id, $validated);

            return $this->successResponse(
                $apiKey,
                'API key updated successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('API key not found');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update API key',
                $e,
                ['api_key_id' => $id, 'request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update API key');
        }
    }

    /**
     * Revoke API key.
     *
     * @group Admin API Management
     *
     * @authenticated
     */
    public function revoke(string $id): JsonResponse
    {
        try {
            $revoked = $this->apiService->revokeApiKey($id);

            if ($revoked) {
                return $this->successResponse(
                    null,
                    'API key revoked successfully'
                );
            } else {
                return $this->serverErrorResponse('Failed to revoke API key');
            }

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('API key not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to revoke API key',
                $e,
                ['api_key_id' => $id]
            );

            return $this->serverErrorResponse('Failed to revoke API key');
        }
    }

    /**
     * Regenerate API key.
     *
     * @group Admin API Management
     *
     * @authenticated
     */
    public function regenerate(string $id): JsonResponse
    {
        try {
            $newKey = $this->apiService->regenerateApiKey($id);

            return $this->successResponse(
                [
                    'key' => $newKey,
                    'warning' => 'Store this new key securely. The old key is now invalid.',
                ],
                'API key regenerated successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('API key not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to regenerate API key',
                $e,
                ['api_key_id' => $id]
            );

            return $this->serverErrorResponse('Failed to regenerate API key');
        }
    }

    /**
     * Bulk revoke API keys.
     *
     * @group Admin API Management
     *
     * @authenticated
     */
    public function bulkRevoke(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'api_key_ids' => 'required|array|min:1|max:50',
                'api_key_ids.*' => 'uuid|exists:api_keys,id',
            ]);

            $results = $this->apiService->bulkRevokeApiKeys($validated['api_key_ids']);

            return $this->successResponse(
                $results,
                "Bulk revocation completed: {$results['revoked']} revoked, {$results['failed']} failed"
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to bulk revoke API keys',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to bulk revoke API keys');
        }
    }

    /**
     * Get API key statistics.
     *
     * @group Admin API Management
     *
     * @authenticated
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = $this->apiService->getApiKeyStatistics();

            return $this->successResponse(
                $stats,
                'API key statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve API key statistics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve API key statistics');
        }
    }

    /**
     * Get API usage analytics.
     *
     * @group Admin API Management
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
                'api_key_id' => 'nullable|uuid|exists:api_keys,id',
            ]);

            $analytics = $this->apiService->getApiUsageAnalytics($validated);

            return $this->successResponse(
                $analytics,
                'API usage analytics retrieved successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve API usage analytics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve API usage analytics');
        }
    }

    /**
     * Search API keys.
     *
     * @group Admin API Management
     *
     * @authenticated
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'query' => 'required|string|min:2|max:100',
            ]);

            $results = $this->apiService->searchApiKeys($validated['query']);

            return $this->successResponse(
                $results,
                'API key search completed successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to search API keys',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to search API keys');
        }
    }

    /**
     * Get API keys by user.
     *
     * @group Admin API Management
     *
     * @authenticated
     */
    public function byUser(string $userId): JsonResponse
    {
        try {
            $apiKeys = $this->apiService->getApiKeysByUser($userId);

            return $this->successResponse(
                $apiKeys,
                'User API keys retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve user API keys',
                $e,
                ['user_id' => $userId]
            );

            return $this->serverErrorResponse('Failed to retrieve user API keys');
        }
    }

    /**
     * Get API keys by business.
     *
     * @group Admin API Management
     *
     * @authenticated
     */
    public function byBusiness(string $businessId): JsonResponse
    {
        try {
            $apiKeys = $this->apiService->getApiKeysByBusiness($businessId);

            return $this->successResponse(
                $apiKeys,
                'Business API keys retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve business API keys',
                $e,
                ['business_id' => $businessId]
            );

            return $this->serverErrorResponse('Failed to retrieve business API keys');
        }
    }

    /**
     * Validate API key permissions.
     *
     * @group Admin API Management
     *
     * @authenticated
     */
    public function validatePermissions(Request $request, string $id): JsonResponse
    {
        try {
            $validated = $request->validate([
                'abilities' => 'required|array|min:1',
                'abilities.*' => 'string|in:'.implode(',', ApiKey::getAvailableAbilities()),
            ]);

            $validation = $this->apiService->validateApiKeyPermissions($id, $validated['abilities']);

            return $this->successResponse(
                $validation,
                'API key permissions validated'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('API key not found');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to validate API key permissions',
                $e,
                ['api_key_id' => $id, 'request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to validate API key permissions');
        }
    }

    /**
     * Clean up expired API keys.
     *
     * @group Admin API Management
     *
     * @authenticated
     */
    public function cleanup(): JsonResponse
    {
        try {
            $results = $this->apiService->cleanupExpiredKeys();

            return $this->successResponse(
                $results,
                "Cleanup completed: {$results['deleted']} deleted, {$results['deactivated']} deactivated"
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to cleanup expired API keys',
                $e
            );

            return $this->serverErrorResponse('Failed to cleanup expired API keys');
        }
    }

    /**
     * Get available abilities.
     *
     * @group Admin API Management
     *
     * @authenticated
     */
    public function abilities(): JsonResponse
    {
        try {
            $abilities = ApiKey::getAvailableAbilities();

            return $this->successResponse(
                $abilities,
                'Available API abilities retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve available API abilities',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve available API abilities');
        }
    }
}
