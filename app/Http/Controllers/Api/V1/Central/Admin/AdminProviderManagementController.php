<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Enums\Delivery\DeliveryProviderStatus;
use App\Http\Controllers\Controller;
use App\Models\Delivery\DeliveryProvider;
use App\Services\System\LoggingService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

/**
 * Central Admin Provider Management Controller
 *
 * Handles cross-tenant delivery provider management for platform administrators.
 * Provides comprehensive provider oversight, verification, and administration.
 */
class AdminProviderManagementController extends Controller
{
    use \App\Traits\ApiResponseTrait, \App\Traits\QueryHandlerTrait;

    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all delivery providers across tenants.
     *
     * @group Admin Provider Management
     *
     * @authenticated
     *
     * @queryParam page integer Page number for pagination. Example: 1
     * @queryParam per_page integer Items per page (max 100). Example: 15
     * @queryParam search string Search providers by name or email. Example: fast delivery
     * @queryParam status string Filter by provider status. Example: active
     * @queryParam tenant_id string Filter by tenant ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     * @queryParam is_verified boolean Filter by verification status. Example: true
     * @queryParam sort_by string Sort by field. Example: created_at
     * @queryParam sort_direction string Sort direction (asc, desc). Example: desc
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Providers retrieved successfully",
     *   "data": {
     *     "data": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *         "company_name": "Fast Delivery Co",
     *         "contact_email": "<EMAIL>",
     *         "contact_phone": "***********",
     *         "status": "active",
     *         "is_verified": true,
     *         "average_rating": 4.7,
     *         "total_deliveries": 1250,
     *         "owner": {
     *           "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *           "name": "John Provider",
     *           "email": "<EMAIL>"
     *         },
     *         "tenant": {
     *           "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *           "name": "Provider Tenant"
     *         },
     *         "created_at": "2024-01-15T10:30:00Z"
     *       }
     *     ],
     *     "current_page": 1,
     *     "per_page": 15,
     *     "total": 50
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Build query with relationships
            $query = DeliveryProvider::with([
                'user:id,first_name,last_name,email,phone_number',
                'tenant:id,name,tenant_type',
            ]);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'company_name',
                    'contact_email',
                    'contact_phone',
                    'description',
                    'user.first_name',
                    'user.last_name',
                    'user.email',
                ],
                'sortFields' => [
                    'company_name',
                    'contact_email',
                    'status',
                    'created_at',
                    'updated_at',
                ],
                'filters' => [
                    'status' => ['type' => 'exact'],
                    'tenant_id' => ['type' => 'exact'],
                    'is_verified' => ['type' => 'boolean'],
                ],
                'message' => 'Providers retrieved successfully',
                'entityName' => 'providers',
                'transformer' => [$this, 'transformProvider'],
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve providers',
                $e,
                [
                    'admin_user_id' => auth()->id(),
                    'request_params' => $request->only(['search', 'status', 'tenant_id', 'is_verified']),
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve providers');
        }
    }

    /**
     * Get specific provider details.
     *
     * @group Admin Provider Management
     *
     * @authenticated
     *
     * @urlParam provider string required Provider ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Provider retrieved successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "company_name": "Fast Delivery Co",
     *     "contact_email": "<EMAIL>",
     *     "contact_phone": "***********",
     *     "address": "123 Provider St, Lagos",
     *     "description": "Professional delivery services",
     *     "status": "active",
     *     "is_verified": true,
     *     "average_rating": 4.7,
     *     "total_deliveries": 1250,
     *     "completed_deliveries": 1200,
     *     "owner": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "name": "John Provider",
     *       "email": "<EMAIL>",
     *       "phone": "***********"
     *     },
     *     "tenant": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "name": "Provider Tenant",
     *       "tenant_type": "provider"
     *     },
     *     "created_at": "2024-01-15T10:30:00Z",
     *     "updated_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function show(string $provider): JsonResponse
    {
        try {
            $query = DeliveryProvider::query();

            return $this->handleShow($query, $provider, [
                'with' => [
                    'user:id,first_name,last_name,email,phone_number,is_active',
                    'tenant:id,name,tenant_type,status',
                ],
                'transformer' => fn ($model) => $this->transformProvider($model, true),
                'message' => 'Provider retrieved successfully',
                'notFoundMessage' => 'Provider not found',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve provider details',
                $e,
                [
                    'admin_user_id' => auth()->id(),
                    'provider_id' => $provider,
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve provider details');
        }
    }

    /**
     * Update provider details.
     *
     * @group Admin Provider Management
     *
     * @authenticated
     *
     * @urlParam provider string required Provider ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam company_name string Company name. Example: Fast Delivery Co
     * @bodyParam contact_email string Contact email. Example: <EMAIL>
     * @bodyParam contact_phone string Contact phone. Example: ***********
     * @bodyParam address string Business address. Example: 123 Provider St, Lagos
     * @bodyParam description string Business description. Example: Professional delivery services
     * @bodyParam status string Provider status. Example: active
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Provider updated successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "company_name": "Fast Delivery Co",
     *     "contact_email": "<EMAIL>",
     *     "status": "active"
     *   }
     * }
     */
    public function update(Request $request, string $provider): JsonResponse
    {
        $request->validate([
            'company_name' => 'sometimes|string|max:255',
            'contact_email' => 'sometimes|email|max:255',
            'contact_phone' => 'sometimes|string|max:20',
            'address' => 'sometimes|string|max:500',
            'description' => 'sometimes|string|max:1000',
            'status' => ['sometimes', 'string', Rule::in(DeliveryProviderStatus::values())],
        ]);

        try {
            $providerModel = DeliveryProvider::findOrFail($provider);

            $updateData = $request->only([
                'company_name', 'contact_email', 'contact_phone',
                'address', 'description', 'status',
            ]);

            // Remove empty values
            $updateData = array_filter($updateData, function ($value) {
                return $value !== null && $value !== '';
            });

            if (empty($updateData)) {
                return $this->errorResponse(
                    'No valid data provided for update',
                    422
                );
            }

            $providerModel->update($updateData);

            $this->loggingService->logInfo(
                'Provider updated by admin',
                [
                    'admin_user_id' => auth()->id(),
                    'provider_id' => $provider,
                    'updated_fields' => array_keys($updateData),
                ]
            );

            return $this->successResponse(
                $this->transformProvider($providerModel->fresh()),
                'Provider updated successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'Provider not found',
                404
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update provider',
                $e,
                [
                    'admin_user_id' => auth()->id(),
                    'provider_id' => $provider,
                    'update_data' => $updateData ?? [],
                ]
            );

            return $this->serverErrorResponse('Failed to update provider');
        }
    }

    /**
     * Activate a provider.
     *
     * @group Admin Provider Management
     *
     * @authenticated
     *
     * @urlParam provider string required Provider ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Provider activated successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "status": "active"
     *   }
     * }
     */
    public function activate(string $provider): JsonResponse
    {
        return $this->updateProviderStatus($provider, DeliveryProviderStatus::ACTIVE, 'Provider activated successfully');
    }

    /**
     * Suspend a provider.
     *
     * @group Admin Provider Management
     *
     * @authenticated
     *
     * @urlParam provider string required Provider ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Provider suspended successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "status": "suspended"
     *   }
     * }
     */
    public function suspend(string $provider): JsonResponse
    {
        return $this->updateProviderStatus($provider, DeliveryProviderStatus::SUSPENDED, 'Provider suspended successfully');
    }

    /**
     * Verify a provider.
     *
     * @group Admin Provider Management
     *
     * @authenticated
     *
     * @urlParam provider string required Provider ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Provider verified successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "is_verified": true
     *   }
     * }
     */
    public function verify(string $provider): JsonResponse
    {
        try {
            $providerModel = DeliveryProvider::findOrFail($provider);

            if ($providerModel->is_verified) {
                return $this->errorResponse(
                    'Provider is already verified',
                    422
                );
            }

            $providerModel->update([
                'is_verified' => true,
                'verified_at' => now(),
            ]);

            $this->loggingService->logInfo(
                'Provider verified by admin',
                [
                    'admin_user_id' => auth()->id(),
                    'provider_id' => $provider,
                ]
            );

            return $this->successResponse(
                [
                    'id' => $providerModel->id,
                    'is_verified' => $providerModel->is_verified,
                    'verified_at' => $providerModel->verified_at?->toISOString(),
                ],
                'Provider verified successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'Provider not found',
                404
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to verify provider',
                $e,
                [
                    'admin_user_id' => auth()->id(),
                    'provider_id' => $provider,
                ]
            );

            return $this->serverErrorResponse('Failed to verify provider');
        }
    }

    // Helper Methods

    /**
     * Update provider status.
     */
    private function updateProviderStatus(string $providerId, DeliveryProviderStatus $status, string $message): JsonResponse
    {
        try {
            $providerModel = DeliveryProvider::findOrFail($providerId);

            $providerModel->update(['status' => $status]);

            $this->loggingService->logInfo(
                'Provider status updated by admin',
                [
                    'admin_user_id' => auth()->id(),
                    'provider_id' => $providerId,
                    'new_status' => $status->value,
                ]
            );

            return $this->successResponse(
                [
                    'id' => $providerModel->id,
                    'status' => $providerModel->status->value,
                ],
                $message
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'Provider not found',
                404
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update provider status',
                $e,
                [
                    'admin_user_id' => auth()->id(),
                    'provider_id' => $providerId,
                    'intended_status' => $status->value,
                ]
            );

            return $this->serverErrorResponse('Failed to update provider status');
        }
    }

    /**
     * Transform provider for API response.
     */
    private function transformProvider(DeliveryProvider $provider, bool $detailed = false): array
    {
        $data = [
            'id' => $provider->id,
            'company_name' => $provider->company_name,
            'contact_email' => $provider->contact_email,
            'contact_phone' => $provider->contact_phone,
            'status' => $provider->status->value,
            'is_verified' => $provider->is_verified ?? false,
            'average_rating' => $provider->average_rating ?? 0,
            'total_deliveries' => 0, // Placeholder - would count from deliveries
            'owner' => $provider->user ? [
                'id' => $provider->user->id,
                'name' => $provider->user->first_name.' '.$provider->user->last_name,
                'email' => $provider->user->email,
            ] : null,
            'tenant' => $provider->tenant ? [
                'id' => $provider->tenant->id,
                'name' => $provider->tenant->name,
            ] : null,
            'created_at' => $provider->created_at->toISOString(),
        ];

        if ($detailed) {
            $data = array_merge($data, [
                'address' => $provider->address,
                'description' => $provider->description,
                'completed_deliveries' => 0, // Placeholder - would count completed deliveries
                'owner' => $provider->user ? [
                    'id' => $provider->user->id,
                    'name' => $provider->user->first_name.' '.$provider->user->last_name,
                    'email' => $provider->user->email,
                    'phone' => $provider->user->phone_number,
                    'is_active' => $provider->user->is_active,
                ] : null,
                'tenant' => $provider->tenant ? [
                    'id' => $provider->tenant->id,
                    'name' => $provider->tenant->name,
                    'tenant_type' => $provider->tenant->tenant_type->value ?? null,
                    'status' => $provider->tenant->status->value ?? null,
                ] : null,
                'verified_at' => $provider->verified_at?->toISOString(),
                'updated_at' => $provider->updated_at->toISOString(),
            ]);
        }

        return $data;
    }
}
