<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Services\System\LoggingService;
use App\Services\System\MaintenanceManagementService;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Maintenance Controller
 *
 * Handles comprehensive platform maintenance and updates for administrators.
 * Manages maintenance windows, system updates, and downtime coordination.
 */
class AdminMaintenanceController extends Controller
{
    use ApiResponseTrait;

    public function __construct(
        private readonly MaintenanceManagementService $maintenanceService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all maintenance windows with filtering.
     *
     * @group Admin Maintenance
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'status' => 'sometimes|string|in:scheduled,in_progress,completed,cancelled',
                'type' => 'sometimes|string|in:system_update,security_patch,database_maintenance,server_maintenance',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
                'per_page' => 'sometimes|integer|min:1|max:100',
            ]);

            $windows = $this->maintenanceService->getMaintenanceWindows($validated);

            // Paginate results manually since we're working with arrays
            $perPage = $validated['per_page'] ?? 15;
            $page = $request->get('page', 1);
            $offset = ($page - 1) * $perPage;

            $paginatedWindows = array_slice($windows, $offset, $perPage);
            $total = count($windows);

            return $this->successResponse([
                'data' => array_map([$this, 'transformMaintenanceWindow'], $paginatedWindows),
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $total,
                    'last_page' => ceil($total / $perPage),
                    'from' => $offset + 1,
                    'to' => min($offset + $perPage, $total),
                ],
            ], 'Maintenance windows retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve maintenance windows',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve maintenance windows');
        }
    }

    /**
     * Schedule a new maintenance window.
     *
     * @group Admin Maintenance
     *
     * @authenticated
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'sometimes|string|max:1000',
                'type' => 'required|string|in:system_update,security_patch,database_maintenance,server_maintenance',
                'priority' => 'sometimes|string|in:low,medium,high,critical',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'scheduled_start' => 'required|date|after:now',
                'scheduled_end' => 'required|date|after:scheduled_start',
                'estimated_duration' => 'sometimes|integer|min:1',
                'impact_level' => 'sometimes|string|in:low,medium,high',
                'affected_services' => 'sometimes|array',
                'affected_services.*' => 'string',
                'notification_settings' => 'sometimes|array',
                'rollback_plan' => 'sometimes|string|max:1000',
            ]);

            $window = $this->maintenanceService->scheduleMaintenanceWindow($validated);

            return $this->successResponse(
                $this->transformMaintenanceWindow($window),
                'Maintenance window scheduled successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to schedule maintenance window',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to schedule maintenance window');
        }
    }

    /**
     * Get maintenance analytics and insights.
     *
     * @group Admin Maintenance
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
            ]);

            $analytics = $this->maintenanceService->getMaintenanceAnalytics($validated);

            return $this->successResponse(
                $analytics,
                'Maintenance analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve maintenance analytics',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve maintenance analytics');
        }
    }

    /**
     * Get current system status.
     *
     * @group Admin Maintenance
     *
     * @authenticated
     */
    public function systemStatus(): JsonResponse
    {
        try {
            $status = $this->maintenanceService->getSystemStatus();

            return $this->successResponse(
                $status,
                'System status retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve system status',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve system status');
        }
    }

    /**
     * Start maintenance window.
     *
     * @group Admin Maintenance
     *
     * @authenticated
     */
    public function start(string $windowId): JsonResponse
    {
        try {
            $window = $this->maintenanceService->startMaintenanceWindow($windowId);

            return $this->successResponse(
                $this->transformMaintenanceWindow($window),
                'Maintenance window started successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to start maintenance window',
                $e,
                ['window_id' => $windowId]
            );

            return $this->serverErrorResponse('Failed to start maintenance window');
        }
    }

    /**
     * Complete maintenance window.
     *
     * @group Admin Maintenance
     *
     * @authenticated
     */
    public function complete(Request $request, string $windowId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'notes' => 'sometimes|string|max:1000',
            ]);

            $window = $this->maintenanceService->completeMaintenanceWindow($windowId, $validated);

            return $this->successResponse(
                $this->transformMaintenanceWindow($window),
                'Maintenance window completed successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to complete maintenance window',
                $e,
                ['window_id' => $windowId]
            );

            return $this->serverErrorResponse('Failed to complete maintenance window');
        }
    }

    /**
     * Cancel maintenance window.
     *
     * @group Admin Maintenance
     *
     * @authenticated
     */
    public function cancel(Request $request, string $windowId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'reason' => 'required|string|max:500',
            ]);

            $window = $this->maintenanceService->cancelMaintenanceWindow(
                $windowId,
                $validated['reason']
            );

            return $this->successResponse(
                $this->transformMaintenanceWindow($window),
                'Maintenance window cancelled successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to cancel maintenance window',
                $e,
                ['window_id' => $windowId, 'reason' => $request->get('reason')]
            );

            return $this->serverErrorResponse('Failed to cancel maintenance window');
        }
    }

    /**
     * Execute rollback procedure.
     *
     * @group Admin Maintenance
     *
     * @authenticated
     */
    public function rollback(Request $request, string $windowId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'rollback_config' => 'sometimes|array',
            ]);

            $rollback = $this->maintenanceService->executeRollback(
                $windowId,
                $validated['rollback_config'] ?? []
            );

            return $this->successResponse(
                $rollback,
                'Rollback executed successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to execute rollback',
                $e,
                ['window_id' => $windowId]
            );

            return $this->serverErrorResponse('Failed to execute rollback');
        }
    }

    /**
     * Transform maintenance window for API response.
     */
    public function transformMaintenanceWindow(array $window): array
    {
        return [
            'id' => $window['id'],
            'title' => $window['title'],
            'description' => $window['description'],
            'type' => $window['type'],
            'priority' => $window['priority'],
            'tenant_id' => $window['tenant_id'],
            'scheduled_start' => $window['scheduled_start'],
            'scheduled_end' => $window['scheduled_end'],
            'estimated_duration' => $window['estimated_duration'],
            'status' => $window['status'],
            'impact_level' => $window['impact_level'],
            'affected_services' => $window['affected_services'],
            'notification_settings' => $window['notification_settings'],
            'rollback_plan' => $window['rollback_plan'],
            'created_at' => $window['created_at'],
            'created_by' => $window['created_by'],
            'actual_start' => $window['actual_start'],
            'actual_end' => $window['actual_end'],
            'completion_notes' => $window['completion_notes'],
            'duration_minutes' => $window['actual_start'] && $window['actual_end']
                ? $this->calculateDuration($window['actual_start'], $window['actual_end'])
                : null,
        ];
    }

    /**
     * Calculate duration in minutes.
     */
    private function calculateDuration(string $start, string $end): int
    {
        $startTime = strtotime($start);
        $endTime = strtotime($end);

        return (int) round(($endTime - $startTime) / 60);
    }
}
