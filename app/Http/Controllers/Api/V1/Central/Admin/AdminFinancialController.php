<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Services\Financial\FinancialManagementService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Financial Management Controller
 *
 * Handles financial management endpoints for platform administrators.
 */
class AdminFinancialController extends Controller
{
    use \App\Traits\ApiResponseTrait;

    public function __construct(
        private readonly FinancialManagementService $financialService
    ) {}

    /**
     * Get platform financial summary.
     *
     * @group Financial Management
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "platform_balance": 150000.00,
     *     "currency": "NGN",
     *     "total_commission_revenue": 75000.00,
     *     "order_commissions": {
     *       "total_amount": 45000.00,
     *       "count": 1500,
     *       "total_order_value": 3000000.00
     *     },
     *     "delivery_commissions": {
     *       "total_amount": 30000.00,
     *       "count": 1000,
     *       "total_delivery_value": 1500000.00
     *     },
     *     "period": {
     *       "start_date": "2024-01-01",
     *       "end_date": "2024-12-31"
     *     }
     *   }
     * }
     */
    public function platformSummary(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
        ]);

        try {
            $summary = $this->financialService->getPlatformFinancialSummary(
                $request->input('start_date'),
                $request->input('end_date')
            );

            return $this->successResponse($summary, 'Platform financial summary retrieved successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve platform financial summary', 500);
        }
    }

    /**
     * Get business financial summary.
     *
     * @group Financial Management
     *
     * @authenticated
     *
     * @urlParam business_id string required The business ID.
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "business_id": "business-uuid",
     *     "business_name": "Sample Restaurant",
     *     "current_balance": 25000.00,
     *     "currency": "NGN",
     *     "total_commissions_paid": 15000.00,
     *     "total_orders_value": 1000000.00,
     *     "commission_count": 500,
     *     "average_commission_rate": 1.5,
     *     "period": {
     *       "start_date": "2024-01-01",
     *       "end_date": "2024-12-31"
     *     }
     *   }
     * }
     */
    public function businessSummary(Request $request, string $businessId): JsonResponse
    {
        $request->validate([
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
        ]);

        try {
            $summary = $this->financialService->getBusinessFinancialSummary(
                $businessId,
                $request->input('start_date'),
                $request->input('end_date')
            );

            return $this->successResponse($summary, 'Business financial summary retrieved successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve business financial summary', 500);
        }
    }

    /**
     * Get provider financial summary.
     *
     * @group Financial Management
     *
     * @authenticated
     *
     * @urlParam provider_id string required The provider ID.
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "provider_id": "provider-uuid",
     *     "provider_name": "Fast Delivery Co",
     *     "current_balance": 18000.00,
     *     "currency": "NGN",
     *     "total_commissions_paid": 12000.00,
     *     "total_deliveries_value": 600000.00,
     *     "commission_count": 300,
     *     "average_commission_rate": 2.0,
     *     "period": {
     *       "start_date": "2024-01-01",
     *       "end_date": "2024-12-31"
     *     }
     *   }
     * }
     */
    public function providerSummary(Request $request, string $providerId): JsonResponse
    {
        $request->validate([
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
        ]);

        try {
            $summary = $this->financialService->getProviderFinancialSummary(
                $providerId,
                $request->input('start_date'),
                $request->input('end_date')
            );

            return $this->successResponse($summary, 'Provider financial summary retrieved successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve provider financial summary', 500);
        }
    }

    /**
     * Get platform account details.
     *
     * @group Financial Management
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "id": "account-uuid",
     *     "accountable_id": "platform",
     *     "accountable_type": "platform",
     *     "balance": 150000.00,
     *     "currency": "NGN",
     *     "created_at": "2024-01-01T00:00:00.000000Z",
     *     "updated_at": "2024-12-01T12:00:00.000000Z"
     *   }
     * }
     */
    public function platformAccount(): JsonResponse
    {
        try {
            $account = $this->financialService->getOrCreatePlatformAccount('platform', 'platform');

            return $this->successResponse($account, 'Platform account retrieved successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve platform account', 500);
        }
    }

    /**
     * Get business account details.
     *
     * @group Financial Management
     *
     * @authenticated
     *
     * @urlParam business_id string required The business ID.
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "id": "account-uuid",
     *     "accountable_id": "business-uuid",
     *     "accountable_type": "business",
     *     "balance": 25000.00,
     *     "currency": "NGN",
     *     "created_at": "2024-01-01T00:00:00.000000Z",
     *     "updated_at": "2024-12-01T12:00:00.000000Z"
     *   }
     * }
     */
    public function businessAccount(string $businessId): JsonResponse
    {
        try {
            $account = $this->financialService->getOrCreatePlatformAccount($businessId, 'business');

            return $this->successResponse($account, 'Business account retrieved successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve business account', 500);
        }
    }

    /**
     * Get provider account details.
     *
     * @group Financial Management
     *
     * @authenticated
     *
     * @urlParam provider_id string required The provider ID.
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "id": "account-uuid",
     *     "accountable_id": "provider-uuid",
     *     "accountable_type": "delivery_provider",
     *     "balance": 18000.00,
     *     "currency": "NGN",
     *     "created_at": "2024-01-01T00:00:00.000000Z",
     *     "updated_at": "2024-12-01T12:00:00.000000Z"
     *   }
     * }
     */
    public function providerAccount(string $providerId): JsonResponse
    {
        try {
            $account = $this->financialService->getOrCreatePlatformAccount($providerId, 'delivery_provider');

            return $this->successResponse($account, 'Provider account retrieved successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve provider account', 500);
        }
    }
}
