<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Models\Business\ProductCategory;
use App\Services\Business\CategoryManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Category Controller
 *
 * Handles product category management across all tenants for administrators.
 * Provides oversight and management of business categories platform-wide.
 */
class AdminCategoryController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly CategoryManagementService $categoryService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all categories across tenants.
     *
     * @group Admin Categories
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = ProductCategory::query()
                ->with([
                    'business:id,business_name,tenant_id',
                    'parent:id,name',
                    'children:id,name,parent_id',
                ]);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'name',
                    'description',
                    'business.business_name',
                ],
                'sortFields' => [
                    'name',
                    'display_order',
                    'created_at',
                    'is_active',
                ],
                'filters' => [
                    'is_active' => ['type' => 'boolean'],
                    'business_id' => ['type' => 'exact'],
                    'tenant_id' => ['type' => 'exact'],
                    'parent_id' => ['type' => 'exact'],
                ],
                'message' => 'Categories retrieved successfully',
                'entityName' => 'categories',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve categories',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve categories');
        }
    }

    /**
     * Get category details.
     *
     * @group Admin Categories
     *
     * @authenticated
     */
    public function show(Request $request, string $categoryId): JsonResponse
    {
        try {
            $category = $this->categoryService->getCategoryWithDetails($categoryId);

            return $this->successResponse(
                $category,
                'Category details retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve category details',
                $e,
                ['category_id' => $categoryId]
            );

            return $this->serverErrorResponse('Failed to retrieve category details');
        }
    }

    /**
     * Update category status.
     *
     * @group Admin Categories
     *
     * @authenticated
     */
    public function updateStatus(Request $request, string $categoryId): JsonResponse
    {
        $request->validate([
            'is_active' => 'required|boolean',
            'reason' => 'sometimes|string|max:500',
        ]);

        try {
            $category = $this->categoryService->updateCategoryStatus(
                $categoryId,
                $request->boolean('is_active'),
                $request->input('reason')
            );

            return $this->successResponse(
                $category,
                'Category status updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update category status',
                $e,
                ['category_id' => $categoryId]
            );

            return $this->serverErrorResponse('Failed to update category status');
        }
    }

    /**
     * Get category analytics.
     *
     * @group Admin Categories
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        $request->validate([
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
        ]);

        try {
            $period = $request->input('period', 'month');
            $analytics = $this->categoryService->getCategoryAnalytics($period);

            return $this->successResponse(
                $analytics,
                'Category analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve category analytics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve category analytics');
        }
    }

    /**
     * Get popular categories.
     *
     * @group Admin Categories
     *
     * @authenticated
     */
    public function popular(Request $request): JsonResponse
    {
        $request->validate([
            'limit' => 'sometimes|integer|min:1|max:50',
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
        ]);

        try {
            $limit = $request->input('limit', 20);
            $period = $request->input('period', 'month');

            $popularCategories = $this->categoryService->getPopularCategories($limit, $period);

            return $this->successResponse(
                $popularCategories,
                'Popular categories retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve popular categories',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve popular categories');
        }
    }

    /**
     * Merge categories.
     *
     * @group Admin Categories
     *
     * @authenticated
     */
    public function merge(Request $request): JsonResponse
    {
        $request->validate([
            'source_category_id' => 'required|uuid|exists:product_categories,id',
            'target_category_id' => 'required|uuid|exists:product_categories,id|different:source_category_id',
            'delete_source' => 'sometimes|boolean',
        ]);

        try {
            $result = $this->categoryService->mergeCategories(
                $request->input('source_category_id'),
                $request->input('target_category_id'),
                $request->boolean('delete_source', true)
            );

            return $this->successResponse(
                $result,
                'Categories merged successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to merge categories',
                $e,
                $request->only(['source_category_id', 'target_category_id'])
            );

            return $this->serverErrorResponse('Failed to merge categories');
        }
    }

    /**
     * Bulk update categories.
     *
     * @group Admin Categories
     *
     * @authenticated
     */
    public function bulkUpdate(Request $request): JsonResponse
    {
        $request->validate([
            'category_ids' => 'required|array|min:1',
            'category_ids.*' => 'uuid|exists:product_categories,id',
            'action' => 'required|string|in:activate,deactivate,delete',
            'reason' => 'sometimes|string|max:500',
        ]);

        try {
            $result = $this->categoryService->bulkUpdateCategories(
                $request->input('category_ids'),
                $request->input('action'),
                $request->input('reason')
            );

            return $this->successResponse(
                $result,
                'Categories updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to bulk update categories',
                $e,
                $request->only(['category_ids', 'action'])
            );

            return $this->serverErrorResponse('Failed to update categories');
        }
    }

    /**
     * Get category hierarchy.
     *
     * @group Admin Categories
     *
     * @authenticated
     */
    public function hierarchy(Request $request): JsonResponse
    {
        $request->validate([
            'business_id' => 'sometimes|uuid|exists:businesses,id',
            'tenant_id' => 'sometimes|string|exists:tenants,id',
        ]);

        try {
            $hierarchy = $this->categoryService->getCategoryHierarchy(
                $request->input('business_id'),
                $request->input('tenant_id')
            );

            return $this->successResponse(
                $hierarchy,
                'Category hierarchy retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve category hierarchy',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve category hierarchy');
        }
    }

    /**
     * Get category statistics.
     *
     * @group Admin Categories
     *
     * @authenticated
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $statistics = $this->categoryService->getCategoryStatistics();

            return $this->successResponse(
                $statistics,
                'Category statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve category statistics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve category statistics');
        }
    }
}
