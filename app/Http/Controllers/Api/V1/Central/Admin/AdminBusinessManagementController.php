<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Enums\Business\BusinessStatus;
use App\Enums\Financial\SubscriptionTargetType;
use App\Enums\System\TenantStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\Business\CreateBusinessRequest;
use App\Http\Requests\Api\V1\Business\UpdateBusinessRequest;
use App\Http\Resources\BusinessResource;
use App\Models\Business\Business;
use App\Models\System\Tenant;
use App\Models\User\Address;
use App\Models\User\User;
use App\Services\Integration\ImageStorageService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

/**
 * Admin Business Controller for central domain.
 * Handles platform admin business management (cross-tenant).
 */
class AdminBusinessManagementController extends Controller
{
    use ApiResponseTrait;
    use QueryHandlerTrait;

    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Display a listing of businesses.
     */
    public function index(Request $request): JsonResponse
    {
        $query = Business::query()->with([
            'owner:id,first_name,last_name,email,phone_number',
            'tenant:id,name,tenant_type,status',
            'primaryAddress:id,address_line_1,city,state,country',
            'country:id,name,code',
            'state:id,name,code',
        ]);

        return $this->handleQuery($query, $request, [
            'searchFields' => [
                'business_name',
                'contact_email',
                'contact_phone',
                'owner.first_name',
                'owner.last_name',
                'owner.email',
            ],
            'sortFields' => [
                'business_name',
                'business_type',
                'status',
                'created_at',
                'updated_at',
            ],
            'filters' => [
                'status' => ['type' => 'exact'],
                'business_type' => ['type' => 'exact'],
                'country_id' => ['type' => 'exact'],
                'state_id' => ['type' => 'exact'],
                'has_subscription' => [
                    'type' => 'boolean',
                    'column' => 'current_subscription_id',
                ],
            ],
            'resource' => BusinessResource::class,
            'message' => 'Businesses retrieved successfully',
            'entityName' => 'businesses',
        ]);
    }

    /**
     * Store a newly created business.
     */
    public function store(CreateBusinessRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Create tenant first
            $tenant = Tenant::create([
                'name' => $request->input('business_name'),
                'tenant_type' => SubscriptionTargetType::BUSINESS,
                'status' => TenantStatus::ACTIVE,
                'created_by' => auth()->id(),
            ]);

            // Get business data
            $businessData = $request->getBusinessData();
            $businessData['tenant_id'] = $tenant->id;
            $businessData['status'] = BusinessStatus::PENDING_VERIFICATION;

            // Assign admin user if provided
            $businessData['user_id'] = $request->getAdminUserId() ?: auth()->id();

            // Create business
            $business = Business::create($businessData);

            // Create address if provided
            if ($addressData = $request->getAddressData()) {
                $address = Address::create([
                    'addressable_type' => Business::class,
                    'addressable_id' => $business->id,
                    'label' => 'business',
                    'street_address' => $addressData['address_line_1'],
                    'city_name_string' => $addressData['city'],
                    'state_province_string' => $addressData['state'] ?? null,
                    'country_id' => $businessData['country_id'],
                    'state_id' => $businessData['state_id'] ?? null,
                    'postal_code' => $addressData['postal_code'] ?? null,
                    'latitude' => $addressData['latitude'] ?? null,
                    'longitude' => $addressData['longitude'] ?? null,
                    'is_default' => true,
                ]);

                $business->update(['primary_address_id' => $address->id]);
            }

            // Handle logo upload if provided
            if ($request->hasFile('logo')) {
                $logoPath = $this->handleLogoUpload($request->file('logo'), $business);
                $business->update(['logo_url' => $logoPath]);
            }

            // Assign business owner role to the admin user
            $adminUser = $business->owner;

            // Create role if it doesn't exist
            \Silber\Bouncer\BouncerFacade::role()->firstOrCreate(['name' => 'business-owner']);

            // For now, assign global role (we'll fix entity-specific roles later)
            $adminUser->assign('business-owner');

            DB::commit();

            // Load relationships for response
            $business->load([
                'owner:id,first_name,last_name,email,phone_number',
                'tenant:id,name,tenant_type,status',
                'primaryAddress',
                'country:id,name,code',
                'state:id,name,code',
            ]);

            return $this->createdResponse(
                new BusinessResource($business),
                'Business created successfully'
            );

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->serverErrorResponse(
                'Failed to create business: '.$e->getMessage()
            );
        }
    }

    /**
     * Display the specified business.
     */
    public function show(string $id): JsonResponse
    {
        $query = Business::query()->with([
            'owner:id,first_name,last_name,email,phone_number',
            'tenant:id,name,tenant_type,status',
            'primaryAddress',
            'country:id,name,code,currency_code,phone_code',
            'state:id,name,code',
            'currentSubscription:id,plan_name,status,starts_at,ends_at',
        ]);

        return $this->handleShow($query, $id, [
            'resource' => BusinessResource::class,
            'message' => 'Business retrieved successfully',
            'notFoundMessage' => 'Business not found',
        ]);
    }

    /**
     * Update the specified business.
     */
    public function update(UpdateBusinessRequest $request, string $id): JsonResponse
    {
        try {
            // Skip explicit transaction during testing to avoid conflicts with RefreshDatabase
            if (! app()->environment('testing')) {
                DB::beginTransaction();
            }

            $business = Business::find($id);
            if (! $business) {
                return $this->notFoundResponse('Business not found');
            }

            // Update business data
            $businessData = $request->getBusinessData();
            $business->update($businessData);

            // Handle logo upload if provided
            if ($request->hasFile('logo')) {
                $logoPath = $this->handleLogoUpload($request->file('logo'), $business);
                $business->update(['logo_url' => $logoPath]);
            }

            // Only commit if we started a transaction (not during testing)
            if (! app()->environment('testing')) {
                DB::commit();
            }

            // Load relationships for response
            $business->load([
                'owner:id,first_name,last_name,email,phone_number',
                'tenant:id,name,tenant_type,status',
                'primaryAddress',
                'country:id,name,code',
                'state:id,name,code',
            ]);

            return $this->successResponse(
                new BusinessResource($business),
                'Business updated successfully'
            );

        } catch (\Exception $e) {
            // Only rollback if we started a transaction (not during testing)
            if (! app()->environment('testing')) {
                DB::rollBack();
            }

            return $this->serverErrorResponse(
                'Failed to update business: '.$e->getMessage()
            );
        }
    }

    /**
     * Remove the specified business (soft delete).
     */
    public function destroy(string $id): JsonResponse
    {
        $query = Business::query();

        return $this->handleDestroy($query, $id, [
            'message' => 'Business archived successfully',
            'notFoundMessage' => 'Business not found',
            'softDelete' => true,
        ]);
    }

    /**
     * Restore an archived business.
     */
    public function restore(string $id): JsonResponse
    {
        $business = Business::withTrashed()->find($id);

        if (! $business) {
            return $this->notFoundResponse('Business not found');
        }

        if (! $business->trashed()) {
            return $this->conflictResponse('Business is not archived');
        }

        $business->restore();

        return $this->successResponse(
            new BusinessResource($business),
            'Business restored successfully'
        );
    }

    /**
     * Activate a business.
     */
    public function activate(string $id): JsonResponse
    {
        return $this->updateBusinessStatus($id, BusinessStatus::ACTIVE, 'Business activated successfully');
    }

    /**
     * Suspend a business.
     */
    public function suspend(string $id): JsonResponse
    {
        return $this->updateBusinessStatus($id, BusinessStatus::SUSPENDED, 'Business suspended successfully');
    }

    /**
     * Mark business as verified.
     */
    public function verify(string $id): JsonResponse
    {
        return $this->updateBusinessStatus($id, BusinessStatus::VERIFIED, 'Business verified successfully');
    }

    /**
     * Update business status.
     */
    private function updateBusinessStatus(string $id, BusinessStatus $status, string $message): JsonResponse
    {
        $business = Business::find($id);

        if (! $business) {
            return $this->notFoundResponse('Business not found');
        }

        $business->update(['status' => $status]);

        $business->load([
            'owner:id,first_name,last_name,email,phone_number',
            'tenant:id,name,tenant_type,status',
        ]);

        return $this->successResponse(
            new BusinessResource($business),
            $message
        );
    }

    /**
     * Get business admins.
     */
    public function getAdmins(string $id): JsonResponse
    {
        $business = Business::find($id);
        if (! $business) {
            return $this->notFoundResponse('Business not found');
        }

        // Get all users with business roles for this business
        $adminUsers = $this->getBusinessAdminUsers($business);

        // Apply search filter if provided
        $search = request('search');
        if ($search) {
            $adminUsers = $adminUsers->filter(fn ($user) => stripos($user->first_name, $search) !== false ||
                stripos($user->last_name, $search) !== false ||
                stripos($user->email, $search) !== false
            );
        }

        // Apply sorting if provided
        $sortBy = request('sort_by', 'first_name');
        $sortDirection = request('sort_direction', 'asc');

        $adminUsers = $adminUsers->sortBy($sortBy, SORT_REGULAR, $sortDirection === 'desc');

        // Format users with roles
        $formattedAdmins = $adminUsers->map(fn ($user) => [
            'id' => $user->id,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'roles' => $this->getUserBusinessRoles($user, $business),
        ])->values();

        // Create paginated response structure
        $paginatedData = [
            'data' => $formattedAdmins->toArray(),
            'current_page' => 1,
            'per_page' => 15,
            'total' => $formattedAdmins->count(),
            'last_page' => 1,
            'from' => 1,
            'to' => $formattedAdmins->count(),
        ];

        return $this->successResponse(
            $paginatedData,
            'Business admins retrieved successfully'
        );
    }

    /**
     * Assign admin to business.
     */
    public function assignAdmin(Request $request, string $id): JsonResponse
    {
        $business = Business::find($id);
        if (! $business) {
            return $this->notFoundResponse('Business not found');
        }

        $request->validate([
            'user_id' => 'required|uuid|exists:users,id',
            'role' => 'required|in:business-admin,business-manager,business-owner',
        ]);

        $user = User::find($request->input('user_id'));
        if (! $user) {
            return $this->notFoundResponse('User not found');
        }

        // Check if user already has a role for this business
        $currentRole = $this->getUserBusinessRole($user, $business);
        if ($currentRole) {
            return response()->json([
                'success' => false,
                'message' => 'User already has a role for this business',
                'data' => ['current_role' => $currentRole],
                'error_code' => 'ROLE_CONFLICT',
                'timestamp' => now()->toISOString(),
            ], 409);
        }

        // Handle business owner assignment
        if ($request->input('role') === 'business-owner') {
            // Remove current owner's role
            $currentOwner = $business->owner;
            if ($currentOwner) {
                $this->removeUserBusinessRole($currentOwner, $business);
            }

            // Update business owner
            $business->update(['user_id' => $user->id]);
        }

        // Assign the role
        $this->assignUserBusinessRole($user, $business, $request->input('role'));

        // Format user response with roles
        $userData = [
            'id' => $user->id,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'roles' => $this->getUserBusinessRoles($user, $business),
        ];

        return $this->successResponse(
            $userData,
            'Admin assigned to business successfully'
        );
    }

    /**
     * Transfer business ownership.
     */
    public function transferOwnership(Request $request, string $id): JsonResponse
    {
        $business = Business::find($id);
        if (! $business) {
            return $this->notFoundResponse('Business not found');
        }

        $request->validate([
            'new_owner_id' => 'required|uuid|exists:users,id',
        ]);

        $newOwner = User::find($request->input('new_owner_id'));
        if (! $newOwner) {
            return $this->notFoundResponse('User not found');
        }
        $previousOwner = $business->owner;

        // Remove all existing roles from new owner
        $this->removeUserBusinessRole($newOwner, $business);

        // Update business owner first (this changes who is considered the owner)
        $business->update(['user_id' => $newOwner->id]);

        // Remove owner role from previous owner and assign manager role
        if ($previousOwner) {
            $this->removeUserBusinessRole($previousOwner, $business);
            $this->assignUserBusinessRole($previousOwner, $business, 'business-manager');
        }

        // Assign owner role to new owner
        $this->assignUserBusinessRole($newOwner, $business, 'business-owner');

        // Prepare response data
        $responseData = [
            'business' => [
                'id' => $business->id,
                'business_name' => $business->business_name,
                'user_id' => $business->user_id,
            ],
            'previous_owner' => $previousOwner ? [
                'id' => $previousOwner->id,
                'first_name' => $previousOwner->first_name,
                'last_name' => $previousOwner->last_name,
                'email' => $previousOwner->email,
            ] : null,
            'new_owner' => [
                'id' => $newOwner->id,
                'first_name' => $newOwner->first_name,
                'last_name' => $newOwner->last_name,
                'email' => $newOwner->email,
            ],
        ];

        return $this->successResponse(
            $responseData,
            'Business ownership transferred successfully'
        );
    }

    /**
     * Update admin role.
     */
    public function updateAdminRole(Request $request, string $id, string $userId): JsonResponse
    {
        $request->validate([
            'role' => 'required|in:business-admin,business-manager',
        ]);

        $business = Business::find($id);
        if (! $business) {
            return $this->notFoundResponse('Business not found');
        }

        $user = User::find($userId);
        if (! $user) {
            return $this->notFoundResponse('User not found');
        }

        // Prevent changing business owner role
        if ($business->user_id === $userId) {
            return $this->errorResponse(
                'Cannot change business owner role. Transfer ownership first.',
                403
            );
        }

        // Check if user has a business role
        $currentRole = $this->getUserBusinessRole($user, $business);
        if (! $currentRole) {
            return $this->notFoundResponse('User does not have a role for this business');
        }

        // Remove existing role and assign new one
        $this->removeUserBusinessRole($user, $business);
        $this->assignUserBusinessRole($user, $business, $request->input('role'));

        return $this->successResponse(
            null,
            'Admin role updated successfully'
        );
    }

    /**
     * Remove admin from business.
     */
    public function removeAdmin(string $id, string $userId): JsonResponse
    {
        $business = Business::find($id);
        if (! $business) {
            return $this->notFoundResponse('Business not found');
        }

        $user = User::find($userId);
        if (! $user) {
            return $this->notFoundResponse('User not found');
        }

        // Cannot remove owner
        if ($business->user_id === $userId) {
            return $this->errorResponse(
                'Cannot remove business owner. Transfer ownership first.',
                403
            );
        }

        // Check if user has a business role
        $currentRole = $this->getUserBusinessRole($user, $business);
        if (! $currentRole) {
            return $this->notFoundResponse('User does not have a role for this business');
        }

        // Remove all business roles from user
        $this->removeUserBusinessRole($user, $business);

        return $this->successResponse(
            null,
            'Admin removed from business successfully'
        );
    }

    /**
     * Handle logo upload using ImageStorageService.
     */
    private function handleLogoUpload($logoFile, Business $business): string
    {
        // Delete old logo if exists
        if ($business->logo_url) {
            Storage::disk('public')->delete($business->logo_url);
        }

        // Store new logo using ImageStorageService
        $imageService = app(ImageStorageService::class);
        $result = $imageService->storeImage(
            $logoFile,
            'business_logos',
            $business->id,
            [
                'visibility' => 'public',
                // Let ImageStorageService auto-select disk (will use public in testing)
                'sizes' => ['thumbnail', 'medium', 'large'], // Generate multiple sizes
            ]
        );

        // Return the medium size URL as the primary logo URL
        return $result['urls']['medium']['webp'] ?? $result['urls']['medium']['original'] ?? $result['urls']['large']['webp'];
    }

    /**
     * Get all users with business admin roles for a business.
     */
    private function getBusinessAdminUsers(Business $business): \Illuminate\Support\Collection
    {
        $adminUsers = collect();

        // Add business owner
        if ($business->owner) {
            $adminUsers->push($business->owner);
        }

        // Get users with cached roles for this business
        $businessRoles = Cache::get("business_roles_{$business->id}", []);

        foreach ($businessRoles as $userId => $role) {
            if ($userId !== $business->user_id) { // Don't duplicate owner
                $user = User::find($userId);
                if ($user) {
                    $adminUsers->push($user);
                }
            }
        }

        return $adminUsers;
    }

    /**
     * Get user's business roles for a specific business.
     */
    private function getUserBusinessRoles(User $user, Business $business): array
    {
        $roles = [];

        // Check if user is business owner
        if ($business->user_id === $user->id) {
            $roles[] = 'business-owner';
        }

        // Check cached roles
        $businessRoles = Cache::get("business_roles_{$business->id}", []);
        if (isset($businessRoles[$user->id])) {
            $role = $businessRoles[$user->id];
            if (! in_array($role, $roles)) {
                $roles[] = $role;
            }
        }

        return $roles;
    }

    /**
     * Get user's primary business role for a specific business.
     */
    private function getUserBusinessRole(User $user, Business $business): ?string
    {
        // Check if user is business owner
        if ($business->user_id === $user->id) {
            return 'business-owner';
        }

        // Check cached roles
        $businessRoles = Cache::get("business_roles_{$business->id}", []);

        return $businessRoles[$user->id] ?? null;
    }

    /**
     * Assign a business role to a user.
     */
    private function assignUserBusinessRole(User $user, Business $business, string $role): void
    {
        $businessRoles = Cache::get("business_roles_{$business->id}", []);
        $businessRoles[$user->id] = $role;
        Cache::put("business_roles_{$business->id}", $businessRoles, 3600); // Cache for 1 hour
    }

    /**
     * Remove all business roles from a user.
     */
    private function removeUserBusinessRole(User $user, Business $business): void
    {
        $businessRoles = Cache::get("business_roles_{$business->id}", []);
        unset($businessRoles[$user->id]);
        Cache::put("business_roles_{$business->id}", $businessRoles, 3600); // Cache for 1 hour
    }
}
