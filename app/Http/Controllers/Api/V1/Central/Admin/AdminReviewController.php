<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Services\Business\ReviewManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Review Controller
 *
 * Handles comprehensive review and rating management for administrators.
 * Manages review moderation, fraud detection, and business reputation oversight.
 */
class AdminReviewController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly ReviewManagementService $reviewService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all reviews with filtering and analytics.
     *
     * @group Admin Reviews
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'business_id' => 'sometimes|uuid|exists:businesses,id',
                'product_id' => 'sometimes|uuid|exists:products,id',
                'customer_id' => 'sometimes|uuid|exists:users,id',
                'rating' => 'sometimes|integer|min:1|max:5',
                'min_rating' => 'sometimes|integer|min:1|max:5',
                'is_approved' => 'sometimes|boolean',
                'is_verified_purchase' => 'sometimes|boolean',
                'has_images' => 'sometimes|boolean',
                'search' => 'sometimes|string|max:255',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
                'per_page' => 'sometimes|integer|min:1|max:100',
                'sort_by' => 'sometimes|string|in:created_at,rating,approved_at',
                'sort_direction' => 'sometimes|string|in:asc,desc',
            ]);

            $query = $this->reviewService->getReviews($validated);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'review',
                ],
                'sortFields' => [
                    'created_at',
                    'rating',
                    'approved_at',
                ],
                'filters' => [
                    'business_id' => ['type' => 'exact'],
                    'product_id' => ['type' => 'exact'],
                    'customer_id' => ['type' => 'exact'],
                    'rating' => ['type' => 'exact'],
                    'is_approved' => ['type' => 'boolean'],
                    'is_verified_purchase' => ['type' => 'boolean'],
                ],
                'message' => 'Reviews retrieved successfully',
                'entityName' => 'reviews',
                'transformer' => [$this, 'transformReview'],
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve reviews',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve reviews');
        }
    }

    /**
     * Get a specific review.
     *
     * @group Admin Reviews
     *
     * @authenticated
     */
    public function show(string $reviewId): JsonResponse
    {
        try {
            $review = \App\Models\Business\ProductReview::with([
                'product:id,name,business_id',
                'customer:id,first_name,last_name,email',
                'business:id,business_name',
                'approvedBy:id,first_name,last_name',
            ])->findOrFail($reviewId);

            return $this->successResponse(
                $this->transformReview($review),
                'Review retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Review not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve review',
                $e,
                ['review_id' => $reviewId]
            );

            return $this->serverErrorResponse('Failed to retrieve review');
        }
    }

    /**
     * Approve a review.
     *
     * @group Admin Reviews
     *
     * @authenticated
     */
    public function approve(Request $request, string $reviewId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'reason' => 'sometimes|string|max:500',
            ]);

            $review = $this->reviewService->approveReview(
                $reviewId,
                $validated['reason'] ?? null
            );

            return $this->successResponse(
                $this->transformReview($review),
                'Review approved successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Review not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to approve review',
                $e,
                ['review_id' => $reviewId]
            );

            return $this->serverErrorResponse('Failed to approve review');
        }
    }

    /**
     * Reject a review.
     *
     * @group Admin Reviews
     *
     * @authenticated
     */
    public function reject(Request $request, string $reviewId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'reason' => 'sometimes|string|max:500',
            ]);

            $review = $this->reviewService->rejectReview(
                $reviewId,
                $validated['reason'] ?? null
            );

            return $this->successResponse(
                $this->transformReview($review),
                'Review rejected successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Review not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to reject review',
                $e,
                ['review_id' => $reviewId]
            );

            return $this->serverErrorResponse('Failed to reject review');
        }
    }

    /**
     * Delete a review.
     *
     * @group Admin Reviews
     *
     * @authenticated
     */
    public function destroy(Request $request, string $reviewId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'reason' => 'sometimes|string|max:500',
            ]);

            $this->reviewService->deleteReview(
                $reviewId,
                $validated['reason'] ?? null
            );

            return $this->successResponse(
                null,
                'Review deleted successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Review not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to delete review',
                $e,
                ['review_id' => $reviewId]
            );

            return $this->serverErrorResponse('Failed to delete review');
        }
    }

    /**
     * Get review analytics and insights.
     *
     * @group Admin Reviews
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'business_id' => 'sometimes|uuid|exists:businesses,id',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
            ]);

            $analytics = $this->reviewService->getReviewAnalytics($validated);

            return $this->successResponse(
                $analytics,
                'Review analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve review analytics',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve review analytics');
        }
    }

    /**
     * Detect fake reviews.
     *
     * @group Admin Reviews
     *
     * @authenticated
     */
    public function fraudDetection(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'business_id' => 'sometimes|uuid|exists:businesses,id',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
            ]);

            $fraudAnalysis = $this->reviewService->detectFakeReviews($validated);

            return $this->successResponse(
                $fraudAnalysis,
                'Review fraud analysis completed'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to analyze review fraud',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to analyze review fraud');
        }
    }

    /**
     * Get business reputation summary.
     *
     * @group Admin Reviews
     *
     * @authenticated
     */
    public function businessReputation(string $businessId): JsonResponse
    {
        try {
            $reputation = $this->reviewService->getBusinessReputationSummary($businessId);

            return $this->successResponse(
                $reputation,
                'Business reputation summary retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve business reputation',
                $e,
                ['business_id' => $businessId]
            );

            return $this->serverErrorResponse('Failed to retrieve business reputation');
        }
    }

    /**
     * Bulk operations on reviews.
     *
     * @group Admin Reviews
     *
     * @authenticated
     */
    public function bulkOperations(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'review_ids' => 'required|array|min:1',
                'review_ids.*' => 'uuid|exists:product_reviews,id',
                'action' => 'required|string|in:approve,reject,delete',
                'reason' => 'sometimes|string|max:500',
            ]);

            $results = $this->reviewService->bulkReviewOperations(
                $validated['review_ids'],
                $validated['action'],
                array_filter([
                    'reason' => $validated['reason'] ?? null,
                ])
            );

            return $this->successResponse(
                $results,
                'Bulk review operations completed'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to perform bulk review operations',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to perform bulk operations');
        }
    }

    /**
     * Get pending reviews for moderation.
     *
     * @group Admin Reviews
     *
     * @authenticated
     */
    public function pendingReviews(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'business_id' => 'sometimes|uuid|exists:businesses,id',
                'per_page' => 'sometimes|integer|min:1|max:100',
            ]);

            $filters = array_merge($validated, ['is_approved' => false]);
            $query = $this->reviewService->getReviews($filters);

            return $this->handleQuery($query, $request, [
                'message' => 'Pending reviews retrieved successfully',
                'entityName' => 'reviews',
                'transformer' => [$this, 'transformReview'],
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve pending reviews',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve pending reviews');
        }
    }

    /**
     * Transform review for API response.
     */
    public function transformReview($review): array
    {
        return [
            'id' => $review->id,
            'tenant_id' => $review->tenant_id,
            'product' => $review->product ? [
                'id' => $review->product->id,
                'name' => $review->product->name,
                'business_id' => $review->product->business_id,
            ] : null,
            'customer' => $review->customer ? [
                'id' => $review->customer->id,
                'name' => $review->customer->first_name.' '.$review->customer->last_name,
                'email' => $review->customer->email,
            ] : null,
            'business' => $review->business ? [
                'id' => $review->business->id,
                'name' => $review->business->business_name,
            ] : null,
            'order_id' => $review->order_id,
            'rating' => $review->rating,
            'review' => $review->review,
            'images' => $review->images,
            'is_verified_purchase' => $review->is_verified_purchase,
            'is_approved' => $review->is_approved,
            'approved_at' => $review->approved_at,
            'approved_by' => $review->approvedBy ? [
                'id' => $review->approvedBy->id,
                'name' => $review->approvedBy->first_name.' '.$review->approvedBy->last_name,
            ] : null,
            'metadata' => $review->metadata,
            'created_at' => $review->created_at,
            'updated_at' => $review->updated_at,
        ];
    }
}
