<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Models\Delivery\Delivery;
use App\Models\Delivery\Order;
use App\Services\Delivery\RealTimeTrackingService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Admin Real-Time Tracking
 *
 * APIs for admins to monitor and manage real-time delivery tracking across the platform
 */
class AdminRealTimeTrackingController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly RealTimeTrackingService $trackingService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get Real-Time Dashboard
     *
     * Get comprehensive real-time tracking dashboard for all active deliveries.
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Real-time dashboard retrieved successfully",
     *   "data": {
     *     "summary": {
     *       "total_active_deliveries": 45,
     *       "deliveries_with_tracking": 38,
     *       "average_delivery_time": "32 minutes",
     *       "on_time_percentage": 87.5
     *     },
     *     "active_deliveries": [
     *       {
     *         "delivery_id": "uuid",
     *         "order_reference": "ORD-2024-ABC123",
     *         "customer_name": "John Doe",
     *         "business_name": "Mario's Pizza",
     *         "driver_name": "Jane Driver",
     *         "status": "in_transit",
     *         "current_location": {
     *           "latitude": 6.5244,
     *           "longitude": 3.3792,
     *           "timestamp": "2024-01-15T10:30:00Z"
     *         },
     *         "estimated_delivery_time": "2024-01-15T11:15:00Z",
     *         "is_delayed": false
     *       }
     *     ]
     *   }
     * }
     */
    public function dashboard(): JsonResponse
    {
        try {
            $activeDeliveries = Delivery::with([
                'order.customer:id,first_name,last_name',
                'order.business:id,business_name',
                'assignedDriver:id,first_name,last_name',
            ])
                ->whereIn('status', ['assigned', 'picked_up', 'in_transit'])
                ->get();

            $deliveriesWithTracking = $activeDeliveries->filter(function ($delivery) {
                return $this->trackingService->getCurrentDeliveryLocation($delivery->id) !== null;
            });

            $summary = [
                'total_active_deliveries' => $activeDeliveries->count(),
                'deliveries_with_tracking' => $deliveriesWithTracking->count(),
                'average_delivery_time' => $this->calculateAverageDeliveryTime(),
                'on_time_percentage' => $this->calculateOnTimePercentage(),
            ];

            $deliveryData = $activeDeliveries->map(function ($delivery) {
                $currentLocation = $this->trackingService->getCurrentDeliveryLocation($delivery->id);

                return [
                    'delivery_id' => $delivery->id,
                    'order_reference' => $delivery->order?->order_reference,
                    'customer_name' => $delivery->order?->customer ?
                        $delivery->order->customer->first_name.' '.$delivery->order->customer->last_name : null,
                    'business_name' => $delivery->order?->business?->business_name,
                    'driver_name' => $delivery->assignedDriver ?
                        $delivery->assignedDriver->first_name.' '.$delivery->assignedDriver->last_name : null,
                    'status' => $delivery->status->value,
                    'current_location' => $currentLocation,
                    'estimated_delivery_time' => $delivery->estimated_delivery_time?->toISOString(),
                    'is_delayed' => $this->isDeliveryDelayed($delivery),
                    'created_at' => $delivery->created_at?->toISOString(),
                ];
            });

            return $this->successResponse([
                'summary' => $summary,
                'active_deliveries' => $deliveryData,
            ], 'Real-time dashboard retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve admin real-time dashboard',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve real-time dashboard');
        }
    }

    /**
     * Track Specific Delivery
     *
     * Get detailed real-time tracking information for a specific delivery.
     *
     * @authenticated
     *
     * @urlParam delivery_id string required The delivery ID to track. Example: uuid
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Delivery tracking retrieved successfully",
     *   "data": {
     *     "delivery": {
     *       "id": "uuid",
     *       "tracking_number": "TRK-2024-ABC123",
     *       "status": "in_transit",
     *       "order_reference": "ORD-2024-ABC123"
     *     },
     *     "current_location": {
     *       "latitude": 6.5244,
     *       "longitude": 3.3792,
     *       "accuracy": 5.0,
     *       "speed": 25.5,
     *       "heading": 180.0,
     *       "timestamp": "2024-01-15T10:30:00Z"
     *     },
     *     "tracking_history": [
     *       {
     *         "latitude": 6.5200,
     *         "longitude": 3.3750,
     *         "timestamp": "2024-01-15T10:25:00Z"
     *       }
     *     ],
     *     "driver": {
     *       "id": "uuid",
     *       "name": "Jane Driver",
     *       "phone": "+2348987654321"
     *     },
     *     "customer": {
     *       "id": "uuid",
     *       "name": "John Doe",
     *       "phone": "+2348123456789"
     *     },
     *     "business": {
     *       "id": "uuid",
     *       "name": "Mario's Pizza",
     *       "address": "123 Business St, Lagos"
     *     }
     *   }
     * }
     */
    public function trackDelivery(string $deliveryId): JsonResponse
    {
        try {
            $delivery = Delivery::with([
                'order.customer:id,first_name,last_name,phone_number',
                'order.business:id,business_name,contact_phone',
                'assignedDriver:id,first_name,last_name,phone_number',
            ])->findOrFail($deliveryId);

            $currentLocation = $this->trackingService->getCurrentDeliveryLocation($delivery->id);
            $trackingHistory = $this->trackingService->getDeliveryTrackingHistory($delivery->id, 50);

            $trackingData = [
                'delivery' => [
                    'id' => $delivery->id,
                    'tracking_number' => $delivery->tracking_number,
                    'status' => $delivery->status->value,
                    'order_reference' => $delivery->order?->order_reference,
                    'estimated_delivery_time' => $delivery->estimated_delivery_time?->toISOString(),
                    'created_at' => $delivery->created_at?->toISOString(),
                ],
                'current_location' => $currentLocation,
                'tracking_history' => $trackingHistory,
                'driver' => $delivery->assignedDriver ? [
                    'id' => $delivery->assignedDriver->id,
                    'name' => $delivery->assignedDriver->first_name.' '.$delivery->assignedDriver->last_name,
                    'phone' => $delivery->assignedDriver->phone_number,
                ] : null,
                'customer' => $delivery->order?->customer ? [
                    'id' => $delivery->order->customer->id,
                    'name' => $delivery->order->customer->first_name.' '.$delivery->order->customer->last_name,
                    'phone' => $delivery->order->customer->phone_number,
                ] : null,
                'business' => $delivery->order?->business ? [
                    'id' => $delivery->order->business->id,
                    'name' => $delivery->order->business->business_name,
                    'phone' => $delivery->order->business->contact_phone,
                ] : null,
            ];

            return $this->successResponse(
                $trackingData,
                'Delivery tracking retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve delivery tracking',
                $e,
                ['delivery_id' => $deliveryId]
            );

            return $this->serverErrorResponse('Failed to retrieve delivery tracking');
        }
    }

    /**
     * Get All Active Trackable Deliveries
     *
     * Get all deliveries that are currently being tracked in real-time.
     *
     * @authenticated
     *
     * @queryParam status string Filter by delivery status. Example: in_transit
     * @queryParam driver_id string Filter by driver ID. Example: uuid
     * @queryParam business_id string Filter by business ID. Example: uuid
     * @queryParam delayed boolean Filter delayed deliveries only. Example: true
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Active trackable deliveries retrieved successfully",
     *   "data": [
     *     {
     *       "delivery_id": "uuid",
     *       "order_reference": "ORD-2024-ABC123",
     *       "customer_name": "John Doe",
     *       "business_name": "Mario's Pizza",
     *       "driver_name": "Jane Driver",
     *       "status": "in_transit",
     *       "current_location": {
     *         "latitude": 6.5244,
     *         "longitude": 3.3792,
     *         "timestamp": "2024-01-15T10:30:00Z"
     *       },
     *       "estimated_delivery_time": "2024-01-15T11:15:00Z",
     *       "is_delayed": false,
     *       "tracking_active": true
     *     }
     *   ]
     * }
     */
    public function getActiveTrackableDeliveries(Request $request): JsonResponse
    {
        try {
            $query = Delivery::with([
                'order.customer:id,first_name,last_name',
                'order.business:id,business_name',
                'assignedDriver:id,first_name,last_name',
            ])
                ->whereIn('status', ['assigned', 'picked_up', 'in_transit']);

            // Apply filters
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }

            if ($request->filled('driver_id')) {
                $query->where('assigned_driver_id', $request->input('driver_id'));
            }

            if ($request->filled('business_id')) {
                $query->whereHas('order', function ($q) use ($request) {
                    $q->where('business_id', $request->input('business_id'));
                });
            }

            $deliveries = $query->get();

            // Filter for only trackable deliveries and apply delayed filter
            $trackableDeliveries = $deliveries->filter(function ($delivery) use ($request) {
                $hasTracking = $this->trackingService->getCurrentDeliveryLocation($delivery->id) !== null;

                if (! $hasTracking) {
                    return false;
                }

                if ($request->boolean('delayed')) {
                    return $this->isDeliveryDelayed($delivery);
                }

                return true;
            });

            $deliveryData = $trackableDeliveries->map(function ($delivery) {
                $currentLocation = $this->trackingService->getCurrentDeliveryLocation($delivery->id);

                return [
                    'delivery_id' => $delivery->id,
                    'order_reference' => $delivery->order?->order_reference,
                    'customer_name' => $delivery->order?->customer ?
                        $delivery->order->customer->first_name.' '.$delivery->order->customer->last_name : null,
                    'business_name' => $delivery->order?->business?->business_name,
                    'driver_name' => $delivery->assignedDriver ?
                        $delivery->assignedDriver->first_name.' '.$delivery->assignedDriver->last_name : null,
                    'status' => $delivery->status->value,
                    'current_location' => $currentLocation,
                    'estimated_delivery_time' => $delivery->estimated_delivery_time?->toISOString(),
                    'is_delayed' => $this->isDeliveryDelayed($delivery),
                    'tracking_active' => true,
                    'created_at' => $delivery->created_at?->toISOString(),
                ];
            })->values();

            return $this->successResponse(
                $deliveryData,
                'Active trackable deliveries retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve active trackable deliveries',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve active trackable deliveries');
        }
    }

    /**
     * Calculate average delivery time.
     */
    private function calculateAverageDeliveryTime(): string
    {
        // This would calculate from completed deliveries
        // For now, return a mock value
        return '32 minutes';
    }

    /**
     * Calculate on-time delivery percentage.
     */
    private function calculateOnTimePercentage(): float
    {
        // This would calculate from delivery performance data
        // For now, return a mock value
        return 87.5;
    }

    /**
     * Check if delivery is delayed.
     */
    private function isDeliveryDelayed(Delivery $delivery): bool
    {
        if (! $delivery->estimated_delivery_time) {
            return false;
        }

        return now()->isAfter($delivery->estimated_delivery_time);
    }
}
