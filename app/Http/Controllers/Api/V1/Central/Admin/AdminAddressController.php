<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Models\User\Address;
use App\Services\Core\AddressManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Address Controller
 *
 * Handles address validation and management for administrators.
 * Manages address standardization, geocoding, validation, and delivery zone configuration.
 */
class AdminAddressController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly AddressManagementService $addressService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all addresses.
     *
     * @group Admin Addresses
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Address::query()
                ->with([
                    'country:id,name,code',
                    'state:id,name',
                    'city:id,name',
                    'user:id,first_name,last_name',
                ]);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'street_address',
                    'city_name_string',
                    'state_province_string',
                    'postal_code',
                    'label',
                ],
                'sortFields' => [
                    'street_address',
                    'city_name_string',
                    'postal_code',
                    'created_at',
                    'is_default',
                ],
                'filters' => [
                    'country_id' => ['type' => 'exact'],
                    'state_id' => ['type' => 'exact'],
                    'city_id' => ['type' => 'exact'],
                    'addressable_type' => ['type' => 'exact'],
                    'addressable_id' => ['type' => 'exact'],
                    'user_id' => ['type' => 'exact'],
                    'is_default' => ['type' => 'boolean'],
                    'has_coordinates' => ['type' => 'custom', 'callback' => function ($query, $value) {
                        if ($value) {
                            $query->whereNotNull('latitude')->whereNotNull('longitude');
                        } else {
                            $query->where(function ($q) {
                                $q->whereNull('latitude')->orWhereNull('longitude');
                            });
                        }
                    }],
                ],
                'message' => 'Addresses retrieved successfully',
                'entityName' => 'addresses',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve addresses',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve addresses');
        }
    }

    /**
     * Show specific address.
     *
     * @group Admin Addresses
     *
     * @authenticated
     */
    public function show(string $id): JsonResponse
    {
        try {
            $addressDetails = $this->addressService->getAddressWithDetails($id);

            return $this->successResponse(
                $addressDetails,
                'Address details retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Address not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve address details',
                $e,
                ['address_id' => $id]
            );

            return $this->serverErrorResponse('Failed to retrieve address details');
        }
    }

    /**
     * Validate address.
     *
     * @group Admin Addresses
     *
     * @authenticated
     */
    public function validate(string $id): JsonResponse
    {
        try {
            $address = Address::findOrFail($id);
            $validation = $this->addressService->validateAddress($address);

            return $this->successResponse(
                $validation,
                'Address validation completed'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Address not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to validate address',
                $e,
                ['address_id' => $id]
            );

            return $this->serverErrorResponse('Failed to validate address');
        }
    }

    /**
     * Geocode address.
     *
     * @group Admin Addresses
     *
     * @authenticated
     */
    public function geocode(string $id): JsonResponse
    {
        try {
            $result = $this->addressService->geocodeAddress($id);

            if ($result['success']) {
                return $this->successResponse(
                    $result,
                    'Address geocoded successfully'
                );
            } else {
                return $this->errorResponse($result['error'], 400);
            }

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Address not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to geocode address',
                $e,
                ['address_id' => $id]
            );

            return $this->serverErrorResponse('Failed to geocode address');
        }
    }

    /**
     * Standardize address.
     *
     * @group Admin Addresses
     *
     * @authenticated
     */
    public function standardize(string $id): JsonResponse
    {
        try {
            $address = $this->addressService->standardizeAddress($id);

            return $this->successResponse(
                $address,
                'Address standardized successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Address not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to standardize address',
                $e,
                ['address_id' => $id]
            );

            return $this->serverErrorResponse('Failed to standardize address');
        }
    }

    /**
     * Bulk validate addresses.
     *
     * @group Admin Addresses
     *
     * @authenticated
     */
    public function bulkValidate(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'address_ids' => 'required|array|min:1|max:100',
                'address_ids.*' => 'uuid|exists:addresses,id',
            ]);

            $results = $this->addressService->bulkValidateAddresses($validated['address_ids']);

            return $this->successResponse(
                $results,
                "Bulk validation completed: {$results['valid']} valid, {$results['invalid']} invalid"
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to bulk validate addresses',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to bulk validate addresses');
        }
    }

    /**
     * Search addresses.
     *
     * @group Admin Addresses
     *
     * @authenticated
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'query' => 'required|string|min:2|max:100',
                'addressable_type' => 'nullable|string',
            ]);

            $results = $this->addressService->searchAddresses(
                $validated['query'],
                $validated['addressable_type'] ?? null
            );

            return $this->successResponse(
                $results,
                'Address search completed successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to search addresses',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to search addresses');
        }
    }

    /**
     * Get address statistics.
     *
     * @group Admin Addresses
     *
     * @authenticated
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = $this->addressService->getAddressStatistics();

            return $this->successResponse(
                $stats,
                'Address statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve address statistics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve address statistics');
        }
    }

    /**
     * Export addresses.
     *
     * @group Admin Addresses
     *
     * @authenticated
     */
    public function export(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'format' => 'required|string|in:csv,json',
                'country_id' => 'nullable|uuid|exists:countries,id',
                'addressable_type' => 'nullable|string',
                'has_coordinates' => 'nullable|boolean',
            ]);

            $filename = $this->addressService->exportAddresses(
                array_filter($validated, fn ($value) => $value !== null),
                $validated['format']
            );

            return $this->successResponse(
                [
                    'filename' => $filename,
                    'download_url' => route('admin.addresses.download', ['filename' => $filename]),
                ],
                'Addresses exported successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to export addresses',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to export addresses');
        }
    }

    /**
     * Download exported addresses.
     *
     * @group Admin Addresses
     *
     * @authenticated
     */
    public function download(string $filename)
    {
        try {
            $filepath = storage_path('app/exports/'.$filename);

            if (! file_exists($filepath)) {
                return $this->notFoundResponse('Export file not found');
            }

            // Security check: ensure filename is safe
            if (! preg_match('/^addresses_export_\d{4}_\d{2}_\d{2}_\d{2}_\d{2}_\d{2}\.(csv|json)$/', $filename)) {
                return $this->errorResponse('Invalid filename', 400);
            }

            return response()->download($filepath)->deleteFileAfterSend();

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to download address export',
                $e,
                ['filename' => $filename]
            );

            return $this->serverErrorResponse('Failed to download address export');
        }
    }

    /**
     * Get addresses by type.
     *
     * @group Admin Addresses
     *
     * @authenticated
     */
    public function byType(Request $request, string $type): JsonResponse
    {
        try {
            $query = Address::where('addressable_type', $type)
                ->with([
                    'country:id,name,code',
                    'state:id,name',
                    'city:id,name',
                    'user:id,first_name,last_name',
                ]);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'street_address',
                    'city_name_string',
                    'postal_code',
                ],
                'sortFields' => [
                    'street_address',
                    'created_at',
                ],
                'filters' => [
                    'country_id' => ['type' => 'exact'],
                    'is_default' => ['type' => 'boolean'],
                ],
                'message' => "Addresses of type '{$type}' retrieved successfully",
                'entityName' => 'addresses',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve addresses by type',
                $e,
                ['type' => $type]
            );

            return $this->serverErrorResponse('Failed to retrieve addresses by type');
        }
    }

    /**
     * Get delivery zones for address.
     *
     * @group Admin Addresses
     *
     * @authenticated
     */
    public function deliveryZones(string $id): JsonResponse
    {
        try {
            $address = Address::findOrFail($id);
            $zones = $this->addressService->getDeliveryZones($address);

            return $this->successResponse(
                $zones,
                'Delivery zones retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Address not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve delivery zones',
                $e,
                ['address_id' => $id]
            );

            return $this->serverErrorResponse('Failed to retrieve delivery zones');
        }
    }
}
