<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Enums\System\TicketStatus;
use App\Http\Controllers\Controller;
use App\Models\System\SupportMessage;
use App\Models\User\SupportTicket;
use App\Services\System\LoggingService;
use App\Services\System\SupportManagementService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;

/**
 * Admin Support Controller
 *
 * Handles customer support management for administrators.
 * Manages support tickets, agent assignments, and customer service operations.
 */
class AdminSupportController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly SupportManagementService $supportService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all support tickets.
     *
     * @group Admin Support
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = SupportTicket::query()
                ->with([
                    'creator',
                    'assignedTo:id,first_name,last_name,email',
                ])
                ->withCount('messages');

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'subject',
                    'description',
                ],
                'sortFields' => [
                    'subject',
                    'status',
                    'priority',
                    'created_at',
                    'resolved_at',
                    'closed_at',
                ],
                'filters' => [
                    'status' => ['type' => 'exact'],
                    'priority' => ['type' => 'exact'],
                    'assigned_to_user_id' => ['type' => 'exact'],
                    'created_by_type' => ['type' => 'exact'],
                ],
                'message' => 'Support tickets retrieved successfully',
                'entityName' => 'tickets',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve support tickets',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve support tickets');
        }
    }

    /**
     * Show specific support ticket.
     *
     * @group Admin Support
     *
     * @authenticated
     */
    public function show(string $id): JsonResponse
    {
        try {
            $ticketDetails = $this->supportService->getTicketWithDetails($id);

            return $this->successResponse(
                $ticketDetails,
                'Support ticket details retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Support ticket not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve support ticket details',
                $e,
                ['ticket_id' => $id]
            );

            return $this->serverErrorResponse('Failed to retrieve support ticket details');
        }
    }

    /**
     * Assign ticket to support agent.
     *
     * @group Admin Support
     *
     * @authenticated
     */
    public function assign(Request $request, string $id): JsonResponse
    {
        try {
            $validated = $request->validate([
                'agent_id' => 'required|uuid|exists:users,id',
            ]);

            $ticket = $this->supportService->assignTicket($id, $validated['agent_id']);

            return $this->successResponse(
                $ticket->load(['assignedTo:id,first_name,last_name,email']),
                'Ticket assigned successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Support ticket not found');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to assign support ticket',
                $e,
                ['ticket_id' => $id, 'request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to assign support ticket');
        }
    }

    /**
     * Update ticket status.
     *
     * @group Admin Support
     *
     * @authenticated
     */
    public function updateStatus(Request $request, string $id): JsonResponse
    {
        try {
            $validated = $request->validate([
                'status' => ['required', Rule::enum(TicketStatus::class)],
                'note' => 'nullable|string|max:500',
            ]);

            $ticket = $this->supportService->updateTicketStatus(
                $id,
                TicketStatus::from($validated['status']),
                $validated['note'] ?? null
            );

            return $this->successResponse(
                $ticket,
                'Ticket status updated successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Support ticket not found');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update support ticket status',
                $e,
                ['ticket_id' => $id, 'request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update support ticket status');
        }
    }

    /**
     * Update ticket priority.
     *
     * @group Admin Support
     *
     * @authenticated
     */
    public function updatePriority(Request $request, string $id): JsonResponse
    {
        try {
            $validated = $request->validate([
                'priority' => 'required|string|in:low,medium,high,urgent',
                'reason' => 'nullable|string|max:500',
            ]);

            $ticket = $this->supportService->updateTicketPriority(
                $id,
                $validated['priority'],
                $validated['reason'] ?? null
            );

            return $this->successResponse(
                $ticket,
                'Ticket priority updated successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Support ticket not found');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update support ticket priority',
                $e,
                ['ticket_id' => $id, 'request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update support ticket priority');
        }
    }

    /**
     * Add message to ticket.
     *
     * @group Admin Support
     *
     * @authenticated
     */
    public function addMessage(Request $request, string $id): JsonResponse
    {
        try {
            $validated = $request->validate([
                'message' => 'required|string|max:2000',
                'is_internal' => 'nullable|boolean',
            ]);

            $message = $this->supportService->addMessage(
                $id,
                $validated['message'],
                $validated['is_internal'] ?? false
            );

            return $this->successResponse(
                $message->load('sender'),
                'Message added successfully',
                201
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Support ticket not found');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to add message to support ticket',
                $e,
                ['ticket_id' => $id, 'request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to add message to support ticket');
        }
    }

    /**
     * Get ticket messages.
     *
     * @group Admin Support
     *
     * @authenticated
     */
    public function messages(Request $request, string $id): JsonResponse
    {
        try {
            $query = SupportMessage::where('ticket_id', $id)
                ->with('sender');

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'message',
                ],
                'sortFields' => [
                    'created_at',
                ],
                'filters' => [
                    'sender_type' => ['type' => 'exact'],
                    'is_internal' => ['type' => 'boolean'],
                ],
                'message' => 'Ticket messages retrieved successfully',
                'entityName' => 'messages',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve ticket messages',
                $e,
                ['ticket_id' => $id]
            );

            return $this->serverErrorResponse('Failed to retrieve ticket messages');
        }
    }

    /**
     * Get support statistics.
     *
     * @group Admin Support
     *
     * @authenticated
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = $this->supportService->getSupportStatistics();

            return $this->successResponse(
                $stats,
                'Support statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve support statistics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve support statistics');
        }
    }

    /**
     * Get agent performance.
     *
     * @group Admin Support
     *
     * @authenticated
     */
    public function agentPerformance(): JsonResponse
    {
        try {
            $performance = $this->supportService->getAgentPerformance();

            return $this->successResponse(
                $performance,
                'Agent performance data retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve agent performance data',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve agent performance data');
        }
    }

    /**
     * Get tickets requiring attention.
     *
     * @group Admin Support
     *
     * @authenticated
     */
    public function requiresAttention(): JsonResponse
    {
        try {
            $tickets = $this->supportService->getTicketsRequiringAttention();

            return $this->successResponse(
                $tickets,
                'Tickets requiring attention retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve tickets requiring attention',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve tickets requiring attention');
        }
    }

    /**
     * Bulk assign tickets.
     *
     * @group Admin Support
     *
     * @authenticated
     */
    public function bulkAssign(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'ticket_ids' => 'required|array|min:1',
                'ticket_ids.*' => 'uuid|exists:support_tickets,id',
                'agent_id' => 'required|uuid|exists:users,id',
            ]);

            $results = $this->supportService->bulkAssignTickets(
                $validated['ticket_ids'],
                $validated['agent_id']
            );

            return $this->successResponse(
                $results,
                "Bulk assignment completed: {$results['success']} successful, {$results['failed']} failed"
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to bulk assign tickets',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to bulk assign tickets');
        }
    }

    /**
     * Bulk update ticket status.
     *
     * @group Admin Support
     *
     * @authenticated
     */
    public function bulkUpdateStatus(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'ticket_ids' => 'required|array|min:1',
                'ticket_ids.*' => 'uuid|exists:support_tickets,id',
                'status' => ['required', Rule::enum(TicketStatus::class)],
            ]);

            $results = $this->supportService->bulkUpdateStatus(
                $validated['ticket_ids'],
                TicketStatus::from($validated['status'])
            );

            return $this->successResponse(
                $results,
                "Bulk status update completed: {$results['success']} successful, {$results['failed']} failed"
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to bulk update ticket status',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to bulk update ticket status');
        }
    }
}
