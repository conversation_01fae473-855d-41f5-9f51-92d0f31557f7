<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Models\Delivery\DeliveryZone;
use App\Services\Delivery\ZoneManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Zone Controller
 *
 * Handles delivery zone management for administrators.
 * Manages geographic zones and delivery coverage areas.
 */
class AdminZoneController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly ZoneManagementService $zoneService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all delivery zones.
     *
     * @group Admin Zones
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = DeliveryZone::query()
                ->with(['providers']);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'name',
                    'description',
                ],
                'sortFields' => [
                    'name',
                    'zone_type',
                    'base_multiplier',
                    'created_at',
                    'is_active',
                ],
                'filters' => [
                    'zone_type' => ['type' => 'exact'],
                    'is_active' => ['type' => 'boolean'],
                ],
                'message' => 'Delivery zones retrieved successfully',
                'entityName' => 'zones',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve delivery zones',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve delivery zones');
        }
    }

    /**
     * Get zone details.
     *
     * @group Admin Zones
     *
     * @authenticated
     */
    public function show(Request $request, string $zoneId): JsonResponse
    {
        try {
            $zone = $this->zoneService->getZoneWithDetails($zoneId);

            return $this->successResponse(
                $zone,
                'Zone details retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve zone details',
                $e,
                ['zone_id' => $zoneId]
            );

            return $this->serverErrorResponse('Failed to retrieve zone details');
        }
    }

    /**
     * Create new delivery zone.
     *
     * @group Admin Zones
     *
     * @authenticated
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'zone_type' => 'required|string|in:city,state,interstate,polygon',
            'states' => 'required_if:zone_type,state,interstate|array',
            'states.*' => 'uuid|exists:states,id',
            'cities' => 'required_if:zone_type,city|array',
            'cities.*' => 'uuid|exists:cities,id',
            'polygon_coordinates' => 'required_if:zone_type,polygon|array',
            'base_multiplier' => 'required|numeric|min:0.1|max:10',
            'description' => 'sometimes|string|max:1000',
            'is_active' => 'sometimes|boolean',
        ]);

        try {
            $zone = $this->zoneService->createZone($request->validated());

            return $this->createdResponse(
                $zone,
                'Delivery zone created successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create delivery zone',
                $e,
                ['request_data' => $request->validated()]
            );

            return $this->serverErrorResponse('Failed to create delivery zone');
        }
    }

    /**
     * Update delivery zone.
     *
     * @group Admin Zones
     *
     * @authenticated
     */
    public function update(Request $request, string $zoneId): JsonResponse
    {
        $request->validate([
            'name' => 'sometimes|string|max:255',
            'zone_type' => 'sometimes|string|in:city,state,interstate,polygon',
            'states' => 'sometimes|array',
            'states.*' => 'uuid|exists:states,id',
            'cities' => 'sometimes|array',
            'cities.*' => 'uuid|exists:cities,id',
            'polygon_coordinates' => 'sometimes|array',
            'base_multiplier' => 'sometimes|numeric|min:0.1|max:10',
            'description' => 'sometimes|string|max:1000',
            'is_active' => 'sometimes|boolean',
        ]);

        try {
            $zone = $this->zoneService->updateZone($zoneId, $request->validated());

            return $this->successResponse(
                $zone,
                'Delivery zone updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update delivery zone',
                $e,
                ['zone_id' => $zoneId, 'request_data' => $request->validated()]
            );

            return $this->serverErrorResponse('Failed to update delivery zone');
        }
    }

    /**
     * Delete delivery zone.
     *
     * @group Admin Zones
     *
     * @authenticated
     */
    public function destroy(Request $request, string $zoneId): JsonResponse
    {
        try {
            $this->zoneService->deleteZone($zoneId);

            return $this->successResponse(
                null,
                'Delivery zone deleted successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to delete delivery zone',
                $e,
                ['zone_id' => $zoneId]
            );

            return $this->serverErrorResponse('Failed to delete delivery zone');
        }
    }

    /**
     * Toggle zone active status.
     *
     * @group Admin Zones
     *
     * @authenticated
     */
    public function toggleStatus(Request $request, string $zoneId): JsonResponse
    {
        $request->validate([
            'is_active' => 'required|boolean',
        ]);

        try {
            $zone = $this->zoneService->toggleZoneStatus($zoneId, $request->boolean('is_active'));

            return $this->successResponse(
                $zone,
                'Zone status updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to toggle zone status',
                $e,
                ['zone_id' => $zoneId]
            );

            return $this->serverErrorResponse('Failed to update zone status');
        }
    }

    /**
     * Get zone coverage analytics.
     *
     * @group Admin Zones
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        $request->validate([
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
        ]);

        try {
            $period = $request->input('period', 'month');
            $analytics = $this->zoneService->getZoneAnalytics($period);

            return $this->successResponse(
                $analytics,
                'Zone analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve zone analytics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve zone analytics');
        }
    }

    /**
     * Get zone coverage map.
     *
     * @group Admin Zones
     *
     * @authenticated
     */
    public function coverage(Request $request): JsonResponse
    {
        try {
            $coverage = $this->zoneService->getZoneCoverage();

            return $this->successResponse(
                $coverage,
                'Zone coverage retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve zone coverage',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve zone coverage');
        }
    }

    /**
     * Test zone coverage for route.
     *
     * @group Admin Zones
     *
     * @authenticated
     */
    public function testCoverage(Request $request): JsonResponse
    {
        $request->validate([
            'from_state_id' => 'required|uuid|exists:states,id',
            'to_state_id' => 'required|uuid|exists:states,id',
            'from_city_id' => 'sometimes|uuid|exists:cities,id',
            'to_city_id' => 'sometimes|uuid|exists:cities,id',
        ]);

        try {
            $coverage = $this->zoneService->testRouteCoverage(
                $request->input('from_state_id'),
                $request->input('to_state_id'),
                $request->input('from_city_id'),
                $request->input('to_city_id')
            );

            return $this->successResponse(
                $coverage,
                'Route coverage tested successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to test route coverage',
                $e,
                $request->only(['from_state_id', 'to_state_id'])
            );

            return $this->serverErrorResponse('Failed to test route coverage');
        }
    }

    /**
     * Get zone statistics.
     *
     * @group Admin Zones
     *
     * @authenticated
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $statistics = $this->zoneService->getZoneStatistics();

            return $this->successResponse(
                $statistics,
                'Zone statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve zone statistics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve zone statistics');
        }
    }
}
