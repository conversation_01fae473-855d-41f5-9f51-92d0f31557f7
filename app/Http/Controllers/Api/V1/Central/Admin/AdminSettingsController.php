<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Models\System\ConfigurationSetting;
use App\Services\System\LoggingService;
use App\Services\System\SettingsManagementService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Settings Controller
 *
 * Handles global settings management for administrators.
 * Manages platform-wide settings, default configurations, regional settings, and business rules.
 */
class AdminSettingsController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly SettingsManagementService $settingsService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all platform settings.
     *
     * @group Admin Settings
     *
     * @authenticated
     */
    public function index(): JsonResponse
    {
        try {
            $settings = $this->settingsService->getPlatformSettings();

            return $this->successResponse(
                $settings,
                'Platform settings retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve platform settings',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve platform settings');
        }
    }

    /**
     * Get settings grouped by category.
     *
     * @group Admin Settings
     *
     * @authenticated
     */
    public function grouped(): JsonResponse
    {
        try {
            $grouped = $this->settingsService->getAllSettingsGrouped();

            return $this->successResponse(
                $grouped,
                'Grouped settings retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve grouped settings',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve grouped settings');
        }
    }

    /**
     * Update a specific setting.
     *
     * @group Admin Settings
     *
     * @authenticated
     */
    public function updateSetting(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'key' => 'required|string|exists:configuration_settings,key',
                'value' => 'required',
            ]);

            $setting = $this->settingsService->updateSetting(
                $validated['key'],
                $validated['value']
            );

            return $this->successResponse(
                $setting,
                'Setting updated successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Setting not found');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 400);
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update setting',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update setting');
        }
    }

    /**
     * Bulk update settings.
     *
     * @group Admin Settings
     *
     * @authenticated
     */
    public function bulkUpdate(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'settings' => 'required|array|min:1|max:50',
                'settings.*' => 'required',
            ]);

            $results = $this->settingsService->bulkUpdateSettings($validated['settings']);

            return $this->successResponse(
                $results,
                "Bulk update completed: {$results['updated']} updated, {$results['failed']} failed"
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to bulk update settings',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to bulk update settings');
        }
    }

    /**
     * Reset settings to default values.
     *
     * @group Admin Settings
     *
     * @authenticated
     */
    public function resetToDefaults(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'setting_keys' => 'nullable|array',
                'setting_keys.*' => 'string|exists:configuration_settings,key',
            ]);

            $results = $this->settingsService->resetToDefaults(
                $validated['setting_keys'] ?? []
            );

            return $this->successResponse(
                $results,
                "Reset completed: {$results['reset_count']} settings reset to defaults"
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to reset settings to defaults',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to reset settings to defaults');
        }
    }

    /**
     * Get regional settings.
     *
     * @group Admin Settings
     *
     * @authenticated
     */
    public function regional(): JsonResponse
    {
        try {
            $settings = $this->settingsService->getRegionalSettings();

            return $this->successResponse(
                $settings,
                'Regional settings retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve regional settings',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve regional settings');
        }
    }

    /**
     * Update regional settings.
     *
     * @group Admin Settings
     *
     * @authenticated
     */
    public function updateRegional(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'default_country' => 'nullable|string|size:2',
                'default_currency' => 'nullable|string|size:3',
                'default_timezone' => 'nullable|string',
                'default_language' => 'nullable|string|size:2',
                'supported_countries' => 'nullable|array',
                'supported_countries.*' => 'string|size:2',
                'supported_currencies' => 'nullable|array',
                'supported_currencies.*' => 'string|size:3',
                'supported_languages' => 'nullable|array',
                'supported_languages.*' => 'string|size:2',
                'date_format' => 'nullable|string',
                'time_format' => 'nullable|string',
                'number_format' => 'nullable|string',
            ]);

            $results = $this->settingsService->updateRegionalSettings($validated);

            return $this->successResponse(
                $results,
                'Regional settings updated successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update regional settings',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update regional settings');
        }
    }

    /**
     * Get business rules configuration.
     *
     * @group Admin Settings
     *
     * @authenticated
     */
    public function businessRules(): JsonResponse
    {
        try {
            $rules = $this->settingsService->getBusinessRules();

            return $this->successResponse(
                $rules,
                'Business rules retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve business rules',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve business rules');
        }
    }

    /**
     * Get delivery configuration.
     *
     * @group Admin Settings
     *
     * @authenticated
     */
    public function deliveryConfig(): JsonResponse
    {
        try {
            $config = $this->settingsService->getDeliveryConfiguration();

            return $this->successResponse(
                $config,
                'Delivery configuration retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve delivery configuration',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve delivery configuration');
        }
    }

    /**
     * Export settings.
     *
     * @group Admin Settings
     *
     * @authenticated
     */
    public function export(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'groups' => 'nullable|array',
                'groups.*' => 'string',
            ]);

            $export = $this->settingsService->exportSettings(
                $validated['groups'] ?? []
            );

            return $this->successResponse(
                $export,
                'Settings exported successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to export settings',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to export settings');
        }
    }

    /**
     * Import settings.
     *
     * @group Admin Settings
     *
     * @authenticated
     */
    public function import(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'settings_data' => 'required|array',
                'settings_data.settings' => 'required|array',
                'settings_data.settings.*.key' => 'required|string',
                'settings_data.settings.*.value' => 'required',
                'settings_data.settings.*.group' => 'required|string',
            ]);

            $results = $this->settingsService->importSettings($validated['settings_data']);

            return $this->successResponse(
                $results,
                "Import completed: {$results['imported']} imported, {$results['updated']} updated, {$results['failed']} failed"
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to import settings',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to import settings');
        }
    }

    /**
     * Get all configuration settings with pagination.
     *
     * @group Admin Settings
     *
     * @authenticated
     */
    public function settings(Request $request): JsonResponse
    {
        try {
            $query = ConfigurationSetting::query()
                ->with(['group:id,name,slug']);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'key',
                    'label',
                    'description',
                ],
                'sortFields' => [
                    'key',
                    'label',
                    'type',
                    'is_active',
                    'created_at',
                ],
                'filters' => [
                    'group_id' => ['type' => 'exact'],
                    'type' => ['type' => 'exact'],
                    'is_active' => ['type' => 'boolean'],
                    'is_public' => ['type' => 'boolean'],
                ],
                'message' => 'Configuration settings retrieved successfully',
                'entityName' => 'settings',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve configuration settings',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve configuration settings');
        }
    }

    /**
     * Show specific setting.
     *
     * @group Admin Settings
     *
     * @authenticated
     */
    public function show(string $key): JsonResponse
    {
        try {
            $setting = ConfigurationSetting::with(['group'])
                ->where('key', $key)
                ->firstOrFail();

            return $this->successResponse(
                [
                    'setting' => $setting,
                    'typed_value' => $setting->getTypedValue(),
                ],
                'Setting details retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Setting not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve setting details',
                $e,
                ['setting_key' => $key]
            );

            return $this->serverErrorResponse('Failed to retrieve setting details');
        }
    }
}
