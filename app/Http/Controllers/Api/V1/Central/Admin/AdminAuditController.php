<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Models\System\AuditLog;
use App\Services\System\AuditManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Audit Controller
 *
 * Handles comprehensive audit management for administrators.
 * Manages audit trails, compliance reporting, security monitoring, and data access auditing.
 */
class AdminAuditController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly AuditManagementService $auditService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all audit logs.
     *
     * @group Admin Audit
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = AuditLog::query()
                ->with(['user:id,first_name,last_name,email']);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'action_type',
                    'auditable_type',
                    'ip_address',
                    'user_agent',
                ],
                'sortFields' => [
                    'created_at',
                    'action_type',
                    'auditable_type',
                    'user_id',
                ],
                'filters' => [
                    'user_id' => ['type' => 'exact'],
                    'action_type' => ['type' => 'exact'],
                    'auditable_type' => ['type' => 'exact'],
                    'auditable_id' => ['type' => 'exact'],
                    'ip_address' => ['type' => 'exact'],
                ],
                'message' => 'Audit logs retrieved successfully',
                'entityName' => 'audit_logs',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve audit logs',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve audit logs');
        }
    }

    /**
     * Show specific audit log.
     *
     * @group Admin Audit
     *
     * @authenticated
     */
    public function show(string $id): JsonResponse
    {
        try {
            $auditLog = AuditLog::with([
                'user:id,first_name,last_name,email',
                'auditable',
            ])->findOrFail($id);

            return $this->successResponse(
                $auditLog,
                'Audit log details retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Audit log not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve audit log details',
                $e,
                ['audit_log_id' => $id]
            );

            return $this->serverErrorResponse('Failed to retrieve audit log details');
        }
    }

    /**
     * Get audit statistics.
     *
     * @group Admin Audit
     *
     * @authenticated
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
                'user_id' => 'nullable|uuid|exists:users,id',
                'action_type' => 'nullable|string',
                'auditable_type' => 'nullable|string',
            ]);

            $statistics = $this->auditService->getAuditStatistics($validated);

            return $this->successResponse(
                $statistics,
                'Audit statistics retrieved successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve audit statistics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve audit statistics');
        }
    }

    /**
     * Get audit summary for dashboard.
     *
     * @group Admin Audit
     *
     * @authenticated
     */
    public function summary(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
            ]);

            $summary = $this->auditService->getAuditSummary($validated);

            return $this->successResponse(
                $summary,
                'Audit summary retrieved successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve audit summary',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve audit summary');
        }
    }

    /**
     * Get security events.
     *
     * @group Admin Audit
     *
     * @authenticated
     */
    public function securityEvents(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'hours' => 'nullable|integer|min:1|max:168', // Max 1 week
            ]);

            $events = $this->auditService->getSecurityEvents(
                $validated['hours'] ?? 24
            );

            return $this->successResponse(
                $events,
                'Security events retrieved successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve security events',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve security events');
        }
    }

    /**
     * Get suspicious activity patterns.
     *
     * @group Admin Audit
     *
     * @authenticated
     */
    public function suspiciousActivity(): JsonResponse
    {
        try {
            $patterns = $this->auditService->getSuspiciousActivity();

            return $this->successResponse(
                $patterns,
                'Suspicious activity patterns retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve suspicious activity patterns',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve suspicious activity patterns');
        }
    }

    /**
     * Export audit logs.
     *
     * @group Admin Audit
     *
     * @authenticated
     */
    public function export(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'format' => 'required|string|in:csv,json',
                'date_from' => 'nullable|date',
                'date_to' => 'nullable|date|after_or_equal:date_from',
                'user_id' => 'nullable|uuid|exists:users,id',
                'action_type' => 'nullable|string',
                'auditable_type' => 'nullable|string',
            ]);

            $filename = $this->auditService->exportAuditLogs(
                array_filter($validated, fn ($value) => $value !== null),
                $validated['format']
            );

            return $this->successResponse(
                [
                    'filename' => $filename,
                    'download_url' => route('admin.audit.download', ['filename' => $filename]),
                ],
                'Audit logs exported successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to export audit logs',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to export audit logs');
        }
    }

    /**
     * Download exported audit logs.
     *
     * @group Admin Audit
     *
     * @authenticated
     */
    public function download(string $filename)
    {
        try {
            $filepath = storage_path('app/exports/'.$filename);

            if (! file_exists($filepath)) {
                return $this->notFoundResponse('Export file not found');
            }

            // Security check: ensure filename is safe
            if (! preg_match('/^audit_logs_\d{4}_\d{2}_\d{2}_\d{2}_\d{2}_\d{2}\.(csv|json)$/', $filename)) {
                return $this->badRequestResponse('Invalid filename');
            }

            return response()->download($filepath)->deleteFileAfterSend();

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to download audit export',
                $e,
                ['filename' => $filename]
            );

            return $this->serverErrorResponse('Failed to download audit export');
        }
    }

    /**
     * Get compliance report.
     *
     * @group Admin Audit
     *
     * @authenticated
     */
    public function complianceReport(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'date_from' => 'required|date',
                'date_to' => 'required|date|after_or_equal:date_from',
            ]);

            $report = $this->auditService->getComplianceReport(
                new \DateTime($validated['date_from']),
                new \DateTime($validated['date_to'])
            );

            return $this->successResponse(
                $report,
                'Compliance report generated successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to generate compliance report',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to generate compliance report');
        }
    }

    /**
     * Get audit logs by user.
     *
     * @group Admin Audit
     *
     * @authenticated
     */
    public function byUser(Request $request, string $userId): JsonResponse
    {
        try {
            $query = AuditLog::byUser($userId)
                ->with(['user:id,first_name,last_name,email']);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'action_type',
                    'auditable_type',
                ],
                'sortFields' => [
                    'created_at',
                    'action_type',
                    'auditable_type',
                ],
                'filters' => [
                    'action_type' => ['type' => 'exact'],
                    'auditable_type' => ['type' => 'exact'],
                ],
                'message' => 'User audit logs retrieved successfully',
                'entityName' => 'audit_logs',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve user audit logs',
                $e,
                ['user_id' => $userId]
            );

            return $this->serverErrorResponse('Failed to retrieve user audit logs');
        }
    }

    /**
     * Get audit logs by model.
     *
     * @group Admin Audit
     *
     * @authenticated
     */
    public function byModel(Request $request, string $modelType, string $modelId): JsonResponse
    {
        try {
            $query = AuditLog::byAuditableType($modelType)
                ->byAuditableId($modelId)
                ->with(['user:id,first_name,last_name,email']);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'action_type',
                ],
                'sortFields' => [
                    'created_at',
                    'action_type',
                ],
                'filters' => [
                    'action_type' => ['type' => 'exact'],
                    'user_id' => ['type' => 'exact'],
                ],
                'message' => 'Model audit logs retrieved successfully',
                'entityName' => 'audit_logs',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve model audit logs',
                $e,
                ['model_type' => $modelType, 'model_id' => $modelId]
            );

            return $this->serverErrorResponse('Failed to retrieve model audit logs');
        }
    }
}
