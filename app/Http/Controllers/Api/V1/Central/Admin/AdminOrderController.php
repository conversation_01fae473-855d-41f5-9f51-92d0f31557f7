<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Enums\Delivery\OrderStatus;
use App\Http\Controllers\Controller;
use App\Models\Delivery\Order;
use App\Services\Order\OrderManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Order Controller
 *
 * Handles cross-tenant order management for administrators.
 * Provides comprehensive order oversight and management capabilities.
 */
class AdminOrderController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly OrderManagementService $orderService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all orders across tenants.
     *
     * @group Admin Orders
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Order::query()
                ->with([
                    'customer:id,first_name,last_name,email,phone_number',
                    'business:id,business_name,tenant_id',
                    'delivery:id,status,delivery_provider_id',
                    'payment:id,status,amount,currency',
                ]);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'order_number',
                    'customer.first_name',
                    'customer.last_name',
                    'customer.email',
                    'business.business_name',
                ],
                'sortFields' => [
                    'created_at',
                    'status',
                    'total_amount',
                    'order_number',
                ],
                'filters' => [
                    'status' => ['type' => 'exact'],
                    'business_id' => ['type' => 'exact'],
                    'tenant_id' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->whereHas('business', function ($q) use ($value) {
                                $q->where('tenant_id', $value);
                            });
                        },
                    ],
                    'payment_status' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->whereHas('payment', function ($q) use ($value) {
                                $q->where('status', $value);
                            });
                        },
                    ],
                    'date_from' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->where('created_at', '>=', $value);
                        },
                    ],
                    'date_to' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->where('created_at', '<=', $value);
                        },
                    ],
                ],
                'message' => 'Orders retrieved successfully',
                'entityName' => 'orders',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve orders',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve orders');
        }
    }

    /**
     * Get order details.
     *
     * @group Admin Orders
     *
     * @authenticated
     */
    public function show(Request $request, string $orderId): JsonResponse
    {
        try {
            $order = $this->orderService->getOrderWithDetails($orderId);

            return $this->successResponse(
                $order,
                'Order details retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve order details',
                $e,
                ['order_id' => $orderId]
            );

            return $this->serverErrorResponse('Failed to retrieve order details');
        }
    }

    /**
     * Update order status.
     *
     * @group Admin Orders
     *
     * @authenticated
     */
    public function updateStatus(Request $request, string $orderId): JsonResponse
    {
        $request->validate([
            'status' => 'required|string|in:pending,confirmed,preparing,ready_for_pickup,completed,cancelled',
            'reason' => 'sometimes|string|max:500',
            'notes' => 'sometimes|string|max:1000',
        ]);

        try {
            $order = $this->orderService->updateOrderStatus(
                $orderId,
                $request->input('status'),
                $request->input('reason'),
                $request->input('notes')
            );

            return $this->successResponse(
                $order,
                'Order status updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update order status',
                $e,
                ['order_id' => $orderId]
            );

            return $this->serverErrorResponse('Failed to update order status');
        }
    }

    /**
     * Cancel order.
     *
     * @group Admin Orders
     *
     * @authenticated
     */
    public function cancel(Request $request, string $orderId): JsonResponse
    {
        $request->validate([
            'reason' => 'required|string|max:500',
            'refund_amount' => 'sometimes|numeric|min:0',
            'notify_customer' => 'sometimes|boolean',
        ]);

        try {
            $result = $this->orderService->cancelOrder(
                $orderId,
                $request->input('reason'),
                $request->input('refund_amount'),
                $request->boolean('notify_customer', true)
            );

            return $this->successResponse(
                $result,
                'Order cancelled successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to cancel order',
                $e,
                ['order_id' => $orderId]
            );

            return $this->serverErrorResponse('Failed to cancel order');
        }
    }

    /**
     * Process refund for order.
     *
     * @group Admin Orders
     *
     * @authenticated
     */
    public function refund(Request $request, string $orderId): JsonResponse
    {
        $request->validate([
            'amount' => 'required|numeric|min:0',
            'reason' => 'required|string|max:500',
            'refund_type' => 'required|string|in:full,partial',
        ]);

        try {
            $refund = $this->orderService->processRefund(
                $orderId,
                $request->input('amount'),
                $request->input('reason'),
                $request->input('refund_type')
            );

            return $this->successResponse(
                $refund,
                'Refund processed successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to process refund',
                $e,
                ['order_id' => $orderId]
            );

            return $this->serverErrorResponse('Failed to process refund');
        }
    }

    /**
     * Get order analytics.
     *
     * @group Admin Orders
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        $request->validate([
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
            'business_id' => 'sometimes|uuid|exists:businesses,id',
            'tenant_id' => 'sometimes|string|exists:tenants,id',
        ]);

        try {
            $period = $request->input('period', 'month');
            $businessId = $request->input('business_id');
            $tenantId = $request->input('tenant_id');

            $analytics = $this->orderService->getOrderAnalytics($period, $businessId, $tenantId);

            return $this->successResponse(
                $analytics,
                'Order analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve order analytics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve order analytics');
        }
    }

    /**
     * Get order statistics.
     *
     * @group Admin Orders
     *
     * @authenticated
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $statistics = $this->orderService->getOrderStatistics();

            return $this->successResponse(
                $statistics,
                'Order statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve order statistics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve order statistics');
        }
    }

    /**
     * Bulk update orders.
     *
     * @group Admin Orders
     *
     * @authenticated
     */
    public function bulkUpdate(Request $request): JsonResponse
    {
        $request->validate([
            'order_ids' => 'required|array|min:1',
            'order_ids.*' => 'uuid|exists:orders,id',
            'action' => 'required|string|in:cancel,confirm,mark_ready',
            'reason' => 'sometimes|string|max:500',
        ]);

        try {
            $result = $this->orderService->bulkUpdateOrders(
                $request->input('order_ids'),
                $request->input('action'),
                $request->input('reason')
            );

            return $this->successResponse(
                $result,
                'Orders updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to bulk update orders',
                $e,
                $request->only(['order_ids', 'action'])
            );

            return $this->serverErrorResponse('Failed to update orders');
        }
    }

    /**
     * Export orders.
     *
     * @group Admin Orders
     *
     * @authenticated
     */
    public function export(Request $request): JsonResponse
    {
        $request->validate([
            'format' => 'required|string|in:csv,json',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'status' => 'nullable|string',
            'payment_status' => 'nullable|string',
            'business_id' => 'nullable|uuid|exists:businesses,id',
        ]);

        try {
            $filename = $this->orderService->exportOrders(
                array_filter($request->validated(), fn ($value) => $value !== null),
                $request->input('format')
            );

            return $this->successResponse(
                [
                    'filename' => $filename,
                    'download_url' => route('admin.orders.download', ['filename' => $filename]),
                ],
                'Orders exported successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to export orders',
                $e,
                $request->validated()
            );

            return $this->serverErrorResponse('Failed to export orders');
        }
    }

    /**
     * Download exported orders.
     *
     * @group Admin Orders
     *
     * @authenticated
     */
    public function download(string $filename)
    {
        try {
            $filepath = storage_path('app/exports/'.$filename);

            if (! file_exists($filepath)) {
                return $this->notFoundResponse('Export file not found');
            }

            // Security check: ensure filename is safe
            if (! preg_match('/^orders_export_\d{4}_\d{2}_\d{2}_\d{2}_\d{2}_\d{2}\.(csv|json)$/', $filename)) {
                return $this->errorResponse('Invalid filename', 400);
            }

            return response()->download($filepath)->deleteFileAfterSend();

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to download order export',
                $e,
                ['filename' => $filename]
            );

            return $this->serverErrorResponse('Failed to download order export');
        }
    }

    /**
     * Get orders requiring attention.
     *
     * @group Admin Orders
     *
     * @authenticated
     */
    public function requiresAttention(): JsonResponse
    {
        try {
            $orders = $this->orderService->getOrdersRequiringAttention();

            return $this->successResponse(
                $orders,
                'Orders requiring attention retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve orders requiring attention',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve orders requiring attention');
        }
    }

    /**
     * Detect fraudulent orders.
     *
     * @group Admin Orders
     *
     * @authenticated
     */
    public function fraudDetection(): JsonResponse
    {
        try {
            $suspiciousOrders = $this->orderService->detectFraudulentOrders();

            return $this->successResponse(
                $suspiciousOrders,
                'Fraud detection completed successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to run fraud detection',
                $e
            );

            return $this->serverErrorResponse('Failed to run fraud detection');
        }
    }

    /**
     * Get order dispute summary.
     *
     * @group Admin Orders
     *
     * @authenticated
     */
    public function disputeSummary(): JsonResponse
    {
        try {
            $summary = $this->orderService->getOrderDisputeSummary();

            return $this->successResponse(
                $summary,
                'Order dispute summary retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve order dispute summary',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve order dispute summary');
        }
    }

    /**
     * Bulk update order status.
     *
     * @group Admin Orders
     *
     * @authenticated
     */
    public function bulkUpdateStatus(Request $request): JsonResponse
    {
        $request->validate([
            'order_ids' => 'required|array|min:1|max:100',
            'order_ids.*' => 'uuid|exists:orders,id',
            'status' => 'required|string|in:pending,confirmed,preparing,ready_for_pickup,out_for_delivery,completed,cancelled',
            'reason' => 'nullable|string|max:500',
        ]);

        try {
            $orderStatus = OrderStatus::from($request->input('status'));
            $results = $this->orderService->bulkUpdateOrderStatus(
                $request->input('order_ids'),
                $orderStatus,
                $request->input('reason')
            );

            return $this->successResponse(
                $results,
                "Bulk status update completed: {$results['updated']} updated, {$results['failed']} failed"
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to bulk update order status',
                $e,
                $request->only(['order_ids', 'status'])
            );

            return $this->serverErrorResponse('Failed to bulk update order status');
        }
    }
}
