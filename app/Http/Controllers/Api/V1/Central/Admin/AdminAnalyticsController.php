<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Services\Analytics\AnalyticsManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * Admin Analytics Controller
 *
 * Handles platform-wide analytics and reporting for administrators.
 * Provides comprehensive insights into platform performance, usage, and trends.
 */
class AdminAnalyticsController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly AnalyticsManagementService $analyticsService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get platform overview analytics.
     *
     * @group Admin Analytics
     *
     * @authenticated
     *
     * @queryParam period string Period for analytics (today, week, month, quarter, year). Example: month
     * @queryParam timezone string Timezone for date calculations. Example: Africa/Lagos
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Platform analytics retrieved successfully",
     *   "data": {
     *     "overview": {
     *       "total_businesses": 150,
     *       "total_providers": 75,
     *       "total_users": 2500,
     *       "total_orders": 15000,
     *       "total_deliveries": 12000,
     *       "platform_revenue": 2500000,
     *       "active_subscriptions": 180
     *     },
     *     "period_stats": {
     *       "period": "month",
     *       "new_businesses": 12,
     *       "new_providers": 8,
     *       "new_users": 450,
     *       "orders_count": 2500,
     *       "deliveries_count": 2100,
     *       "revenue": 450000
     *     },
     *     "growth_metrics": {
     *       "business_growth": 8.5,
     *       "provider_growth": 12.3,
     *       "user_growth": 22.1,
     *       "revenue_growth": 15.7
     *     }
     *   }
     * }
     */
    public function overview(Request $request): JsonResponse
    {
        $request->validate([
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
            'timezone' => 'sometimes|string',
        ]);

        try {
            $period = $request->input('period', 'month');
            $timezone = $request->input('timezone', 'Africa/Lagos');
            $now = Carbon::now($timezone);

            $periodDates = $this->getPeriodDates($period, $now);

            $analytics = [
                'overview' => $this->getOverviewStats(),
                'period_stats' => $this->getPeriodStats($periodDates),
                'growth_metrics' => $this->getGrowthMetrics($periodDates),
                'top_performing' => $this->getTopPerforming($periodDates),
                'geographic_distribution' => $this->getGeographicDistribution(),
            ];

            return $this->successResponse(
                $analytics,
                'Platform analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve platform analytics',
                $e,
                ['request_params' => $request->only(['period', 'timezone'])]
            );

            return $this->serverErrorResponse('Failed to retrieve platform analytics');
        }
    }

    /**
     * Get subscription analytics.
     *
     * @group Admin Analytics
     *
     * @authenticated
     */
    public function subscriptions(Request $request): JsonResponse
    {
        $request->validate([
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
        ]);

        try {
            $period = $request->input('period', 'month');
            $now = Carbon::now();
            $periodDates = $this->getPeriodDates($period, $now);

            $analytics = [
                'subscription_overview' => $this->getSubscriptionOverview(),
                'plan_distribution' => $this->getPlanDistribution(),
                'churn_analysis' => $this->getChurnAnalysis($periodDates),
                'revenue_by_plan' => $this->getRevenueByPlan($periodDates),
                'upgrade_downgrade_trends' => $this->getUpgradeDowngradeTrends($periodDates),
            ];

            return $this->successResponse(
                $analytics,
                'Subscription analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve subscription analytics',
                $e,
                ['request_params' => $request->only(['period'])]
            );

            return $this->serverErrorResponse('Failed to retrieve subscription analytics');
        }
    }

    /**
     * Get financial analytics.
     *
     * @group Admin Analytics
     *
     * @authenticated
     */
    public function financial(Request $request): JsonResponse
    {
        $request->validate([
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
            'currency' => 'sometimes|string|in:NGN,USD',
        ]);

        try {
            $period = $request->input('period', 'month');
            $currency = $request->input('currency', 'NGN');
            $now = Carbon::now();
            $periodDates = $this->getPeriodDates($period, $now);

            $analytics = [
                'revenue_overview' => $this->getRevenueOverview($periodDates, $currency),
                'commission_breakdown' => $this->getCommissionBreakdown($periodDates, $currency),
                'payout_analytics' => $this->getPayoutAnalytics($periodDates, $currency),
                'payment_methods' => $this->getPaymentMethodAnalytics($periodDates),
                'financial_trends' => $this->getFinancialTrends($periodDates, $currency),
            ];

            return $this->successResponse(
                $analytics,
                'Financial analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve financial analytics',
                $e,
                ['request_params' => $request->only(['period', 'currency'])]
            );

            return $this->serverErrorResponse('Failed to retrieve financial analytics');
        }
    }

    /**
     * Get usage analytics.
     *
     * @group Admin Analytics
     *
     * @authenticated
     */
    public function usage(Request $request): JsonResponse
    {
        $request->validate([
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
        ]);

        try {
            $period = $request->input('period', 'month');
            $now = Carbon::now();
            $periodDates = $this->getPeriodDates($period, $now);

            $analytics = [
                'api_usage' => $this->getApiUsageAnalytics($periodDates),
                'feature_usage' => $this->getFeatureUsageAnalytics($periodDates),
                'user_activity' => $this->getUserActivityAnalytics($periodDates),
                'system_performance' => $this->getSystemPerformanceMetrics($periodDates),
            ];

            return $this->successResponse(
                $analytics,
                'Usage analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve usage analytics',
                $e,
                ['request_params' => $request->only(['period'])]
            );

            return $this->serverErrorResponse('Failed to retrieve usage analytics');
        }
    }

    /**
     * Export analytics data.
     *
     * @group Admin Analytics
     *
     * @authenticated
     */
    public function export(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'required|string|in:overview,subscriptions,financial,usage',
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
            'format' => 'sometimes|string|in:csv,xlsx,pdf',
        ]);

        try {
            $type = $request->input('type');
            $period = $request->input('period', 'month');
            $format = $request->input('format', 'csv');

            // Generate export file
            $exportData = $this->generateExportData($type, $period);
            $filename = $this->generateExportFile($exportData, $type, $format);

            return $this->successResponse([
                'download_url' => url("storage/exports/{$filename}"),
                'filename' => $filename,
                'expires_at' => now()->addHours(24)->toISOString(),
            ], 'Analytics export generated successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to export analytics data',
                $e,
                ['request_params' => $request->only(['type', 'period', 'format'])]
            );

            return $this->serverErrorResponse('Failed to export analytics data');
        }
    }

    /**
     * Get period dates based on period type.
     */
    private function getPeriodDates(string $period, Carbon $now): array
    {
        return match ($period) {
            'today' => [
                'start' => $now->copy()->startOfDay(),
                'end' => $now->copy()->endOfDay(),
            ],
            'week' => [
                'start' => $now->copy()->startOfWeek(),
                'end' => $now->copy()->endOfWeek(),
            ],
            'month' => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
            'quarter' => [
                'start' => $now->copy()->startOfQuarter(),
                'end' => $now->copy()->endOfQuarter(),
            ],
            'year' => [
                'start' => $now->copy()->startOfYear(),
                'end' => $now->copy()->endOfYear(),
            ],
            default => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
        };
    }

    /**
     * Get overview statistics.
     */
    private function getOverviewStats(): array
    {
        return [
            'total_businesses' => DB::table('businesses')->count(),
            'total_providers' => DB::table('delivery_providers')->count(),
            'total_users' => DB::table('users')->count(),
            'total_orders' => DB::table('orders')->count(),
            'total_deliveries' => DB::table('deliveries')->count(),
            'platform_revenue' => DB::table('payments')
                ->where('status', 'paid')
                ->sum('amount'),
            'active_subscriptions' => DB::table('user_subscriptions')
                ->where('status', 'active')
                ->count(),
        ];
    }

    /**
     * Get period statistics.
     */
    private function getPeriodStats(array $periodDates): array
    {
        $startDate = $periodDates['start'];
        $endDate = $periodDates['end'];

        return [
            'new_businesses' => DB::table('businesses')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count(),
            'new_providers' => DB::table('delivery_providers')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count(),
            'new_users' => DB::table('users')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count(),
            'orders_count' => DB::table('orders')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count(),
            'deliveries_count' => DB::table('deliveries')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count(),
            'revenue' => DB::table('payments')
                ->where('status', 'paid')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->sum('amount'),
        ];
    }

    /**
     * Get growth metrics.
     */
    private function getGrowthMetrics(array $periodDates): array
    {
        // Calculate previous period for comparison
        $currentStart = $periodDates['start'];
        $currentEnd = $periodDates['end'];
        $periodLength = $currentStart->diffInDays($currentEnd);

        $previousStart = $currentStart->copy()->subDays($periodLength + 1);
        $previousEnd = $currentStart->copy()->subDay();

        $currentStats = $this->getPeriodStats($periodDates);
        $previousStats = $this->getPeriodStats([
            'start' => $previousStart,
            'end' => $previousEnd,
        ]);

        return [
            'business_growth' => $this->calculateGrowthRate(
                $previousStats['new_businesses'],
                $currentStats['new_businesses']
            ),
            'provider_growth' => $this->calculateGrowthRate(
                $previousStats['new_providers'],
                $currentStats['new_providers']
            ),
            'user_growth' => $this->calculateGrowthRate(
                $previousStats['new_users'],
                $currentStats['new_users']
            ),
            'revenue_growth' => $this->calculateGrowthRate(
                $previousStats['revenue'],
                $currentStats['revenue']
            ),
        ];
    }

    /**
     * Calculate growth rate percentage.
     */
    private function calculateGrowthRate(float $previous, float $current): float
    {
        if ($previous == 0) {
            return $current > 0 ? 100.0 : 0.0;
        }

        return round((($current - $previous) / $previous) * 100, 2);
    }

    /**
     * Get top performing entities.
     */
    private function getTopPerforming(array $periodDates): array
    {
        $startDate = $periodDates['start'];
        $endDate = $periodDates['end'];

        return [
            'top_businesses' => DB::table('businesses')
                ->join('orders', 'businesses.id', '=', 'orders.business_id')
                ->join('payments', 'orders.id', '=', 'payments.order_id')
                ->where('payments.status', 'paid')
                ->whereBetween('orders.created_at', [$startDate, $endDate])
                ->select('businesses.business_name', DB::raw('SUM(payments.amount) as revenue'))
                ->groupBy('businesses.id', 'businesses.business_name')
                ->orderByDesc('revenue')
                ->limit(10)
                ->get(),
            'top_providers' => DB::table('delivery_providers')
                ->join('deliveries', 'delivery_providers.id', '=', 'deliveries.delivery_provider_id')
                ->whereBetween('deliveries.created_at', [$startDate, $endDate])
                ->where('deliveries.status', 'delivered')
                ->select('delivery_providers.business_name', DB::raw('COUNT(*) as deliveries_count'))
                ->groupBy('delivery_providers.id', 'delivery_providers.business_name')
                ->orderByDesc('deliveries_count')
                ->limit(10)
                ->get(),
        ];
    }

    /**
     * Get geographic distribution.
     */
    private function getGeographicDistribution(): array
    {
        return [
            'businesses_by_state' => DB::table('businesses')
                ->join('states', 'businesses.state_id', '=', 'states.id')
                ->select('states.name as state_name', DB::raw('COUNT(*) as count'))
                ->groupBy('states.id', 'states.name')
                ->orderByDesc('count')
                ->get(),
            'providers_by_state' => DB::table('delivery_providers')
                ->join('states', 'delivery_providers.state_id', '=', 'states.id')
                ->select('states.name as state_name', DB::raw('COUNT(*) as count'))
                ->groupBy('states.id', 'states.name')
                ->orderByDesc('count')
                ->get(),
        ];
    }

    /**
     * Get subscription overview.
     */
    private function getSubscriptionOverview(): array
    {
        return [
            'total_subscriptions' => DB::table('user_subscriptions')->count(),
            'active_subscriptions' => DB::table('user_subscriptions')
                ->where('status', 'active')
                ->count(),
            'expired_subscriptions' => DB::table('user_subscriptions')
                ->where('status', 'expired')
                ->count(),
            'cancelled_subscriptions' => DB::table('user_subscriptions')
                ->where('status', 'cancelled')
                ->count(),
            'monthly_recurring_revenue' => DB::table('user_subscriptions')
                ->join('plan_prices', 'user_subscriptions.plan_price_id', '=', 'plan_prices.id')
                ->where('user_subscriptions.status', 'active')
                ->where('plan_prices.billing_interval', 'monthly')
                ->sum('plan_prices.price'),
        ];
    }

    /**
     * Get plan distribution.
     */
    private function getPlanDistribution(): array
    {
        return DB::table('user_subscriptions')
            ->join('subscription_plans', 'user_subscriptions.plan_id', '=', 'subscription_plans.id')
            ->where('user_subscriptions.status', 'active')
            ->select('subscription_plans.name as plan_name', DB::raw('COUNT(*) as count'))
            ->groupBy('subscription_plans.id', 'subscription_plans.name')
            ->orderByDesc('count')
            ->get()
            ->toArray();
    }

    /**
     * Get churn analysis.
     */
    private function getChurnAnalysis(array $periodDates): array
    {
        $startDate = $periodDates['start'];
        $endDate = $periodDates['end'];

        $totalActiveStart = DB::table('user_subscriptions')
            ->where('status', 'active')
            ->where('created_at', '<', $startDate)
            ->count();

        $cancelled = DB::table('user_subscriptions')
            ->where('status', 'cancelled')
            ->whereBetween('updated_at', [$startDate, $endDate])
            ->count();

        $churnRate = $totalActiveStart > 0 ? ($cancelled / $totalActiveStart) * 100 : 0;

        return [
            'churn_rate' => round($churnRate, 2),
            'cancelled_subscriptions' => $cancelled,
            'active_at_period_start' => $totalActiveStart,
        ];
    }

    /**
     * Get revenue by plan.
     */
    private function getRevenueByPlan(array $periodDates): array
    {
        $startDate = $periodDates['start'];
        $endDate = $periodDates['end'];

        return DB::table('payments')
            ->join('user_subscriptions', 'payments.subscription_id', '=', 'user_subscriptions.id')
            ->join('subscription_plans', 'user_subscriptions.plan_id', '=', 'subscription_plans.id')
            ->where('payments.status', 'paid')
            ->whereBetween('payments.created_at', [$startDate, $endDate])
            ->select('subscription_plans.name as plan_name', DB::raw('SUM(payments.amount) as revenue'))
            ->groupBy('subscription_plans.id', 'subscription_plans.name')
            ->orderByDesc('revenue')
            ->get()
            ->toArray();
    }

    /**
     * Get upgrade/downgrade trends.
     */
    private function getUpgradeDowngradeTrends(array $periodDates): array
    {
        // This would require tracking subscription changes in a separate table
        // For now, return placeholder data
        return [
            'upgrades' => 0,
            'downgrades' => 0,
            'net_change' => 0,
        ];
    }

    /**
     * Get revenue overview.
     */
    private function getRevenueOverview(array $periodDates, string $currency): array
    {
        $startDate = $periodDates['start'];
        $endDate = $periodDates['end'];

        return [
            'total_revenue' => DB::table('payments')
                ->where('status', 'paid')
                ->where('currency', $currency)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->sum('amount'),
            'subscription_revenue' => DB::table('payments')
                ->where('status', 'paid')
                ->where('currency', $currency)
                ->whereNotNull('subscription_id')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->sum('amount'),
            'order_revenue' => DB::table('payments')
                ->where('status', 'paid')
                ->where('currency', $currency)
                ->whereNotNull('order_id')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->sum('amount'),
        ];
    }

    /**
     * Get commission breakdown.
     */
    private function getCommissionBreakdown(array $periodDates, string $currency): array
    {
        $startDate = $periodDates['start'];
        $endDate = $periodDates['end'];

        return [
            'total_commissions' => DB::table('platform_transactions')
                ->where('transaction_type', 'commission')
                ->where('currency', $currency)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->sum('amount'),
            'order_commissions' => DB::table('platform_transactions')
                ->where('transaction_type', 'order_commission')
                ->where('currency', $currency)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->sum('amount'),
            'delivery_commissions' => DB::table('platform_transactions')
                ->where('transaction_type', 'delivery_commission')
                ->where('currency', $currency)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->sum('amount'),
        ];
    }

    /**
     * Placeholder methods for remaining analytics.
     */
    private function getPayoutAnalytics(array $periodDates, string $currency): array
    {
        return ['total_payouts' => 0, 'pending_payouts' => 0];
    }

    private function getPaymentMethodAnalytics(array $periodDates): array
    {
        return ['card' => 0, 'bank_transfer' => 0, 'wallet' => 0];
    }

    private function getFinancialTrends(array $periodDates, string $currency): array
    {
        return ['daily_revenue' => [], 'weekly_revenue' => []];
    }

    private function getApiUsageAnalytics(array $periodDates): array
    {
        return ['total_requests' => 0, 'requests_by_endpoint' => []];
    }

    private function getFeatureUsageAnalytics(array $periodDates): array
    {
        return ['feature_adoption' => [], 'most_used_features' => []];
    }

    private function getUserActivityAnalytics(array $periodDates): array
    {
        return ['active_users' => 0, 'user_sessions' => 0];
    }

    private function getSystemPerformanceMetrics(array $periodDates): array
    {
        return ['avg_response_time' => 0, 'error_rate' => 0];
    }

    private function generateExportData(string $type, string $period): array
    {
        return ['data' => 'placeholder'];
    }

    private function generateExportFile(array $data, string $type, string $format): string
    {
        return "analytics_{$type}_{$format}_".now()->format('Y-m-d_H-i-s').".{$format}";
    }

    /**
     * Get real-time dashboard data.
     *
     * @group Admin Analytics
     *
     * @authenticated
     */
    public function realTimeDashboard(): JsonResponse
    {
        try {
            $data = [
                'current_active_users' => rand(150, 300), // Mock real-time data
                'orders_last_hour' => rand(25, 75),
                'revenue_last_hour' => rand(15000, 45000),
                'deliveries_in_progress' => rand(40, 120),
                'system_status' => [
                    'api_status' => 'healthy',
                    'database_status' => 'healthy',
                    'queue_status' => 'healthy',
                    'cache_status' => 'healthy',
                ],
                'alerts' => [
                    [
                        'type' => 'warning',
                        'message' => 'High order volume detected',
                        'timestamp' => now()->subMinutes(5),
                    ],
                ],
                'last_updated' => now(),
            ];

            return $this->successResponse(
                $data,
                'Real-time dashboard data retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve real-time dashboard data',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve real-time dashboard data');
        }
    }

    /**
     * Get business performance analytics.
     *
     * @group Admin Analytics
     *
     * @authenticated
     */
    public function businessPerformance(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        try {
            $analytics = $this->analyticsService->getBusinessPerformanceAnalytics(
                array_filter($request->only(['date_from', 'date_to']))
            );

            return $this->successResponse(
                $analytics,
                'Business performance analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve business performance analytics',
                $e,
                $request->validated()
            );

            return $this->serverErrorResponse('Failed to retrieve business performance analytics');
        }
    }

    /**
     * Get revenue analytics.
     *
     * @group Admin Analytics
     *
     * @authenticated
     */
    public function revenueAnalytics(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        try {
            $analytics = $this->analyticsService->getRevenueAnalytics(
                array_filter($request->only(['date_from', 'date_to']))
            );

            return $this->successResponse(
                $analytics,
                'Revenue analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve revenue analytics',
                $e,
                $request->validated()
            );

            return $this->serverErrorResponse('Failed to retrieve revenue analytics');
        }
    }

    /**
     * Get user engagement analytics.
     *
     * @group Admin Analytics
     *
     * @authenticated
     */
    public function userEngagement(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        try {
            $analytics = $this->analyticsService->getUserEngagementAnalytics(
                array_filter($request->only(['date_from', 'date_to']))
            );

            return $this->successResponse(
                $analytics,
                'User engagement analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve user engagement analytics',
                $e,
                $request->validated()
            );

            return $this->serverErrorResponse('Failed to retrieve user engagement analytics');
        }
    }

    /**
     * Get operational metrics.
     *
     * @group Admin Analytics
     *
     * @authenticated
     */
    public function operationalMetrics(Request $request): JsonResponse
    {
        $request->validate([
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        try {
            $metrics = $this->analyticsService->getOperationalMetrics(
                array_filter($request->only(['date_from', 'date_to']))
            );

            return $this->successResponse(
                $metrics,
                'Operational metrics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve operational metrics',
                $e,
                $request->validated()
            );

            return $this->serverErrorResponse('Failed to retrieve operational metrics');
        }
    }

    /**
     * Generate custom report.
     *
     * @group Admin Analytics
     *
     * @authenticated
     */
    public function customReport(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'required|string|in:business_performance,revenue_analysis,user_engagement,operational_overview,platform_overview',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
            'filters' => 'nullable|array',
        ]);

        try {
            $config = array_merge(
                $request->only(['type', 'date_from', 'date_to']),
                $request->input('filters', [])
            );

            $report = $this->analyticsService->generateCustomReport($config);

            return $this->successResponse(
                $report,
                'Custom report generated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to generate custom report',
                $e,
                $request->validated()
            );

            return $this->serverErrorResponse('Failed to generate custom report');
        }
    }

    /**
     * Download exported analytics data.
     *
     * @group Admin Analytics
     *
     * @authenticated
     */
    public function download(string $filename)
    {
        try {
            $filepath = storage_path('app/exports/'.$filename);

            if (! file_exists($filepath)) {
                return $this->notFoundResponse('Export file not found');
            }

            // Security check: ensure filename is safe
            if (! preg_match('/^analytics_\w+_\d{4}_\d{2}_\d{2}_\d{2}_\d{2}_\d{2}\.(csv|json)$/', $filename)) {
                return $this->errorResponse('Invalid filename', 400);
            }

            return response()->download($filepath)->deleteFileAfterSend();

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to download analytics export',
                $e,
                ['filename' => $filename]
            );

            return $this->serverErrorResponse('Failed to download analytics export');
        }
    }
}
