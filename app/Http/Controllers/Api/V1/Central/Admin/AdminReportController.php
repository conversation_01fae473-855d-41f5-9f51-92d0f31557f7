<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Services\Business\ReportManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Report Controller
 *
 * Handles comprehensive reporting and analytics for administrators.
 * Manages business intelligence, financial reports, and custom analytics.
 */
class AdminReportController extends Controller
{
    use ApiResponseTrait;

    public function __construct(
        private readonly ReportManagementService $reportService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Generate platform overview report.
     *
     * @group Admin Reports
     *
     * @authenticated
     */
    public function platformOverview(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
            ]);

            $report = $this->reportService->generatePlatformOverviewReport($validated);

            return $this->successResponse(
                $report,
                'Platform overview report generated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to generate platform overview report',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to generate platform overview report');
        }
    }

    /**
     * Generate business performance report.
     *
     * @group Admin Reports
     *
     * @authenticated
     */
    public function businessPerformance(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'business_id' => 'sometimes|uuid|exists:businesses,id',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
            ]);

            $report = $this->reportService->generateBusinessPerformanceReport($validated);

            return $this->successResponse(
                $report,
                'Business performance report generated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to generate business performance report',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to generate business performance report');
        }
    }

    /**
     * Generate financial analytics report.
     *
     * @group Admin Reports
     *
     * @authenticated
     */
    public function financialAnalytics(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'business_id' => 'sometimes|uuid|exists:businesses,id',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
            ]);

            $report = $this->reportService->generateFinancialReport($validated);

            return $this->successResponse(
                $report,
                'Financial analytics report generated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to generate financial analytics report',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to generate financial analytics report');
        }
    }

    /**
     * Generate operational efficiency report.
     *
     * @group Admin Reports
     *
     * @authenticated
     */
    public function operationalEfficiency(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'business_id' => 'sometimes|uuid|exists:businesses,id',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
            ]);

            $report = $this->reportService->generateOperationalReport($validated);

            return $this->successResponse(
                $report,
                'Operational efficiency report generated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to generate operational efficiency report',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to generate operational efficiency report');
        }
    }

    /**
     * Generate custom report.
     *
     * @group Admin Reports
     *
     * @authenticated
     */
    public function customReport(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
                'metrics' => 'required|array|min:1',
                'metrics.*' => 'string',
                'filters' => 'sometimes|array',
                'filters.business_id' => 'sometimes|uuid|exists:businesses,id',
                'filters.tenant_id' => 'sometimes|uuid|exists:tenants,id',
            ]);

            $report = $this->reportService->generateCustomReport($validated);

            return $this->successResponse(
                $report,
                'Custom report generated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to generate custom report',
                $e,
                ['admin_id' => auth()->id(), 'config' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to generate custom report');
        }
    }

    /**
     * Export report data.
     *
     * @group Admin Reports
     *
     * @authenticated
     */
    public function exportReport(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'report_data' => 'required|array',
                'format' => 'sometimes|string|in:json,csv,excel,pdf',
            ]);

            $export = $this->reportService->exportReport(
                $validated['report_data'],
                $validated['format'] ?? 'json'
            );

            return $this->successResponse(
                $export,
                'Report exported successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to export report',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to export report');
        }
    }

    /**
     * Get available report templates.
     *
     * @group Admin Reports
     *
     * @authenticated
     */
    public function templates(): JsonResponse
    {
        try {
            $templates = $this->reportService->getReportTemplates();

            return $this->successResponse(
                $templates,
                'Report templates retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve report templates',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve report templates');
        }
    }

    /**
     * Schedule automated report generation.
     *
     * @group Admin Reports
     *
     * @authenticated
     */
    public function scheduleReport(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'report_type' => 'required|string|in:platform_overview,business_performance,financial_analysis,operational_efficiency,custom',
                'frequency' => 'required|string|in:daily,weekly,monthly',
                'recipients' => 'sometimes|array',
                'recipients.*' => 'email',
                'filters' => 'sometimes|array',
                'format' => 'sometimes|string|in:json,csv,excel,pdf',
            ]);

            $schedule = $this->reportService->scheduleReport($validated);

            return $this->successResponse(
                $schedule,
                'Report scheduled successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to schedule report',
                $e,
                ['admin_id' => auth()->id(), 'config' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to schedule report');
        }
    }
}
