<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Services\System\FeedbackManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Feedback Controller
 *
 * Handles comprehensive user feedback and suggestion management for administrators.
 * Manages feedback collection, categorization, responses, and analytics.
 */
class AdminFeedbackController extends Controller
{
    use ApiResponseTrait;

    public function __construct(
        private readonly FeedbackManagementService $feedbackService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all feedback with filtering.
     *
     * @group Admin Feedback
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'type' => 'sometimes|string|in:bug_report,feature_request,general_feedback,complaint',
                'status' => 'sometimes|string|in:open,in_progress,resolved,closed',
                'category' => 'sometimes|string|in:ui_ux,performance,functionality,security,other',
                'priority' => 'sometimes|string|in:low,medium,high,critical',
                'user_id' => 'sometimes|uuid',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
                'per_page' => 'sometimes|integer|min:1|max:100',
            ]);

            $feedback = $this->feedbackService->getFeedback($validated);

            // Paginate results manually since we're working with arrays
            $perPage = $validated['per_page'] ?? 15;
            $page = $request->get('page', 1);
            $offset = ($page - 1) * $perPage;

            $paginatedFeedback = array_slice($feedback, $offset, $perPage);
            $total = count($feedback);

            return $this->successResponse([
                'data' => array_map([$this, 'transformFeedback'], $paginatedFeedback),
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $total,
                    'last_page' => ceil($total / $perPage),
                    'from' => $offset + 1,
                    'to' => min($offset + $perPage, $total),
                ],
            ], 'Feedback retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve feedback',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve feedback');
        }
    }

    /**
     * Create new feedback entry.
     *
     * @group Admin Feedback
     *
     * @authenticated
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'type' => 'required|string|in:bug_report,feature_request,general_feedback,complaint',
                'category' => 'sometimes|string|in:ui_ux,performance,functionality,security,other',
                'title' => 'required|string|max:255',
                'description' => 'required|string|max:2000',
                'priority' => 'sometimes|string|in:low,medium,high,critical',
                'user_id' => 'sometimes|uuid',
                'user_email' => 'sometimes|email',
                'user_name' => 'sometimes|string|max:255',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'attachments' => 'sometimes|array',
                'attachments.*' => 'string',
                'metadata' => 'sometimes|array',
                'tags' => 'sometimes|array',
                'tags.*' => 'string',
            ]);

            $feedback = $this->feedbackService->createFeedback($validated);

            return $this->successResponse(
                $this->transformFeedback($feedback),
                'Feedback created successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create feedback',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create feedback');
        }
    }

    /**
     * Get feedback analytics and insights.
     *
     * @group Admin Feedback
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'type' => 'sometimes|string|in:bug_report,feature_request,general_feedback,complaint',
                'category' => 'sometimes|string|in:ui_ux,performance,functionality,security,other',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
            ]);

            $analytics = $this->feedbackService->getFeedbackAnalytics($validated);

            return $this->successResponse(
                $analytics,
                'Feedback analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve feedback analytics',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve feedback analytics');
        }
    }

    /**
     * Update feedback status and details.
     *
     * @group Admin Feedback
     *
     * @authenticated
     */
    public function update(Request $request, string $feedbackId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'status' => 'sometimes|string|in:open,in_progress,resolved,closed',
                'priority' => 'sometimes|string|in:low,medium,high,critical',
                'category' => 'sometimes|string|in:ui_ux,performance,functionality,security,other',
                'assigned_to' => 'sometimes|string|max:255',
                'resolution' => 'sometimes|string|max:1000',
                'tags' => 'sometimes|array',
                'tags.*' => 'string',
            ]);

            $feedback = $this->feedbackService->updateFeedback($feedbackId, $validated);

            return $this->successResponse(
                $this->transformFeedback($feedback),
                'Feedback updated successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->notFoundResponse('Feedback not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update feedback',
                $e,
                ['feedback_id' => $feedbackId, 'updates' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update feedback');
        }
    }

    /**
     * Add response to feedback.
     *
     * @group Admin Feedback
     *
     * @authenticated
     */
    public function addResponse(Request $request, string $feedbackId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'message' => 'required|string|max:2000',
                'is_public' => 'sometimes|boolean',
                'update_status' => 'sometimes|string|in:open,in_progress,resolved,closed',
                'attachments' => 'sometimes|array',
                'attachments.*' => 'string',
            ]);

            $response = $this->feedbackService->addFeedbackResponse($feedbackId, $validated);

            return $this->successResponse(
                $response,
                'Response added successfully',
                201
            );

        } catch (\InvalidArgumentException $e) {
            return $this->notFoundResponse('Feedback not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to add feedback response',
                $e,
                ['feedback_id' => $feedbackId, 'response_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to add response');
        }
    }

    /**
     * Vote on feedback.
     *
     * @group Admin Feedback
     *
     * @authenticated
     */
    public function vote(Request $request, string $feedbackId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'vote_type' => 'required|string|in:upvote,downvote',
            ]);

            $feedback = $this->feedbackService->voteFeedback($feedbackId, $validated['vote_type']);

            return $this->successResponse(
                $this->transformFeedback($feedback),
                'Vote recorded successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->notFoundResponse('Feedback not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to vote on feedback',
                $e,
                ['feedback_id' => $feedbackId, 'vote_type' => $request->get('vote_type')]
            );

            return $this->serverErrorResponse('Failed to record vote');
        }
    }

    /**
     * Create satisfaction survey.
     *
     * @group Admin Feedback
     *
     * @authenticated
     */
    public function createSurvey(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'title' => 'required|string|max:255',
                'description' => 'sometimes|string|max:1000',
                'questions' => 'required|array|min:1',
                'questions.*.question' => 'required|string|max:500',
                'questions.*.type' => 'required|string|in:rating,text,multiple_choice,yes_no',
                'questions.*.options' => 'sometimes|array',
                'target_audience' => 'sometimes|string|in:all_users,customers,businesses,providers',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'is_active' => 'sometimes|boolean',
                'start_date' => 'sometimes|date',
                'end_date' => 'sometimes|date|after:start_date',
            ]);

            $survey = $this->feedbackService->createSatisfactionSurvey($validated);

            return $this->successResponse(
                $survey,
                'Satisfaction survey created successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create satisfaction survey',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create satisfaction survey');
        }
    }

    /**
     * Bulk feedback operations.
     *
     * @group Admin Feedback
     *
     * @authenticated
     */
    public function bulkOperations(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'feedback_ids' => 'required|array|min:1',
                'feedback_ids.*' => 'string',
                'action' => 'required|string|in:update_status,update_priority,assign,add_tags',
                'status' => 'sometimes|string|in:open,in_progress,resolved,closed',
                'priority' => 'sometimes|string|in:low,medium,high,critical',
                'assigned_to' => 'sometimes|string|max:255',
                'tags' => 'sometimes|array',
                'tags.*' => 'string',
            ]);

            $options = array_filter([
                'status' => $validated['status'] ?? null,
                'priority' => $validated['priority'] ?? null,
                'assigned_to' => $validated['assigned_to'] ?? null,
                'tags' => $validated['tags'] ?? null,
            ]);

            $results = $this->feedbackService->bulkFeedbackOperations(
                $validated['feedback_ids'],
                $validated['action'],
                $options
            );

            return $this->successResponse(
                $results,
                'Bulk feedback operations completed'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to perform bulk feedback operations',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to perform bulk operations');
        }
    }

    /**
     * Transform feedback for API response.
     */
    public function transformFeedback(array $feedback): array
    {
        return [
            'id' => $feedback['id'],
            'type' => $feedback['type'],
            'category' => $feedback['category'],
            'title' => $feedback['title'],
            'description' => $feedback['description'],
            'priority' => $feedback['priority'],
            'status' => $feedback['status'],
            'user_id' => $feedback['user_id'],
            'user_email' => $feedback['user_email'],
            'user_name' => $feedback['user_name'],
            'tenant_id' => $feedback['tenant_id'],
            'attachments' => $feedback['attachments'],
            'metadata' => $feedback['metadata'],
            'votes' => $feedback['votes'],
            'created_at' => $feedback['created_at'],
            'updated_at' => $feedback['updated_at'],
            'assigned_to' => $feedback['assigned_to'],
            'resolution' => $feedback['resolution'],
            'resolved_at' => $feedback['resolved_at'],
            'tags' => $feedback['tags'],
        ];
    }
}
