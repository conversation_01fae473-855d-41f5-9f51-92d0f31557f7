<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Queue;
use Illuminate\Support\Facades\Redis;

/**
 * Admin System Controller
 *
 * Handles system health monitoring, configuration management, and maintenance operations.
 * Provides comprehensive system administration capabilities.
 */
class AdminSystemController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get system health overview.
     *
     * @group Admin System
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "System health retrieved successfully",
     *   "data": {
     *     "overall_status": "healthy",
     *     "services": {
     *       "database": {
     *         "status": "healthy",
     *         "response_time": 15,
     *         "connections": 25
     *       },
     *       "redis": {
     *         "status": "healthy",
     *         "memory_usage": "45%",
     *         "connected_clients": 12
     *       },
     *       "queue": {
     *         "status": "healthy",
     *         "pending_jobs": 5,
     *         "failed_jobs": 0
     *       }
     *     },
     *     "performance": {
     *       "avg_response_time": 120,
     *       "memory_usage": "65%",
     *       "cpu_usage": "35%",
     *       "disk_usage": "45%"
     *     }
     *   }
     * }
     */
    public function health(Request $request): JsonResponse
    {
        try {
            $health = [
                'overall_status' => $this->getOverallSystemStatus(),
                'services' => $this->getServiceHealthStatus(),
                'performance' => $this->getPerformanceMetrics(),
                'uptime' => $this->getSystemUptime(),
                'last_check' => now()->toISOString(),
            ];

            return $this->successResponse(
                $health,
                'System health retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve system health',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve system health');
        }
    }

    /**
     * Get system configuration.
     *
     * @group Admin System
     *
     * @authenticated
     */
    public function configuration(Request $request): JsonResponse
    {
        try {
            $config = [
                'application' => $this->getApplicationConfig(),
                'database' => $this->getDatabaseConfig(),
                'cache' => $this->getCacheConfig(),
                'queue' => $this->getQueueConfig(),
                'mail' => $this->getMailConfig(),
                'storage' => $this->getStorageConfig(),
                'security' => $this->getSecurityConfig(),
            ];

            return $this->successResponse(
                $config,
                'System configuration retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve system configuration',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve system configuration');
        }
    }

    /**
     * Clear system caches.
     *
     * @group Admin System
     *
     * @authenticated
     */
    public function clearCache(Request $request): JsonResponse
    {
        $request->validate([
            'cache_types' => 'sometimes|array',
            'cache_types.*' => 'string|in:application,config,route,view,event,tenant',
        ]);

        try {
            $cacheTypes = $request->input('cache_types', ['application', 'config', 'route', 'view']);
            $clearedCaches = [];

            foreach ($cacheTypes as $cacheType) {
                $result = $this->clearSpecificCache($cacheType);
                $clearedCaches[$cacheType] = $result;
            }

            // Log the cache clearing operation
            $this->loggingService->logActivity(
                'cache_cleared',
                'System caches cleared by admin',
                [
                    'cache_types' => $cacheTypes,
                    'cleared_by' => auth()->id(),
                    'results' => $clearedCaches,
                ]
            );

            return $this->successResponse([
                'cleared_caches' => $clearedCaches,
                'timestamp' => now()->toISOString(),
            ], 'System caches cleared successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to clear system caches',
                $e,
                ['cache_types' => $request->input('cache_types')]
            );

            return $this->serverErrorResponse('Failed to clear system caches');
        }
    }

    /**
     * Run system maintenance.
     *
     * @group Admin System
     *
     * @authenticated
     */
    public function maintenance(Request $request): JsonResponse
    {
        $request->validate([
            'operations' => 'required|array',
            'operations.*' => 'string|in:optimize,cleanup,backup,migrate,seed',
        ]);

        try {
            $operations = $request->input('operations');
            $results = [];

            foreach ($operations as $operation) {
                $result = $this->runMaintenanceOperation($operation);
                $results[$operation] = $result;
            }

            // Log the maintenance operation
            $this->loggingService->logActivity(
                'maintenance_executed',
                'System maintenance executed by admin',
                [
                    'operations' => $operations,
                    'executed_by' => auth()->id(),
                    'results' => $results,
                ]
            );

            return $this->successResponse([
                'maintenance_results' => $results,
                'timestamp' => now()->toISOString(),
            ], 'System maintenance completed successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to run system maintenance',
                $e,
                ['operations' => $request->input('operations')]
            );

            return $this->serverErrorResponse('Failed to run system maintenance');
        }
    }

    /**
     * Get system logs.
     *
     * @group Admin System
     *
     * @authenticated
     */
    public function logs(Request $request): JsonResponse
    {
        $request->validate([
            'log_type' => 'sometimes|string|in:application,error,access,security',
            'level' => 'sometimes|string|in:debug,info,warning,error,critical',
            'lines' => 'sometimes|integer|min:10|max:1000',
        ]);

        try {
            $logType = $request->input('log_type', 'application');
            $level = $request->input('level');
            $lines = $request->input('lines', 100);

            $logs = $this->getSystemLogs($logType, $level, $lines);

            return $this->successResponse([
                'log_type' => $logType,
                'level' => $level,
                'lines_requested' => $lines,
                'logs' => $logs,
                'timestamp' => now()->toISOString(),
            ], 'System logs retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve system logs',
                $e,
                ['request_params' => $request->only(['log_type', 'level', 'lines'])]
            );

            return $this->serverErrorResponse('Failed to retrieve system logs');
        }
    }

    /**
     * Get queue status.
     *
     * @group Admin System
     *
     * @authenticated
     */
    public function queueStatus(Request $request): JsonResponse
    {
        try {
            $queueStatus = [
                'default_queue' => $this->getQueueInfo('default'),
                'high_priority' => $this->getQueueInfo('high'),
                'low_priority' => $this->getQueueInfo('low'),
                'failed_jobs' => $this->getFailedJobsInfo(),
                'workers' => $this->getWorkerInfo(),
            ];

            return $this->successResponse(
                $queueStatus,
                'Queue status retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve queue status',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve queue status');
        }
    }

    /**
     * Restart queue workers.
     *
     * @group Admin System
     *
     * @authenticated
     */
    public function restartQueue(Request $request): JsonResponse
    {
        try {
            // Restart queue workers
            Artisan::call('queue:restart');

            // Log the queue restart
            $this->loggingService->logActivity(
                'queue_restarted',
                'Queue workers restarted by admin',
                [
                    'restarted_by' => auth()->id(),
                    'timestamp' => now()->toISOString(),
                ]
            );

            return $this->successResponse([
                'message' => 'Queue workers restart signal sent',
                'timestamp' => now()->toISOString(),
            ], 'Queue workers restarted successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to restart queue workers',
                $e
            );

            return $this->serverErrorResponse('Failed to restart queue workers');
        }
    }

    /**
     * Get overall system status.
     */
    private function getOverallSystemStatus(): string
    {
        $services = $this->getServiceHealthStatus();

        $healthyServices = collect($services)->filter(function ($service) {
            return $service['status'] === 'healthy';
        })->count();

        $totalServices = count($services);
        $healthPercentage = ($healthyServices / $totalServices) * 100;

        return match (true) {
            $healthPercentage >= 90 => 'healthy',
            $healthPercentage >= 70 => 'warning',
            default => 'critical',
        };
    }

    /**
     * Get service health status.
     */
    private function getServiceHealthStatus(): array
    {
        return [
            'database' => $this->getDatabaseHealth(),
            'redis' => $this->getRedisHealth(),
            'queue' => $this->getQueueHealth(),
            'storage' => $this->getStorageHealth(),
            'mail' => $this->getMailHealth(),
        ];
    }

    /**
     * Get database health.
     */
    private function getDatabaseHealth(): array
    {
        try {
            $start = microtime(true);
            DB::select('SELECT 1');
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            $connections = DB::select("SELECT count(*) as count FROM pg_stat_activity WHERE state = 'active'")[0]->count ?? 0;

            return [
                'status' => 'healthy',
                'response_time' => $responseTime,
                'connections' => $connections,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get Redis health.
     */
    private function getRedisHealth(): array
    {
        try {
            $start = microtime(true);
            Redis::ping();
            $responseTime = round((microtime(true) - $start) * 1000, 2);

            $info = Redis::info();
            $memoryUsage = $info['used_memory_human'] ?? 'Unknown';
            $connectedClients = $info['connected_clients'] ?? 0;

            return [
                'status' => 'healthy',
                'response_time' => $responseTime,
                'memory_usage' => $memoryUsage,
                'connected_clients' => $connectedClients,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get queue health.
     */
    private function getQueueHealth(): array
    {
        try {
            $pendingJobs = Queue::size('default');
            $failedJobs = DB::table('failed_jobs')->count();

            $status = match (true) {
                $failedJobs > 10 => 'warning',
                $pendingJobs > 1000 => 'warning',
                default => 'healthy',
            };

            return [
                'status' => $status,
                'pending_jobs' => $pendingJobs,
                'failed_jobs' => $failedJobs,
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get storage health.
     */
    private function getStorageHealth(): array
    {
        try {
            $diskFree = disk_free_space(storage_path());
            $diskTotal = disk_total_space(storage_path());
            $diskUsagePercent = round((($diskTotal - $diskFree) / $diskTotal) * 100, 2);

            $status = match (true) {
                $diskUsagePercent > 90 => 'critical',
                $diskUsagePercent > 80 => 'warning',
                default => 'healthy',
            };

            return [
                'status' => $status,
                'disk_usage_percent' => $diskUsagePercent,
                'free_space' => $this->formatBytes($diskFree),
                'total_space' => $this->formatBytes($diskTotal),
            ];
        } catch (\Exception $e) {
            return [
                'status' => 'unhealthy',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get mail health.
     */
    private function getMailHealth(): array
    {
        // This would test mail connectivity
        return [
            'status' => 'healthy',
            'provider' => config('mail.default'),
            'last_test' => 'Not implemented',
        ];
    }

    /**
     * Get performance metrics.
     */
    private function getPerformanceMetrics(): array
    {
        return [
            'avg_response_time' => $this->getAverageResponseTime(),
            'memory_usage' => $this->getMemoryUsage(),
            'cpu_usage' => $this->getCpuUsage(),
            'disk_usage' => $this->getDiskUsage(),
        ];
    }

    /**
     * Get system uptime.
     */
    private function getSystemUptime(): array
    {
        // This would integrate with system monitoring
        return [
            'uptime_seconds' => 86400, // 24 hours
            'uptime_formatted' => '1 day',
            'last_restart' => now()->subDay()->toISOString(),
        ];
    }

    /**
     * Clear specific cache type.
     */
    private function clearSpecificCache(string $cacheType): array
    {
        try {
            $result = match ($cacheType) {
                'application' => $this->clearApplicationCache(),
                'config' => $this->clearConfigCache(),
                'route' => $this->clearRouteCache(),
                'view' => $this->clearViewCache(),
                'event' => $this->clearEventCache(),
                'tenant' => $this->clearTenantCache(),
                default => ['success' => false, 'message' => 'Unknown cache type'],
            };

            return $result;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
            ];
        }
    }

    /**
     * Run maintenance operation.
     */
    private function runMaintenanceOperation(string $operation): array
    {
        try {
            $result = match ($operation) {
                'optimize' => $this->runOptimization(),
                'cleanup' => $this->runCleanup(),
                'backup' => $this->runBackup(),
                'migrate' => $this->runMigrations(),
                'seed' => $this->runSeeding(),
                default => ['success' => false, 'message' => 'Unknown operation'],
            };

            return $result;
        } catch (\Exception $e) {
            return [
                'success' => false,
                'message' => $e->getMessage(),
            ];
        }
    }

    /**
     * Placeholder methods for cache clearing.
     */
    private function clearApplicationCache(): array
    {
        Cache::flush();

        return ['success' => true, 'message' => 'Application cache cleared'];
    }

    private function clearConfigCache(): array
    {
        Artisan::call('config:clear');

        return ['success' => true, 'message' => 'Config cache cleared'];
    }

    private function clearRouteCache(): array
    {
        Artisan::call('route:clear');

        return ['success' => true, 'message' => 'Route cache cleared'];
    }

    private function clearViewCache(): array
    {
        Artisan::call('view:clear');

        return ['success' => true, 'message' => 'View cache cleared'];
    }

    private function clearEventCache(): array
    {
        Artisan::call('event:clear');

        return ['success' => true, 'message' => 'Event cache cleared'];
    }

    private function clearTenantCache(): array
    {
        // This would clear tenant-specific caches
        return ['success' => true, 'message' => 'Tenant cache cleared'];
    }

    /**
     * Placeholder methods for maintenance operations.
     */
    private function runOptimization(): array
    {
        Artisan::call('optimize');

        return ['success' => true, 'message' => 'System optimized'];
    }

    private function runCleanup(): array
    {
        // Clean up temporary files, logs, etc.
        return ['success' => true, 'message' => 'System cleanup completed'];
    }

    private function runBackup(): array
    {
        // Run database backup
        return ['success' => true, 'message' => 'Backup completed'];
    }

    private function runMigrations(): array
    {
        Artisan::call('migrate', ['--force' => true]);

        return ['success' => true, 'message' => 'Migrations completed'];
    }

    private function runSeeding(): array
    {
        Artisan::call('db:seed', ['--force' => true]);

        return ['success' => true, 'message' => 'Seeding completed'];
    }

    /**
     * Placeholder methods for system metrics.
     */
    private function getAverageResponseTime(): float
    {
        return 120.5; // milliseconds
    }

    private function getMemoryUsage(): string
    {
        return '65%';
    }

    private function getCpuUsage(): string
    {
        return '35%';
    }

    private function getDiskUsage(): string
    {
        return '45%';
    }

    /**
     * Format bytes to human readable format.
     */
    private function formatBytes(int $bytes): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);

        return round($bytes, 2).' '.$units[$pow];
    }

    /**
     * Placeholder methods for configuration.
     */
    private function getApplicationConfig(): array
    {
        return [
            'name' => config('app.name'),
            'env' => config('app.env'),
            'debug' => config('app.debug'),
            'url' => config('app.url'),
        ];
    }

    private function getDatabaseConfig(): array
    {
        return [
            'connection' => config('database.default'),
            'host' => config('database.connections.pgsql.host'),
            'database' => config('database.connections.pgsql.database'),
        ];
    }

    private function getCacheConfig(): array
    {
        return [
            'driver' => config('cache.default'),
            'prefix' => config('cache.prefix'),
        ];
    }

    private function getQueueConfig(): array
    {
        return [
            'driver' => config('queue.default'),
            'connection' => config('queue.connections.redis.connection'),
        ];
    }

    private function getMailConfig(): array
    {
        return [
            'driver' => config('mail.default'),
            'host' => config('mail.mailers.smtp.host'),
        ];
    }

    private function getStorageConfig(): array
    {
        return [
            'driver' => config('filesystems.default'),
            'cloud' => config('filesystems.cloud'),
        ];
    }

    private function getSecurityConfig(): array
    {
        return [
            'session_lifetime' => config('session.lifetime'),
            'csrf_protection' => true,
            'https_only' => config('session.secure'),
        ];
    }

    /**
     * Placeholder methods for logs and queue info.
     */
    private function getSystemLogs(string $logType, ?string $level, int $lines): array
    {
        return ['logs' => 'Log retrieval not implemented'];
    }

    private function getQueueInfo(string $queueName): array
    {
        return ['pending' => 0, 'processed' => 0, 'failed' => 0];
    }

    private function getFailedJobsInfo(): array
    {
        return ['count' => 0, 'recent_failures' => []];
    }

    private function getWorkerInfo(): array
    {
        return ['active_workers' => 0, 'worker_status' => []];
    }
}
