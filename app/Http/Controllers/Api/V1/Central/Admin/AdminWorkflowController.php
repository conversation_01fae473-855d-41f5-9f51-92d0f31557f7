<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Services\System\LoggingService;
use App\Services\System\WorkflowManagementService;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Workflow Controller
 *
 * Handles comprehensive workflow automation and process management for administrators.
 * Manages workflow creation, execution, monitoring, and optimization.
 */
class AdminWorkflowController extends Controller
{
    use ApiResponseTrait;

    public function __construct(
        private readonly WorkflowManagementService $workflowService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all workflows with filtering.
     *
     * @group Admin Workflows
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'status' => 'sometimes|string|in:draft,published,archived',
                'category' => 'sometimes|string|in:order_processing,customer_service,marketing,inventory,finance',
                'trigger_type' => 'sometimes|string|in:event,schedule,manual,webhook',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'is_active' => 'sometimes|boolean',
                'per_page' => 'sometimes|integer|min:1|max:100',
            ]);

            $workflows = $this->workflowService->getWorkflows($validated);

            // Paginate results manually since we're working with arrays
            $perPage = $validated['per_page'] ?? 15;
            $page = $request->get('page', 1);
            $offset = ($page - 1) * $perPage;

            $paginatedWorkflows = array_slice($workflows, $offset, $perPage);
            $total = count($workflows);

            return $this->successResponse([
                'data' => array_map([$this, 'transformWorkflow'], $paginatedWorkflows),
                'pagination' => [
                    'current_page' => $page,
                    'per_page' => $perPage,
                    'total' => $total,
                    'last_page' => ceil($total / $perPage),
                    'from' => $offset + 1,
                    'to' => min($offset + $perPage, $total),
                ],
            ], 'Workflows retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve workflows',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve workflows');
        }
    }

    /**
     * Create a new workflow.
     *
     * @group Admin Workflows
     *
     * @authenticated
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255',
                'description' => 'sometimes|string|max:1000',
                'category' => 'required|string|in:order_processing,customer_service,marketing,inventory,finance',
                'trigger_type' => 'required|string|in:event,schedule,manual,webhook',
                'trigger_config' => 'required|array',
                'steps' => 'required|array|min:1',
                'steps.*.type' => 'required|string',
                'steps.*.config' => 'sometimes|array',
                'conditions' => 'sometimes|array',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'priority' => 'sometimes|string|in:low,medium,high,critical',
                'timeout_minutes' => 'sometimes|integer|min:1|max:1440',
                'retry_config' => 'sometimes|array',
                'retry_config.max_retries' => 'sometimes|integer|min:0|max:10',
                'retry_config.retry_delay' => 'sometimes|integer|min:60|max:3600',
                'metadata' => 'sometimes|array',
            ]);

            $workflow = $this->workflowService->createWorkflow($validated);

            return $this->successResponse(
                $this->transformWorkflow($workflow),
                'Workflow created successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create workflow',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create workflow');
        }
    }

    /**
     * Get workflow analytics and performance metrics.
     *
     * @group Admin Workflows
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'category' => 'sometimes|string|in:order_processing,customer_service,marketing,inventory,finance',
                'tenant_id' => 'sometimes|uuid|exists:tenants,id',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
            ]);

            $analytics = $this->workflowService->getWorkflowAnalytics($validated);

            return $this->successResponse(
                $analytics,
                'Workflow analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve workflow analytics',
                $e,
                ['admin_id' => auth()->id()]
            );

            return $this->serverErrorResponse('Failed to retrieve workflow analytics');
        }
    }

    /**
     * Update workflow configuration.
     *
     * @group Admin Workflows
     *
     * @authenticated
     */
    public function update(Request $request, string $workflowId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'sometimes|string|max:255',
                'description' => 'sometimes|string|max:1000',
                'steps' => 'sometimes|array|min:1',
                'steps.*.type' => 'sometimes|string',
                'steps.*.config' => 'sometimes|array',
                'conditions' => 'sometimes|array',
                'trigger_config' => 'sometimes|array',
                'priority' => 'sometimes|string|in:low,medium,high,critical',
                'timeout_minutes' => 'sometimes|integer|min:1|max:1440',
                'retry_config' => 'sometimes|array',
                'status' => 'sometimes|string|in:draft,published,archived',
            ]);

            $workflow = $this->workflowService->updateWorkflow($workflowId, $validated);

            return $this->successResponse(
                $this->transformWorkflow($workflow),
                'Workflow updated successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->notFoundResponse('Workflow not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update workflow',
                $e,
                ['workflow_id' => $workflowId, 'updates' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update workflow');
        }
    }

    /**
     * Execute workflow manually.
     *
     * @group Admin Workflows
     *
     * @authenticated
     */
    public function execute(Request $request, string $workflowId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'context' => 'sometimes|array',
                'force_execution' => 'sometimes|boolean',
            ]);

            $execution = $this->workflowService->executeWorkflow(
                $workflowId,
                $validated['context'] ?? []
            );

            return $this->successResponse(
                $execution,
                'Workflow executed successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to execute workflow',
                $e,
                ['workflow_id' => $workflowId, 'context' => $request->get('context', [])]
            );

            return $this->serverErrorResponse('Failed to execute workflow');
        }
    }

    /**
     * Toggle workflow activation status.
     *
     * @group Admin Workflows
     *
     * @authenticated
     */
    public function toggle(Request $request, string $workflowId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'is_active' => 'required|boolean',
            ]);

            $workflow = $this->workflowService->toggleWorkflow(
                $workflowId,
                $validated['is_active']
            );

            $action = $validated['is_active'] ? 'activated' : 'deactivated';

            return $this->successResponse(
                $this->transformWorkflow($workflow),
                "Workflow {$action} successfully"
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to toggle workflow',
                $e,
                ['workflow_id' => $workflowId, 'is_active' => $request->get('is_active')]
            );

            return $this->serverErrorResponse('Failed to toggle workflow');
        }
    }

    /**
     * Get workflow execution history.
     *
     * @group Admin Workflows
     *
     * @authenticated
     */
    public function executions(Request $request, string $workflowId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'status' => 'sometimes|string|in:running,completed,failed,cancelled',
                'date_from' => 'sometimes|date',
                'date_to' => 'sometimes|date|after_or_equal:date_from',
                'per_page' => 'sometimes|integer|min:1|max:100',
            ]);

            $executions = $this->workflowService->getWorkflowExecutions($workflowId, $validated);

            return $this->successResponse(
                $executions,
                'Workflow executions retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve workflow executions',
                $e,
                ['workflow_id' => $workflowId]
            );

            return $this->serverErrorResponse('Failed to retrieve workflow executions');
        }
    }

    /**
     * Delete workflow.
     *
     * @group Admin Workflows
     *
     * @authenticated
     */
    public function destroy(Request $request, string $workflowId): JsonResponse
    {
        try {
            $validated = $request->validate([
                'reason' => 'sometimes|string|max:500',
            ]);

            $this->workflowService->deleteWorkflow(
                $workflowId,
                $validated['reason'] ?? null
            );

            return $this->successResponse(
                null,
                'Workflow deleted successfully'
            );

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to delete workflow',
                $e,
                ['workflow_id' => $workflowId]
            );

            return $this->serverErrorResponse('Failed to delete workflow');
        }
    }

    /**
     * Bulk workflow operations.
     *
     * @group Admin Workflows
     *
     * @authenticated
     */
    public function bulkOperations(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'workflow_ids' => 'required|array|min:1',
                'workflow_ids.*' => 'string',
                'action' => 'required|string|in:activate,deactivate,publish,delete',
                'reason' => 'sometimes|string|max:500',
            ]);

            $results = $this->workflowService->bulkWorkflowOperations(
                $validated['workflow_ids'],
                $validated['action'],
                array_filter([
                    'reason' => $validated['reason'] ?? null,
                ])
            );

            return $this->successResponse(
                $results,
                'Bulk workflow operations completed'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to perform bulk workflow operations',
                $e,
                ['admin_id' => auth()->id(), 'data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to perform bulk operations');
        }
    }

    /**
     * Transform workflow for API response.
     */
    public function transformWorkflow(array $workflow): array
    {
        return [
            'id' => $workflow['id'],
            'name' => $workflow['name'],
            'description' => $workflow['description'],
            'category' => $workflow['category'],
            'trigger_type' => $workflow['trigger_type'],
            'trigger_config' => $workflow['trigger_config'],
            'steps' => $workflow['steps'],
            'conditions' => $workflow['conditions'],
            'tenant_id' => $workflow['tenant_id'],
            'status' => $workflow['status'],
            'is_active' => $workflow['is_active'],
            'priority' => $workflow['priority'],
            'timeout_minutes' => $workflow['timeout_minutes'],
            'retry_config' => $workflow['retry_config'],
            'created_at' => $workflow['created_at'],
            'created_by' => $workflow['created_by'],
            'updated_at' => $workflow['updated_at'],
            'version' => $workflow['version'],
            'execution_count' => $workflow['execution_count'],
            'success_count' => $workflow['success_count'],
            'failure_count' => $workflow['failure_count'],
            'last_executed' => $workflow['last_executed'],
            'success_rate' => $workflow['execution_count'] > 0
                ? round(($workflow['success_count'] / $workflow['execution_count']) * 100, 2)
                : 0,
            'metadata' => $workflow['metadata'],
        ];
    }
}
