<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Models\Core\Feature;
use App\Services\Core\FeatureManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Feature Controller
 *
 * Handles platform feature management for administrators.
 * Manages features available across subscription plans.
 */
class AdminFeatureController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly FeatureManagementService $featureService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all platform features.
     *
     * @group Admin Features
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Feature::query()
                ->with(['planFeatures.plan']);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'name',
                    'slug',
                    'description',
                ],
                'sortFields' => [
                    'name',
                    'target_type',
                    'created_at',
                ],
                'filters' => [
                    'target_type' => ['type' => 'exact'],
                ],
                'message' => 'Features retrieved successfully',
                'entityName' => 'features',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve features',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve features');
        }
    }

    /**
     * Get feature details.
     *
     * @group Admin Features
     *
     * @authenticated
     */
    public function show(Request $request, string $featureId): JsonResponse
    {
        try {
            $feature = $this->featureService->getFeatureWithDetails($featureId);

            return $this->successResponse(
                $feature,
                'Feature details retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve feature details',
                $e,
                ['feature_id' => $featureId]
            );

            return $this->serverErrorResponse('Failed to retrieve feature details');
        }
    }

    /**
     * Create new feature.
     *
     * @group Admin Features
     *
     * @authenticated
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'slug' => 'required|string|max:255|unique:features,slug',
            'description' => 'required|string|max:1000',
            'target_type' => 'required|string|in:business,provider,both',
        ]);

        try {
            $feature = $this->featureService->createFeature($request->validated());

            return $this->createdResponse(
                $feature,
                'Feature created successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create feature',
                $e,
                ['request_data' => $request->validated()]
            );

            return $this->serverErrorResponse('Failed to create feature');
        }
    }

    /**
     * Update feature.
     *
     * @group Admin Features
     *
     * @authenticated
     */
    public function update(Request $request, string $featureId): JsonResponse
    {
        $request->validate([
            'name' => 'sometimes|string|max:255',
            'slug' => 'sometimes|string|max:255|unique:features,slug,'.$featureId,
            'description' => 'sometimes|string|max:1000',
            'target_type' => 'sometimes|string|in:business,provider,both',
        ]);

        try {
            $feature = $this->featureService->updateFeature($featureId, $request->validated());

            return $this->successResponse(
                $feature,
                'Feature updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update feature',
                $e,
                ['feature_id' => $featureId, 'request_data' => $request->validated()]
            );

            return $this->serverErrorResponse('Failed to update feature');
        }
    }

    /**
     * Delete feature.
     *
     * @group Admin Features
     *
     * @authenticated
     */
    public function destroy(Request $request, string $featureId): JsonResponse
    {
        try {
            $this->featureService->deleteFeature($featureId);

            return $this->successResponse(
                null,
                'Feature deleted successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to delete feature',
                $e,
                ['feature_id' => $featureId]
            );

            return $this->serverErrorResponse('Failed to delete feature');
        }
    }

    /**
     * Get feature usage analytics.
     *
     * @group Admin Features
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        $request->validate([
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
            'target_type' => 'sometimes|string|in:business,provider,both',
        ]);

        try {
            $period = $request->input('period', 'month');
            $targetType = $request->input('target_type');

            $analytics = $this->featureService->getFeatureAnalytics($period, $targetType);

            return $this->successResponse(
                $analytics,
                'Feature analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve feature analytics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve feature analytics');
        }
    }

    /**
     * Get feature adoption rates.
     *
     * @group Admin Features
     *
     * @authenticated
     */
    public function adoption(Request $request): JsonResponse
    {
        try {
            $adoption = $this->featureService->getFeatureAdoption();

            return $this->successResponse(
                $adoption,
                'Feature adoption rates retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve feature adoption rates',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve feature adoption rates');
        }
    }

    /**
     * Get feature usage by plan.
     *
     * @group Admin Features
     *
     * @authenticated
     */
    public function usageByPlan(Request $request): JsonResponse
    {
        try {
            $usage = $this->featureService->getFeatureUsageByPlan();

            return $this->successResponse(
                $usage,
                'Feature usage by plan retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve feature usage by plan',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve feature usage by plan');
        }
    }

    /**
     * Bulk update features.
     *
     * @group Admin Features
     *
     * @authenticated
     */
    public function bulkUpdate(Request $request): JsonResponse
    {
        $request->validate([
            'feature_ids' => 'required|array|min:1',
            'feature_ids.*' => 'uuid|exists:features,id',
            'action' => 'required|string|in:delete,update_target_type',
            'target_type' => 'required_if:action,update_target_type|string|in:business,provider,both',
        ]);

        try {
            $result = $this->featureService->bulkUpdateFeatures(
                $request->input('feature_ids'),
                $request->input('action'),
                $request->only(['target_type'])
            );

            return $this->successResponse(
                $result,
                'Features updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to bulk update features',
                $e,
                $request->only(['feature_ids', 'action'])
            );

            return $this->serverErrorResponse('Failed to update features');
        }
    }

    /**
     * Get feature statistics.
     *
     * @group Admin Features
     *
     * @authenticated
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $statistics = $this->featureService->getFeatureStatistics();

            return $this->successResponse(
                $statistics,
                'Feature statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve feature statistics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve feature statistics');
        }
    }
}
