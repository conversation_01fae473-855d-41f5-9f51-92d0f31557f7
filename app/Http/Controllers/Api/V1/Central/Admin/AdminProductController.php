<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Models\Business\Product;
use App\Services\Business\ProductManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Product Controller
 *
 * Handles cross-tenant product management for administrators.
 * Provides comprehensive product oversight and management capabilities.
 */
class AdminProductController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly ProductManagementService $productService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all products across tenants.
     *
     * @group Admin Products
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Product::query()
                ->with([
                    'business:id,business_name,tenant_id',
                    'category:id,name',
                    'collection:id,name',
                ]);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'name',
                    'description',
                    'sku',
                    'business.business_name',
                    'category.name',
                ],
                'sortFields' => [
                    'name',
                    'price',
                    'created_at',
                    'is_active',
                    'stock_quantity',
                ],
                'filters' => [
                    'is_active' => ['type' => 'boolean'],
                    'business_id' => ['type' => 'exact'],
                    'category_id' => ['type' => 'exact'],
                    'collection_id' => ['type' => 'exact'],
                    'tenant_id' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->whereHas('business', function ($q) use ($value) {
                                $q->where('tenant_id', $value);
                            });
                        },
                    ],
                    'price_min' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->where('price', '>=', $value);
                        },
                    ],
                    'price_max' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->where('price', '<=', $value);
                        },
                    ],
                    'low_stock' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            if ($value) {
                                $query->where('stock_quantity', '<=', 10);
                            }
                        },
                    ],
                ],
                'message' => 'Products retrieved successfully',
                'entityName' => 'products',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve products',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve products');
        }
    }

    /**
     * Get product details.
     *
     * @group Admin Products
     *
     * @authenticated
     */
    public function show(Request $request, string $productId): JsonResponse
    {
        try {
            $product = $this->productService->getProductWithDetails($productId);

            return $this->successResponse(
                $product,
                'Product details retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve product details',
                $e,
                ['product_id' => $productId]
            );

            return $this->serverErrorResponse('Failed to retrieve product details');
        }
    }

    /**
     * Update product status.
     *
     * @group Admin Products
     *
     * @authenticated
     */
    public function updateStatus(Request $request, string $productId): JsonResponse
    {
        $request->validate([
            'is_active' => 'required|boolean',
            'reason' => 'sometimes|string|max:500',
        ]);

        try {
            $product = $this->productService->updateProductStatus(
                $productId,
                $request->boolean('is_active'),
                $request->input('reason')
            );

            return $this->successResponse(
                $product,
                'Product status updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update product status',
                $e,
                ['product_id' => $productId]
            );

            return $this->serverErrorResponse('Failed to update product status');
        }
    }

    /**
     * Get product analytics.
     *
     * @group Admin Products
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        $request->validate([
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
            'business_id' => 'sometimes|uuid|exists:businesses,id',
            'category_id' => 'sometimes|uuid|exists:product_categories,id',
        ]);

        try {
            $period = $request->input('period', 'month');
            $businessId = $request->input('business_id');
            $categoryId = $request->input('category_id');

            $analytics = $this->productService->getProductAnalytics($period, $businessId, $categoryId);

            return $this->successResponse(
                $analytics,
                'Product analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve product analytics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve product analytics');
        }
    }

    /**
     * Get product statistics.
     *
     * @group Admin Products
     *
     * @authenticated
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $statistics = $this->productService->getProductStatistics();

            return $this->successResponse(
                $statistics,
                'Product statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve product statistics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve product statistics');
        }
    }

    /**
     * Get popular products.
     *
     * @group Admin Products
     *
     * @authenticated
     */
    public function popular(Request $request): JsonResponse
    {
        $request->validate([
            'limit' => 'sometimes|integer|min:1|max:100',
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
            'business_id' => 'sometimes|uuid|exists:businesses,id',
        ]);

        try {
            $limit = $request->input('limit', 20);
            $period = $request->input('period', 'month');
            $businessId = $request->input('business_id');

            $popularProducts = $this->productService->getPopularProducts($limit, $period, $businessId);

            return $this->successResponse(
                $popularProducts,
                'Popular products retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve popular products',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve popular products');
        }
    }

    /**
     * Get low stock products.
     *
     * @group Admin Products
     *
     * @authenticated
     */
    public function lowStock(Request $request): JsonResponse
    {
        $request->validate([
            'threshold' => 'sometimes|integer|min:0',
            'business_id' => 'sometimes|uuid|exists:businesses,id',
        ]);

        try {
            $threshold = $request->input('threshold', 10);
            $businessId = $request->input('business_id');

            $lowStockProducts = $this->productService->getLowStockProducts($threshold, $businessId);

            return $this->successResponse(
                $lowStockProducts,
                'Low stock products retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve low stock products',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve low stock products');
        }
    }

    /**
     * Bulk update products.
     *
     * @group Admin Products
     *
     * @authenticated
     */
    public function bulkUpdate(Request $request): JsonResponse
    {
        $request->validate([
            'product_ids' => 'required|array|min:1',
            'product_ids.*' => 'uuid|exists:products,id',
            'action' => 'required|string|in:activate,deactivate,delete,update_category',
            'category_id' => 'required_if:action,update_category|uuid|exists:product_categories,id',
            'reason' => 'sometimes|string|max:500',
        ]);

        try {
            $result = $this->productService->bulkUpdateProducts(
                $request->input('product_ids'),
                $request->input('action'),
                $request->only(['category_id', 'reason'])
            );

            return $this->successResponse(
                $result,
                'Products updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to bulk update products',
                $e,
                $request->only(['product_ids', 'action'])
            );

            return $this->serverErrorResponse('Failed to update products');
        }
    }

    /**
     * Get product performance.
     *
     * @group Admin Products
     *
     * @authenticated
     */
    public function performance(Request $request): JsonResponse
    {
        $request->validate([
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
            'metric' => 'sometimes|string|in:revenue,orders,views,conversion',
            'limit' => 'sometimes|integer|min:1|max:100',
        ]);

        try {
            $period = $request->input('period', 'month');
            $metric = $request->input('metric', 'revenue');
            $limit = $request->input('limit', 20);

            $performance = $this->productService->getProductPerformance($period, $metric, $limit);

            return $this->successResponse(
                $performance,
                'Product performance retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve product performance',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve product performance');
        }
    }

    /**
     * Export products.
     *
     * @group Admin Products
     *
     * @authenticated
     */
    public function export(Request $request): JsonResponse
    {
        $request->validate([
            'format' => 'required|string|in:csv,json',
            'business_id' => 'nullable|uuid|exists:businesses,id',
            'category_id' => 'nullable|uuid|exists:product_categories,id',
            'is_available' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
            'stock_status' => 'nullable|string|in:out_of_stock,low_stock,in_stock,unlimited',
        ]);

        try {
            $filename = $this->productService->exportProducts(
                array_filter($request->validated(), fn ($value) => $value !== null),
                $request->input('format')
            );

            return $this->successResponse(
                [
                    'filename' => $filename,
                    'download_url' => route('admin.products.download', ['filename' => $filename]),
                ],
                'Products exported successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to export products',
                $e,
                $request->validated()
            );

            return $this->serverErrorResponse('Failed to export products');
        }
    }

    /**
     * Download exported products.
     *
     * @group Admin Products
     *
     * @authenticated
     */
    public function download(string $filename)
    {
        try {
            $filepath = storage_path('app/exports/'.$filename);

            if (! file_exists($filepath)) {
                return $this->notFoundResponse('Export file not found');
            }

            // Security check: ensure filename is safe
            if (! preg_match('/^products_export_\d{4}_\d{2}_\d{2}_\d{2}_\d{2}_\d{2}\.(csv|json)$/', $filename)) {
                return $this->errorResponse('Invalid filename', 400);
            }

            return response()->download($filepath)->deleteFileAfterSend();

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to download product export',
                $e,
                ['filename' => $filename]
            );

            return $this->serverErrorResponse('Failed to download product export');
        }
    }

    /**
     * Get products requiring attention.
     *
     * @group Admin Products
     *
     * @authenticated
     */
    public function requiresAttention(): JsonResponse
    {
        try {
            $products = $this->productService->getProductsRequiringAttention();

            return $this->successResponse(
                $products,
                'Products requiring attention retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve products requiring attention',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve products requiring attention');
        }
    }

    /**
     * Get product compliance issues.
     *
     * @group Admin Products
     *
     * @authenticated
     */
    public function complianceIssues(): JsonResponse
    {
        try {
            $issues = $this->productService->getProductComplianceIssues();

            return $this->successResponse(
                $issues,
                'Product compliance issues retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve product compliance issues',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve product compliance issues');
        }
    }

    /**
     * Search products.
     *
     * @group Admin Products
     *
     * @authenticated
     */
    public function search(Request $request): JsonResponse
    {
        $request->validate([
            'query' => 'required|string|min:2|max:100',
            'business_id' => 'nullable|uuid|exists:businesses,id',
            'category_id' => 'nullable|uuid|exists:product_categories,id',
            'is_available' => 'nullable|boolean',
            'is_featured' => 'nullable|boolean',
        ]);

        try {
            $results = $this->productService->searchProducts(
                $request->input('query'),
                array_filter($request->only(['business_id', 'category_id', 'is_available', 'is_featured']))
            );

            return $this->successResponse(
                $results,
                'Product search completed successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to search products',
                $e,
                $request->validated()
            );

            return $this->serverErrorResponse('Failed to search products');
        }
    }

    /**
     * Get product inventory summary.
     *
     * @group Admin Products
     *
     * @authenticated
     */
    public function inventorySummary(): JsonResponse
    {
        try {
            $summary = $this->productService->getProductInventorySummary();

            return $this->successResponse(
                $summary,
                'Product inventory summary retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve product inventory summary',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve product inventory summary');
        }
    }

    /**
     * Bulk update product status.
     *
     * @group Admin Products
     *
     * @authenticated
     */
    public function bulkUpdateStatus(Request $request): JsonResponse
    {
        $request->validate([
            'product_ids' => 'required|array|min:1|max:100',
            'product_ids.*' => 'uuid|exists:products,id',
            'is_available' => 'required|boolean',
            'reason' => 'nullable|string|max:500',
        ]);

        try {
            $results = $this->productService->bulkUpdateProductStatus(
                $request->input('product_ids'),
                $request->boolean('is_available'),
                $request->input('reason')
            );

            return $this->successResponse(
                $results,
                "Bulk status update completed: {$results['updated']} updated, {$results['failed']} failed"
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to bulk update product status',
                $e,
                $request->only(['product_ids', 'is_available'])
            );

            return $this->serverErrorResponse('Failed to bulk update product status');
        }
    }
}
