<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Services\System\LoggingService;
use App\Services\System\PermissionManagementService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Silber\Bouncer\Database\Ability;
use Si<PERSON>ber\Bouncer\Database\Role;

/**
 * Admin Permission Controller
 *
 * Handles role and permission management for administrators.
 * Manages platform roles, permissions, role assignments, and access control policies.
 */
class AdminPermissionController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly PermissionManagementService $permissionService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all roles.
     *
     * @group Admin Permissions
     *
     * @authenticated
     */
    public function roles(Request $request): JsonResponse
    {
        try {
            $query = Role::query()
                ->withCount(['users'])
                ->with(['abilities:id,name,title']);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'name',
                    'title',
                ],
                'sortFields' => [
                    'name',
                    'title',
                    'users_count',
                    'created_at',
                ],
                'message' => 'Roles retrieved successfully',
                'entityName' => 'roles',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve roles',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve roles');
        }
    }

    /**
     * Get all abilities.
     *
     * @group Admin Permissions
     *
     * @authenticated
     */
    public function abilities(Request $request): JsonResponse
    {
        try {
            $query = Ability::query();

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'name',
                    'title',
                ],
                'sortFields' => [
                    'name',
                    'title',
                    'created_at',
                ],
                'message' => 'Abilities retrieved successfully',
                'entityName' => 'abilities',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve abilities',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve abilities');
        }
    }

    /**
     * Get abilities grouped by category.
     *
     * @group Admin Permissions
     *
     * @authenticated
     */
    public function abilitiesGrouped(): JsonResponse
    {
        try {
            $grouped = $this->permissionService->getAllAbilitiesGrouped();

            return $this->successResponse(
                $grouped,
                'Grouped abilities retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve grouped abilities',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve grouped abilities');
        }
    }

    /**
     * Create a new role.
     *
     * @group Admin Permissions
     *
     * @authenticated
     */
    public function createRole(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:roles,name',
                'title' => 'required|string|max:255',
                'abilities' => 'nullable|array',
                'abilities.*' => 'string|exists:abilities,name',
            ]);

            $role = $this->permissionService->createRole(
                $validated['name'],
                $validated['title'],
                $validated['abilities'] ?? []
            );

            return $this->successResponse(
                $role->load(['abilities']),
                'Role created successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create role',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create role');
        }
    }

    /**
     * Update role abilities.
     *
     * @group Admin Permissions
     *
     * @authenticated
     */
    public function updateRoleAbilities(Request $request, string $roleName): JsonResponse
    {
        try {
            $validated = $request->validate([
                'abilities' => 'required|array',
                'abilities.*' => 'string|exists:abilities,name',
            ]);

            $role = $this->permissionService->updateRoleAbilities(
                $roleName,
                $validated['abilities']
            );

            return $this->successResponse(
                $role,
                'Role abilities updated successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Role not found');
        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update role abilities',
                $e,
                ['role_name' => $roleName, 'request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to update role abilities');
        }
    }

    /**
     * Delete a role.
     *
     * @group Admin Permissions
     *
     * @authenticated
     */
    public function deleteRole(string $roleName): JsonResponse
    {
        try {
            $deleted = $this->permissionService->deleteRole($roleName);

            if ($deleted) {
                return $this->successResponse(
                    null,
                    'Role deleted successfully'
                );
            } else {
                return $this->serverErrorResponse('Failed to delete role');
            }

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Role not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to delete role',
                $e,
                ['role_name' => $roleName]
            );

            return $this->badRequestResponse($e->getMessage());
        }
    }

    /**
     * Create a new ability.
     *
     * @group Admin Permissions
     *
     * @authenticated
     */
    public function createAbility(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:abilities,name',
                'title' => 'nullable|string|max:255',
            ]);

            $ability = $this->permissionService->createAbility(
                $validated['name'],
                $validated['title'] ?? null
            );

            return $this->successResponse(
                $ability,
                'Ability created successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create ability',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to create ability');
        }
    }

    /**
     * Assign role to user.
     *
     * @group Admin Permissions
     *
     * @authenticated
     */
    public function assignRole(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'user_id' => 'required|uuid|exists:users,id',
                'role_name' => 'required|string|exists:roles,name',
            ]);

            $this->permissionService->assignRoleToUser(
                $validated['user_id'],
                $validated['role_name']
            );

            return $this->successResponse(
                null,
                'Role assigned to user successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to assign role to user',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to assign role to user');
        }
    }

    /**
     * Remove role from user.
     *
     * @group Admin Permissions
     *
     * @authenticated
     */
    public function removeRole(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'user_id' => 'required|uuid|exists:users,id',
                'role_name' => 'required|string|exists:roles,name',
            ]);

            $this->permissionService->removeRoleFromUser(
                $validated['user_id'],
                $validated['role_name']
            );

            return $this->successResponse(
                null,
                'Role removed from user successfully'
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to remove role from user',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to remove role from user');
        }
    }

    /**
     * Get user permissions.
     *
     * @group Admin Permissions
     *
     * @authenticated
     */
    public function userPermissions(string $userId): JsonResponse
    {
        try {
            $permissions = $this->permissionService->getUserPermissions($userId);

            return $this->successResponse(
                $permissions,
                'User permissions retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('User not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve user permissions',
                $e,
                ['user_id' => $userId]
            );

            return $this->serverErrorResponse('Failed to retrieve user permissions');
        }
    }

    /**
     * Bulk assign roles.
     *
     * @group Admin Permissions
     *
     * @authenticated
     */
    public function bulkAssignRoles(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'user_ids' => 'required|array|min:1|max:100',
                'user_ids.*' => 'uuid|exists:users,id',
                'role_names' => 'required|array|min:1',
                'role_names.*' => 'string|exists:roles,name',
            ]);

            $results = $this->permissionService->bulkAssignRoles(
                $validated['user_ids'],
                $validated['role_names']
            );

            return $this->successResponse(
                $results,
                "Bulk role assignment completed: {$results['success']} successful, {$results['failed']} failed"
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to bulk assign roles',
                $e,
                ['request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to bulk assign roles');
        }
    }

    /**
     * Get role statistics.
     *
     * @group Admin Permissions
     *
     * @authenticated
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = $this->permissionService->getRoleStatistics();

            return $this->successResponse(
                $stats,
                'Role statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve role statistics',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve role statistics');
        }
    }

    /**
     * Search users by role.
     *
     * @group Admin Permissions
     *
     * @authenticated
     */
    public function searchUsersByRole(Request $request, string $roleName): JsonResponse
    {
        try {
            $validated = $request->validate([
                'query' => 'nullable|string|min:2|max:100',
            ]);

            $users = $this->permissionService->searchUsersByRole(
                $roleName,
                $validated['query'] ?? null
            );

            return $this->successResponse(
                $users,
                "Users with role '{$roleName}' retrieved successfully"
            );

        } catch (\Illuminate\Validation\ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to search users by role',
                $e,
                ['role_name' => $roleName, 'request_data' => $request->all()]
            );

            return $this->serverErrorResponse('Failed to search users by role');
        }
    }

    /**
     * Get role hierarchy.
     *
     * @group Admin Permissions
     *
     * @authenticated
     */
    public function roleHierarchy(): JsonResponse
    {
        try {
            $hierarchy = $this->permissionService->getRoleHierarchy();

            return $this->successResponse(
                $hierarchy,
                'Role hierarchy retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve role hierarchy',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve role hierarchy');
        }
    }

    /**
     * Validate role permissions.
     *
     * @group Admin Permissions
     *
     * @authenticated
     */
    public function validateRole(string $roleName): JsonResponse
    {
        try {
            $validation = $this->permissionService->validateRolePermissions($roleName);

            return $this->successResponse(
                $validation,
                'Role validation completed'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Role not found');
        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to validate role permissions',
                $e,
                ['role_name' => $roleName]
            );

            return $this->serverErrorResponse('Failed to validate role permissions');
        }
    }
}
