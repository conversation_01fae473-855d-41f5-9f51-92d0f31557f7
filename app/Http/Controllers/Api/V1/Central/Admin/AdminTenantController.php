<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Enums\System\TenantStatus;
use App\Http\Controllers\Controller;
use App\Http\Requests\CreateTenantRequest;
use App\Http\Requests\UpdateTenantRequest;
use App\Http\Requests\UpdateTenantStatusRequest;
use App\Models\System\Tenant;
use App\Services\System\LoggingService;
use App\Services\System\TenantService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Tenant Management Controller
 *
 * Handles CRUD operations for tenant management using TenantService
 * and leveraging Stancl Tenancy v4 built-in features.
 */
class AdminTenantController extends Controller
{
    use \App\Traits\ApiResponseTrait, \App\Traits\QueryHandlerTrait;

    public function __construct(
        private readonly TenantService $tenantService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Display a paginated listing of tenants with filtering.
     *
     * @group Admin Tenant Management
     *
     * @authenticated
     *
     * @queryParam page integer Page number for pagination. Example: 1
     * @queryParam per_page integer Items per page (max 100). Example: 15
     * @queryParam search string Search tenants by name. Example: restaurant
     * @queryParam status string Filter by status. Example: active
     * @queryParam tenant_type string Filter by tenant type. Example: business
     * @queryParam created_by string Filter by creator ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     * @queryParam sort_by string Sort by field. Example: name
     * @queryParam sort_direction string Sort direction. Example: asc
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Tenants retrieved successfully",
     *   "data": {
     *     "data": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *         "name": "Restaurant Chain Ltd",
     *         "tenant_type": "business",
     *         "status": "active",
     *         "subscription_plan": "premium",
     *         "created_at": "2024-01-15T10:30:00Z"
     *       }
     *     ],
     *     "current_page": 1,
     *     "per_page": 15,
     *     "total": 50
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        try {
            // Build query with basic relationships (avoid problematic ones for now)
            $query = Tenant::query();

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'name',
                ],
                'sortFields' => [
                    'name',
                    'tenant_type',
                    'status',
                    'created_at',
                    'updated_at',
                ],
                'filters' => [
                    'status' => ['type' => 'exact'],
                    'tenant_type' => ['type' => 'exact'],
                    'created_by' => ['type' => 'exact'],
                ],
                'message' => 'Tenants retrieved successfully',
                'entityName' => 'tenants',
                'transformer' => [$this, 'transformTenant'],
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve tenants',
                $e,
                ['request_params' => $request->only(['search', 'status', 'tenant_type', 'created_by'])]
            );

            return $this->serverErrorResponse('Failed to retrieve tenants');
        }
    }

    /**
     * Store a newly created tenant.
     */
    public function store(CreateTenantRequest $request): JsonResponse
    {
        try {
            $tenant = $this->tenantService->createTenant($request->validated());

            return $this->successResponse(
                $tenant->load(['subscriptionPlan', 'creator', 'domains']),
                'Tenant created successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create tenant',
                $e,
                ['request_data' => $request->validated()]
            );

            return $this->errorResponse(
                $e->getMessage(),
                $e instanceof \App\Exceptions\BusinessLogicException ? 422 : 500,
                $e instanceof \App\Exceptions\BusinessLogicException ? $e->getCode() : 'TENANT_CREATION_FAILED'
            );
        }
    }

    /**
     * Display the specified tenant.
     */
    public function show(string $id): JsonResponse
    {
        try {
            $tenant = $this->tenantService->getTenant($id);

            return $this->successResponse(
                $tenant->load(['subscriptionPlan', 'creator', 'domains', 'businesses', 'deliveryProviders']),
                'Tenant retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve tenant',
                $e,
                ['tenant_id' => $id]
            );

            return $this->errorResponse(
                $e->getMessage(),
                $e instanceof \App\Exceptions\BusinessLogicException ? 404 : 500,
                $e instanceof \App\Exceptions\BusinessLogicException ? $e->getCode() : 'TENANT_RETRIEVAL_FAILED'
            );
        }
    }

    /**
     * Update the specified tenant.
     */
    public function update(UpdateTenantRequest $request, string $id): JsonResponse
    {
        try {
            $tenant = $this->tenantService->updateTenant($id, $request->validated());

            return $this->successResponse(
                $tenant->load(['subscriptionPlan', 'creator', 'domains']),
                'Tenant updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update tenant',
                $e,
                ['tenant_id' => $id, 'request_data' => $request->validated()]
            );

            return $this->errorResponse(
                $e->getMessage(),
                $e instanceof \App\Exceptions\BusinessLogicException ? 422 : 500,
                $e instanceof \App\Exceptions\BusinessLogicException ? $e->getCode() : 'TENANT_UPDATE_FAILED'
            );
        }
    }

    /**
     * Archive the specified tenant (soft delete).
     */
    public function destroy(string $id, Request $request): JsonResponse
    {
        try {
            $reason = $request->get('reason');
            $tenant = $this->tenantService->archiveTenant($id, $reason);

            return $this->successResponse(
                $tenant,
                'Tenant archived successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to archive tenant',
                $e,
                ['tenant_id' => $id, 'reason' => $request->get('reason')]
            );

            return $this->errorResponse(
                $e->getMessage(),
                $e instanceof \App\Exceptions\BusinessLogicException ? 422 : 500,
                $e instanceof \App\Exceptions\BusinessLogicException ? $e->getCode() : 'TENANT_ARCHIVE_FAILED'
            );
        }
    }

    /**
     * Restore an archived tenant.
     */
    public function restore(string $id): JsonResponse
    {
        try {
            $tenant = $this->tenantService->restoreTenant($id);

            return $this->successResponse(
                $tenant,
                'Tenant restored successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to restore tenant',
                $e,
                ['tenant_id' => $id]
            );

            return $this->errorResponse(
                $e->getMessage(),
                $e instanceof \App\Exceptions\BusinessLogicException ? 422 : 500,
                $e instanceof \App\Exceptions\BusinessLogicException ? $e->getCode() : 'TENANT_RESTORE_FAILED'
            );
        }
    }

    /**
     * Update tenant status.
     */
    public function updateStatus(UpdateTenantStatusRequest $request, string $id): JsonResponse
    {
        try {
            $validated = $request->validated();
            $tenant = $this->tenantService->updateTenantStatus(
                $id,
                TenantStatus::from($validated['status']),
                $validated['reason'] ?? null
            );

            return $this->successResponse(
                $tenant,
                'Tenant status updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update tenant status',
                $e,
                ['tenant_id' => $id, 'request_data' => $request->validated()]
            );

            return $this->errorResponse(
                $e->getMessage(),
                $e instanceof \App\Exceptions\BusinessLogicException ? 422 : 500,
                $e instanceof \App\Exceptions\BusinessLogicException ? $e->getCode() : 'TENANT_STATUS_UPDATE_FAILED'
            );
        }
    }

    /**
     * Get tenant statistics for admin dashboard.
     */
    public function statistics(): JsonResponse
    {
        try {
            $statistics = $this->tenantService->getTenantStatistics();

            return $this->successResponse(
                $statistics,
                'Tenant statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve tenant statistics',
                $e
            );

            return $this->errorResponse(
                'Failed to retrieve tenant statistics',
                500,
                'TENANT_STATISTICS_FAILED'
            );
        }
    }

    /**
     * Check if subdomain is available.
     */
    public function checkSubdomain(Request $request): JsonResponse
    {
        try {
            $subdomain = $request->get('subdomain');

            if (! $subdomain) {
                return $this->errorResponse(
                    'Subdomain parameter is required',
                    422,
                    'SUBDOMAIN_REQUIRED'
                );
            }

            $available = $this->tenantService->isSubdomainAvailable($subdomain);

            return $this->successResponse(
                ['available' => $available],
                $available ? 'Subdomain is available' : 'Subdomain is not available'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to check subdomain availability',
                $e,
                ['subdomain' => $request->get('subdomain')]
            );

            return $this->errorResponse(
                'Failed to check subdomain availability',
                500,
                'SUBDOMAIN_CHECK_FAILED'
            );
        }
    }

    // Helper Methods

    /**
     * Transform tenant for API response.
     */
    private function transformTenant($tenant, bool $detailed = false): array
    {
        $data = [
            'id' => $tenant->id,
            'name' => $tenant->name,
            'tenant_type' => $tenant->tenant_type?->value ?? null,
            'status' => $tenant->status?->value ?? null,
            'created_at' => $tenant->created_at?->toISOString(),
        ];

        if ($detailed) {
            $data = array_merge($data, [
                'settings' => $tenant->settings ?? [],
                'updated_at' => $tenant->updated_at?->toISOString(),
            ]);
        }

        return $data;
    }
}
