<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Services\Marketing\CampaignManagementService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Admin Campaign Controller
 *
 * Handles marketing campaign management for administrators.
 * Provides comprehensive campaign creation, management, and analytics.
 */
class AdminCampaignController extends Controller
{
    use ApiResponseTrait;

    public function __construct(
        private readonly CampaignManagementService $campaignService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get campaign overview.
     *
     * @group Admin Campaigns
     *
     * @authenticated
     */
    public function index(): JsonResponse
    {
        try {
            $overview = $this->campaignService->getCampaignOverview();

            return $this->successResponse(
                $overview,
                'Campaign overview retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve campaign overview',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve campaign overview');
        }
    }

    /**
     * Get campaign analytics.
     *
     * @group Admin Campaigns
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        $request->validate([
            'status' => 'nullable|string|in:draft,active,paused,completed,cancelled',
            'type' => 'nullable|string|in:email,sms,push,whatsapp,multi_channel',
            'date_from' => 'nullable|date',
            'date_to' => 'nullable|date|after_or_equal:date_from',
        ]);

        try {
            $analytics = $this->campaignService->getCampaignAnalytics(
                array_filter($request->only(['status', 'type', 'date_from', 'date_to']))
            );

            return $this->successResponse(
                $analytics,
                'Campaign analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve campaign analytics',
                $e,
                $request->validated()
            );

            return $this->serverErrorResponse('Failed to retrieve campaign analytics');
        }
    }

    /**
     * Create a new campaign.
     *
     * @group Admin Campaigns
     *
     * @authenticated
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'nullable|string|max:1000',
            'type' => 'required|string|in:email,sms,push,whatsapp,multi_channel',
            'channels' => 'required|array|min:1',
            'channels.*' => 'string|in:email,sms,push,whatsapp',
            'subject' => 'nullable|string|max:255',
            'message' => 'required|string',
            'template_id' => 'nullable|uuid',
            'variables' => 'nullable|array',
            'send_immediately' => 'nullable|boolean',
            'scheduled_at' => 'nullable|date|after:now',
            'timezone' => 'nullable|string',
            'frequency' => 'nullable|string|in:once,daily,weekly,monthly',
            'ab_test_enabled' => 'nullable|boolean',
            'total_budget' => 'nullable|numeric|min:0',
            'daily_budget' => 'nullable|numeric|min:0',
            'currency' => 'nullable|string|size:3',
            'audiences' => 'required|array|min:1',
            'audiences.*.name' => 'required|string|max:255',
            'audiences.*.criteria' => 'required|array|min:1',
            'audiences.*.criteria.*.field' => 'required|string',
            'audiences.*.criteria.*.operator' => 'required|string',
            'audiences.*.criteria.*.value' => 'required',
        ]);

        try {
            $campaign = $this->campaignService->createCampaign($request->validated());

            return $this->successResponse(
                $campaign,
                'Campaign created successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create campaign',
                $e,
                $request->validated()
            );

            return $this->serverErrorResponse('Failed to create campaign');
        }
    }

    /**
     * Get a specific campaign.
     *
     * @group Admin Campaigns
     *
     * @authenticated
     */
    public function show(string $campaignId): JsonResponse
    {
        try {
            $campaign = \App\Models\Marketing\Campaign::with(['audiences', 'metrics', 'creator', 'launcher'])
                ->findOrFail($campaignId);

            return $this->successResponse(
                $campaign,
                'Campaign retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Campaign not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve campaign',
                $e,
                ['campaign_id' => $campaignId]
            );

            return $this->serverErrorResponse('Failed to retrieve campaign');
        }
    }

    /**
     * Update a campaign.
     *
     * @group Admin Campaigns
     *
     * @authenticated
     */
    public function update(Request $request, string $campaignId): JsonResponse
    {
        $request->validate([
            'name' => 'sometimes|string|max:255',
            'description' => 'nullable|string|max:1000',
            'type' => 'sometimes|string|in:email,sms,push,whatsapp,multi_channel',
            'channels' => 'sometimes|array|min:1',
            'channels.*' => 'string|in:email,sms,push,whatsapp',
            'status' => 'sometimes|string|in:draft,active,paused,completed,cancelled',
            'content' => 'sometimes|array',
            'content.subject' => 'nullable|string|max:255',
            'content.message' => 'sometimes|string',
            'content.template_id' => 'nullable|uuid',
            'content.variables' => 'nullable|array',
            'settings' => 'sometimes|array',
            'settings.send_immediately' => 'nullable|boolean',
            'settings.scheduled_at' => 'nullable|date|after:now',
            'settings.timezone' => 'nullable|string',
            'settings.frequency' => 'nullable|string|in:once,daily,weekly,monthly',
            'settings.ab_test_enabled' => 'nullable|boolean',
            'budget' => 'sometimes|array',
            'budget.total_budget' => 'nullable|numeric|min:0',
            'budget.daily_budget' => 'nullable|numeric|min:0',
            'budget.currency' => 'nullable|string|size:3',
        ]);

        try {
            $campaign = $this->campaignService->updateCampaign($campaignId, $request->validated());

            return $this->successResponse(
                $campaign,
                'Campaign updated successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Campaign not found');

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 400);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update campaign',
                $e,
                array_merge($request->validated(), ['campaign_id' => $campaignId])
            );

            return $this->serverErrorResponse('Failed to update campaign');
        }
    }

    /**
     * Launch a campaign.
     *
     * @group Admin Campaigns
     *
     * @authenticated
     */
    public function launch(string $campaignId): JsonResponse
    {
        try {
            $result = $this->campaignService->launchCampaign($campaignId);

            return $this->successResponse(
                $result,
                'Campaign launched successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Campaign not found');

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 400);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to launch campaign',
                $e,
                ['campaign_id' => $campaignId]
            );

            return $this->serverErrorResponse('Failed to launch campaign');
        }
    }

    /**
     * Pause a campaign.
     *
     * @group Admin Campaigns
     *
     * @authenticated
     */
    public function pause(Request $request, string $campaignId): JsonResponse
    {
        $request->validate([
            'reason' => 'nullable|string|max:500',
        ]);

        try {
            $campaign = $this->campaignService->pauseCampaign(
                $campaignId,
                $request->input('reason')
            );

            return $this->successResponse(
                $campaign,
                'Campaign paused successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Campaign not found');

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 400);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to pause campaign',
                $e,
                array_merge($request->validated(), ['campaign_id' => $campaignId])
            );

            return $this->serverErrorResponse('Failed to pause campaign');
        }
    }

    /**
     * Get campaign recipients.
     *
     * @group Admin Campaigns
     *
     * @authenticated
     */
    public function recipients(Request $request, string $campaignId): JsonResponse
    {
        $request->validate([
            'limit' => 'nullable|integer|min:1|max:1000',
        ]);

        try {
            $recipients = $this->campaignService->getCampaignRecipients(
                $campaignId,
                $request->validated()
            );

            return $this->successResponse(
                $recipients,
                'Campaign recipients retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Campaign not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve campaign recipients',
                $e,
                array_merge($request->validated(), ['campaign_id' => $campaignId])
            );

            return $this->serverErrorResponse('Failed to retrieve campaign recipients');
        }
    }

    /**
     * Update campaign metrics.
     *
     * @group Admin Campaigns
     *
     * @authenticated
     */
    public function updateMetrics(Request $request, string $campaignId): JsonResponse
    {
        $request->validate([
            'total_sent' => 'nullable|integer|min:0',
            'total_delivered' => 'nullable|integer|min:0',
            'total_opened' => 'nullable|integer|min:0',
            'total_clicked' => 'nullable|integer|min:0',
            'total_conversions' => 'nullable|integer|min:0',
            'total_unsubscribed' => 'nullable|integer|min:0',
            'revenue_generated' => 'nullable|numeric|min:0',
            'cost_spent' => 'nullable|numeric|min:0',
        ]);

        try {
            $metrics = $this->campaignService->updateCampaignMetrics(
                $campaignId,
                $request->validated()
            );

            return $this->successResponse(
                $metrics,
                'Campaign metrics updated successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Campaign not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update campaign metrics',
                $e,
                array_merge($request->validated(), ['campaign_id' => $campaignId])
            );

            return $this->serverErrorResponse('Failed to update campaign metrics');
        }
    }

    /**
     * Get A/B test results.
     *
     * @group Admin Campaigns
     *
     * @authenticated
     */
    public function abTestResults(string $campaignId): JsonResponse
    {
        try {
            $results = $this->campaignService->getABTestResults($campaignId);

            return $this->successResponse(
                $results,
                'A/B test results retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Campaign not found');

        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 400);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve A/B test results',
                $e,
                ['campaign_id' => $campaignId]
            );

            return $this->serverErrorResponse('Failed to retrieve A/B test results');
        }
    }

    /**
     * Delete a campaign.
     *
     * @group Admin Campaigns
     *
     * @authenticated
     */
    public function destroy(string $campaignId): JsonResponse
    {
        try {
            $campaign = \App\Models\Marketing\Campaign::findOrFail($campaignId);

            // Prevent deletion of active campaigns
            if ($campaign->status === 'active') {
                return $this->errorResponse('Cannot delete active campaign', 400);
            }

            $campaign->delete();

            $this->loggingService->logInfo('Campaign deleted', [
                'campaign_id' => $campaignId,
                'campaign_name' => $campaign->name,
                'deleted_by' => auth()->id(),
            ]);

            return $this->successResponse(
                null,
                'Campaign deleted successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Campaign not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to delete campaign',
                $e,
                ['campaign_id' => $campaignId]
            );

            return $this->serverErrorResponse('Failed to delete campaign');
        }
    }
}
