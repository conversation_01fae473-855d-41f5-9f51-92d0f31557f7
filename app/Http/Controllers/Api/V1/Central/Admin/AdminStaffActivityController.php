<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Http\Controllers\Controller;
use App\Models\Business\Business;
use App\Models\User\StaffActivity;
use App\Models\User\User;
use App\Services\Business\StaffActivityService;
use App\Services\System\LoggingService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Staff Activity Controller
 *
 * Handles staff activity tracking and audit log management for administrators.
 * Provides comprehensive monitoring and reporting of staff actions.
 */
class AdminStaffActivityController extends Controller
{
    use \App\Traits\ApiResponseTrait, \App\Traits\QueryHandlerTrait;

    public function __construct(
        private readonly StaffActivityService $staffActivityService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get staff activities with filtering and pagination.
     *
     * @group Admin Staff Activities
     *
     * @authenticated
     *
     * @queryParam activity_type string Filter by activity type. Example: order_update
     * @queryParam activity_category string Filter by category (authentication, order_management, inventory, etc.). Example: order_management
     * @queryParam severity string Filter by severity (low, medium, high, critical). Example: high
     * @queryParam sensitive_only boolean Show only sensitive activities. Example: true
     * @queryParam user_id string Filter by specific user ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     * @queryParam business_id string Filter by specific business ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87b
     * @queryParam start_date string Start date for filtering (Y-m-d). Example: 2024-01-01
     * @queryParam end_date string End date for filtering (Y-m-d). Example: 2024-01-31
     * @queryParam per_page integer Items per page (1-100). Example: 25
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Staff activities retrieved successfully",
     *   "data": [
     *     {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "activity_type": "order_update",
     *       "activity_category": "order_management",
     *       "description": "Order updated: ORD-2024-ABC123",
     *       "severity": "low",
     *       "is_sensitive": false,
     *       "user": {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *         "name": "John Doe",
     *         "email": "<EMAIL>"
     *       },
     *       "business": {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
     *         "name": "Coffee Shop"
     *       },
     *       "subject_type": "App\\Models\\Order",
     *       "subject_id": "019723aa-3202-70dd-a0c1-3565681dd87d",
     *       "changes": {
     *         "status": {
     *           "old": "pending",
     *           "new": "confirmed"
     *         }
     *       },
     *       "ip_address": "***********",
     *       "created_at": "2024-01-22T15:30:00Z"
     *     }
     *   ],
     *   "meta": {
     *     "current_page": 1,
     *     "per_page": 25,
     *     "total": 150,
     *     "last_page": 6
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'activity_type' => 'sometimes|string',
            'activity_category' => 'sometimes|string|in:authentication,order_management,inventory,user_management,payment,delivery,settings,security',
            'severity' => 'sometimes|string|in:low,medium,high,critical',
            'sensitive_only' => 'sometimes|boolean',
            'user_id' => 'sometimes|uuid|exists:users,id',
            'business_id' => 'sometimes|uuid|exists:businesses,id',
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
            'per_page' => 'sometimes|integer|min:1|max:100',
        ]);

        try {
            $query = StaffActivity::with([
                'user:id,first_name,last_name,email',
                'business:id,business_name',
                'deliveryProvider:id,business_name',
            ])->orderBy('created_at', 'desc');

            return $this->handleQuery($query, $request, [
                'searchFields' => ['description', 'activity_type'],
                'sortFields' => ['created_at', 'activity_type', 'activity_category', 'severity'],
                'filters' => [
                    'activity_type' => ['type' => 'exact'],
                    'activity_category' => ['type' => 'exact'],
                    'severity' => ['type' => 'exact'],
                    'user_id' => ['type' => 'exact'],
                    'business_id' => ['type' => 'exact'],
                    'sensitive_only' => ['type' => 'boolean', 'field' => 'is_sensitive'],
                    'start_date' => ['type' => 'date_range', 'field' => 'created_at', 'operator' => '>='],
                    'end_date' => ['type' => 'date_range', 'field' => 'created_at', 'operator' => '<='],
                ],
                'transformer' => function ($activity) {
                    return [
                        'id' => $activity->id,
                        'activity_type' => $activity->activity_type,
                        'activity_category' => $activity->activity_category,
                        'description' => $activity->description,
                        'severity' => $activity->severity,
                        'is_sensitive' => $activity->is_sensitive,
                        'user' => $activity->user ? [
                            'id' => $activity->user->id,
                            'name' => $activity->user->first_name.' '.$activity->user->last_name,
                            'email' => $activity->user->email,
                        ] : null,
                        'business' => $activity->business ? [
                            'id' => $activity->business->id,
                            'name' => $activity->business->business_name,
                        ] : null,
                        'delivery_provider' => $activity->deliveryProvider ? [
                            'id' => $activity->deliveryProvider->id,
                            'name' => $activity->deliveryProvider->business_name,
                        ] : null,
                        'subject_type' => $activity->subject_type,
                        'subject_id' => $activity->subject_id,
                        'changes' => $activity->getChanges(),
                        'ip_address' => $activity->ip_address,
                        'user_agent' => $activity->user_agent,
                        'created_at' => $activity->created_at,
                    ];
                },
                'message' => 'Staff activities retrieved successfully',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve staff activities', $e, [
                'user_id' => auth()->id(),
                'filters' => $request->all(),
            ]);

            return $this->serverErrorResponse('Failed to retrieve staff activities');
        }
    }

    /**
     * Get activity details.
     *
     * @group Admin Staff Activities
     *
     * @authenticated
     *
     * @urlParam activity string required Activity ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Activity details retrieved successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "activity_type": "order_update",
     *     "activity_category": "order_management",
     *     "description": "Order updated: ORD-2024-ABC123",
     *     "severity": "low",
     *     "is_sensitive": false,
     *     "user": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *       "name": "John Doe",
     *       "email": "<EMAIL>"
     *     },
     *     "old_values": {
     *       "status": "pending",
     *       "updated_at": "2024-01-22T15:25:00Z"
     *     },
     *     "new_values": {
     *       "status": "confirmed",
     *       "updated_at": "2024-01-22T15:30:00Z"
     *     },
     *     "metadata": {
     *       "ip_address": "***********",
     *       "user_agent": "Mozilla/5.0...",
     *       "session_id": "abc123def456"
     *     },
     *     "created_at": "2024-01-22T15:30:00Z"
     *   }
     * }
     */
    public function show(string $activity, Request $request): JsonResponse
    {
        try {
            $activityModel = StaffActivity::with([
                'user:id,first_name,last_name,email',
                'business:id,business_name',
                'deliveryProvider:id,business_name',
            ])->findOrFail($activity);

            $data = [
                'id' => $activityModel->id,
                'activity_type' => $activityModel->activity_type,
                'activity_category' => $activityModel->activity_category,
                'description' => $activityModel->description,
                'severity' => $activityModel->severity,
                'is_sensitive' => $activityModel->is_sensitive,
                'user' => $activityModel->user ? [
                    'id' => $activityModel->user->id,
                    'name' => $activityModel->user->first_name.' '.$activityModel->user->last_name,
                    'email' => $activityModel->user->email,
                ] : null,
                'business' => $activityModel->business ? [
                    'id' => $activityModel->business->id,
                    'name' => $activityModel->business->business_name,
                ] : null,
                'delivery_provider' => $activityModel->deliveryProvider ? [
                    'id' => $activityModel->deliveryProvider->id,
                    'name' => $activityModel->deliveryProvider->business_name,
                ] : null,
                'subject_type' => $activityModel->subject_type,
                'subject_id' => $activityModel->subject_id,
                'old_values' => $activityModel->old_values,
                'new_values' => $activityModel->new_values,
                'metadata' => $activityModel->metadata,
                'ip_address' => $activityModel->ip_address,
                'user_agent' => $activityModel->user_agent,
                'session_id' => $activityModel->session_id,
                'created_at' => $activityModel->created_at,
            ];

            return $this->successResponse($data, 'Activity details retrieved successfully');

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Activity not found', 404);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve activity details', $e, [
                'user_id' => auth()->id(),
                'activity_id' => $activity,
            ]);

            return $this->serverErrorResponse('Failed to retrieve activity details');
        }
    }

    /**
     * Get activity statistics.
     *
     * @group Admin Staff Activities
     *
     * @authenticated
     *
     * @queryParam start_date string Start date for statistics (Y-m-d). Example: 2024-01-01
     * @queryParam end_date string End date for statistics (Y-m-d). Example: 2024-01-31
     * @queryParam business_id string Filter by specific business ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Activity statistics retrieved successfully",
     *   "data": {
     *     "total_activities": 1250,
     *     "sensitive_activities": 45,
     *     "activities_by_category": {
     *       "order_management": 650,
     *       "inventory": 300,
     *       "authentication": 200,
     *       "user_management": 50,
     *       "payment": 30,
     *       "settings": 20
     *     },
     *     "activities_by_severity": {
     *       "low": 1000,
     *       "medium": 200,
     *       "high": 40,
     *       "critical": 10
     *     },
     *     "top_users": [
     *       {
     *         "user_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *         "user_name": "John Doe",
     *         "activity_count": 150
     *       }
     *     ]
     *   }
     * }
     */
    public function statistics(Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'sometimes|date',
            'end_date' => 'sometimes|date|after_or_equal:start_date',
            'business_id' => 'sometimes|uuid|exists:businesses,id',
        ]);

        try {
            $filters = [
                'start_date' => $request->input('start_date'),
                'end_date' => $request->input('end_date'),
                'business_id' => $request->input('business_id'),
            ];

            $statistics = $this->staffActivityService->getActivityStatistics($filters);

            return $this->successResponse($statistics, 'Activity statistics retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve activity statistics', $e, [
                'user_id' => auth()->id(),
                'filters' => $request->all(),
            ]);

            return $this->serverErrorResponse('Failed to retrieve activity statistics');
        }
    }

    /**
     * Get recent critical activities.
     *
     * @group Admin Staff Activities
     *
     * @authenticated
     *
     * @queryParam limit integer Number of activities to retrieve (1-100). Example: 50
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Critical activities retrieved successfully",
     *   "data": [
     *     {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "activity_type": "user_suspended",
     *       "description": "User suspended: John Doe (<EMAIL>)",
     *       "severity": "critical",
     *       "user": {
     *         "name": "Admin User",
     *         "email": "<EMAIL>"
     *       },
     *       "created_at": "2024-01-22T15:30:00Z"
     *     }
     *   ]
     * }
     */
    public function critical(Request $request): JsonResponse
    {
        $request->validate([
            'limit' => 'sometimes|integer|min:1|max:100',
        ]);

        try {
            $limit = $request->integer('limit', 50);
            $activities = $this->staffActivityService->getRecentCriticalActivities($limit);

            $data = $activities->map(function ($activity) {
                return [
                    'id' => $activity->id,
                    'activity_type' => $activity->activity_type,
                    'activity_category' => $activity->activity_category,
                    'description' => $activity->description,
                    'severity' => $activity->severity,
                    'is_sensitive' => $activity->is_sensitive,
                    'user' => $activity->user ? [
                        'name' => $activity->user->first_name.' '.$activity->user->last_name,
                        'email' => $activity->user->email,
                    ] : null,
                    'business' => $activity->business ? [
                        'name' => $activity->business->business_name,
                    ] : null,
                    'created_at' => $activity->created_at,
                ];
            });

            return $this->successResponse($data, 'Critical activities retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve critical activities', $e, [
                'user_id' => auth()->id(),
            ]);

            return $this->serverErrorResponse('Failed to retrieve critical activities');
        }
    }
}
