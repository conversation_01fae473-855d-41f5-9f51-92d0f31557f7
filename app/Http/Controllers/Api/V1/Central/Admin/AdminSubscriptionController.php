<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Central\Admin;

use App\Enums\Financial\UserSubscriptionStatus;
use App\Http\Controllers\Controller;
use App\Models\Financial\SubscriptionPlan;
use App\Models\Financial\UserSubscription;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

/**
 * Admin Subscription Controller
 *
 * Handles subscription management and billing oversight for administrators.
 * Provides comprehensive subscription analytics and management capabilities.
 */
class AdminSubscriptionController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get subscription overview.
     *
     * @group Admin Subscriptions
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Subscription overview retrieved successfully",
     *   "data": {
     *     "overview": {
     *       "total_subscriptions": 450,
     *       "active_subscriptions": 380,
     *       "expired_subscriptions": 45,
     *       "cancelled_subscriptions": 25,
     *       "monthly_recurring_revenue": 8500000,
     *       "annual_recurring_revenue": 102000000
     *     },
     *     "plan_distribution": {
     *       "business_free": 150,
     *       "business_starter": 120,
     *       "business_business": 80,
     *       "business_enterprise": 30,
     *       "provider_free": 100,
     *       "provider_starter": 70,
     *       "provider_business": 50,
     *       "provider_enterprise": 20
     *     }
     *   }
     * }
     */
    public function overview(Request $request): JsonResponse
    {
        try {
            $overview = [
                'overview' => $this->getSubscriptionOverviewStats(),
                'plan_distribution' => $this->getPlanDistribution(),
                'revenue_metrics' => $this->getRevenueMetrics(),
                'churn_metrics' => $this->getChurnMetrics(),
            ];

            return $this->successResponse(
                $overview,
                'Subscription overview retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve subscription overview',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve subscription overview');
        }
    }

    /**
     * Get all subscriptions with filtering and pagination.
     *
     * @group Admin Subscriptions
     *
     * @authenticated
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = UserSubscription::query()
                ->with([
                    'plan:id,name,slug,target_type',
                    'planPrice:id,plan_id,billing_interval,price,currency',
                    'tenant:id,name,tenant_type',
                ]);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'plan.name',
                    'tenant.name',
                ],
                'sortFields' => [
                    'created_at',
                    'expires_at',
                    'status',
                    'started_at',
                ],
                'filters' => [
                    'status' => ['type' => 'exact'],
                    'target_type' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->whereHas('plan', function ($q) use ($value) {
                                $q->where('target_type', $value);
                            });
                        },
                    ],
                    'plan_id' => ['type' => 'exact'],
                    'billing_interval' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->whereHas('planPrice', function ($q) use ($value) {
                                $q->where('billing_interval', $value);
                            });
                        },
                    ],
                ],
                'message' => 'Subscriptions retrieved successfully',
                'entityName' => 'subscriptions',
                'transformer' => [$this, 'transformSubscription'],
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve subscriptions',
                $e,
                ['request_params' => $request->only(['search', 'status', 'target_type'])]
            );

            return $this->serverErrorResponse('Failed to retrieve subscriptions');
        }
    }

    /**
     * Get subscription details.
     *
     * @group Admin Subscriptions
     *
     * @authenticated
     */
    public function show(Request $request, string $subscriptionId): JsonResponse
    {
        try {
            $subscription = UserSubscription::with([
                'plan:id,name,slug,description,target_type',
                'planPrice:id,plan_id,billing_interval,price,currency',
                'tenant:id,name,tenant_type,status',
                'payments' => function ($query) {
                    $query->orderBy('created_at', 'desc')->limit(10);
                },
            ])->find($subscriptionId);

            if (! $subscription) {
                return $this->notFoundResponse('Subscription not found');
            }

            return $this->successResponse([
                'subscription' => $this->transformSubscription($subscription),
                'usage_stats' => $this->getSubscriptionUsageStats($subscription),
                'billing_history' => $subscription->payments,
                'feature_usage' => $this->getFeatureUsageStats($subscription),
            ], 'Subscription details retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve subscription details',
                $e,
                ['subscription_id' => $subscriptionId]
            );

            return $this->serverErrorResponse('Failed to retrieve subscription details');
        }
    }

    /**
     * Update subscription status.
     *
     * @group Admin Subscriptions
     *
     * @authenticated
     */
    public function updateStatus(Request $request, string $subscriptionId): JsonResponse
    {
        $request->validate([
            'status' => 'required|string|in:active,cancelled,suspended,expired',
            'reason' => 'sometimes|string|max:500',
            'notes' => 'sometimes|string|max:1000',
        ]);

        try {
            $subscription = UserSubscription::find($subscriptionId);
            if (! $subscription) {
                return $this->notFoundResponse('Subscription not found');
            }

            $oldStatus = $subscription->status;
            $newStatus = UserSubscriptionStatus::from($request->input('status'));

            if ($oldStatus === $newStatus) {
                return $this->errorResponse(
                    'Subscription is already in the requested status',
                    422,
                    null,
                    'SAME_STATUS'
                );
            }

            DB::transaction(function () use ($subscription, $newStatus, $request) {
                $subscription->update([
                    'status' => $newStatus,
                    'admin_notes' => $request->input('notes'),
                ]);

                // Log the status change
                $this->loggingService->logActivity(
                    'subscription_status_updated',
                    'Subscription status updated by admin',
                    [
                        'subscription_id' => $subscription->id,
                        'old_status' => $subscription->getOriginal('status'),
                        'new_status' => $newStatus->value,
                        'reason' => $request->input('reason'),
                        'notes' => $request->input('notes'),
                        'updated_by' => auth()->id(),
                    ]
                );
            });

            return $this->successResponse(
                $this->transformSubscription($subscription->fresh()),
                'Subscription status updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update subscription status',
                $e,
                ['subscription_id' => $subscriptionId]
            );

            return $this->serverErrorResponse('Failed to update subscription status');
        }
    }

    /**
     * Get subscription analytics.
     *
     * @group Admin Subscriptions
     *
     * @authenticated
     */
    public function analytics(Request $request): JsonResponse
    {
        $request->validate([
            'period' => 'sometimes|string|in:today,week,month,quarter,year',
            'target_type' => 'sometimes|string|in:business,provider',
        ]);

        try {
            $period = $request->input('period', 'month');
            $targetType = $request->input('target_type');
            $periodDates = $this->getPeriodDates($period);

            $analytics = [
                'subscription_trends' => $this->getSubscriptionTrends($periodDates, $targetType),
                'revenue_trends' => $this->getRevenueTrends($periodDates, $targetType),
                'churn_analysis' => $this->getChurnAnalysis($periodDates, $targetType),
                'plan_performance' => $this->getPlanPerformance($periodDates, $targetType),
                'upgrade_downgrade_trends' => $this->getUpgradeDowngradeTrends($periodDates, $targetType),
            ];

            return $this->successResponse(
                $analytics,
                'Subscription analytics retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve subscription analytics',
                $e,
                ['request_params' => $request->only(['period', 'target_type'])]
            );

            return $this->serverErrorResponse('Failed to retrieve subscription analytics');
        }
    }

    /**
     * Get subscription plans management.
     *
     * @group Admin Subscriptions
     *
     * @authenticated
     */
    public function plans(Request $request): JsonResponse
    {
        try {
            $query = SubscriptionPlan::query()
                ->with(['planPrices', 'planFeatures.feature']);

            return $this->handleQuery($query, $request, [
                'searchFields' => ['name', 'description'],
                'sortFields' => ['name', 'target_type', 'created_at'],
                'filters' => [
                    'target_type' => ['type' => 'exact'],
                    'is_active' => ['type' => 'boolean'],
                ],
                'message' => 'Subscription plans retrieved successfully',
                'entityName' => 'plans',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve subscription plans',
                $e
            );

            return $this->serverErrorResponse('Failed to retrieve subscription plans');
        }
    }

    /**
     * Transform subscription for API response.
     */
    public function transformSubscription(UserSubscription $subscription): array
    {
        return [
            'id' => $subscription->id,
            'status' => $subscription->status->value,
            'started_at' => $subscription->started_at->toISOString(),
            'expires_at' => $subscription->expires_at?->toISOString(),
            'auto_renew' => $subscription->auto_renew,
            'billing_cycle_anchor' => $subscription->billing_cycle_anchor?->toISOString(),
            'grace_period_ends_at' => $subscription->grace_period_ends_at?->toISOString(),
            'created_at' => $subscription->created_at->toISOString(),
            'plan' => $subscription->plan ? [
                'id' => $subscription->plan->id,
                'name' => $subscription->plan->name,
                'slug' => $subscription->plan->slug,
                'target_type' => $subscription->plan->target_type->value,
            ] : null,
            'plan_price' => $subscription->planPrice ? [
                'id' => $subscription->planPrice->id,
                'billing_interval' => $subscription->planPrice->billing_interval->value,
                'price' => $subscription->planPrice->price,
                'currency' => $subscription->planPrice->currency,
            ] : null,
            'tenant' => $subscription->tenant ? [
                'id' => $subscription->tenant->id,
                'name' => $subscription->tenant->name,
                'tenant_type' => $subscription->tenant->tenant_type,
            ] : null,
        ];
    }

    /**
     * Get subscription overview statistics.
     */
    private function getSubscriptionOverviewStats(): array
    {
        return [
            'total_subscriptions' => UserSubscription::count(),
            'active_subscriptions' => UserSubscription::where('status', UserSubscriptionStatus::ACTIVE)->count(),
            'expired_subscriptions' => UserSubscription::where('status', UserSubscriptionStatus::EXPIRED)->count(),
            'cancelled_subscriptions' => UserSubscription::where('status', UserSubscriptionStatus::CANCELLED)->count(),
            'past_due_subscriptions' => UserSubscription::where('status', UserSubscriptionStatus::PAST_DUE)->count(),
            'suspended_subscriptions' => UserSubscription::where('status', UserSubscriptionStatus::SUSPENDED)->count(),
        ];
    }

    /**
     * Get plan distribution.
     */
    private function getPlanDistribution(): array
    {
        return UserSubscription::join('subscription_plans', 'user_subscriptions.plan_id', '=', 'subscription_plans.id')
            ->where('user_subscriptions.status', UserSubscriptionStatus::ACTIVE)
            ->select('subscription_plans.slug', DB::raw('COUNT(*) as count'))
            ->groupBy('subscription_plans.slug')
            ->pluck('count', 'slug')
            ->toArray();
    }

    /**
     * Get revenue metrics.
     */
    private function getRevenueMetrics(): array
    {
        $monthlyRevenue = UserSubscription::join('plan_prices', 'user_subscriptions.plan_price_id', '=', 'plan_prices.id')
            ->where('user_subscriptions.status', UserSubscriptionStatus::ACTIVE)
            ->where('plan_prices.billing_interval', 'monthly')
            ->sum('plan_prices.price');

        $annualRevenue = UserSubscription::join('plan_prices', 'user_subscriptions.plan_price_id', '=', 'plan_prices.id')
            ->where('user_subscriptions.status', UserSubscriptionStatus::ACTIVE)
            ->where('plan_prices.billing_interval', 'annually')
            ->sum('plan_prices.price');

        return [
            'monthly_recurring_revenue' => $monthlyRevenue,
            'annual_recurring_revenue' => $annualRevenue,
            'total_arr' => ($monthlyRevenue * 12) + $annualRevenue,
        ];
    }

    /**
     * Get churn metrics.
     */
    private function getChurnMetrics(): array
    {
        $totalActive = UserSubscription::where('status', UserSubscriptionStatus::ACTIVE)->count();
        $recentlyCancelled = UserSubscription::where('status', UserSubscriptionStatus::CANCELLED)
            ->where('updated_at', '>=', now()->subMonth())
            ->count();

        $churnRate = $totalActive > 0 ? ($recentlyCancelled / $totalActive) * 100 : 0;

        return [
            'monthly_churn_rate' => round($churnRate, 2),
            'cancelled_this_month' => $recentlyCancelled,
            'total_active' => $totalActive,
        ];
    }

    /**
     * Get subscription usage statistics.
     */
    private function getSubscriptionUsageStats(UserSubscription $subscription): array
    {
        // This would integrate with actual usage tracking
        return [
            'api_requests_used' => 0,
            'api_requests_limit' => 10000,
            'orders_this_month' => 0,
            'orders_limit' => 1000,
            'storage_used_gb' => 0,
            'storage_limit_gb' => 25,
        ];
    }

    /**
     * Get feature usage statistics.
     */
    private function getFeatureUsageStats(UserSubscription $subscription): array
    {
        // This would integrate with actual feature usage tracking
        return [
            'features_used' => [],
            'features_available' => [],
            'usage_percentage' => 0,
        ];
    }

    /**
     * Get period dates for analytics.
     */
    private function getPeriodDates(string $period): array
    {
        $now = now();

        return match ($period) {
            'today' => [
                'start' => $now->copy()->startOfDay(),
                'end' => $now->copy()->endOfDay(),
            ],
            'week' => [
                'start' => $now->copy()->startOfWeek(),
                'end' => $now->copy()->endOfWeek(),
            ],
            'month' => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
            'quarter' => [
                'start' => $now->copy()->startOfQuarter(),
                'end' => $now->copy()->endOfQuarter(),
            ],
            'year' => [
                'start' => $now->copy()->startOfYear(),
                'end' => $now->copy()->endOfYear(),
            ],
            default => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
        };
    }

    /**
     * Placeholder methods for analytics.
     */
    private function getSubscriptionTrends(array $periodDates, ?string $targetType): array
    {
        return ['new_subscriptions' => 0, 'cancelled_subscriptions' => 0];
    }

    private function getRevenueTrends(array $periodDates, ?string $targetType): array
    {
        return ['revenue_growth' => 0, 'revenue_by_day' => []];
    }

    private function getChurnAnalysis(array $periodDates, ?string $targetType): array
    {
        return ['churn_rate' => 0, 'retention_rate' => 0];
    }

    private function getPlanPerformance(array $periodDates, ?string $targetType): array
    {
        return ['top_performing_plans' => [], 'plan_conversion_rates' => []];
    }

    private function getUpgradeDowngradeTrends(array $periodDates, ?string $targetType): array
    {
        return ['upgrades' => 0, 'downgrades' => 0];
    }
}
