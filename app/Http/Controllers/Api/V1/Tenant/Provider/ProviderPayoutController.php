<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Provider;

use App\Enums\Financial\PayoutMethodType;
use App\Http\Controllers\Controller;
use App\Models\Financial\Payout;
use App\Services\Financial\PayoutManagementService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rules\Enum;

/**
 * Provider Payout Controller
 *
 * Handles payout requests and management for delivery providers.
 */
class ProviderPayoutController extends Controller
{
    use \App\Traits\ApiResponseTrait;

    public function __construct(
        private readonly PayoutManagementService $payoutService
    ) {}

    /**
     * Request a payout.
     *
     * @group Provider Payouts
     *
     * @authenticated
     *
     * @bodyParam amount number required The payout amount.
     * @bodyParam payment_method string required The payment method (bank_transfer, wallet).
     * @bodyParam bank_details object required Bank account details for bank transfers.
     * @bodyParam bank_details.account_number string required Bank account number.
     * @bodyParam bank_details.bank_code string required Bank code.
     * @bodyParam bank_details.account_name string required Account holder name.
     * @bodyParam notes string optional Additional notes for the payout.
     *
     * @response 201 {
     *   "success": true,
     *   "data": {
     *     "id": "payout-uuid",
     *     "amount": 8000.00,
     *     "currency": "NGN",
     *     "transaction_fee": 160.00,
     *     "status": "pending",
     *     "payment_method": "bank_transfer",
     *     "reference": "PO_DEF456_20241201",
     *     "balance_before": 12000.00,
     *     "balance_after": 4000.00,
     *     "created_at": "2024-12-01T12:00:00.000000Z"
     *   },
     *   "message": "Payout requested successfully"
     * }
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'amount' => 'required|numeric|min:100',
            'payment_method' => ['required', new Enum(PayoutMethodType::class)],
            'bank_details' => 'required_if:payment_method,bank_transfer|array',
            'bank_details.account_number' => 'required_if:payment_method,bank_transfer|string',
            'bank_details.bank_code' => 'required_if:payment_method,bank_transfer|string',
            'bank_details.account_name' => 'required_if:payment_method,bank_transfer|string',
            'notes' => 'sometimes|string|max:500',
        ]);

        try {
            $provider = auth()->user()->deliveryProvider;

            if (! $provider) {
                return $this->errorResponse('Delivery provider not found', 404);
            }

            $payout = $this->payoutService->requestProviderPayout(
                $provider->id,
                $request->input('amount'),
                PayoutMethodType::from($request->input('payment_method')),
                $request->input('bank_details', []),
                $request->input('notes')
            );

            return $this->successResponse($payout, 'Payout requested successfully', 201);

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 400);
        }
    }

    /**
     * Get payout history.
     *
     * @group Provider Payouts
     *
     * @authenticated
     *
     * @queryParam limit integer The number of payouts to retrieve (default: 50).
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "payouts": [
     *       {
     *         "id": "payout-uuid",
     *         "amount": 8000.00,
     *         "currency": "NGN",
     *         "transaction_fee": 160.00,
     *         "status": "paid",
     *         "payment_method": "bank_transfer",
     *         "reference": "PO_DEF456_20241201",
     *         "processed_at": "2024-12-01T14:00:00.000000Z",
     *         "created_at": "2024-12-01T12:00:00.000000Z"
     *       }
     *     ],
     *     "summary": {
     *       "total_payouts": 3,
     *       "total_amount": 24000.00,
     *       "total_fees": 480.00,
     *       "successful_payouts": 2,
     *       "pending_payouts": 1,
     *       "failed_payouts": 0
     *     }
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'limit' => 'sometimes|integer|min:1|max:100',
        ]);

        try {
            $provider = auth()->user()->deliveryProvider;

            if (! $provider) {
                return $this->errorResponse('Delivery provider not found', 404);
            }

            $history = $this->payoutService->getPayoutHistory(
                $provider->id,
                'App\Models\DeliveryProvider',
                $request->input('limit', 50)
            );

            return $this->successResponse($history, 'Payout history retrieved successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve payout history', 500);
        }
    }

    /**
     * Get specific payout details.
     *
     * @group Provider Payouts
     *
     * @authenticated
     *
     * @urlParam payout string required The payout ID.
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "id": "payout-uuid",
     *     "amount": 8000.00,
     *     "currency": "NGN",
     *     "transaction_fee": 160.00,
     *     "status": "paid",
     *     "payment_method": "bank_transfer",
     *     "bank_details": {
     *       "account_number": "**********",
     *       "bank_code": "058",
     *       "account_name": "Provider Account"
     *     },
     *     "reference": "PO_DEF456_20241201",
     *     "balance_before": 12000.00,
     *     "balance_after": 4000.00,
     *     "notes": "Weekly payout",
     *     "processed_at": "2024-12-01T14:00:00.000000Z",
     *     "created_at": "2024-12-01T12:00:00.000000Z"
     *   }
     * }
     */
    public function show(string $payout): JsonResponse
    {
        try {
            $provider = auth()->user()->deliveryProvider;

            if (! $provider) {
                return $this->errorResponse('Delivery provider not found', 404);
            }

            $payoutModel = Payout::where('id', $payout)
                ->where('payable_id', $provider->id)
                ->where('payable_type', 'App\Models\DeliveryProvider')
                ->firstOrFail();

            return $this->successResponse($payoutModel, 'Payout details retrieved successfully');

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->errorResponse('Payout not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve payout details', 500);
        }
    }
}
