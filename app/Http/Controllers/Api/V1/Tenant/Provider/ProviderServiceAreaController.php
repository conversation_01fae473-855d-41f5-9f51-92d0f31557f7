<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Provider;

use App\Http\Controllers\Controller;
use App\Models\Delivery\DeliveryProvider;
use App\Models\Delivery\ProviderServiceArea;
use App\Services\Communication\NotificationService;
use App\Services\System\LoggingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Provider Service Area Management Controller
 *
 * Handles service area management for delivery providers including
 * coverage areas, zones, and delivery boundaries.
 */
class ProviderServiceAreaController extends Controller
{
    use \App\Traits\ApiResponseTrait;

    public function __construct(
        private readonly LoggingService $loggingService,
        private readonly NotificationService $notificationService
    ) {}

    /**
     * Get provider service areas.
     *
     * @group Provider Service Areas
     *
     * @authenticated
     *
     * @queryParam page integer Page number for pagination. Example: 1
     * @queryParam per_page integer Items per page (max 50). Example: 15
     * @queryParam search string Search service areas by name. Example: lagos
     * @queryParam is_active boolean Filter by active status. Example: true
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Service areas retrieved successfully",
     *   "data": {
     *     "data": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *         "name": "Lagos Island",
     *         "description": "Lagos Island and surrounding areas",
     *         "is_active": true,
     *         "delivery_fee": 1000,
     *         "minimum_order": 500,
     *         "estimated_time": "30-45 minutes",
     *         "coverage_radius": 15,
     *         "coordinates": {
     *           "center": {"lat": 6.4541, "lng": 3.3947},
     *           "bounds": [
     *             {"lat": 6.4400, "lng": 3.3800},
     *             {"lat": 6.4700, "lng": 3.4100}
     *           ]
     *         },
     *         "created_at": "2024-01-15T10:30:00Z"
     *       }
     *     ],
     *     "current_page": 1,
     *     "per_page": 15,
     *     "total": 5
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:50',
            'search' => 'sometimes|string|max:255',
            'is_active' => 'sometimes|boolean',
        ]);

        try {
            $provider = $this->getCurrentProvider();

            // Build query for service areas
            $query = ProviderServiceArea::where('tenant_id', tenant()?->id)
                ->where('provider_id', $provider->id);

            // Apply search filter
            if ($request->filled('search')) {
                $search = $request->input('search');
                $query->where('name', 'ILIKE', "%{$search}%");
            }

            // Apply active status filter
            if ($request->has('is_active')) {
                $query->where('is_active', $request->boolean('is_active'));
            }

            // Paginate results
            $perPage = min($request->input('per_page', 15), 50);
            $serviceAreas = $query->orderBy('created_at', 'desc')
                ->paginate($perPage);

            // Transform the data
            $transformedData = $serviceAreas->getCollection()->map(function ($area) {
                return $this->transformServiceArea($area);
            });

            return $this->successResponse(
                [
                    'data' => $transformedData,
                    'current_page' => $serviceAreas->currentPage(),
                    'per_page' => $serviceAreas->perPage(),
                    'total' => $serviceAreas->total(),
                    'last_page' => $serviceAreas->lastPage(),
                ],
                'Service areas retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve service areas',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve service areas');
        }
    }

    /**
     * Get specific service area details.
     *
     * @group Provider Service Areas
     *
     * @authenticated
     *
     * @urlParam area string required Service area ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Service area retrieved successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "Lagos Island",
     *     "description": "Lagos Island and surrounding areas",
     *     "is_active": true,
     *     "delivery_fee": 1000,
     *     "minimum_order": 500,
     *     "estimated_time": "30-45 minutes",
     *     "coverage_radius": 15,
     *     "coordinates": {
     *       "center": {"lat": 6.4541, "lng": 3.3947},
     *       "bounds": [
     *         {"lat": 6.4400, "lng": 3.3800},
     *         {"lat": 6.4700, "lng": 3.4100}
     *       ]
     *     },
     *     "statistics": {
     *       "total_deliveries": 1250,
     *       "completed_deliveries": 1200,
     *       "average_delivery_time": "35 minutes",
     *       "success_rate": 96.0
     *     },
     *     "created_at": "2024-01-15T10:30:00Z",
     *     "updated_at": "2024-01-20T14:30:00Z"
     *   }
     * }
     */
    public function show(string $area): JsonResponse
    {
        try {
            $provider = $this->getCurrentProvider();

            // Find service area
            $serviceArea = ProviderServiceArea::where('id', $area)
                ->where('tenant_id', tenant()?->id)
                ->where('provider_id', $provider->id)
                ->first();

            if (! $serviceArea) {
                return $this->notFoundResponse('Service area not found');
            }

            // Get delivery statistics for this area (placeholder for now)
            $statistics = [
                'total_deliveries' => 0, // TODO: Implement when delivery system is ready
                'completed_deliveries' => 0,
                'average_delivery_time' => 'N/A',
                'success_rate' => 0.0,
            ];

            $transformedData = $this->transformServiceArea($serviceArea, true);
            $transformedData['statistics'] = $statistics;

            return $this->successResponse(
                $transformedData,
                'Service area retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve service area details',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'area_id' => $area,
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve service area details');
        }
    }

    /**
     * Add service area.
     *
     * @group Provider Service Areas
     *
     * @authenticated
     *
     * @bodyParam name string required Service area name. Example: Lagos Island
     * @bodyParam polygon_coordinates string required Polygon coordinates as GeoJSON or simple coordinates. Example: [[6.4400,3.3800],[6.4700,3.4100]]
     * @bodyParam price_multiplier number Price multiplier for this area. Example: 1.2
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Service area created successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "Lagos Island",
     *     "polygon_coordinates": "[[6.4400,3.3800],[6.4700,3.4100]]",
     *     "price_multiplier": 1.2,
     *     "is_active": true,
     *     "created_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'polygon_coordinates' => 'required|string',
            'price_multiplier' => 'sometimes|numeric|min:0.1|max:10',
        ]);

        try {
            $provider = $this->getCurrentProvider();

            // Check for duplicate name within provider
            $existingArea = ProviderServiceArea::where('tenant_id', tenant()?->id)
                ->where('provider_id', $provider->id)
                ->where('name', $request->name)
                ->first();

            if ($existingArea) {
                return $this->errorResponse('Service area with this name already exists', 422);
            }

            // Create service area
            $serviceArea = ProviderServiceArea::create([
                'tenant_id' => tenant()?->id,
                'provider_id' => $provider->id,
                'name' => $request->name,
                'polygon_coordinates' => $request->polygon_coordinates,
                'price_multiplier' => $request->input('price_multiplier', 1.0),
                'is_active' => true,
            ]);

            $this->loggingService->logInfo(
                'Service area created successfully',
                [
                    'tenant_id' => tenant()?->id,
                    'provider_id' => $provider->id,
                    'service_area_id' => $serviceArea->id,
                    'created_by' => auth()->id(),
                ]
            );

            return $this->successResponse(
                $this->transformServiceArea($serviceArea),
                'Service area created successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create service area',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'request_data' => $request->all(),
                ]
            );

            return $this->serverErrorResponse('Failed to create service area');
        }
    }

    /**
     * Update service area.
     *
     * @group Provider Service Areas
     *
     * @authenticated
     *
     * @urlParam area string required Service area ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam name string Service area name. Example: Lagos Island Updated
     * @bodyParam polygon_coordinates string Polygon coordinates. Example: [[6.4400,3.3800],[6.4700,3.4100]]
     * @bodyParam price_multiplier number Price multiplier for this area. Example: 1.5
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Service area updated successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "Lagos Island Updated",
     *     "polygon_coordinates": "[[6.4400,3.3800],[6.4700,3.4100]]",
     *     "price_multiplier": 1.5,
     *     "is_active": true,
     *     "updated_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function update(Request $request, string $area): JsonResponse
    {
        $request->validate([
            'name' => 'sometimes|string|max:255',
            'polygon_coordinates' => 'sometimes|string',
            'price_multiplier' => 'sometimes|numeric|min:0.1|max:10',
        ]);

        try {
            $provider = $this->getCurrentProvider();

            // Find service area
            $serviceArea = ProviderServiceArea::where('id', $area)
                ->where('tenant_id', tenant()?->id)
                ->where('provider_id', $provider->id)
                ->first();

            if (! $serviceArea) {
                return $this->notFoundResponse('Service area not found');
            }

            // Check for duplicate name if name is being updated
            if ($request->filled('name') && $request->name !== $serviceArea->name) {
                $existingArea = ProviderServiceArea::where('tenant_id', tenant()?->id)
                    ->where('provider_id', $provider->id)
                    ->where('name', $request->name)
                    ->where('id', '!=', $area)
                    ->first();

                if ($existingArea) {
                    return $this->errorResponse('Service area with this name already exists', 422);
                }
            }

            // Update service area
            $updateData = [];
            if ($request->filled('name')) {
                $updateData['name'] = $request->name;
            }
            if ($request->filled('polygon_coordinates')) {
                $updateData['polygon_coordinates'] = $request->polygon_coordinates;
            }
            if ($request->filled('price_multiplier')) {
                $updateData['price_multiplier'] = $request->price_multiplier;
            }

            if (! empty($updateData)) {
                $serviceArea->update($updateData);
            }

            $this->loggingService->logInfo(
                'Service area updated successfully',
                [
                    'tenant_id' => tenant()?->id,
                    'provider_id' => $provider->id,
                    'service_area_id' => $serviceArea->id,
                    'updated_by' => auth()->id(),
                    'updates' => $updateData,
                ]
            );

            return $this->successResponse(
                $this->transformServiceArea($serviceArea->fresh()),
                'Service area updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update service area',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'area_id' => $area,
                ]
            );

            return $this->serverErrorResponse('Failed to update service area');
        }
    }

    /**
     * Remove service area.
     *
     * @group Provider Service Areas
     *
     * @authenticated
     *
     * @urlParam area string required Service area ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Service area removed successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "Lagos Island",
     *     "removed_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function destroy(string $area): JsonResponse
    {
        try {
            $provider = $this->getCurrentProvider();

            // Find service area
            $serviceArea = ProviderServiceArea::where('id', $area)
                ->where('tenant_id', tenant()?->id)
                ->where('provider_id', $provider->id)
                ->first();

            if (! $serviceArea) {
                return $this->notFoundResponse('Service area not found');
            }

            // Check if service area has active deliveries (placeholder for now)
            $hasActiveDeliveries = false; // TODO: Check when delivery system is implemented

            if ($hasActiveDeliveries) {
                return $this->errorResponse('Cannot remove service area with active deliveries', 422);
            }

            $areaName = $serviceArea->name;

            // Delete the service area
            $serviceArea->delete();

            $this->loggingService->logInfo(
                'Service area removed successfully',
                [
                    'tenant_id' => tenant()?->id,
                    'provider_id' => $provider->id,
                    'service_area_id' => $area,
                    'area_name' => $areaName,
                    'removed_by' => auth()->id(),
                ]
            );

            return $this->successResponse(
                [
                    'id' => $area,
                    'name' => $areaName,
                    'removed_at' => now()->toISOString(),
                ],
                'Service area removed successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to remove service area',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'area_id' => $area,
                ]
            );

            return $this->serverErrorResponse('Failed to remove service area');
        }
    }

    /**
     * Activate service area.
     */
    public function activate(string $area): JsonResponse
    {
        return $this->toggleServiceAreaStatus($area, true);
    }

    /**
     * Deactivate service area.
     */
    public function deactivate(string $area): JsonResponse
    {
        return $this->toggleServiceAreaStatus($area, false);
    }

    // Helper Methods

    /**
     * Get current provider.
     */
    private function getCurrentProvider(): DeliveryProvider
    {
        $user = auth()->user();

        // Find the provider associated with the current user
        $provider = DeliveryProvider::where('tenant_id', tenant()?->id)
            ->whereHas('teamMembers', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->first();

        if (! $provider) {
            throw new \Exception('Provider not found for current user');
        }

        return $provider;
    }

    /**
     * Transform service area for API response.
     */
    private function transformServiceArea(ProviderServiceArea $serviceArea, bool $includeDetails = false): array
    {
        $data = [
            'id' => $serviceArea->id,
            'name' => $serviceArea->name,
            'polygon_coordinates' => $serviceArea->polygon_coordinates,
            'price_multiplier' => (float) $serviceArea->price_multiplier,
            'is_active' => $serviceArea->is_active,
            'created_at' => $serviceArea->created_at->toISOString(),
            'updated_at' => $serviceArea->updated_at->toISOString(),
        ];

        if ($includeDetails) {
            // Add additional details for show method
            $data['provider_id'] = $serviceArea->provider_id;
            $data['tenant_id'] = $serviceArea->tenant_id;
        }

        return $data;
    }

    /**
     * Toggle service area active status.
     */
    private function toggleServiceAreaStatus(string $area, bool $isActive): JsonResponse
    {
        try {
            $provider = $this->getCurrentProvider();

            // Find service area
            $serviceArea = ProviderServiceArea::where('id', $area)
                ->where('tenant_id', tenant()?->id)
                ->where('provider_id', $provider->id)
                ->first();

            if (! $serviceArea) {
                return $this->notFoundResponse('Service area not found');
            }

            if ($serviceArea->is_active === $isActive) {
                $status = $isActive ? 'active' : 'inactive';

                return $this->errorResponse("Service area is already {$status}", 422);
            }

            $serviceArea->update(['is_active' => $isActive]);

            $action = $isActive ? 'activated' : 'deactivated';
            $this->loggingService->logInfo(
                "Service area {$action} successfully",
                [
                    'tenant_id' => tenant()?->id,
                    'provider_id' => $provider->id,
                    'service_area_id' => $serviceArea->id,
                    'action' => $action,
                    'updated_by' => auth()->id(),
                ]
            );

            return $this->successResponse(
                $this->transformServiceArea($serviceArea->fresh()),
                "Service area {$action} successfully"
            );

        } catch (\Exception $e) {
            $action = $isActive ? 'activate' : 'deactivate';
            $this->loggingService->logError(
                "Failed to {$action} service area",
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'area_id' => $area,
                ]
            );

            return $this->serverErrorResponse("Failed to {$action} service area");
        }
    }
}
