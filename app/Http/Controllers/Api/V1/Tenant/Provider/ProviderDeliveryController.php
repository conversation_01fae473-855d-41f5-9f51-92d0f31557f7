<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Provider;

use App\Enums\Delivery\DeliveryStatus;
use App\Exceptions\BusinessLogicException;
use App\Http\Controllers\Controller;
use App\Models\Delivery\Delivery;
use App\Models\Delivery\DeliveryProvider;
use App\Services\System\LoggingService;
use App\Traits\QueryHandlerTrait;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Provider Delivery Management Controller
 *
 * Handles delivery operations for providers including listing, accepting,
 * updating status, and managing delivery lifecycle.
 */
class ProviderDeliveryController extends Controller
{
    use \App\Traits\ApiResponseTrait, \App\Traits\QueryHandlerTrait;

    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get provider's deliveries.
     *
     * @group Provider Deliveries
     *
     * @authenticated
     *
     * @queryParam page integer Page number for pagination. Example: 1
     * @queryParam per_page integer Items per page (max 100). Example: 15
     * @queryParam status string Filter by delivery status. Example: pending
     * @queryParam date_from string Filter deliveries from date (Y-m-d). Example: 2024-01-01
     * @queryParam date_to string Filter deliveries to date (Y-m-d). Example: 2024-01-31
     * @queryParam sort_by string Sort by field (created_at, delivery_fee, estimated_delivery_time). Example: created_at
     * @queryParam sort_direction string Sort direction (asc, desc). Example: desc
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Deliveries retrieved successfully",
     *   "data": {
     *     "data": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *         "order_reference": "ORD-2024-001",
     *         "business_name": "Tasty Kitchen",
     *         "customer_name": "John Doe",
     *         "pickup_address": "123 Business St, Lagos",
     *         "delivery_address": "456 Customer Ave, Lagos",
     *         "delivery_fee": 500,
     *         "status": "pending",
     *         "estimated_delivery_time": "2024-01-15T15:30:00Z",
     *         "created_at": "2024-01-15T14:00:00Z",
     *         "distance_km": 5.2
     *       }
     *     ],
     *     "current_page": 1,
     *     "per_page": 15,
     *     "total": 25
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100',
            'status' => 'sometimes|string|in:pending,accepted,picked_up,in_transit,delivered,cancelled',
            'date_from' => 'sometimes|date_format:Y-m-d',
            'date_to' => 'sometimes|date_format:Y-m-d',
            'sort_by' => 'sometimes|string|in:created_at,delivery_fee,estimated_delivery_time',
            'sort_direction' => 'sometimes|string|in:asc,desc',
        ]);

        try {
            $provider = $this->getCurrentProvider();

            // Build query
            $query = Delivery::where('delivery_provider_id', $provider->id)
                ->with(['deliverable']);

            // Use QueryHandlerTrait for consistent pagination and response
            return $this->handleQuery($query, $request, [
                'sortFields' => ['created_at', 'delivery_fee', 'estimated_delivery_time'],
                'filters' => [
                    'status' => ['type' => 'exact'],
                    'date_from' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->whereDate('created_at', '>=', $value);
                        },
                    ],
                    'date_to' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->whereDate('created_at', '<=', $value);
                        },
                    ],
                ],
                'transformer' => function ($delivery) {
                    return $this->transformDelivery($delivery);
                },
                'message' => 'Deliveries retrieved successfully',
            ]);

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve provider deliveries',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'request_params' => $request->only(['status', 'date_from', 'date_to', 'sort_by', 'sort_direction']),
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve deliveries');
        }
    }

    /**
     * Get specific delivery details.
     *
     * @group Provider Deliveries
     *
     * @authenticated
     *
     * @urlParam delivery string required Delivery ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Delivery retrieved successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "order_reference": "ORD-2024-001",
     *     "business": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "business_name": "Tasty Kitchen",
     *       "contact_phone": "***********"
     *     },
     *     "customer": {
     *       "name": "John Doe",
     *       "phone": "***********"
     *     },
     *     "pickup_address": "123 Business St, Lagos",
     *     "delivery_address": "456 Customer Ave, Lagos",
     *     "delivery_fee": 500,
     *     "status": "pending",
     *     "estimated_delivery_time": "2024-01-15T15:30:00Z",
     *     "special_instructions": "Handle with care",
     *     "distance_km": 5.2,
     *     "created_at": "2024-01-15T14:00:00Z"
     *   }
     * }
     */
    public function show(string $delivery): JsonResponse
    {
        try {
            $provider = $this->getCurrentProvider();

            $deliveryModel = Delivery::where('delivery_provider_id', $provider->id)
                ->where('id', $delivery)
                ->with(['deliverable'])
                ->firstOrFail();

            return $this->successResponse(
                $this->transformDelivery($deliveryModel, true),
                'Delivery retrieved successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'Delivery not found',
                404
            );

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve delivery details',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'delivery_id' => $delivery,
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve delivery details');
        }
    }

    /**
     * Accept a delivery request.
     *
     * @group Provider Deliveries
     *
     * @authenticated
     *
     * @urlParam delivery string required Delivery ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Delivery accepted successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "status": "accepted",
     *     "accepted_at": "2024-01-15T14:05:00Z"
     *   }
     * }
     */
    public function accept(string $delivery): JsonResponse
    {
        try {
            $provider = $this->getCurrentProvider();

            $deliveryModel = Delivery::where('delivery_provider_id', $provider->id)
                ->where('id', $delivery)
                ->where('status', DeliveryStatus::PENDING_ACCEPTANCE)
                ->firstOrFail();

            $deliveryModel->update([
                'status' => DeliveryStatus::ACCEPTED,
            ]);

            return $this->successResponse(
                [
                    'id' => $deliveryModel->id,
                    'status' => $deliveryModel->status->value,
                    'accepted_at' => now()->toISOString(),
                ],
                'Delivery accepted successfully'
            );

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'Delivery not found or cannot be accepted',
                404
            );

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to accept delivery',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'delivery_id' => $delivery,
                ]
            );

            return $this->serverErrorResponse('Failed to accept delivery');
        }
    }

    // Helper Methods

    /**
     * Get current provider.
     */
    private function getCurrentProvider(): DeliveryProvider
    {
        $provider = DeliveryProvider::where('user_id', auth()->id())
            ->where('tenant_id', tenant()?->id ?? auth()->user()->tenant_id)
            ->first();

        if (! $provider) {
            throw new BusinessLogicException('Provider profile not found');
        }

        return $provider;
    }

    /**
     * Transform delivery for API response.
     */
    private function transformDelivery(Delivery $delivery, bool $detailed = false): array
    {
        $data = [
            'id' => $delivery->id,
            'order_reference' => $delivery->deliverable->order_reference ?? 'N/A',
            'business_name' => 'N/A', // Would need to get from deliverable relationship
            'customer_name' => 'N/A', // Would need to get from deliverable relationship
            'pickup_address' => 'N/A', // Would need to get from delivery locations
            'delivery_address' => 'N/A', // Would need to get from delivery locations
            'delivery_fee' => (float) ($delivery->platform_commission_amount ?? 0),
            'status' => $delivery->status->value,
            'estimated_delivery_time' => $delivery->estimated_delivery_time?->toISOString(),
            'created_at' => $delivery->created_at->toISOString(),
            'distance_km' => $delivery->distance ? round($delivery->distance, 1) : null,
        ];

        if ($detailed) {
            $data = array_merge($data, [
                'business' => [
                    'id' => null,
                    'business_name' => 'N/A',
                    'contact_phone' => null,
                ],
                'customer' => [
                    'name' => 'N/A',
                    'phone' => null,
                ],
                'special_instructions' => $delivery->delivery_notes,
                'accepted_at' => null, // Would need to track acceptance time
                'picked_up_at' => $delivery->actual_pickup_time?->toISOString(),
                'delivered_at' => $delivery->actual_delivery_time?->toISOString(),
            ]);
        }

        return $data;
    }
}
