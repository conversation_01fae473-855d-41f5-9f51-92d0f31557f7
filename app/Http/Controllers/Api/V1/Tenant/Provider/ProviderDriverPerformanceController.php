<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Provider;

use App\Enums\User\UserType;
use App\Exceptions\BusinessLogicException;
use App\Http\Controllers\Controller;
use App\Models\Delivery\DeliveryProvider;
use App\Models\User\User;
use App\Services\Delivery\DriverPerformanceService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Driver Performance Controller
 *
 * Handles driver performance tracking, ratings, and analytics for providers.
 */
class ProviderDriverPerformanceController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly DriverPerformanceService $performanceService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get driver performance metrics.
     *
     * @group Provider Driver Performance
     *
     * @authenticated
     *
     * @urlParam driver string required Driver ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @queryParam start_date string Start date for metrics (Y-m-d). Example: 2024-01-01
     * @queryParam end_date string End date for metrics (Y-m-d). Example: 2024-01-31
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Driver performance retrieved successfully",
     *   "data": {
     *     "driver_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "driver_name": "John Doe",
     *     "period": {
     *       "start_date": "2024-01-01",
     *       "end_date": "2024-01-31"
     *     },
     *     "delivery_stats": {
     *       "total_deliveries": 45,
     *       "completed_deliveries": 42,
     *       "cancelled_deliveries": 3,
     *       "completion_rate": 93.33,
     *       "cancellation_rate": 6.67
     *     },
     *     "performance_metrics": {
     *       "average_delivery_time_minutes": 28.5,
     *       "on_time_delivery_rate": 89.5,
     *       "total_distance_km": 245.8
     *     },
     *     "ratings": {
     *       "average_rating": 4.7,
     *       "total_ratings": 42
     *     },
     *     "earnings": {
     *       "total_earnings": 15750.00,
     *       "average_per_delivery": 375.00
     *     },
     *     "performance_trend": {
     *       "completion_rate_change": 2.5,
     *       "on_time_rate_change": -1.2,
     *       "trend_direction": "improving"
     *     }
     *   }
     * }
     */
    public function show(string $driver, Request $request): JsonResponse
    {
        $request->validate([
            'start_date' => 'sometimes|date|date_format:Y-m-d',
            'end_date' => 'sometimes|date|date_format:Y-m-d|after_or_equal:start_date',
        ]);

        try {
            $provider = $this->getCurrentProvider();

            // Get driver and validate they belong to this provider
            $driverModel = User::where('id', $driver)
                ->where('tenant_id', $provider->tenant_id)
                ->whereIn('user_type', UserType::getDeliveryTypes())
                ->firstOrFail();

            $startDate = $request->filled('start_date') ? Carbon::parse($request->input('start_date')) : null;
            $endDate = $request->filled('end_date') ? Carbon::parse($request->input('end_date')) : null;

            $performance = $this->performanceService->getDriverPerformance($driverModel, $startDate, $endDate);

            return $this->successResponse($performance, 'Driver performance retrieved successfully');

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Driver not found', 404);

        } catch (BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), 422, null, $e->getErrorCode());

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve driver performance', $e, [
                'tenant_id' => tenant()?->id,
                'user_id' => auth()->id(),
                'driver_id' => $driver,
            ]);

            return $this->serverErrorResponse('Failed to retrieve driver performance');
        }
    }

    /**
     * Rate a driver.
     *
     * @group Provider Driver Performance
     *
     * @authenticated
     *
     * @urlParam driver string required Driver ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam rating integer required Rating from 1 to 5. Example: 5
     * @bodyParam comment string Optional comment about the driver. Example: Excellent service
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Driver rated successfully",
     *   "data": {
     *     "driver_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "rating": 5,
     *     "comment": "Excellent service",
     *     "rated_at": "2024-01-22T15:30:00Z"
     *   }
     * }
     */
    public function rate(string $driver, Request $request): JsonResponse
    {
        $request->validate([
            'rating' => 'required|integer|min:1|max:5',
            'comment' => 'sometimes|string|max:500',
        ]);

        try {
            $provider = $this->getCurrentProvider();

            // Get driver and validate they belong to this provider
            $driverModel = User::where('id', $driver)
                ->where('tenant_id', $provider->tenant_id)
                ->whereIn('user_type', UserType::getDeliveryTypes())
                ->firstOrFail();

            $rating = $request->integer('rating');
            $comment = $request->input('comment');

            $success = $this->performanceService->rateDriver(
                $driverModel,
                $rating,
                $comment,
                'provider:'.$provider->id
            );

            if (! $success) {
                return $this->errorResponse('Failed to rate driver', 500);
            }

            $data = [
                'driver_id' => $driverModel->id,
                'rating' => $rating,
                'comment' => $comment,
                'rated_at' => now()->toISOString(),
            ];

            return $this->successResponse($data, 'Driver rated successfully');

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Driver not found', 404);

        } catch (BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), 422, null, $e->getErrorCode());

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to rate driver', $e, [
                'tenant_id' => tenant()?->id,
                'user_id' => auth()->id(),
                'driver_id' => $driver,
                'rating' => $request->integer('rating'),
            ]);

            return $this->serverErrorResponse('Failed to rate driver');
        }
    }

    /**
     * Get driver leaderboard.
     *
     * @group Provider Driver Performance
     *
     * @authenticated
     *
     * @queryParam metric string Metric to rank by (completion_rate, on_time_rate, average_rating, total_deliveries). Example: completion_rate
     * @queryParam limit integer Number of drivers to return (max 50). Example: 10
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Driver leaderboard retrieved successfully",
     *   "data": [
     *     {
     *       "driver_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "driver_name": "John Doe",
     *       "score": 95.5,
     *       "total_deliveries": 45,
     *       "completion_rate": 95.5,
     *       "average_rating": 4.8
     *     },
     *     {
     *       "driver_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *       "driver_name": "Jane Smith",
     *       "score": 92.3,
     *       "total_deliveries": 38,
     *       "completion_rate": 92.3,
     *       "average_rating": 4.6
     *     }
     *   ]
     * }
     */
    public function leaderboard(Request $request): JsonResponse
    {
        $request->validate([
            'metric' => 'sometimes|string|in:completion_rate,on_time_rate,average_rating,total_deliveries',
            'limit' => 'sometimes|integer|min:1|max:50',
        ]);

        try {
            $provider = $this->getCurrentProvider();

            $metric = $request->input('metric', 'completion_rate');
            $limit = $request->integer('limit', 10);

            $leaderboard = $this->performanceService->getDriverLeaderboard($provider, $metric, $limit);

            return $this->successResponse($leaderboard, 'Driver leaderboard retrieved successfully');

        } catch (BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), 422, null, $e->getErrorCode());

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve driver leaderboard', $e, [
                'tenant_id' => tenant()?->id,
                'user_id' => auth()->id(),
                'metric' => $request->input('metric'),
            ]);

            return $this->serverErrorResponse('Failed to retrieve driver leaderboard');
        }
    }

    /**
     * Get current provider.
     */
    private function getCurrentProvider(): DeliveryProvider
    {
        $provider = DeliveryProvider::where('user_id', auth()->id())
            ->where('tenant_id', tenant()?->id ?? auth()->user()->tenant_id)
            ->first();

        if (! $provider) {
            throw new BusinessLogicException('Provider profile not found');
        }

        return $provider;
    }
}
