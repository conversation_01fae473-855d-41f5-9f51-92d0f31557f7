<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Provider;

use App\Enums\Delivery\VehicleStatus;
use App\Enums\Delivery\VehicleType;
use App\Enums\User\UserType;
use App\Exceptions\BusinessLogicException;
use App\Http\Controllers\Controller;
use App\Models\Delivery\DeliveryProvider;
use App\Models\Delivery\Vehicle;
use App\Models\User\User;
use App\Services\Delivery\VehicleAssignmentService;
use App\Services\System\LoggingService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Vehicle Management Controller
 *
 * Handles vehicle management for delivery providers including CRUD operations,
 * driver assignments, and vehicle utilization tracking.
 */
class ProviderVehicleController extends Controller
{
    use \App\Traits\ApiResponseTrait, \App\Traits\QueryHandlerTrait;

    public function __construct(
        private readonly VehicleAssignmentService $vehicleAssignmentService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get list of vehicles for the provider.
     *
     * @group Provider Vehicle Management
     *
     * @authenticated
     *
     * @queryParam search string Search by license plate or type. Example: ABC123
     * @queryParam status string Filter by vehicle status (available, in_use, maintenance). Example: available
     * @queryParam type string Filter by vehicle type (bicycle, motorcycle, car, van, truck). Example: car
     * @queryParam driver_id string Filter by assigned driver ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     * @queryParam sort_by string Sort by field (license_plate, type, status, created_at). Example: license_plate
     * @queryParam sort_direction string Sort direction (asc, desc). Example: asc
     * @queryParam per_page integer Items per page (1-100). Example: 15
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Vehicles retrieved successfully",
     *   "data": [
     *     {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "type": "car",
     *       "license_plate": "ABC123",
     *       "status": "available",
     *       "capacity": "500.00",
     *       "driver": {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *         "name": "John Doe",
     *         "phone": "+2348012345678"
     *       },
     *       "current_deliveries": 0,
     *       "needs_maintenance": false,
     *       "days_since_maintenance": 15,
     *       "created_at": "2024-01-22T10:30:00Z"
     *     }
     *   ],
     *   "meta": {
     *     "current_page": 1,
     *     "per_page": 15,
     *     "total": 25,
     *     "last_page": 2
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $provider = $this->getCurrentProvider();

            $query = Vehicle::where('provider_id', $provider->id)
                ->with(['driver:id,first_name,last_name,phone']);

            return $this->handleQuery($query, $request, [
                'searchFields' => ['license_plate'],
                'sortFields' => ['license_plate', 'type', 'status', 'created_at'],
                'filters' => [
                    'status' => ['type' => 'exact'],
                    'type' => ['type' => 'exact'],
                    'driver_id' => ['type' => 'exact'],
                ],
                'transformer' => function ($vehicle) {
                    return [
                        'id' => $vehicle->id,
                        'type' => $vehicle->type->value,
                        'license_plate' => $vehicle->license_plate,
                        'status' => $vehicle->status->value,
                        'capacity' => $vehicle->capacity,
                        'driver' => $vehicle->driver ? [
                            'id' => $vehicle->driver->id,
                            'name' => $vehicle->driver->first_name.' '.$vehicle->driver->last_name,
                            'phone' => $vehicle->driver->phone,
                        ] : null,
                        'current_deliveries' => $vehicle->getCurrentDeliveryCount(),
                        'needs_maintenance' => $vehicle->needsMaintenance(),
                        'days_since_maintenance' => $vehicle->days_since_maintenance,
                        'created_at' => $vehicle->created_at,
                    ];
                },
                'message' => 'Vehicles retrieved successfully',
            ]);

        } catch (BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), 422, null, $e->getErrorCode());

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve vehicles', $e, [
                'tenant_id' => tenant()?->id,
                'user_id' => auth()->id(),
            ]);

            return $this->serverErrorResponse('Failed to retrieve vehicles');
        }
    }

    /**
     * Get vehicle details.
     *
     * @group Provider Vehicle Management
     *
     * @authenticated
     *
     * @urlParam vehicle string required Vehicle ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Vehicle retrieved successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "type": "car",
     *     "license_plate": "ABC123",
     *     "status": "available",
     *     "capacity": "500.00",
     *     "last_maintenance_date": "2024-01-07T00:00:00Z",
     *     "driver": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *       "name": "John Doe",
     *       "phone": "+2348012345678"
     *     },
     *     "current_deliveries": 0,
     *     "needs_maintenance": false,
     *     "days_since_maintenance": 15,
     *     "recent_deliveries": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
     *         "tracking_id": "DN123456",
     *         "status": "delivered",
     *         "completed_at": "2024-01-21T16:30:00Z"
     *       }
     *     ],
     *     "created_at": "2024-01-01T10:30:00Z"
     *   }
     * }
     */
    public function show(string $vehicle, Request $request): JsonResponse
    {
        try {
            $provider = $this->getCurrentProvider();

            $vehicleModel = Vehicle::where('id', $vehicle)
                ->where('provider_id', $provider->id)
                ->with([
                    'driver:id,first_name,last_name,phone',
                    'deliveries' => function ($query) {
                        $query->latest()->limit(5)->select('id', 'vehicle_id', 'tracking_id', 'status', 'actual_delivery_time');
                    },
                ])
                ->firstOrFail();

            $data = [
                'id' => $vehicleModel->id,
                'type' => $vehicleModel->type->value,
                'license_plate' => $vehicleModel->license_plate,
                'status' => $vehicleModel->status->value,
                'capacity' => $vehicleModel->capacity,
                'last_maintenance_date' => $vehicleModel->last_maintenance_date,
                'driver' => $vehicleModel->driver ? [
                    'id' => $vehicleModel->driver->id,
                    'name' => $vehicleModel->driver->first_name.' '.$vehicleModel->driver->last_name,
                    'phone' => $vehicleModel->driver->phone,
                ] : null,
                'current_deliveries' => $vehicleModel->getCurrentDeliveryCount(),
                'needs_maintenance' => $vehicleModel->needsMaintenance(),
                'days_since_maintenance' => $vehicleModel->days_since_maintenance,
                'recent_deliveries' => $vehicleModel->deliveries->map(function ($delivery) {
                    return [
                        'id' => $delivery->id,
                        'tracking_id' => $delivery->tracking_id,
                        'status' => $delivery->status->value,
                        'completed_at' => $delivery->actual_delivery_time,
                    ];
                }),
                'created_at' => $vehicleModel->created_at,
            ];

            return $this->successResponse($data, 'Vehicle retrieved successfully');

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Vehicle not found', 404);

        } catch (BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), 422, null, $e->getErrorCode());

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve vehicle', $e, [
                'tenant_id' => tenant()?->id,
                'user_id' => auth()->id(),
                'vehicle_id' => $vehicle,
            ]);

            return $this->serverErrorResponse('Failed to retrieve vehicle');
        }
    }

    /**
     * Create a new vehicle.
     *
     * @group Provider Vehicle Management
     *
     * @authenticated
     *
     * @bodyParam type string required Vehicle type (bicycle, motorcycle, car, van, truck). Example: car
     * @bodyParam license_plate string required Vehicle license plate. Example: ABC123
     * @bodyParam capacity numeric Vehicle capacity in kg. Example: 500.00
     * @bodyParam driver_id string Optional driver to assign. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Vehicle created successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "type": "car",
     *     "license_plate": "ABC123",
     *     "status": "available",
     *     "capacity": "500.00",
     *     "driver": null,
     *     "created_at": "2024-01-22T15:30:00Z"
     *   }
     * }
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'type' => 'required|string|in:'.implode(',', array_column(VehicleType::cases(), 'value')),
            'license_plate' => 'required|string|max:20',
            'capacity' => 'sometimes|numeric|min:0|max:10000',
            'driver_id' => 'sometimes|uuid|exists:users,id',
        ]);

        try {
            $provider = $this->getCurrentProvider();

            // Check for duplicate license plate within provider
            $existingVehicle = Vehicle::where('provider_id', $provider->id)
                ->where('license_plate', $request->input('license_plate'))
                ->first();

            if ($existingVehicle) {
                return $this->errorResponse('A vehicle with this license plate already exists', 422);
            }

            $vehicle = Vehicle::create([
                'tenant_id' => tenant()?->id ?? auth()->user()->tenant_id,
                'provider_id' => $provider->id,
                'type' => VehicleType::from($request->input('type')),
                'license_plate' => $request->input('license_plate'),
                'capacity' => $request->input('capacity'),
                'status' => VehicleStatus::AVAILABLE,
            ]);

            // Assign driver if provided
            if ($request->filled('driver_id')) {
                $driver = User::where('id', $request->input('driver_id'))
                    ->where('user_type', UserType::DELIVERY_PARTNER)
                    ->where('tenant_id', tenant()?->id ?? auth()->user()->tenant_id)
                    ->first();

                if ($driver) {
                    $this->vehicleAssignmentService->assignVehicleToDriver($vehicle, $driver);
                    $vehicle->load('driver:id,first_name,last_name,phone');
                }
            }

            $data = [
                'id' => $vehicle->id,
                'type' => $vehicle->type->value,
                'license_plate' => $vehicle->license_plate,
                'status' => $vehicle->status->value,
                'capacity' => $vehicle->capacity,
                'driver' => $vehicle->driver ? [
                    'id' => $vehicle->driver->id,
                    'name' => $vehicle->driver->first_name.' '.$vehicle->driver->last_name,
                    'phone' => $vehicle->driver->phone,
                ] : null,
                'created_at' => $vehicle->created_at,
            ];

            return $this->successResponse($data, 'Vehicle created successfully', 201);

        } catch (BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), 422, null, $e->getErrorCode());

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to create vehicle', $e, [
                'tenant_id' => tenant()?->id,
                'user_id' => auth()->id(),
                'request_data' => $request->all(),
            ]);

            return $this->serverErrorResponse('Failed to create vehicle');
        }
    }

    /**
     * Assign vehicle to driver.
     *
     * @group Provider Vehicle Management
     *
     * @authenticated
     *
     * @urlParam vehicle string required Vehicle ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam driver_id string required Driver ID to assign. Example: 019723aa-3202-70dd-a0c1-3565681dd87b
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Vehicle assigned to driver successfully",
     *   "data": {
     *     "vehicle_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "driver_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *     "driver_name": "John Doe",
     *     "assigned_at": "2024-01-22T15:30:00Z"
     *   }
     * }
     */
    public function assignDriver(string $vehicle, Request $request): JsonResponse
    {
        $request->validate([
            'driver_id' => 'required|uuid|exists:users,id',
        ]);

        try {
            $provider = $this->getCurrentProvider();

            $vehicleModel = Vehicle::where('id', $vehicle)
                ->where('provider_id', $provider->id)
                ->firstOrFail();

            $driver = User::where('id', $request->input('driver_id'))
                ->where('user_type', UserType::DELIVERY_PARTNER)
                ->where('tenant_id', tenant()?->id ?? auth()->user()->tenant_id)
                ->firstOrFail();

            $success = $this->vehicleAssignmentService->assignVehicleToDriver($vehicleModel, $driver);

            if (! $success) {
                return $this->errorResponse('Failed to assign vehicle to driver', 500);
            }

            $data = [
                'vehicle_id' => $vehicleModel->id,
                'driver_id' => $driver->id,
                'driver_name' => $driver->first_name.' '.$driver->last_name,
                'assigned_at' => now()->toISOString(),
            ];

            return $this->successResponse($data, 'Vehicle assigned to driver successfully');

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Vehicle or driver not found', 404);

        } catch (BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), 422, null, $e->getErrorCode());

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to assign vehicle to driver', $e, [
                'tenant_id' => tenant()?->id,
                'user_id' => auth()->id(),
                'vehicle_id' => $vehicle,
                'driver_id' => $request->input('driver_id'),
            ]);

            return $this->serverErrorResponse('Failed to assign vehicle to driver');
        }
    }

    /**
     * Unassign vehicle from driver.
     *
     * @group Provider Vehicle Management
     *
     * @authenticated
     *
     * @urlParam vehicle string required Vehicle ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Vehicle unassigned from driver successfully",
     *   "data": {
     *     "vehicle_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "unassigned_at": "2024-01-22T15:30:00Z"
     *   }
     * }
     */
    public function unassignDriver(string $vehicle, Request $request): JsonResponse
    {
        try {
            $provider = $this->getCurrentProvider();

            $vehicleModel = Vehicle::where('id', $vehicle)
                ->where('provider_id', $provider->id)
                ->firstOrFail();

            $success = $this->vehicleAssignmentService->unassignVehicleFromDriver($vehicleModel);

            if (! $success) {
                return $this->errorResponse('Failed to unassign vehicle from driver', 500);
            }

            $data = [
                'vehicle_id' => $vehicleModel->id,
                'unassigned_at' => now()->toISOString(),
            ];

            return $this->successResponse($data, 'Vehicle unassigned from driver successfully');

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse('Vehicle not found', 404);

        } catch (BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), 422, null, $e->getErrorCode());

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to unassign vehicle from driver', $e, [
                'tenant_id' => tenant()?->id,
                'user_id' => auth()->id(),
                'vehicle_id' => $vehicle,
            ]);

            return $this->serverErrorResponse('Failed to unassign vehicle from driver');
        }
    }

    /**
     * Get vehicle utilization statistics.
     *
     * @group Provider Vehicle Management
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Vehicle utilization statistics retrieved successfully",
     *   "data": {
     *     "total_vehicles": 10,
     *     "available_vehicles": 6,
     *     "in_use_vehicles": 3,
     *     "maintenance_vehicles": 1,
     *     "assigned_vehicles": 8,
     *     "unassigned_vehicles": 2,
     *     "utilization_rate": 30.0,
     *     "assignment_rate": 80.0
     *   }
     * }
     */
    public function utilization(Request $request): JsonResponse
    {
        try {
            $provider = $this->getCurrentProvider();

            $stats = $this->vehicleAssignmentService->getVehicleUtilizationStats($provider);

            return $this->successResponse($stats, 'Vehicle utilization statistics retrieved successfully');

        } catch (BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), 422, null, $e->getErrorCode());

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve vehicle utilization statistics', $e, [
                'tenant_id' => tenant()?->id,
                'user_id' => auth()->id(),
            ]);

            return $this->serverErrorResponse('Failed to retrieve vehicle utilization statistics');
        }
    }

    /**
     * Get current provider.
     */
    private function getCurrentProvider(): DeliveryProvider
    {
        $provider = DeliveryProvider::where('user_id', auth()->id())
            ->where('tenant_id', tenant()?->id ?? auth()->user()->tenant_id)
            ->first();

        if (! $provider) {
            throw new BusinessLogicException('Provider profile not found');
        }

        return $provider;
    }
}
