<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Provider;

use App\Http\Controllers\Controller;
use App\Models\Delivery\DeliveryRequest;
use App\Services\Delivery\FirstToAcceptService;
use App\Services\ProviderService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group Provider Delivery Requests
 *
 * APIs for providers to manage delivery requests
 */
class ProviderDeliveryRequestController extends Controller
{
    use \App\Traits\ApiResponseTrait, \App\Traits\QueryHandlerTrait;

    public function __construct(
        private readonly FirstToAcceptService $firstToAcceptService,
        private readonly ProviderService $providerService
    ) {}

    /**
     * Get delivery requests for the current provider.
     *
     * @group Provider Delivery Requests
     *
     * @authenticated
     *
     * @queryParam status string Filter by request status (pending, accepted, cancelled, expired). Example: pending
     * @queryParam page integer Page number for pagination. Example: 1
     * @queryParam per_page integer Number of items per page. Example: 15
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Delivery requests retrieved successfully",
     *   "data": [
     *     {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "order_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *       "broadcast_id": "broadcast_019723aa_1640995200",
     *       "status": "pending",
     *       "expires_at": "2024-01-15T12:30:00Z",
     *       "time_remaining_seconds": 450,
     *       "order_details": {
     *         "order_reference": "ORD-2024-001",
     *         "pickup_address": {...},
     *         "delivery_address": {...},
     *         "estimated_distance": 5.2,
     *         "estimated_duration": 25,
     *         "order_value": 2500,
     *         "delivery_fee": 500
     *       }
     *     }
     *   ],
     *   "pagination": {...}
     * }
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $provider = $this->providerService->getCurrentProvider();

            $query = DeliveryRequest::query()
                ->where('provider_id', $provider->id)
                ->with(['order.business'])
                ->orderBy('created_at', 'desc');

            // Apply filters
            if ($request->filled('status')) {
                $query->where('status', $request->input('status'));
            }

            $result = $this->handleQuery($query, $request);

            $transformedData = $result['data']->map(function ($request) {
                return [
                    'id' => $request->id,
                    'order_id' => $request->order_id,
                    'broadcast_id' => $request->broadcast_id,
                    'status' => $request->status->value,
                    'status_label' => $request->status->label(),
                    'expires_at' => $request->expires_at->toISOString(),
                    'time_remaining_seconds' => $request->getTimeRemainingSeconds(),
                    'is_valid' => $request->isValid(),
                    'response_time_seconds' => $request->getResponseTimeSeconds(),
                    'order_details' => $request->request_data,
                    'created_at' => $request->created_at->toISOString(),
                ];
            });

            return $this->successResponse(
                $transformedData,
                'Delivery requests retrieved successfully',
                200,
                $result['pagination']
            );

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve delivery requests', 500);
        }
    }

    /**
     * Get a specific delivery request.
     *
     * @group Provider Delivery Requests
     *
     * @authenticated
     *
     * @urlParam requestId string required The delivery request ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Delivery request retrieved successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "order_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *     "broadcast_id": "broadcast_019723aa_1640995200",
     *     "status": "pending",
     *     "expires_at": "2024-01-15T12:30:00Z",
     *     "time_remaining_seconds": 450,
     *     "order_details": {...},
     *     "order": {...}
     *   }
     * }
     */
    public function show(string $requestId): JsonResponse
    {
        try {
            $provider = $this->providerService->getCurrentProvider();

            $request = DeliveryRequest::with(['order.business', 'order.deliveryAddress'])
                ->where('provider_id', $provider->id)
                ->findOrFail($requestId);

            return $this->successResponse([
                'id' => $request->id,
                'order_id' => $request->order_id,
                'broadcast_id' => $request->broadcast_id,
                'status' => $request->status->value,
                'status_label' => $request->status->label(),
                'expires_at' => $request->expires_at->toISOString(),
                'time_remaining_seconds' => $request->getTimeRemainingSeconds(),
                'is_valid' => $request->isValid(),
                'response_time_seconds' => $request->getResponseTimeSeconds(),
                'order_details' => $request->request_data,
                'order' => [
                    'id' => $request->order->id,
                    'order_reference' => $request->order->order_reference,
                    'status' => $request->order->status->value,
                    'total_amount' => $request->order->total_amount,
                    'business' => [
                        'id' => $request->order->business->id,
                        'name' => $request->order->business->name,
                        'phone' => $request->order->business->phone,
                    ],
                ],
                'created_at' => $request->created_at->toISOString(),
            ], 'Delivery request retrieved successfully');

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Delivery request not found');
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve delivery request', 500);
        }
    }

    /**
     * Accept a delivery request (first-to-accept).
     *
     * @group Provider Delivery Requests
     *
     * @authenticated
     *
     * @urlParam requestId string required The delivery request ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Congratulations! You won the delivery assignment",
     *   "data": {
     *     "delivery_id": "019723aa-3202-70dd-a0c1-3565681dd87c",
     *     "response_time_seconds": 45,
     *     "assignment_type": "first_to_accept",
     *     "race_winner": true
     *   }
     * }
     * @response 422 {
     *   "success": false,
     *   "message": "This delivery request is no longer available",
     *   "error": {
     *     "code": "REQUEST_UNAVAILABLE",
     *     "current_status": "accepted"
     *   }
     * }
     */
    public function accept(string $requestId): JsonResponse
    {
        try {
            $provider = $this->providerService->getCurrentProvider();

            $result = $this->firstToAcceptService->handleAcceptanceAttempt($requestId, $provider);

            if ($result['success']) {
                return $this->successResponse(
                    $result,
                    $result['message'] ?? 'Delivery request accepted successfully'
                );
            } else {
                $statusCode = match ($result['code'] ?? 'UNKNOWN') {
                    'REQUEST_UNAVAILABLE', 'REQUEST_EXPIRED', 'ORDER_ALREADY_ASSIGNED' => 422,
                    'UNAUTHORIZED' => 403,
                    'CONCURRENT_ACCEPTANCE' => 409,
                    default => 400,
                };

                return $this->errorResponse(
                    $result['error'] ?? 'Failed to accept delivery request',
                    $statusCode,
                    ['code' => $result['code'] ?? 'UNKNOWN']
                );
            }

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Delivery request not found');
        } catch (\Exception $e) {
            return $this->errorResponse('An error occurred while processing your acceptance', 500);
        }
    }

    /**
     * Get pending delivery requests for the current provider.
     *
     * @group Provider Delivery Requests
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Pending delivery requests retrieved successfully",
     *   "data": [
     *     {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "order_reference": "ORD-2024-001",
     *       "expires_at": "2024-01-15T12:30:00Z",
     *       "time_remaining_seconds": 450,
     *       "estimated_distance": 5.2,
     *       "estimated_duration": 25,
     *       "delivery_fee": 500,
     *       "priority": "normal"
     *     }
     *   ]
     * }
     */
    public function pending(): JsonResponse
    {
        try {
            $provider = $this->providerService->getCurrentProvider();

            $pendingRequests = DeliveryRequest::where('provider_id', $provider->id)
                ->pending()
                ->valid()
                ->orderBy('created_at', 'asc')
                ->get();

            $transformedData = $pendingRequests->map(function ($request) {
                return [
                    'id' => $request->id,
                    'order_reference' => $request->getRequestData('order_reference'),
                    'expires_at' => $request->expires_at->toISOString(),
                    'time_remaining_seconds' => $request->getTimeRemainingSeconds(),
                    'estimated_distance' => $request->getRequestData('estimated_distance'),
                    'estimated_duration' => $request->getRequestData('estimated_duration'),
                    'order_value' => $request->getRequestData('order_value'),
                    'delivery_fee' => $request->getRequestData('delivery_fee'),
                    'pickup_location' => $request->getRequestData('pickup_address'),
                    'delivery_location' => $request->getRequestData('delivery_address'),
                    'special_instructions' => $request->getRequestData('special_instructions'),
                    'created_at' => $request->created_at->toISOString(),
                ];
            });

            return $this->successResponse(
                $transformedData,
                'Pending delivery requests retrieved successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve pending delivery requests', 500);
        }
    }

    /**
     * Get delivery request statistics for the current provider.
     *
     * @group Provider Delivery Requests
     *
     * @authenticated
     *
     * @queryParam days integer Number of days to include in statistics. Example: 30
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Delivery request statistics retrieved successfully",
     *   "data": {
     *     "period_days": 30,
     *     "total_requests": 45,
     *     "accepted_requests": 38,
     *     "acceptance_rate": 84.44,
     *     "average_response_time_seconds": 67.5,
     *     "fastest_response_time_seconds": 12,
     *     "expired_requests": 5,
     *     "cancelled_requests": 2
     *   }
     * }
     */
    public function statistics(Request $request): JsonResponse
    {
        try {
            $provider = $this->providerService->getCurrentProvider();
            $days = $request->input('days', 30);

            $startDate = now()->subDays($days);

            $totalRequests = DeliveryRequest::where('provider_id', $provider->id)
                ->where('created_at', '>=', $startDate)
                ->count();

            $acceptedRequests = DeliveryRequest::where('provider_id', $provider->id)
                ->where('created_at', '>=', $startDate)
                ->accepted()
                ->count();

            $averageResponseTime = DeliveryRequest::where('provider_id', $provider->id)
                ->where('created_at', '>=', $startDate)
                ->accepted()
                ->whereNotNull('accepted_at')
                ->selectRaw('AVG(TIMESTAMPDIFF(SECOND, created_at, accepted_at)) as avg_response_time')
                ->value('avg_response_time');

            $fastestResponseTime = DeliveryRequest::where('provider_id', $provider->id)
                ->where('created_at', '>=', $startDate)
                ->accepted()
                ->whereNotNull('accepted_at')
                ->selectRaw('MIN(TIMESTAMPDIFF(SECOND, created_at, accepted_at)) as min_response_time')
                ->value('min_response_time');

            $expiredRequests = DeliveryRequest::where('provider_id', $provider->id)
                ->where('created_at', '>=', $startDate)
                ->expired()
                ->count();

            $cancelledRequests = DeliveryRequest::where('provider_id', $provider->id)
                ->where('created_at', '>=', $startDate)
                ->cancelled()
                ->count();

            return $this->successResponse([
                'period_days' => $days,
                'total_requests' => $totalRequests,
                'accepted_requests' => $acceptedRequests,
                'acceptance_rate' => $totalRequests > 0 ? round(($acceptedRequests / $totalRequests) * 100, 2) : 0,
                'average_response_time_seconds' => $averageResponseTime ? round($averageResponseTime, 2) : null,
                'fastest_response_time_seconds' => $fastestResponseTime ? (int) $fastestResponseTime : null,
                'expired_requests' => $expiredRequests,
                'cancelled_requests' => $cancelledRequests,
            ], 'Delivery request statistics retrieved successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve delivery request statistics', 500);
        }
    }
}
