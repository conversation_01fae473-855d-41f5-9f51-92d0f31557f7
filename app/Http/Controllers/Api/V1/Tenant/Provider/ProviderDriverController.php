<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Provider;

use App\Enums\User\UserType;
use App\Exceptions\BusinessLogicException;
use App\Http\Controllers\Controller;
use App\Models\Delivery\DeliveryProvider;
use App\Models\User\User;
use App\Services\System\LoggingService;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

/**
 * Provider Driver Management Controller
 *
 * Handles driver management for delivery providers including listing,
 * adding, updating, and managing driver assignments.
 */
class ProviderDriverController extends Controller
{
    use \App\Traits\ApiResponseTrait, \App\Traits\QueryHandlerTrait;

    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get provider's drivers.
     *
     * @group Provider Drivers
     *
     * @authenticated
     *
     * @queryParam page integer Page number for pagination. Example: 1
     * @queryParam per_page integer Items per page (max 100). Example: 15
     * @queryParam search string Search drivers by name or email. Example: john
     * @queryParam status string Filter by status (active, inactive). Example: active
     * @queryParam sort_by string Sort by field (name, email, created_at). Example: name
     * @queryParam sort_direction string Sort direction (asc, desc). Example: asc
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Drivers retrieved successfully",
     *   "data": {
     *     "data": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *         "name": "John Driver",
     *         "email": "<EMAIL>",
     *         "phone": "08012345678",
     *         "is_active": true,
     *         "is_available": true,
     *         "vehicle_type": "motorcycle",
     *         "license_number": "ABC123456",
     *         "created_at": "2024-01-15T10:30:00Z"
     *       }
     *     ],
     *     "current_page": 1,
     *     "per_page": 15,
     *     "total": 10
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $provider = $this->getCurrentProvider();

            // Build query for drivers (users with delivery partner type in current tenant)
            $query = User::where('tenant_id', tenant()?->id)
                ->where('user_type', UserType::DELIVERY_PARTNER);

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'first_name',
                    'last_name',
                    'email',
                ],
                'sortFields' => [
                    'first_name',
                    'last_name',
                    'email',
                    'created_at',
                ],
                'filters' => [
                    'status' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $isActive = $value === 'active';
                            $query->where('is_active', $isActive);
                        },
                    ],
                ],
                'message' => 'Drivers retrieved successfully',
                'entityName' => 'drivers',
                'transformer' => [$this, 'transformDriver'],
            ]);

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve drivers',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'request_params' => $request->only(['search', 'status', 'sort_by', 'sort_direction']),
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve drivers');
        }
    }

    /**
     * Get specific driver details.
     *
     * @group Provider Drivers
     *
     * @authenticated
     *
     * @urlParam driver string required Driver ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Driver retrieved successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "John Driver",
     *     "email": "<EMAIL>",
     *     "phone": "08012345678",
     *     "is_active": true,
     *     "is_available": true,
     *     "vehicle_type": "motorcycle",
     *     "license_number": "ABC123456",
     *     "total_deliveries": 150,
     *     "completed_deliveries": 145,
     *     "average_rating": 4.7,
     *     "created_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function show(string $driver): JsonResponse
    {
        try {
            $provider = $this->getCurrentProvider();

            $query = User::where('tenant_id', tenant()?->id)
                ->where('user_type', UserType::DELIVERY_PARTNER);

            return $this->handleShow($query, $driver, [
                'transformer' => fn ($model) => $this->transformDriver($model, true),
                'message' => 'Driver retrieved successfully',
                'notFoundMessage' => 'Driver not found',
            ]);

        } catch (ModelNotFoundException $e) {
            return $this->errorResponse(
                'Driver not found',
                404
            );

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve driver details',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'driver_id' => $driver,
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve driver details');
        }
    }

    /**
     * Add new driver.
     *
     * @group Provider Drivers
     *
     * @authenticated
     *
     * @bodyParam first_name string required Driver's first name. Example: John
     * @bodyParam last_name string required Driver's last name. Example: Doe
     * @bodyParam email string required Driver's email address. Example: <EMAIL>
     * @bodyParam phone_number string required Driver's phone number. Example: 08012345678
     * @bodyParam license_number string required Driver's license number. Example: ABC123456
     * @bodyParam vehicle_type string required Vehicle type. Example: motorcycle
     * @bodyParam password string required Driver's password. Example: password123
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Driver added successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "John Doe",
     *     "email": "<EMAIL>",
     *     "phone": "08012345678",
     *     "is_active": true,
     *     "is_available": true,
     *     "vehicle_type": "motorcycle",
     *     "license_number": "ABC123456",
     *     "created_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone_number' => 'required|string|max:20',
            'license_number' => 'required|string|max:50',
            'vehicle_type' => 'required|string|in:motorcycle,bicycle,car,van,truck',
            'password' => 'required|string|min:8',
        ]);

        try {
            $provider = $this->getCurrentProvider();

            // Create driver user account
            $driver = User::create([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'phone_number' => $request->phone_number,
                'password' => Hash::make($request->password),
                'user_type' => UserType::DELIVERY_PARTNER,
                'tenant_id' => tenant()?->id,
                'is_active' => true,
                'email_verified_at' => now(), // Auto-verify for provider-added drivers
            ]);

            // Create driver profile (placeholder for future driver profile model)
            // This would typically create a DriverProfile model with license_number, vehicle_type, etc.

            $this->loggingService->logInfo(
                'Driver added successfully',
                [
                    'tenant_id' => tenant()?->id,
                    'provider_id' => $provider->id,
                    'driver_id' => $driver->id,
                    'user_id' => auth()->id(),
                ]
            );

            return $this->successResponse(
                $this->transformDriver($driver),
                'Driver added successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to add driver',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'request_data' => $request->except(['password']),
                ]
            );

            return $this->serverErrorResponse('Failed to add driver');
        }
    }

    /**
     * Update driver.
     *
     * @group Provider Drivers
     *
     * @authenticated
     *
     * @bodyParam first_name string Driver's first name. Example: John
     * @bodyParam last_name string Driver's last name. Example: Doe
     * @bodyParam phone_number string Driver's phone number. Example: 08012345678
     * @bodyParam license_number string Driver's license number. Example: ABC123456
     * @bodyParam vehicle_type string Vehicle type. Example: motorcycle
     * @bodyParam is_active boolean Driver's active status. Example: true
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Driver updated successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "John Doe",
     *     "email": "<EMAIL>",
     *     "phone": "08012345678",
     *     "is_active": true,
     *     "is_available": true,
     *     "vehicle_type": "motorcycle",
     *     "license_number": "ABC123456",
     *     "created_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function update(Request $request, string $driver): JsonResponse
    {
        $request->validate([
            'first_name' => 'sometimes|string|max:255',
            'last_name' => 'sometimes|string|max:255',
            'phone_number' => 'sometimes|string|max:20',
            'license_number' => 'sometimes|string|max:50',
            'vehicle_type' => 'sometimes|string|in:motorcycle,bicycle,car,van,truck',
            'is_active' => 'sometimes|boolean',
        ]);

        try {
            $provider = $this->getCurrentProvider();

            // Find driver in current tenant
            $driverUser = User::where('id', $driver)
                ->where('tenant_id', tenant()?->id)
                ->where('user_type', UserType::DELIVERY_PARTNER)
                ->first();

            if (! $driverUser) {
                return $this->notFoundResponse('Driver not found');
            }

            // Update driver user account
            $updateData = $request->only([
                'first_name', 'last_name', 'phone_number', 'is_active',
            ]);

            if (! empty($updateData)) {
                $driverUser->update($updateData);
            }

            // Update driver profile (placeholder for future driver profile model)
            // This would typically update a DriverProfile model with license_number, vehicle_type, etc.

            $this->loggingService->logInfo(
                'Driver updated successfully',
                [
                    'tenant_id' => tenant()?->id,
                    'provider_id' => $provider->id,
                    'driver_id' => $driverUser->id,
                    'user_id' => auth()->id(),
                    'updated_fields' => array_keys($updateData),
                ]
            );

            return $this->successResponse(
                $this->transformDriver($driverUser),
                'Driver updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update driver',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'driver_id' => $driver,
                ]
            );

            return $this->serverErrorResponse('Failed to update driver');
        }
    }

    /**
     * Remove driver.
     *
     * @group Provider Drivers
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Driver removed successfully"
     * }
     */
    public function destroy(string $driver): JsonResponse
    {
        try {
            $provider = $this->getCurrentProvider();

            // Find driver in current tenant
            $driverUser = User::where('id', $driver)
                ->where('tenant_id', tenant()?->id)
                ->where('user_type', UserType::DELIVERY_PARTNER)
                ->first();

            if (! $driverUser) {
                return $this->notFoundResponse('Driver not found');
            }

            // Soft delete the driver (deactivate instead of hard delete)
            $driverUser->update(['is_active' => false]);

            $this->loggingService->logInfo(
                'Driver removed successfully',
                [
                    'tenant_id' => tenant()?->id,
                    'provider_id' => $provider->id,
                    'driver_id' => $driverUser->id,
                    'user_id' => auth()->id(),
                ]
            );

            return $this->successResponse(
                null,
                'Driver removed successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to remove driver',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'driver_id' => $driver,
                ]
            );

            return $this->serverErrorResponse('Failed to remove driver');
        }
    }

    // Helper Methods

    /**
     * Get current provider.
     */
    private function getCurrentProvider(): DeliveryProvider
    {
        $provider = DeliveryProvider::where('user_id', auth()->id())
            ->where('tenant_id', tenant()?->id ?? auth()->user()->tenant_id)
            ->first();

        if (! $provider) {
            throw new BusinessLogicException('Provider profile not found');
        }

        return $provider;
    }

    /**
     * Transform driver for API response.
     */
    private function transformDriver(User $driver, bool $detailed = false): array
    {
        $data = [
            'id' => $driver->id,
            'name' => $driver->first_name.' '.$driver->last_name,
            'email' => $driver->email,
            'phone' => $driver->phone_number,
            'is_active' => $driver->is_active,
            'is_available' => true, // Placeholder - would track availability
            'vehicle_type' => 'motorcycle', // Placeholder - would get from driver profile
            'license_number' => null, // Placeholder - would get from driver profile
            'created_at' => $driver->created_at->toISOString(),
        ];

        if ($detailed) {
            $data = array_merge($data, [
                'total_deliveries' => 0, // Placeholder - would count from deliveries
                'completed_deliveries' => 0, // Placeholder - would count completed deliveries
                'average_rating' => 0, // Placeholder - would calculate from ratings
            ]);
        }

        return $data;
    }
}
