<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Business;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\Tenant\Business\CreateDeliveryRequest;
use App\Http\Requests\Api\V1\TrackingSubscriptionRequest;
use App\Models\Delivery\Delivery;
use App\Models\Delivery\DeliveryLocation;
use App\Models\Delivery\DeliveryProvider;
use App\Models\Delivery\Order;
use App\Services\Business\BusinessService;
use App\Services\Delivery\DeliveryService;
use App\Services\Delivery\RealTimeTrackingService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Business Delivery Controller
 *
 * Handles delivery management for businesses.
 * Allows businesses to create deliveries, track status, and manage delivery operations.
 */
class BusinessDeliveryController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly BusinessService $businessService,
        private readonly DeliveryService $deliveryService,
        private readonly RealTimeTrackingService $trackingService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get deliveries for the business
     *
     * @group Business Delivery
     *
     * @authenticated
     *
     * @header Authorization Bearer {token}
     *
     * @queryParam search string Search in order reference, customer name, or delivery address. Example: ORD-2024
     * @queryParam status string Filter by delivery status. Example: in_transit
     * @queryParam driver_id string Filter by driver ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     * @queryParam date_from string Filter deliveries from date (Y-m-d). Example: 2024-01-01
     * @queryParam date_to string Filter deliveries to date (Y-m-d). Example: 2024-01-31
     * @queryParam sort string Sort field. Example: created_at
     * @queryParam direction string Sort direction (asc/desc). Example: desc
     * @queryParam page integer Page number. Example: 1
     * @queryParam per_page integer Items per page (max 50). Example: 15
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Deliveries retrieved successfully",
     *   "data": [
     *     {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "order_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *       "order_reference": "ORD-2024-001",
     *       "status": "in_transit",
     *       "customer_name": "John Doe",
     *       "delivery_address": {
     *         "street": "123 Main St",
     *         "city": "Lagos",
     *         "state": "Lagos",
     *         "latitude": 6.5244,
     *         "longitude": 3.3792
     *       },
     *       "driver": {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
     *         "name": "Driver Name",
     *         "phone": "+234801234567"
     *       },
     *       "estimated_delivery_time": "2024-01-22T15:30:00Z",
     *       "delivery_fee": 800,
     *       "distance_km": 5.2,
     *       "created_at": "2024-01-22T14:00:00Z"
     *     }
     *   ],
     *   "meta": {
     *     "current_page": 1,
     *     "per_page": 15,
     *     "total": 25,
     *     "last_page": 2
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            $query = Delivery::where('business_id', $business->id)
                ->with(['order:id,order_reference,customer_id', 'order.customer:id,first_name,last_name', 'driver:id,first_name,last_name,phone']);

            // Use QueryHandlerTrait for consistent pagination and response
            return $this->handleQuery($query, $request, [
                'searchFields' => ['order.order_reference'],
                'sortFields' => ['created_at', 'status', 'estimated_delivery_time', 'delivery_fee'],
                'filters' => [
                    'status' => ['type' => 'exact'],
                    'driver_id' => ['type' => 'exact'],
                    'date_from' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->whereDate('created_at', '>=', $value);
                        },
                    ],
                    'date_to' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->whereDate('created_at', '<=', $value);
                        },
                    ],
                ],
                'transformer' => function ($delivery) {
                    return [
                        'id' => $delivery->id,
                        'order_id' => $delivery->order_id,
                        'order_reference' => $delivery->order?->order_reference,
                        'status' => $delivery->status->value,
                        'customer_name' => $delivery->order?->customer ?
                            $delivery->order->customer->first_name.' '.$delivery->order->customer->last_name : null,
                        'delivery_address' => $delivery->delivery_address,
                        'driver' => $delivery->driver ? [
                            'id' => $delivery->driver->id,
                            'name' => $delivery->driver->first_name.' '.$delivery->driver->last_name,
                            'phone' => $delivery->driver->phone,
                        ] : null,
                        'estimated_delivery_time' => $delivery->estimated_delivery_time,
                        'delivery_fee' => $delivery->delivery_fee,
                        'distance_km' => $delivery->distance_km,
                        'created_at' => $delivery->created_at,
                    ];
                },
                'message' => 'Deliveries retrieved successfully',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve deliveries', $e, [
                'business_id' => $business->id ?? null,
                'request_data' => $request->all(),
            ]);

            return $this->errorResponse('Failed to retrieve deliveries', 500);
        }
    }

    /**
     * Create a delivery for an order
     *
     * @group Business Delivery
     *
     * @authenticated
     *
     * @header Authorization Bearer {token}
     *
     * @bodyParam order_id string required Order ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87b
     * @bodyParam provider_id string Provider ID (optional - will auto-match if not provided). Example: 019723aa-3202-70dd-a0c1-3565681dd87c
     * @bodyParam priority string Delivery priority (normal, urgent, economy). Example: normal
     * @bodyParam delivery_instructions string Special delivery instructions. Example: Leave at front door
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Delivery created successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "order_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *     "provider_id": "019723aa-3202-70dd-a0c1-3565681dd87c",
     *     "status": "pending",
     *     "estimated_pickup_time": "2024-01-22T14:20:00Z",
     *     "estimated_delivery_time": "2024-01-22T15:30:00Z",
     *     "delivery_fee": 800,
     *     "distance_km": 5.2
     *   }
     * }
     */
    public function store(CreateDeliveryRequest $request): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();
            $validated = $request->validated();

            // Get the order and validate it belongs to the business
            $order = Order::where('id', $validated['order_id'])
                ->where('business_id', $business->id)
                ->firstOrFail();

            // Get provider if specified
            $provider = null;
            if (isset($validated['provider_id'])) {
                $provider = DeliveryProvider::findOrFail($validated['provider_id']);
            }

            // Prepare preferences
            $preferences = [];
            if (isset($validated['priority'])) {
                $preferences['priority'] = $validated['priority'];
            }

            // Create delivery
            $delivery = $this->deliveryService->createDelivery($order, $provider, $preferences);

            // Update delivery instructions if provided
            if (isset($validated['delivery_instructions'])) {
                $delivery->update(['delivery_instructions' => $validated['delivery_instructions']]);
            }

            $data = [
                'id' => $delivery->id,
                'order_id' => $delivery->order_id,
                'provider_id' => $delivery->provider_id,
                'status' => $delivery->status->value,
                'estimated_pickup_time' => $delivery->estimated_pickup_time,
                'estimated_delivery_time' => $delivery->estimated_delivery_time,
                'delivery_fee' => $delivery->delivery_fee,
                'distance_km' => $delivery->distance_km,
            ];

            return $this->successResponse($data, 'Delivery created successfully', null, 201);

        } catch (\App\Exceptions\BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to create delivery', $e, [
                'business_id' => $business->id ?? null,
                'request_data' => $request->validated(),
            ]);

            return $this->errorResponse('Failed to create delivery', 500);
        }
    }

    /**
     * Get delivery details
     *
     * @group Business Delivery
     *
     * @authenticated
     *
     * @header Authorization Bearer {token}
     *
     * @urlParam delivery string required The delivery ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Delivery details retrieved successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "order": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *       "order_reference": "ORD-2024-001",
     *       "total_amount": 2500
     *     },
     *     "customer": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
     *       "name": "John Doe",
     *       "phone": "+234801234567"
     *     },
     *     "provider": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87d",
     *       "business_name": "FastDelivery Ltd"
     *     },
     *     "driver": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87e",
     *       "name": "Driver Name",
     *       "phone": "+234801234568",
     *       "vehicle_info": "Honda CRV - ABC123"
     *     },
     *     "status": "in_transit",
     *     "pickup_address": {
     *       "street": "456 Business St",
     *       "city": "Lagos",
     *       "state": "Lagos"
     *     },
     *     "delivery_address": {
     *       "street": "123 Main St",
     *       "city": "Lagos",
     *       "state": "Lagos"
     *     },
     *     "estimated_pickup_time": "2024-01-22T14:20:00Z",
     *     "estimated_delivery_time": "2024-01-22T15:30:00Z",
     *     "picked_up_at": "2024-01-22T14:25:00Z",
     *     "delivery_fee": 800,
     *     "distance_km": 5.2,
     *     "driver_location": {
     *       "latitude": 6.5244,
     *       "longitude": 3.3792,
     *       "updated_at": "2024-01-22T15:00:00Z"
     *     }
     *   }
     * }
     */
    public function show(Delivery $delivery): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            // Ensure delivery belongs to the business
            if ($delivery->business_id !== $business->id) {
                return $this->errorResponse('Delivery not found', 404);
            }

            $delivery->load([
                'order:id,order_reference,total_amount,customer_id',
                'order.customer:id,first_name,last_name,phone',
                'provider:id,business_name',
                'driver:id,first_name,last_name,phone,vehicle_type,vehicle_plate_number',
            ]);

            $data = [
                'id' => $delivery->id,
                'order' => $delivery->order ? [
                    'id' => $delivery->order->id,
                    'order_reference' => $delivery->order->order_reference,
                    'total_amount' => $delivery->order->total_amount,
                ] : null,
                'customer' => $delivery->order?->customer ? [
                    'id' => $delivery->order->customer->id,
                    'name' => $delivery->order->customer->first_name.' '.$delivery->order->customer->last_name,
                    'phone' => $delivery->order->customer->phone,
                ] : null,
                'provider' => $delivery->provider ? [
                    'id' => $delivery->provider->id,
                    'business_name' => $delivery->provider->business_name,
                ] : null,
                'driver' => $delivery->driver ? [
                    'id' => $delivery->driver->id,
                    'name' => $delivery->driver->first_name.' '.$delivery->driver->last_name,
                    'phone' => $delivery->driver->phone,
                    'vehicle_info' => $delivery->driver->vehicle_type.' - '.$delivery->driver->vehicle_plate_number,
                ] : null,
                'status' => $delivery->status->value,
                'pickup_address' => $delivery->pickup_address,
                'delivery_address' => $delivery->delivery_address,
                'estimated_pickup_time' => $delivery->estimated_pickup_time,
                'estimated_delivery_time' => $delivery->estimated_delivery_time,
                'picked_up_at' => $delivery->picked_up_at,
                'delivered_at' => $delivery->delivered_at,
                'delivery_fee' => $delivery->delivery_fee,
                'distance_km' => $delivery->distance_km,
                'driver_location' => $delivery->driver_location ? [
                    'latitude' => $delivery->driver_location['latitude'] ?? null,
                    'longitude' => $delivery->driver_location['longitude'] ?? null,
                    'updated_at' => $delivery->location_updated_at,
                ] : null,
                'delivery_instructions' => $delivery->delivery_instructions,
                'created_at' => $delivery->created_at,
            ];

            return $this->successResponse($data, 'Delivery details retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve delivery details', $e, [
                'delivery_id' => $delivery->id,
                'business_id' => $business->id ?? null,
            ]);

            return $this->errorResponse('Failed to retrieve delivery details', 500);
        }
    }

    /**
     * Cancel a delivery
     *
     * @group Business Delivery
     *
     * @authenticated
     *
     * @header Authorization Bearer {token}
     *
     * @urlParam delivery string required The delivery ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam reason string required Cancellation reason. Example: Customer requested cancellation
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Delivery cancelled successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "status": "cancelled",
     *     "cancelled_at": "2024-01-22T15:00:00Z",
     *     "cancellation_reason": "Customer requested cancellation"
     *   }
     * }
     */
    public function cancel(Request $request, Delivery $delivery): JsonResponse
    {
        $request->validate([
            'reason' => 'required|string|max:500',
        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();

            // Ensure delivery belongs to the business
            if ($delivery->business_id !== $business->id) {
                return $this->errorResponse('Delivery not found', 404);
            }

            $success = $this->deliveryService->cancelDelivery(
                $delivery,
                $request->string('reason')->value(),
                'business'
            );

            if (! $success) {
                return $this->errorResponse('Failed to cancel delivery', 500);
            }

            $data = [
                'id' => $delivery->id,
                'status' => $delivery->fresh()->status->value,
                'cancelled_at' => $delivery->fresh()->cancelled_at,
                'cancellation_reason' => $delivery->fresh()->cancellation_reason,
            ];

            return $this->successResponse($data, 'Delivery cancelled successfully');

        } catch (\App\Exceptions\BusinessLogicException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to cancel delivery', $e, [
                'delivery_id' => $delivery->id,
                'business_id' => $business->id ?? null,
                'reason' => $request->string('reason')->value(),
            ]);

            return $this->errorResponse('Failed to cancel delivery', 500);
        }
    }

    /**
     * Get real-time tracking for a delivery.
     *
     * @group Business Delivery
     *
     * @authenticated
     *
     * @urlParam delivery string required The delivery ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @queryParam include_history boolean Include location history. Example: true
     * @queryParam history_limit integer Number of history points to include (max 100). Example: 20
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Real-time tracking retrieved successfully",
     *   "data": {
     *     "delivery_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "status": "en_route",
     *     "current_location": {
     *       "latitude": 6.5244,
     *       "longitude": 3.3792,
     *       "accuracy": 10.5,
     *       "speed": 45.2,
     *       "heading": 180.0,
     *       "timestamp": "2024-01-15T14:30:00Z"
     *     },
     *     "driver": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *       "name": "John Doe",
     *       "phone": "+234801234567",
     *       "vehicle": "Honda CRV - ABC123"
     *     },
     *     "estimated_arrival": "2024-01-15T15:30:00Z",
     *     "distance_remaining_km": 2.5,
     *     "route_progress": 75.5,
     *     "location_history": [
     *       {
     *         "latitude": 6.5200,
     *         "longitude": 3.3750,
     *         "timestamp": "2024-01-15T14:25:00Z"
     *       }
     *     ],
     *     "last_updated": "2024-01-15T14:30:00Z"
     *   }
     * }
     */
    public function getRealTimeTracking(string $delivery, Request $request): JsonResponse
    {
        $request->validate([
            'include_history' => 'sometimes|boolean',
            'history_limit' => 'sometimes|integer|min:1|max:100',
        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();

            // Check subscription tier for real-time tracking access
            if (! $business->subscription || ! in_array($business->subscription->tier, ['business', 'enterprise'])) {
                return $this->errorResponse('Real-time tracking requires Business+ subscription', 403);
            }

            $deliveryModel = Delivery::where('id', $delivery)
                ->where('business_id', $business->id)
                ->with(['driver', 'order'])
                ->firstOrFail();

            // Get current location from tracking service
            $currentLocation = $this->trackingService->getCurrentDeliveryLocation($delivery);

            if (! $currentLocation) {
                return $this->errorResponse('No tracking data available for this delivery', 404);
            }

            $trackingData = [
                'delivery_id' => $delivery,
                'status' => $deliveryModel->status->value,
                'current_location' => [
                    'latitude' => (float) $currentLocation['latitude'],
                    'longitude' => (float) $currentLocation['longitude'],
                    'accuracy' => $currentLocation['accuracy'] ? (float) $currentLocation['accuracy'] : null,
                    'speed' => $currentLocation['speed'] ? (float) $currentLocation['speed'] : null,
                    'heading' => $currentLocation['heading'] ? (float) $currentLocation['heading'] : null,
                    'timestamp' => \Carbon\Carbon::createFromTimestamp($currentLocation['timestamp'])->toISOString(),
                ],
                'driver' => $deliveryModel->driver ? [
                    'id' => $deliveryModel->driver->id,
                    'name' => $deliveryModel->driver->first_name.' '.$deliveryModel->driver->last_name,
                    'phone' => $deliveryModel->driver->phone,
                    'vehicle' => ($deliveryModel->driver->vehicle_type ?? 'Vehicle').' - '.($deliveryModel->driver->vehicle_plate_number ?? 'N/A'),
                ] : null,
                'estimated_arrival' => $deliveryModel->estimated_delivery_time?->toISOString(),
                'distance_remaining_km' => $this->calculateRemainingDistance($currentLocation, $deliveryModel),
                'route_progress' => $this->calculateRouteProgress($deliveryModel, $currentLocation),
                'last_updated' => \Carbon\Carbon::createFromTimestamp($currentLocation['timestamp'])->toISOString(),
            ];

            // Include location history if requested
            if ($request->boolean('include_history', false)) {
                $historyLimit = $request->integer('history_limit', 20);
                $history = $this->trackingService->getDeliveryTrackingHistory($delivery, $historyLimit);

                $trackingData['location_history'] = array_map(function ($point) {
                    return [
                        'latitude' => (float) $point['latitude'],
                        'longitude' => (float) $point['longitude'],
                        'timestamp' => \Carbon\Carbon::createFromTimestamp($point['timestamp'])->toISOString(),
                    ];
                }, $history);
            }

            return $this->successResponse($trackingData, 'Real-time tracking retrieved successfully');

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->errorResponse('Delivery not found', 404);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve real-time tracking', $e, [
                'business_id' => $business->id ?? null,
                'delivery_id' => $delivery,
            ]);

            return $this->errorResponse('Failed to retrieve tracking data', 500);
        }
    }

    /**
     * Get detailed tracking history for a delivery.
     *
     * @group Business Delivery
     *
     * @authenticated
     *
     * @urlParam delivery string required The delivery ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @queryParam limit integer Number of history points to retrieve (max 500). Example: 100
     * @queryParam date_from string Filter history from date (Y-m-d H:i:s). Example: 2024-01-15 10:00:00
     * @queryParam date_to string Filter history to date (Y-m-d H:i:s). Example: 2024-01-15 16:00:00
     * @queryParam include_events boolean Include delivery events in timeline. Example: true
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Detailed tracking history retrieved successfully",
     *   "data": {
     *     "delivery_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "total_points": 150,
     *     "total_distance_km": 12.5,
     *     "tracking_duration_minutes": 45,
     *     "location_history": [
     *       {
     *         "latitude": 6.5244,
     *         "longitude": 3.3792,
     *         "accuracy": 10.5,
     *         "speed": 45.2,
     *         "heading": 180.0,
     *         "timestamp": "2024-01-15T14:30:00Z",
     *         "address": "Victoria Island, Lagos"
     *       }
     *     ],
     *     "events": [
     *       {
     *         "type": "status_change",
     *         "description": "Delivery status changed to en_route",
     *         "timestamp": "2024-01-15T14:00:00Z",
     *         "location": {
     *           "latitude": 6.5200,
     *           "longitude": 3.3750
     *         }
     *       }
     *     ],
     *     "statistics": {
     *       "average_speed_kmh": 35.2,
     *       "max_speed_kmh": 60.0,
     *       "stops_count": 2,
     *       "total_stop_duration_minutes": 8
     *     }
     *   }
     * }
     */
    public function getDetailedHistory(string $delivery, Request $request): JsonResponse
    {
        $request->validate([
            'limit' => 'sometimes|integer|min:1|max:500',
            'date_from' => 'sometimes|date_format:Y-m-d H:i:s',
            'date_to' => 'sometimes|date_format:Y-m-d H:i:s|after:date_from',
            'include_events' => 'sometimes|boolean',
        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();

            // Check subscription tier for detailed history access
            if (! $business->subscription || ! in_array($business->subscription->tier, ['business', 'enterprise'])) {
                return $this->errorResponse('Detailed tracking history requires Business+ subscription', 403);
            }

            $deliveryModel = Delivery::where('id', $delivery)
                ->where('business_id', $business->id)
                ->firstOrFail();

            $limit = $request->integer('limit', 100);

            // Get location history from database
            $locationQuery = DeliveryLocation::where('delivery_id', $delivery)
                ->orderBy('recorded_at', 'desc');

            if ($request->has('date_from')) {
                $locationQuery->where('recorded_at', '>=', $request->input('date_from'));
            }

            if ($request->has('date_to')) {
                $locationQuery->where('recorded_at', '<=', $request->input('date_to'));
            }

            $locations = $locationQuery->limit($limit)->get();

            if ($locations->isEmpty()) {
                return $this->errorResponse('No tracking history found for this delivery', 404);
            }

            // Calculate statistics
            $statistics = $this->calculateTrackingStatistics($locations);

            $historyData = [
                'delivery_id' => $delivery,
                'total_points' => $locations->count(),
                'total_distance_km' => $statistics['total_distance'],
                'tracking_duration_minutes' => $statistics['duration_minutes'],
                'location_history' => $locations->map(function ($location) {
                    return [
                        'latitude' => (float) $location->latitude,
                        'longitude' => (float) $location->longitude,
                        'accuracy' => $location->accuracy ? (float) $location->accuracy : null,
                        'speed' => $location->speed ? (float) $location->speed : null,
                        'heading' => $location->heading ? (float) $location->heading : null,
                        'timestamp' => $location->recorded_at->toISOString(),
                        'address' => $this->reverseGeocode($location->latitude, $location->longitude),
                    ];
                })->toArray(),
                'statistics' => $statistics,
            ];

            // Include events if requested
            if ($request->boolean('include_events', false)) {
                $historyData['events'] = $this->getDeliveryEvents($delivery);
            }

            return $this->successResponse($historyData, 'Detailed tracking history retrieved successfully');

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->errorResponse('Delivery not found', 404);
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve detailed tracking history', $e, [
                'business_id' => $business->id ?? null,
                'delivery_id' => $delivery,
            ]);

            return $this->errorResponse('Failed to retrieve tracking history', 500);
        }
    }

    /**
     * Subscribe to delivery tracking updates via webhooks.
     *
     * @group Business Delivery
     *
     * @authenticated
     *
     * @bodyParam webhook_url string required The webhook URL to receive updates. Example: https://your-app.com/webhooks/delivery-tracking
     * @bodyParam events array required Array of events to subscribe to. Example: ["location_updated", "status_changed"]
     * @bodyParam webhook_secret string Webhook secret for signature verification. Example: your-secret-key
     * @bodyParam delivery_filters object Filters for which deliveries to track. Example: {"statuses": ["en_route", "picked_up"]}
     * @bodyParam update_frequency string Update frequency (real_time, every_30_seconds, every_minute). Example: every_minute
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Webhook subscription created successfully",
     *   "data": {
     *     "subscription_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "webhook_url": "https://your-app.com/webhooks/delivery-tracking",
     *     "events": ["location_updated", "status_changed"],
     *     "status": "active",
     *     "created_at": "2024-01-15T14:30:00Z",
     *     "test_webhook_sent": true
     *   }
     * }
     */
    public function subscribeToUpdates(TrackingSubscriptionRequest $request): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            // Check subscription tier for webhook access
            if (! $business->subscription || $business->subscription->tier !== 'enterprise') {
                return $this->errorResponse('Webhook subscriptions require Enterprise subscription', 403);
            }

            $validated = $request->validated();

            // Create webhook subscription record
            $subscription = $this->createWebhookSubscription($business, $validated);

            // Test webhook if requested
            $testWebhookSent = false;
            if ($request->boolean('test_webhook', true)) {
                $testWebhookSent = $this->sendTestWebhook($subscription);
            }

            $this->loggingService->logInfo('Webhook subscription created', [
                'business_id' => $business->id,
                'subscription_id' => $subscription['id'],
                'webhook_url' => $validated['webhook_url'],
                'events' => $validated['events'],
            ]);

            return $this->successResponse([
                'subscription_id' => $subscription['id'],
                'webhook_url' => $validated['webhook_url'],
                'events' => $validated['events'],
                'status' => 'active',
                'created_at' => now()->toISOString(),
                'test_webhook_sent' => $testWebhookSent,
            ], 'Webhook subscription created successfully', 201);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to create webhook subscription', $e, [
                'business_id' => $business->id ?? null,
                'webhook_url' => $request->input('webhook_url'),
            ]);

            return $this->errorResponse('Failed to create webhook subscription', 500);
        }
    }

    // Helper methods for tracking functionality

    /**
     * Calculate remaining distance to destination.
     */
    private function calculateRemainingDistance(array $currentLocation, Delivery $delivery): float
    {
        // This would calculate the actual remaining distance using the delivery address
        // For now, return a placeholder value
        return 2.5;
    }

    /**
     * Calculate route progress percentage.
     */
    private function calculateRouteProgress(Delivery $delivery, array $currentLocation): float
    {
        // This would calculate the actual progress based on route and current location
        // For now, return a placeholder value
        return 75.5;
    }

    /**
     * Calculate tracking statistics from location history.
     */
    private function calculateTrackingStatistics($locations): array
    {
        if ($locations->isEmpty()) {
            return [
                'total_distance' => 0,
                'duration_minutes' => 0,
                'average_speed_kmh' => 0,
                'max_speed_kmh' => 0,
                'stops_count' => 0,
                'total_stop_duration_minutes' => 0,
            ];
        }

        $totalDistance = 0;
        $speeds = [];
        $stops = 0;
        $stopDuration = 0;

        // Calculate distance between consecutive points
        for ($i = 1; $i < $locations->count(); $i++) {
            $prev = $locations[$i];
            $current = $locations[$i - 1];

            $distance = $this->calculateDistanceBetweenPoints(
                (float) $prev->latitude,
                (float) $prev->longitude,
                (float) $current->latitude,
                (float) $current->longitude
            );

            $totalDistance += $distance;

            if ($current->speed !== null) {
                $speeds[] = (float) $current->speed;

                // Detect stops (speed < 5 km/h)
                if ($current->speed < 5) {
                    $stops++;
                    $timeDiff = $current->recorded_at->diffInMinutes($prev->recorded_at);
                    $stopDuration += $timeDiff;
                }
            }
        }

        $firstLocation = $locations->last();
        $lastLocation = $locations->first();
        $durationMinutes = $firstLocation->recorded_at->diffInMinutes($lastLocation->recorded_at);

        return [
            'total_distance' => round($totalDistance, 2),
            'duration_minutes' => $durationMinutes,
            'average_speed_kmh' => ! empty($speeds) ? round(array_sum($speeds) / count($speeds), 1) : 0,
            'max_speed_kmh' => ! empty($speeds) ? round(max($speeds), 1) : 0,
            'stops_count' => $stops,
            'total_stop_duration_minutes' => $stopDuration,
        ];
    }

    /**
     * Calculate distance between two geographic points.
     */
    private function calculateDistanceBetweenPoints(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        $earthRadius = 6371; // Earth's radius in kilometers

        $dLat = deg2rad($lat2 - $lat1);
        $dLon = deg2rad($lon2 - $lon1);

        $a = sin($dLat / 2) * sin($dLat / 2) +
             cos(deg2rad($lat1)) * cos(deg2rad($lat2)) *
             sin($dLon / 2) * sin($dLon / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Reverse geocode coordinates to address.
     */
    private function reverseGeocode(float $latitude, float $longitude): ?string
    {
        // This would use a geocoding service to convert coordinates to address
        // For now, return a placeholder
        return 'Lagos, Nigeria';
    }

    /**
     * Get delivery events timeline.
     */
    private function getDeliveryEvents(string $deliveryId): array
    {
        // This would fetch delivery events from the database
        // For now, return placeholder data
        return [
            [
                'type' => 'status_change',
                'description' => 'Delivery status changed to en_route',
                'timestamp' => now()->subMinutes(30)->toISOString(),
                'location' => [
                    'latitude' => 6.5200,
                    'longitude' => 3.3750,
                ],
            ],
            [
                'type' => 'driver_assigned',
                'description' => 'Driver assigned to delivery',
                'timestamp' => now()->subHour()->toISOString(),
                'location' => null,
            ],
        ];
    }

    /**
     * Create webhook subscription record.
     */
    private function createWebhookSubscription($business, array $data): array
    {
        // This would create a webhook subscription in the database
        // For now, return placeholder data
        return [
            'id' => \Illuminate\Support\Str::uuid(),
            'business_id' => $business->id,
            'webhook_url' => $data['webhook_url'],
            'events' => $data['events'],
            'status' => 'active',
            'created_at' => now(),
        ];
    }

    /**
     * Send test webhook to verify endpoint.
     */
    private function sendTestWebhook(array $subscription): bool
    {
        try {
            // This would send a test webhook to the provided URL
            // For now, return true to indicate success
            return true;
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send test webhook', $e, [
                'subscription_id' => $subscription['id'],
                'webhook_url' => $subscription['webhook_url'],
            ]);

            return false;
        }
    }
}
