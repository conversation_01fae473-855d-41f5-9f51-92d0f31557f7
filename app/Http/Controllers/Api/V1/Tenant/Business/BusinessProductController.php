<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Business;

use App\Exceptions\BusinessLogicException;
use App\Http\Controllers\Controller;
use App\Models\Business\Product;
use App\Models\Business\ProductCategory;
use App\Services\Business\BusinessService;
use App\Services\System\ImageStorageService;
use App\Services\System\LoggingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

/**
 * Business Product Controller
 *
 * Manages products for business owners within their tenant context.
 * Provides CRUD operations, inventory management, and product analytics.
 */
class BusinessProductController extends Controller
{
    use \App\Traits\ApiResponseTrait, \App\Traits\QueryHandlerTrait;

    public function __construct(
        private readonly BusinessService $businessService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Display a listing of products for the business.
     *
     * @group Business Products
     *
     * @authenticated
     *
     * @queryParam page int Page number for pagination. Example: 1
     * @queryParam per_page int Number of items per page (max 100). Example: 15
     * @queryParam search string Search in product name, description, SKU. Example: Jollof
     * @queryParam category_id string Filter by category ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     * @queryParam is_available boolean Filter by availability. Example: true
     * @queryParam in_stock boolean Filter by stock status. Example: true
     * @queryParam price_min float Minimum price filter. Example: 1000
     * @queryParam price_max float Maximum price filter. Example: 5000
     * @queryParam sort_by string Sort by field (name, price, quantity, created_at). Example: name
     * @queryParam sort_direction string Sort direction (asc, desc). Example: asc
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Products retrieved successfully",
     *   "data": {
     *     "products": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *         "name": "Jollof Rice",
     *         "slug": "jollof-rice",
     *         "description": "Delicious Nigerian jollof rice",
     *         "price": 2500,
     *         "sale_price": null,
     *         "effective_price": 2500,
     *         "sku": "JR001",
     *         "quantity": 50,
     *         "is_available": true,
     *         "is_in_stock": true,
     *         "main_image_url": "https://example.com/jollof.jpg",
     *         "category": {
     *           "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *           "name": "Main Dishes"
     *         },
     *         "average_rating": 4.5,
     *         "total_ratings": 25,
     *         "created_at": "2024-01-15T10:30:00Z"
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 15,
     *       "total": 50,
     *       "last_page": 4
     *     }
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100',
            'search' => 'sometimes|string|max:255',
            'category_id' => 'sometimes|string|exists:product_categories,id',
            'is_available' => 'sometimes|boolean',
            'in_stock' => 'sometimes|boolean',
            'price_min' => 'sometimes|numeric|min:0',
            'price_max' => 'sometimes|numeric|min:0|gte:price_min',
            'sort_by' => 'sometimes|string|in:name,price,quantity,created_at',
            'sort_direction' => 'sometimes|string|in:asc,desc',
        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();

            $query = Product::where('business_id', $business->id)
                ->with(['category', 'ratings']);

            $result = $this->handleQuery($query, $request, [
                'searchFields' => [
                    'name',
                    'description',
                    'sku',
                    'category.name',
                ],
                'sortFields' => [
                    'name',
                    'price',
                    'quantity',
                    'created_at',
                ],
                'filters' => [
                    'category_id' => ['type' => 'exact'],
                    'is_available' => ['type' => 'boolean'],
                    'in_stock' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            if (filter_var($value, FILTER_VALIDATE_BOOLEAN)) {
                                $query->where('quantity', '>', 0);
                            } else {
                                $query->where('quantity', '<=', 0);
                            }
                        },
                    ],
                    'price_min' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->where('price', '>=', $value);
                        },
                    ],
                    'price_max' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            $query->where('price', '<=', $value);
                        },
                    ],
                ],
                'message' => 'Products retrieved successfully',
                'entityName' => 'products',
                'transformer' => [$this, 'transformProduct'],
            ]);

            // Convert to expected format for backward compatibility
            $resultData = json_decode($result->getContent(), true);
            if (isset($resultData['data']['data'])) {
                $transformedData = [
                    'products' => $resultData['data']['data'],
                    'pagination' => [
                        'current_page' => $resultData['data']['current_page'],
                        'per_page' => $resultData['data']['per_page'],
                        'total' => $resultData['data']['total'],
                        'last_page' => $resultData['data']['last_page'],
                    ],
                ];

                return $this->successResponse(
                    $transformedData,
                    'Products retrieved successfully'
                );
            }

            return $result;

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve products',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'business_id' => $business->id ?? null,
                    'filters' => $request->only(['category_id', 'is_available', 'in_stock', 'price_min', 'price_max']),
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve products');
        }
    }

    /**
     * Store a newly created product.
     *
     * @group Business Products
     *
     * @authenticated
     *
     * @bodyParam name string required Product name. Example: Jollof Rice
     * @bodyParam description string Product description. Example: Delicious Nigerian jollof rice
     * @bodyParam price numeric required Product price. Example: 2500
     * @bodyParam cost_price numeric Cost price for profit calculation. Example: 1500
     * @bodyParam sale_price numeric Sale price if on discount. Example: 2000
     * @bodyParam sku string Product SKU. Example: JR001
     * @bodyParam barcode string Product barcode. Example: 1234567890123
     * @bodyParam quantity integer Stock quantity (-1 for unlimited). Example: 50
     * @bodyParam weight numeric Product weight in kg. Example: 0.5
     * @bodyParam category_id string required Category ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     * @bodyParam is_available boolean Product availability. Example: true
     * @bodyParam allows_pickup boolean Allow pickup orders. Example: true
     * @bodyParam preparation_time_minutes integer Preparation time in minutes. Example: 30
     * @bodyParam main_image_url string Main product image URL. Example: https://example.com/image.jpg
     * @bodyParam additional_images array Additional image URLs. Example: ["https://example.com/img1.jpg"]
     * @bodyParam tags array Product tags. Example: ["spicy", "popular"]
     * @bodyParam attributes object Product attributes. Example: {"spice_level": "medium", "serves": 2}
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Product created successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "Jollof Rice",
     *     "slug": "jollof-rice",
     *     "description": "Delicious Nigerian jollof rice",
     *     "price": 2500,
     *     "effective_price": 2500,
     *     "sku": "JR001",
     *     "quantity": 50,
     *     "is_available": true,
     *     "created_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'sometimes|string|max:2000',
            'price' => 'required|numeric|min:0',
            'cost_price' => 'sometimes|numeric|min:0',
            'sale_price' => 'sometimes|numeric|min:0|lt:price',
            'sku' => 'sometimes|string|max:100|unique:products,sku',
            'barcode' => 'sometimes|string|max:100|unique:products,barcode',
            'quantity' => 'sometimes|integer|min:-1',
            'weight' => 'sometimes|numeric|min:0',
            'category_id' => 'required|string|exists:product_categories,id',
            'is_available' => 'sometimes|boolean',
            'allows_pickup' => 'sometimes|boolean',
            'preparation_time_minutes' => 'sometimes|integer|min:1|max:300',
            'main_image_url' => 'sometimes|string|url|max:500',
            'main_image' => 'sometimes|image|mimes:jpeg,png,jpg,gif,webp,heif,heic|max:10240',
            'additional_images' => 'sometimes|array|max:10',
            'additional_images.*' => 'string|url|max:500',
            'tags' => 'sometimes|array|max:20',
            'tags.*' => 'string|max:50',
            'attributes' => 'sometimes|array',
        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();

            // Verify category belongs to business
            $category = ProductCategory::where('id', $request->input('category_id'))
                ->where('business_id', $business->id)
                ->first();

            if (! $category) {
                return $this->errorResponse('Category not found or does not belong to your business', 404);
            }

            // Generate slug
            $slug = Str::slug($request->input('name'));
            $originalSlug = $slug;
            $counter = 1;

            // Ensure unique slug
            while (Product::where('business_id', $business->id)->where('slug', $slug)->exists()) {
                $slug = $originalSlug.'-'.$counter;
                $counter++;
            }

            // Handle main image upload if provided
            $mainImageUrl = $request->input('main_image_url');
            if ($request->hasFile('main_image')) {
                try {
                    $imageService = app(ImageStorageService::class);
                    $result = $imageService->storeImage(
                        $request->file('main_image'),
                        'products',
                        null, // Will be updated with product ID after creation
                        [
                            'visibility' => 'public',
                            'disk' => 'r2',
                            'sizes' => ['thumbnail', 'small', 'medium', 'large'],
                        ]
                    );

                    // Use the large size URL as the main image
                    $mainImageUrl = $result['urls']['large']['webp'] ?? $result['urls']['large']['original'] ?? null;
                } catch (\Exception $e) {
                    $this->loggingService->logError('Failed to upload product image', $e, [
                        'tenant_id' => tenant()?->id,
                        'business_id' => $business->id,
                    ]);
                    // Continue without image if upload fails
                }
            }

            // Prepare product data
            $productData = [
                'tenant_id' => tenant()->id,
                'business_id' => $business->id,
                'name' => $request->input('name'),
                'slug' => $slug,
                'description' => $request->input('description'),
                'price' => $request->input('price'),
                'cost_price' => $request->input('cost_price'),
                'sale_price' => $request->input('sale_price'),
                'sku' => $request->input('sku'),
                'barcode' => $request->input('barcode'),
                'quantity' => $request->input('quantity', 0),
                'weight' => $request->input('weight'),
                'category_id' => $request->input('category_id'),
                'is_available' => $request->input('is_available', true),
                'allows_pickup' => $request->input('allows_pickup', true),
                'preparation_time_minutes' => $request->input('preparation_time_minutes', 30),
                'main_image_url' => $mainImageUrl,
                'additional_images' => $request->input('additional_images', []),
                'tags' => $request->input('tags', []),
                'attributes' => $request->input('attributes', []),
            ];

            // Create product
            $product = Product::create($productData);

            // Load relationships
            $product->load(['category']);

            $this->loggingService->logAudit(
                'product_created',
                'Product created successfully',
                [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'business_id' => $business->id,
                    'tenant_id' => tenant()?->id,
                ],
                auth()->id()
            );

            return $this->successResponse(
                $this->transformProduct($product),
                'Product created successfully',
                201
            );

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create product',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'business_id' => $business->id ?? null,
                    'product_data' => $request->only(['name', 'price', 'category_id']),
                ]
            );

            return $this->serverErrorResponse('Failed to create product');
        }
    }

    /**
     * Display the specified product.
     *
     * @group Business Products
     *
     * @authenticated
     *
     * @urlParam id string required The product ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Product retrieved successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "Jollof Rice",
     *     "slug": "jollof-rice",
     *     "description": "Delicious Nigerian jollof rice",
     *     "price": 2500,
     *     "cost_price": 1500,
     *     "sale_price": null,
     *     "effective_price": 2500,
     *     "profit_margin": 40.0,
     *     "sku": "JR001",
     *     "barcode": "1234567890123",
     *     "quantity": 50,
     *     "weight": 0.5,
     *     "is_available": true,
     *     "is_in_stock": true,
     *     "is_on_sale": false,
     *     "allows_pickup": true,
     *     "preparation_time_minutes": 30,
     *     "main_image_url": "https://example.com/jollof.jpg",
     *     "additional_images": ["https://example.com/img1.jpg"],
     *     "all_images": ["https://example.com/jollof.jpg", "https://example.com/img1.jpg"],
     *     "category": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *       "name": "Main Dishes",
     *       "slug": "main-dishes"
     *     },
     *     "tags": ["spicy", "popular"],
     *     "attributes": {"spice_level": "medium", "serves": 2},
     *     "average_rating": 4.5,
     *     "total_ratings": 25,
     *     "created_at": "2024-01-15T10:30:00Z",
     *     "updated_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function show(string $id): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            $product = Product::where('business_id', $business->id)
                ->where('id', $id)
                ->with(['category', 'ratings', 'variants', 'optionGroups'])
                ->first();

            if (! $product) {
                return $this->errorResponse('Product not found', 404);
            }

            return $this->successResponse(
                $this->transformProduct($product, true), // detailed view
                'Product retrieved successfully'
            );

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve product',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'product_id' => $id,
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve product');
        }
    }

    /**
     * Update the specified product.
     *
     * @group Business Products
     *
     * @authenticated
     *
     * @urlParam id string required The product ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam name string Product name. Example: Jollof Rice Special
     * @bodyParam description string Product description. Example: Updated description
     * @bodyParam price numeric Product price. Example: 2800
     * @bodyParam cost_price numeric Cost price. Example: 1600
     * @bodyParam sale_price numeric Sale price. Example: 2400
     * @bodyParam quantity integer Stock quantity. Example: 75
     * @bodyParam is_available boolean Product availability. Example: false
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Product updated successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "Jollof Rice Special",
     *     "price": 2800,
     *     "updated_at": "2024-01-15T11:00:00Z"
     *   }
     * }
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $request->validate([
            'name' => 'sometimes|string|max:255',
            'description' => 'sometimes|string|max:2000',
            'price' => 'sometimes|numeric|min:0',
            'cost_price' => 'sometimes|numeric|min:0',
            'sale_price' => 'sometimes|numeric|min:0',
            'sku' => 'sometimes|string|max:100|unique:products,sku,'.$id,
            'barcode' => 'sometimes|string|max:100|unique:products,barcode,'.$id,
            'quantity' => 'sometimes|integer|min:-1',
            'weight' => 'sometimes|numeric|min:0',
            'category_id' => 'sometimes|string|exists:product_categories,id',
            'is_available' => 'sometimes|boolean',
            'allows_pickup' => 'sometimes|boolean',
            'preparation_time_minutes' => 'sometimes|integer|min:1|max:300',
            'main_image_url' => 'sometimes|string|url|max:500',
            'main_image' => 'sometimes|image|mimes:jpeg,png,jpg,gif,webp,heif,heic|max:10240',
            'additional_images' => 'sometimes|array|max:10',
            'additional_images.*' => 'string|url|max:500',
            'tags' => 'sometimes|array|max:20',
            'tags.*' => 'string|max:50',
            'attributes' => 'sometimes|array',
        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();

            $product = Product::where('business_id', $business->id)
                ->where('id', $id)
                ->first();

            if (! $product) {
                return $this->errorResponse('Product not found', 404);
            }

            // Verify category belongs to business if provided
            if ($request->filled('category_id')) {
                $category = ProductCategory::where('id', $request->input('category_id'))
                    ->where('business_id', $business->id)
                    ->first();

                if (! $category) {
                    return $this->errorResponse('Category not found or does not belong to your business', 404);
                }
            }

            // Handle main image upload if provided
            $mainImageUrl = $request->input('main_image_url');
            if ($request->hasFile('main_image')) {
                try {
                    $imageService = app(ImageStorageService::class);
                    $result = $imageService->storeImage(
                        $request->file('main_image'),
                        'products',
                        $product->id,
                        [
                            'visibility' => 'public',
                            // Let ImageStorageService auto-select disk
                            'sizes' => ['thumbnail', 'small', 'medium', 'large'],
                        ]
                    );

                    // Use the large size URL as the main image
                    $mainImageUrl = $result['urls']['large']['webp'] ?? $result['urls']['large']['original'] ?? null;
                } catch (\Exception $e) {
                    $this->loggingService->logError('Failed to upload product image during update', $e, [
                        'tenant_id' => tenant()?->id,
                        'product_id' => $product->id,
                    ]);
                    // Continue without image if upload fails
                }
            }

            // Update slug if name changed
            $updateData = $request->only([
                'name', 'description', 'price', 'cost_price', 'sale_price',
                'sku', 'barcode', 'quantity', 'weight', 'category_id',
                'is_available', 'allows_pickup', 'preparation_time_minutes',
                'additional_images', 'tags', 'attributes',
            ]);

            // Add the main image URL (either from upload or from request)
            if ($mainImageUrl !== null) {
                $updateData['main_image_url'] = $mainImageUrl;
            }

            if ($request->filled('name') && $request->input('name') !== $product->name) {
                $slug = Str::slug($request->input('name'));
                $originalSlug = $slug;
                $counter = 1;

                // Ensure unique slug (excluding current product)
                while (Product::where('business_id', $business->id)
                    ->where('slug', $slug)
                    ->where('id', '!=', $id)
                    ->exists()) {
                    $slug = $originalSlug.'-'.$counter;
                    $counter++;
                }

                $updateData['slug'] = $slug;
            }

            // Validate sale price vs price
            if ($request->filled('sale_price') && $request->filled('price')) {
                if ($request->input('sale_price') >= $request->input('price')) {
                    return $this->errorResponse('Sale price must be less than regular price', 422);
                }
            } elseif ($request->filled('sale_price') && $request->input('sale_price') >= $product->price) {
                return $this->errorResponse('Sale price must be less than regular price', 422);
            }

            // Update product
            $product->update($updateData);

            // Load relationships
            $product->load(['category']);

            $this->loggingService->logAudit(
                'product_updated',
                'Product updated successfully',
                [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'business_id' => $business->id,
                    'tenant_id' => tenant()?->id,
                    'updated_fields' => array_keys($updateData),
                ],
                auth()->id()
            );

            return $this->successResponse(
                $this->transformProduct($product),
                'Product updated successfully'
            );

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update product',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'product_id' => $id,
                    'update_data' => $request->only(['name', 'price', 'quantity']),
                ]
            );

            return $this->serverErrorResponse('Failed to update product');
        }
    }

    /**
     * Remove the specified product.
     *
     * @group Business Products
     *
     * @authenticated
     *
     * @urlParam id string required The product ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Product deleted successfully",
     *   "data": null
     * }
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            $product = Product::where('business_id', $business->id)
                ->where('id', $id)
                ->first();

            if (! $product) {
                return $this->errorResponse('Product not found', 404);
            }

            // Check if product has orders (prevent deletion if it has order history)
            if ($product->orderItems()->exists()) {
                return $this->errorResponse(
                    'Cannot delete product that has order history. Consider marking it as unavailable instead.',
                    422
                );
            }

            $productName = $product->name;

            // Delete the product
            $product->delete();

            $this->loggingService->logAudit(
                'product_deleted',
                'Product deleted successfully',
                [
                    'product_id' => $id,
                    'product_name' => $productName,
                    'business_id' => $business->id,
                    'tenant_id' => tenant()?->id,
                ],
                auth()->id()
            );

            return $this->successResponse(
                null,
                'Product deleted successfully'
            );

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to delete product',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'product_id' => $id,
                ]
            );

            return $this->serverErrorResponse('Failed to delete product');
        }
    }

    /**
     * Upload product image.
     *
     * @group Business Products
     *
     * @authenticated
     *
     * @urlParam id string required The product ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam image file required Product image (max 10MB, JPEG/PNG/GIF/WebP/HEIF/HEIC)
     * @bodyParam type string Image type (main or additional). Example: main
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Product image uploaded successfully",
     *   "data": {
     *     "image_url": "https://cdn.example.com/products/large/image.webp",
     *     "variants": {
     *       "thumbnail": "https://cdn.example.com/products/thumbnail/image.webp",
     *       "small": "https://cdn.example.com/products/small/image.webp",
     *       "medium": "https://cdn.example.com/products/medium/image.webp",
     *       "large": "https://cdn.example.com/products/large/image.webp"
     *     }
     *   }
     * }
     */
    public function uploadImage(Request $request, string $id): JsonResponse
    {
        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif,webp,heif,heic|max:10240',
            'type' => 'sometimes|string|in:main,additional',
        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();

            $product = Product::where('business_id', $business->id)
                ->where('id', $id)
                ->first();

            if (! $product) {
                return $this->errorResponse('Product not found', 404);
            }

            // Upload image using ImageStorageService
            $imageService = app(ImageStorageService::class);
            $result = $imageService->storeImage(
                $request->file('image'),
                'products',
                $product->id,
                [
                    'visibility' => 'public',
                    // Let ImageStorageService auto-select disk
                    'sizes' => ['thumbnail', 'small', 'medium', 'large'],
                ]
            );

            $imageType = $request->input('type', 'main');
            $imageUrl = $result['urls']['large']['webp'] ?? $result['urls']['large']['original'] ?? null;

            if ($imageType === 'main') {
                // Update main image
                $product->update(['main_image_url' => $imageUrl]);
            } else {
                // Add to additional images
                $additionalImages = $product->additional_images ?? [];
                $additionalImages[] = $imageUrl;
                $product->update(['additional_images' => $additionalImages]);
            }

            $this->loggingService->logAudit(
                'product_image_uploaded',
                'Product image uploaded successfully',
                [
                    'product_id' => $product->id,
                    'business_id' => $business->id,
                    'tenant_id' => tenant()?->id,
                    'image_type' => $imageType,
                    'storage_disk' => $result['storage_disk'],
                ],
                auth()->id()
            );

            return $this->successResponse([
                'image_url' => $imageUrl,
                'variants' => $result['urls'],
                'type' => $imageType,
            ], 'Product image uploaded successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to upload product image', $e, [
                'tenant_id' => tenant()?->id,
                'product_id' => $id,
            ]);

            return $this->serverErrorResponse('Failed to upload product image');
        }
    }

    // Helper Methods

    /**
     * Transform product data for API response.
     */
    private function transformProduct(Product $product, bool $detailed = false): array
    {
        $data = [
            'id' => $product->id,
            'name' => $product->name,
            'slug' => $product->slug,
            'description' => $product->description,
            'price' => (float) $product->price,
            'sale_price' => $product->sale_price ? (float) $product->sale_price : null,
            'effective_price' => $product->getEffectivePrice(),
            'sku' => $product->sku,
            'quantity' => $product->quantity,
            'is_available' => $product->is_available,
            'is_in_stock' => $product->isInStock(),
            'is_on_sale' => $product->isOnSale(),
            'main_image_url' => $product->main_image_url,
            'category' => $product->category ? [
                'id' => $product->category->id,
                'name' => $product->category->name,
                'slug' => $product->category->slug,
            ] : null,
            'average_rating' => $product->getAverageRating(),
            'total_ratings' => $product->getTotalRatings(),
            'created_at' => $product->created_at->toISOString(),
        ];

        if ($detailed) {
            $data = array_merge($data, [
                'cost_price' => $product->cost_price ? (float) $product->cost_price : null,
                'profit_margin' => $product->getProfitMargin(),
                'barcode' => $product->barcode,
                'weight' => $product->weight ? (float) $product->weight : null,
                'allows_pickup' => $product->allows_pickup,
                'preparation_time_minutes' => $product->preparation_time_minutes,
                'additional_images' => $product->additional_images ?? [],
                'all_images' => $product->getAllImages(),
                'tags' => $product->tags ?? [],
                'attributes' => $product->attributes ?? [],
                'discount_percentage' => $product->getDiscountPercentage(),
                'updated_at' => $product->updated_at->toISOString(),
            ]);
        }

        return $data;
    }
}
