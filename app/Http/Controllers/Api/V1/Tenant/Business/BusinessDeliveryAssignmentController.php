<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Business;

use App\Http\Controllers\Controller;
use App\Models\Delivery\Order;
use App\Services\Business\BusinessService;
use App\Services\Delivery\FirstToAcceptService;
use App\Services\Delivery\ProviderBroadcastService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

/**
 * @group Business Delivery Assignment
 *
 * APIs for businesses to manage delivery assignments
 */
class BusinessDeliveryAssignmentController extends Controller
{
    use \App\Traits\ApiResponseTrait;

    public function __construct(
        private readonly BusinessService $businessService,
        private readonly FirstToAcceptService $firstToAcceptService,
        private readonly ProviderBroadcastService $providerBroadcastService
    ) {}

    /**
     * Initiate first-to-accept assignment for an order.
     *
     * @group Business Delivery Assignment
     *
     * @authenticated
     *
     * @urlParam orderId string required The order ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam max_providers integer Maximum number of providers to notify. Example: 5
     * @bodyParam timeout_minutes integer Timeout in minutes for provider responses. Example: 10
     * @bodyParam priority string Assignment priority (normal, high, urgent). Example: normal
     *
     * @response 200 {
     *   "success": true,
     *   "message": "First-to-accept assignment initiated successfully",
     *   "data": {
     *     "assignment_type": "first_to_accept",
     *     "broadcast_id": "broadcast_019723aa_1640995200",
     *     "providers_notified": 5,
     *     "timeout_at": "2024-01-15T12:30:00Z",
     *     "status": "waiting_for_acceptance"
     *   }
     * }
     */
    public function initiateFirstToAccept(Request $request, string $orderId): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            // Validate that the order belongs to the current business
            $order = $business->orders()->findOrFail($orderId);

            $request->validate([
                'max_providers' => 'sometimes|integer|min:1|max:10',
                'timeout_minutes' => 'sometimes|integer|min:5|max:60',
                'priority' => 'sometimes|string|in:normal,high,urgent',
            ]);

            $options = [
                'max_providers' => $request->input('max_providers', 5),
                'timeout_minutes' => $request->input('timeout_minutes', 10),
                'priority' => $request->input('priority', 'normal'),
            ];

            $result = $this->firstToAcceptService->initiateFirstToAccept($order, $options);

            if ($result['success']) {
                return $this->successResponse(
                    $result,
                    'First-to-accept assignment initiated successfully'
                );
            } else {
                return $this->errorResponse($result['error'], 422);
            }

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Order not found');
        } catch (ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to initiate first-to-accept assignment', 500);
        }
    }

    /**
     * Get assignment status for an order.
     *
     * @group Business Delivery Assignment
     *
     * @authenticated
     *
     * @urlParam orderId string required The order ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Assignment status retrieved successfully",
     *   "data": {
     *     "order_id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "status": "waiting_for_acceptance",
     *     "broadcast_id": "broadcast_019723aa_1640995200",
     *     "providers_count": 5,
     *     "timeout_at": "2024-01-15T12:30:00Z",
     *     "time_remaining_seconds": 450,
     *     "requests": [
     *       {
     *         "provider_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *         "provider_name": "FastDelivery Lagos",
     *         "status": "pending",
     *         "sent_at": "2024-01-15T12:20:00Z"
     *       }
     *     ]
     *   }
     * }
     */
    public function getAssignmentStatus(string $orderId): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            // Validate that the order belongs to the current business
            $order = $business->orders()->with(['deliveryRequests.provider'])->findOrFail($orderId);

            // Get the latest broadcast for this order
            $latestBroadcast = $order->deliveryRequests()
                ->orderBy('created_at', 'desc')
                ->first();

            if (! $latestBroadcast) {
                return $this->successResponse([
                    'order_id' => $orderId,
                    'status' => 'no_assignment_initiated',
                    'message' => 'No delivery assignment has been initiated for this order',
                ], 'Assignment status retrieved successfully');
            }

            $broadcastId = $latestBroadcast->broadcast_id;
            $requests = $order->deliveryRequests()
                ->where('broadcast_id', $broadcastId)
                ->with('provider')
                ->get();

            $acceptedRequest = $requests->where('status', 'accepted')->first();
            $timeoutAt = $requests->first()?->expires_at;

            $status = 'unknown';
            if ($acceptedRequest) {
                $status = 'assigned';
            } elseif ($timeoutAt && $timeoutAt->isPast()) {
                $status = 'timeout';
            } elseif ($requests->where('status', 'pending')->isNotEmpty()) {
                $status = 'waiting_for_acceptance';
            }

            return $this->successResponse([
                'order_id' => $orderId,
                'status' => $status,
                'broadcast_id' => $broadcastId,
                'providers_count' => $requests->count(),
                'timeout_at' => $timeoutAt?->toISOString(),
                'time_remaining_seconds' => $timeoutAt && ! $timeoutAt->isPast() ?
                    now()->diffInSeconds($timeoutAt) : 0,
                'assigned_provider' => $acceptedRequest ? [
                    'id' => $acceptedRequest->provider_id,
                    'name' => $acceptedRequest->provider->business_name,
                    'response_time_seconds' => $acceptedRequest->getResponseTimeSeconds(),
                ] : null,
                'requests' => $requests->map(function ($request) {
                    return [
                        'id' => $request->id,
                        'provider_id' => $request->provider_id,
                        'provider_name' => $request->provider->business_name,
                        'status' => $request->status->value,
                        'status_label' => $request->status->label(),
                        'sent_at' => $request->created_at->toISOString(),
                        'response_time_seconds' => $request->getResponseTimeSeconds(),
                    ];
                }),
            ], 'Assignment status retrieved successfully');

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Order not found');
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve assignment status', 500);
        }
    }

    /**
     * Get broadcast statistics for a specific broadcast.
     *
     * @group Business Delivery Assignment
     *
     * @authenticated
     *
     * @urlParam broadcastId string required The broadcast ID. Example: broadcast_019723aa_1640995200
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Broadcast statistics retrieved successfully",
     *   "data": {
     *     "broadcast_id": "broadcast_019723aa_1640995200",
     *     "total_requests": 5,
     *     "accepted_count": 1,
     *     "cancelled_count": 4,
     *     "expired_count": 0,
     *     "response_time_seconds": 67,
     *     "accepted_provider": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *       "name": "FastDelivery Lagos"
     *     }
     *   }
     * }
     */
    public function getBroadcastStats(string $broadcastId): JsonResponse
    {
        try {
            $stats = $this->providerBroadcastService->getBroadcastStats($broadcastId);

            return $this->successResponse(
                $stats,
                'Broadcast statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve broadcast statistics', 500);
        }
    }

    /**
     * Get first-to-accept assignment statistics for the business.
     *
     * @group Business Delivery Assignment
     *
     * @authenticated
     *
     * @queryParam days integer Number of days to include in statistics. Example: 30
     *
     * @response 200 {
     *   "success": true,
     *   "message": "First-to-accept statistics retrieved successfully",
     *   "data": {
     *     "period_days": 30,
     *     "total_broadcasts": 25,
     *     "successful_assignments": 22,
     *     "success_rate": 88.0,
     *     "average_response_time_seconds": 45.5,
     *     "timeout_count": 3,
     *     "timeout_rate": 12.0
     *   }
     * }
     */
    public function getFirstToAcceptStats(Request $request): JsonResponse
    {
        try {
            $days = $request->input('days', 30);

            $stats = $this->firstToAcceptService->getFirstToAcceptStats($days);

            return $this->successResponse(
                $stats,
                'First-to-accept statistics retrieved successfully'
            );

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve first-to-accept statistics', 500);
        }
    }

    /**
     * Cancel pending delivery requests for an order.
     *
     * @group Business Delivery Assignment
     *
     * @authenticated
     *
     * @urlParam orderId string required The order ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam reason string Reason for cancellation. Example: Customer cancelled order
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Delivery requests cancelled successfully",
     *   "data": {
     *     "cancelled_requests": 3
     *   }
     * }
     */
    public function cancelDeliveryRequests(Request $request, string $orderId): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            // Validate that the order belongs to the current business
            $order = $business->orders()->findOrFail($orderId);

            $request->validate([
                'reason' => 'required|string|max:255',
            ]);

            $reason = $request->input('reason');

            $cancelledCount = $order->deliveryRequests()
                ->pending()
                ->update([
                    'status' => 'cancelled',
                    'cancelled_at' => now(),
                    'cancellation_reason' => $reason,
                    'cancellation_metadata' => [
                        'cancelled_by' => 'business',
                        'cancelled_by_user_id' => auth()->id(),
                    ],
                ]);

            return $this->successResponse([
                'cancelled_requests' => $cancelledCount,
            ], 'Delivery requests cancelled successfully');

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Order not found');
        } catch (ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to cancel delivery requests', 500);
        }
    }
}
