<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Business;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\Tenant\Business\CreatePickupSlotRequest;
use App\Http\Requests\Api\V1\Tenant\Business\UpdatePickupSlotRequest;
use App\Models\Delivery\PickupSlot;
use App\Services\Business\BusinessService;
use App\Services\System\LoggingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Pickup Slot Management Controller
 *
 * Handles tenant-scoped pickup slot management for businesses.
 * Manages pickup time slots, availability, and capacity management.
 */
class BusinessPickupSlotController extends Controller
{
    use \App\Traits\ApiResponseTrait, \App\Traits\QueryHandlerTrait;

    public function __construct(
        private readonly BusinessService $businessService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get pickup slots list
     *
     * @group Business Pickup Management
     *
     * @authenticated
     *
     * @header Authorization Bearer {token}
     *
     * @queryParam page integer Page number for pagination. Example: 1
     * @queryParam per_page integer Items per page (max 100). Example: 15
     * @queryParam search string Search pickup slots by day name. Example: monday
     * @queryParam day_of_week integer Filter by day of week (0-6). Example: 1
     * @queryParam is_active boolean Filter by active status. Example: true
     * @queryParam branch_id string Filter by business branch ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87b
     * @queryParam sort string Sort field (day_of_week, start_time, created_at). Example: day_of_week
     * @queryParam direction string Sort direction (asc, desc). Example: asc
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Pickup slots retrieved successfully",
     *   "data": [
     *     {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "day_of_week": 1,
     *       "day_name": "Monday",
     *       "start_time": "09:00",
     *       "end_time": "12:00",
     *       "time_range": "09:00 - 12:00",
     *       "display_name": "Monday 09:00 - 12:00",
     *       "max_orders": 20,
     *       "has_limit": true,
     *       "is_active": true,
     *       "current_orders_count": 5,
     *       "remaining_capacity": 15,
     *       "is_currently_open": false,
     *       "next_occurrence": "2024-01-22T09:00:00Z",
     *       "business_branch": {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *         "name": "Main Branch"
     *       }
     *     }
     *   ],
     *   "meta": {
     *     "current_page": 1,
     *     "per_page": 15,
     *     "total": 25,
     *     "last_page": 2
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            $query = PickupSlot::where('business_id', $business->id)
                ->with(['businessBranch:id,name']);

            // Apply filters
            if ($request->filled('day_of_week')) {
                $query->where('day_of_week', $request->integer('day_of_week'));
            }

            if ($request->filled('is_active')) {
                $query->where('is_active', $request->boolean('is_active'));
            }

            if ($request->filled('branch_id')) {
                $query->where('business_branch_id', $request->string('branch_id'));
            }

            // Apply search
            if ($request->filled('search')) {
                $search = $request->string('search')->lower()->toString();
                $dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
                $dayNumbers = array_keys(array_filter($dayNames, fn ($day) => str_contains($day, $search)));

                if (! empty($dayNumbers)) {
                    $query->whereIn('day_of_week', $dayNumbers);
                }
            }

            // Handle query with pagination
            $perPage = min((int) $request->input('per_page', 15), 100);
            $results = $query->paginate($perPage);

            // Transform the data
            $transformedData = $results->getCollection()->map(function ($slot) {
                $summary = $slot->getSummary();
                $summary['business_branch'] = $slot->businessBranch ? [
                    'id' => $slot->businessBranch->id,
                    'name' => $slot->businessBranch->name,
                ] : null;

                return $summary;
            });

            return $this->paginatedResponse(
                $results->setCollection($transformedData),
                'Pickup slots retrieved successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve pickup slots', $e, [
                'business_id' => $business->id ?? null,
                'request_data' => $request->all(),
            ]);

            return $this->errorResponse('Failed to retrieve pickup slots', 500);
        }
    }

    /**
     * Get specific pickup slot
     *
     * @group Business Pickup Management
     *
     * @authenticated
     *
     * @header Authorization Bearer {token}
     *
     * @urlParam pickupSlot string required The pickup slot ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Pickup slot retrieved successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "day_of_week": 1,
     *     "day_name": "Monday",
     *     "start_time": "09:00",
     *     "end_time": "12:00",
     *     "time_range": "09:00 - 12:00",
     *     "display_name": "Monday 09:00 - 12:00",
     *     "max_orders": 20,
     *     "has_limit": true,
     *     "is_active": true,
     *     "current_orders_count": 5,
     *     "remaining_capacity": 15,
     *     "is_currently_open": false,
     *     "next_occurrence": "2024-01-22T09:00:00Z",
     *     "business_branch": {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *       "name": "Main Branch"
     *     },
     *     "recent_orders": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
     *         "order_reference": "ORD-2024-001",
     *         "customer_name": "John Doe",
     *         "scheduled_pickup_time": "2024-01-22T09:30:00Z",
     *         "status": "ready_for_pickup"
     *       }
     *     ]
     *   }
     * }
     */
    public function show(PickupSlot $pickupSlot): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            // Ensure the pickup slot belongs to the current business
            if ($pickupSlot->business_id !== $business->id) {
                return $this->errorResponse('Pickup slot not found', 404);
            }

            $pickupSlot->load([
                'businessBranch:id,name',
                'orders' => function ($query) {
                    $query->with('customer:id,name')
                        ->whereDate('scheduled_pickup_time', '>=', today())
                        ->orderBy('scheduled_pickup_time')
                        ->limit(10);
                },
            ]);

            $data = $pickupSlot->getSummary();
            $data['business_branch'] = $pickupSlot->businessBranch ? [
                'id' => $pickupSlot->businessBranch->id,
                'name' => $pickupSlot->businessBranch->name,
            ] : null;

            $data['recent_orders'] = $pickupSlot->orders->map(function ($order) {
                return [
                    'id' => $order->id,
                    'order_reference' => $order->order_reference,
                    'customer_name' => $order->customer?->name ?? 'Unknown',
                    'scheduled_pickup_time' => $order->scheduled_pickup_time,
                    'status' => $order->status->value,
                ];
            });

            return $this->successResponse($data, 'Pickup slot retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve pickup slot', $e, [
                'pickup_slot_id' => $pickupSlot->id,
            ]);

            return $this->errorResponse('Failed to retrieve pickup slot', 500);
        }
    }

    /**
     * Create new pickup slot
     *
     * @group Business Pickup Management
     *
     * @authenticated
     *
     * @header Authorization Bearer {token}
     *
     * @bodyParam business_branch_id string required Business branch ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87b
     * @bodyParam day_of_week integer required Day of week (0=Sunday, 1=Monday, etc.). Example: 1
     * @bodyParam start_time string required Start time in HH:MM format. Example: 09:00
     * @bodyParam end_time string required End time in HH:MM format. Example: 12:00
     * @bodyParam max_orders integer Maximum number of orders for this slot. Example: 20
     * @bodyParam is_active boolean Whether the slot is active. Example: true
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Pickup slot created successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "day_of_week": 1,
     *     "day_name": "Monday",
     *     "start_time": "09:00",
     *     "end_time": "12:00",
     *     "time_range": "09:00 - 12:00",
     *     "display_name": "Monday 09:00 - 12:00",
     *     "max_orders": 20,
     *     "has_limit": true,
     *     "is_active": true,
     *     "current_orders_count": 0,
     *     "remaining_capacity": 20,
     *     "is_currently_open": false,
     *     "next_occurrence": "2024-01-22T09:00:00Z"
     *   }
     * }
     */
    public function store(CreatePickupSlotRequest $request): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();
            $validated = $request->validated();

            $pickupSlot = PickupSlot::create([
                'tenant_id' => $business->tenant_id,
                'business_id' => $business->id,
                'business_branch_id' => $validated['business_branch_id'],
                'day_of_week' => $validated['day_of_week'],
                'start_time' => $validated['start_time'],
                'end_time' => $validated['end_time'],
                'max_orders' => $validated['max_orders'] ?? null,
                'is_active' => $validated['is_active'] ?? true,
            ]);

            $this->loggingService->logInfo('Pickup slot created successfully', [
                'pickup_slot_id' => $pickupSlot->id,
                'business_id' => $business->id,
                'day_of_week' => $validated['day_of_week'],
                'time_range' => $validated['start_time'].' - '.$validated['end_time'],
            ]);

            return $this->successResponse(
                $pickupSlot->getSummary(),
                'Pickup slot created successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to create pickup slot', $e, [
                'business_id' => $business->id ?? null,
                'request_data' => $request->validated(),
            ]);

            return $this->errorResponse('Failed to create pickup slot', 500);
        }
    }

    /**
     * Update pickup slot
     *
     * @group Business Pickup Management
     *
     * @authenticated
     *
     * @header Authorization Bearer {token}
     *
     * @urlParam pickupSlot string required The pickup slot ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam day_of_week integer Day of week (0=Sunday, 1=Monday, etc.). Example: 1
     * @bodyParam start_time string Start time in HH:MM format. Example: 09:00
     * @bodyParam end_time string End time in HH:MM format. Example: 12:00
     * @bodyParam max_orders integer Maximum number of orders for this slot. Example: 20
     * @bodyParam is_active boolean Whether the slot is active. Example: true
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Pickup slot updated successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "day_of_week": 1,
     *     "day_name": "Monday",
     *     "start_time": "09:00",
     *     "end_time": "12:00",
     *     "time_range": "09:00 - 12:00",
     *     "display_name": "Monday 09:00 - 12:00",
     *     "max_orders": 25,
     *     "has_limit": true,
     *     "is_active": true,
     *     "current_orders_count": 5,
     *     "remaining_capacity": 20,
     *     "is_currently_open": false,
     *     "next_occurrence": "2024-01-22T09:00:00Z"
     *   }
     * }
     */
    public function update(UpdatePickupSlotRequest $request, PickupSlot $pickupSlot): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            // Ensure the pickup slot belongs to the current business
            if ($pickupSlot->business_id !== $business->id) {
                return $this->errorResponse('Pickup slot not found', 404);
            }

            $validated = $request->validated();
            $pickupSlot->update($validated);

            $this->loggingService->logInfo('Pickup slot updated successfully', [
                'pickup_slot_id' => $pickupSlot->id,
                'business_id' => $business->id,
                'updated_fields' => array_keys($validated),
            ]);

            return $this->successResponse(
                $pickupSlot->fresh()->getSummary(),
                'Pickup slot updated successfully'
            );

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to update pickup slot', $e, [
                'pickup_slot_id' => $pickupSlot->id,
                'request_data' => $request->validated(),
            ]);

            return $this->errorResponse('Failed to update pickup slot', 500);
        }
    }

    /**
     * Delete pickup slot
     *
     * @group Business Pickup Management
     *
     * @authenticated
     *
     * @header Authorization Bearer {token}
     *
     * @urlParam pickupSlot string required The pickup slot ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Pickup slot deleted successfully"
     * }
     */
    public function destroy(PickupSlot $pickupSlot): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            // Ensure the pickup slot belongs to the current business
            if ($pickupSlot->business_id !== $business->id) {
                return $this->errorResponse('Pickup slot not found', 404);
            }

            // Check if there are any future orders using this slot
            $futureOrdersCount = $pickupSlot->orders()
                ->whereDate('scheduled_pickup_time', '>=', today())
                ->count();

            if ($futureOrdersCount > 0) {
                return $this->errorResponse(
                    "Cannot delete pickup slot with {$futureOrdersCount} future orders. Please reassign or cancel the orders first.",
                    422
                );
            }

            $pickupSlot->delete();

            $this->loggingService->logInfo('Pickup slot deleted successfully', [
                'pickup_slot_id' => $pickupSlot->id,
                'business_id' => $business->id,
            ]);

            return $this->successResponse(null, 'Pickup slot deleted successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to delete pickup slot', $e, [
                'pickup_slot_id' => $pickupSlot->id,
            ]);

            return $this->errorResponse('Failed to delete pickup slot', 500);
        }
    }

    /**
     * Get weekly schedule for business
     *
     * @group Business Pickup Management
     *
     * @authenticated
     *
     * @header Authorization Bearer {token}
     *
     * @queryParam branch_id string Filter by business branch ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87b
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Weekly schedule retrieved successfully",
     *   "data": {
     *     "0": {
     *       "day_name": "Sunday",
     *       "slots": []
     *     },
     *     "1": {
     *       "day_name": "Monday",
     *       "slots": [
     *         {
     *           "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *           "day_of_week": 1,
     *           "day_name": "Monday",
     *           "start_time": "09:00",
     *           "end_time": "12:00",
     *           "time_range": "09:00 - 12:00",
     *           "display_name": "Monday 09:00 - 12:00",
     *           "max_orders": 20,
     *           "has_limit": true,
     *           "is_active": true,
     *           "current_orders_count": 5,
     *           "remaining_capacity": 15,
     *           "is_currently_open": false,
     *           "next_occurrence": "2024-01-22T09:00:00Z"
     *         }
     *       ]
     *     }
     *   }
     * }
     */
    public function weeklySchedule(Request $request): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();
            $branchId = $request->string('branch_id')->value();

            $schedule = PickupSlot::getWeeklySchedule($business->id, $branchId);

            return $this->successResponse($schedule, 'Weekly schedule retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve weekly schedule', $e, [
                'business_id' => $business->id ?? null,
                'branch_id' => $request->string('branch_id')->value(),
            ]);

            return $this->errorResponse('Failed to retrieve weekly schedule', 500);
        }
    }

    /**
     * Get available pickup slots for a specific date
     *
     * @group Business Pickup Management
     *
     * @authenticated
     *
     * @header Authorization Bearer {token}
     *
     * @queryParam date string required Date in Y-m-d format. Example: 2024-01-22
     * @queryParam branch_id string Filter by business branch ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87b
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Available pickup slots retrieved successfully",
     *   "data": [
     *     {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "day_of_week": 1,
     *       "day_name": "Monday",
     *       "start_time": "09:00",
     *       "end_time": "12:00",
     *       "time_range": "09:00 - 12:00",
     *       "display_name": "Monday 09:00 - 12:00",
     *       "max_orders": 20,
     *       "has_limit": true,
     *       "is_active": true,
     *       "current_orders_count": 5,
     *       "remaining_capacity": 15,
     *       "is_currently_open": false,
     *       "next_occurrence": "2024-01-22T09:00:00Z"
     *     }
     *   ]
     * }
     */
    public function availableForDate(Request $request): JsonResponse
    {
        $request->validate([
            'date' => 'required|date|after_or_equal:today',
            'branch_id' => 'sometimes|string|exists:business_branches,id',
        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();
            $date = $request->string('date')->value();
            $branchId = $request->string('branch_id')->value();

            $slots = PickupSlot::getAvailableForBusinessAndDate($business->id, $date);

            if ($branchId) {
                $slots = $slots->where('business_branch_id', $branchId);
            }

            $data = $slots->map(fn ($slot) => $slot->getSummary())->values();

            return $this->successResponse($data, 'Available pickup slots retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve available pickup slots', $e, [
                'business_id' => $business->id ?? null,
                'date' => $request->string('date')->value(),
                'branch_id' => $request->string('branch_id')->value(),
            ]);

            return $this->errorResponse('Failed to retrieve available pickup slots', 500);
        }
    }
}
