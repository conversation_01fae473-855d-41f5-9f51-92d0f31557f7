<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Business;

use App\Exceptions\BusinessLogicException;
use App\Http\Controllers\Controller;
use App\Models\Business\ProductCategory;
use App\Services\Business\BusinessService;
use App\Services\System\LoggingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Business Category Controller
 *
 * Manages product categories for business owners within their tenant context.
 * Provides CRUD operations, hierarchical management, and category analytics.
 */
class BusinessCategoryController extends Controller
{
    use \App\Traits\ApiResponseTrait, \App\Traits\QueryHandlerTrait;

    public function __construct(
        private readonly BusinessService $businessService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Display a listing of categories for the business.
     *
     * @group Business Categories
     *
     * @authenticated
     *
     * @queryParam page int Page number for pagination. Example: 1
     * @queryParam per_page int Number of items per page (max 100). Example: 15
     * @queryParam search string Search in category name, description. Example: Main
     * @queryParam parent_id string Filter by parent category ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     * @queryParam is_active boolean Filter by active status. Example: true
     * @queryParam with_products boolean Filter categories that have products. Example: true
     * @queryParam tree boolean Return hierarchical tree structure. Example: true
     * @queryParam sort_by string Sort by field (name, sort_order, created_at). Example: sort_order
     * @queryParam sort_direction string Sort direction (asc, desc). Example: asc
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Categories retrieved successfully",
     *   "data": {
     *     "categories": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *         "name": "Main Dishes",
     *         "slug": "main-dishes",
     *         "description": "Primary food items",
     *         "image_url": "https://example.com/main-dishes.jpg",
     *         "is_active": true,
     *         "sort_order": 1,
     *         "is_root": true,
     *         "has_children": true,
     *         "has_products": true,
     *         "products_count": 15,
     *         "children_count": 3,
     *         "depth": 0,
     *         "full_path": "Main Dishes",
     *         "parent": null,
     *         "children": [],
     *         "created_at": "2024-01-15T10:30:00Z"
     *       }
     *     ],
     *     "pagination": {
     *       "current_page": 1,
     *       "per_page": 15,
     *       "total": 10,
     *       "last_page": 1
     *     }
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'page' => 'sometimes|integer|min:1',
            'per_page' => 'sometimes|integer|min:1|max:100',
            'search' => 'sometimes|string|max:255',
            'parent_id' => 'sometimes|string|exists:product_categories,id',
            'is_active' => 'sometimes|boolean',
            'with_products' => 'sometimes|boolean',
            'tree' => 'sometimes|boolean',
            'sort_by' => 'sometimes|string|in:name,display_order,created_at',
            'sort_direction' => 'sometimes|string|in:asc,desc',
        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();

            // Return tree structure if requested
            if ($request->boolean('tree')) {
                return $this->getTreeStructure($business);
            }

            $query = ProductCategory::where('business_id', $business->id)
                ->with(['parent', 'children']);

            $result = $this->handleQuery($query, $request, [
                'searchFields' => ['name', 'description'],
                'sortFields' => ['name', 'display_order', 'created_at'],
                'defaultSort' => 'display_order',
                'defaultDirection' => 'asc',
                'filters' => [
                    'parent_id' => ['type' => 'exact'],
                    'is_active' => ['type' => 'boolean'],
                    'with_products' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            if (filter_var($value, FILTER_VALIDATE_BOOLEAN)) {
                                $query->has('products');
                            }
                        },
                    ],
                ],
                'message' => 'Categories retrieved successfully',
                'entityName' => 'categories',
                'transformer' => [$this, 'transformCategory'],
            ]);

            // Convert to expected format for backward compatibility
            $resultData = json_decode($result->getContent(), true);
            if (isset($resultData['data']['data'])) {
                $transformedData = [
                    'categories' => $resultData['data']['data'],
                    'pagination' => [
                        'current_page' => $resultData['data']['current_page'],
                        'per_page' => $resultData['data']['per_page'],
                        'total' => $resultData['data']['total'],
                        'last_page' => $resultData['data']['last_page'],
                    ],
                ];

                return $this->successResponse(
                    $transformedData,
                    'Categories retrieved successfully'
                );
            }

            return $result;

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve categories',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'business_id' => $business->id ?? null,
                    'filters' => $request->only(['parent_id', 'is_active', 'with_products']),
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve categories');
        }
    }

    /**
     * Store a newly created category.
     *
     * @group Business Categories
     *
     * @authenticated
     *
     * @bodyParam name string required Category name. Example: Main Dishes
     * @bodyParam description string Category description. Example: Primary food items
     * @bodyParam parent_id string Parent category ID for subcategories. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     * @bodyParam image_url string Category image URL. Example: https://example.com/main-dishes.jpg
     * @bodyParam is_active boolean Category active status. Example: true
     * @bodyParam sort_order integer Sort order for display. Example: 1
     * @bodyParam attributes object Category attributes. Example: {"color": "#ff0000", "icon": "utensils"}
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Category created successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "Main Dishes",
     *     "slug": "main-dishes",
     *     "description": "Primary food items",
     *     "image_url": "https://example.com/main-dishes.jpg",
     *     "is_active": true,
     *     "sort_order": 1,
     *     "is_root": true,
     *     "has_children": false,
     *     "has_products": false,
     *     "products_count": 0,
     *     "children_count": 0,
     *     "depth": 0,
     *     "full_path": "Main Dishes",
     *     "parent": null,
     *     "created_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'sometimes|string|max:1000',
            'parent_id' => 'sometimes|string|exists:product_categories,id',
            'image_url' => 'sometimes|string|url|max:500',
            'is_active' => 'sometimes|boolean',
            'display_order' => 'sometimes|integer|min:0',

        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();

            // Verify parent category belongs to business if provided
            if ($request->filled('parent_id')) {
                $parentCategory = ProductCategory::where('id', $request->input('parent_id'))
                    ->where('business_id', $business->id)
                    ->first();

                if (! $parentCategory) {
                    return $this->errorResponse('Parent category not found or does not belong to your business', 404);
                }
            }

            // Generate unique slug
            $slug = ProductCategory::generateUniqueSlug($request->input('name'), $business->id);

            // Determine display order if not provided
            $displayOrder = $request->input('display_order');
            if (is_null($displayOrder)) {
                $maxDisplayOrder = ProductCategory::where('business_id', $business->id)
                    ->where('parent_id', $request->input('parent_id'))
                    ->max('display_order') ?? 0;
                $displayOrder = $maxDisplayOrder + 1;
            }

            // Prepare category data
            $categoryData = [
                'tenant_id' => $business->tenant_id,
                'business_id' => $business->id,
                'name' => $request->input('name'),
                'slug' => $slug,
                'description' => $request->input('description'),
                'parent_id' => $request->input('parent_id'),
                'image_url' => $request->input('image_url'),
                'is_active' => $request->input('is_active', true),
                'display_order' => $displayOrder,
            ];

            // Create category
            $category = ProductCategory::create($categoryData);

            // Load relationships
            $category->load(['parent', 'children']);

            $this->loggingService->logAudit(
                'category_created',
                'Category created successfully',
                [
                    'category_id' => $category->id,
                    'category_name' => $category->name,
                    'business_id' => $business->id,
                    'tenant_id' => tenant()?->id,
                    'parent_id' => $category->parent_id,
                ],
                auth()->id()
            );

            return $this->successResponse(
                $this->transformCategory($category),
                'Category created successfully',
                201
            );

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create category',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'business_id' => $business->id ?? null,
                    'category_data' => $request->only(['name', 'parent_id']),
                ]
            );

            return $this->serverErrorResponse('Failed to create category');
        }
    }

    /**
     * Display the specified category.
     *
     * @group Business Categories
     *
     * @authenticated
     *
     * @urlParam id string required The category ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Category retrieved successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "Main Dishes",
     *     "slug": "main-dishes",
     *     "description": "Primary food items",
     *     "image_url": "https://example.com/main-dishes.jpg",
     *     "is_active": true,
     *     "sort_order": 1,
     *     "is_root": true,
     *     "has_children": true,
     *     "has_products": true,
     *     "products_count": 15,
     *     "children_count": 3,
     *     "depth": 0,
     *     "full_path": "Main Dishes",
     *     "breadcrumb": [{"id": "...", "name": "Main Dishes", "slug": "main-dishes"}],
     *     "parent": null,
     *     "children": [],
     *     "attributes": {"color": "#ff0000", "icon": "utensils"},
     *     "created_at": "2024-01-15T10:30:00Z",
     *     "updated_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function show(string $id): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            $query = ProductCategory::where('business_id', $business->id);

            return $this->handleShow($query, $id, [
                'with' => ['parent', 'children', 'products'],
                'transformer' => fn ($model) => $this->transformCategory($model, true),
                'message' => 'Category retrieved successfully',
                'notFoundMessage' => 'Category not found',
            ]);

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve category',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'category_id' => $id,
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve category');
        }
    }

    /**
     * Update the specified category.
     *
     * @group Business Categories
     *
     * @authenticated
     *
     * @urlParam id string required The category ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam name string Category name. Example: Updated Main Dishes
     * @bodyParam description string Category description. Example: Updated description
     * @bodyParam parent_id string Parent category ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87b
     * @bodyParam image_url string Category image URL. Example: https://example.com/updated.jpg
     * @bodyParam is_active boolean Category active status. Example: false
     * @bodyParam sort_order integer Sort order. Example: 5
     * @bodyParam attributes object Category attributes. Example: {"color": "#00ff00"}
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Category updated successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "Updated Main Dishes",
     *     "updated_at": "2024-01-15T11:00:00Z"
     *   }
     * }
     */
    public function update(Request $request, string $id): JsonResponse
    {
        $request->validate([
            'name' => 'sometimes|string|max:255',
            'description' => 'sometimes|string|max:1000',
            'parent_id' => 'sometimes|string|exists:product_categories,id',
            'image_url' => 'sometimes|string|url|max:500',
            'is_active' => 'sometimes|boolean',
            'display_order' => 'sometimes|integer|min:0',

        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();

            $category = ProductCategory::where('business_id', $business->id)
                ->where('id', $id)
                ->first();

            if (! $category) {
                return $this->errorResponse('Category not found', 404);
            }

            // Verify parent category belongs to business if provided
            if ($request->filled('parent_id')) {
                // Prevent setting self as parent
                if ($request->input('parent_id') === $id) {
                    return $this->errorResponse('Category cannot be its own parent', 422);
                }

                // Prevent circular references
                $descendants = $category->getDescendants();
                if ($descendants->contains('id', $request->input('parent_id'))) {
                    return $this->errorResponse('Cannot move category to its descendant', 422);
                }

                $parentCategory = ProductCategory::where('id', $request->input('parent_id'))
                    ->where('business_id', $business->id)
                    ->first();

                if (! $parentCategory) {
                    return $this->errorResponse('Parent category not found or does not belong to your business', 404);
                }
            }

            // Update slug if name changed
            $updateData = $request->only([
                'name', 'description', 'parent_id', 'image_url',
                'is_active', 'display_order',
            ]);

            if ($request->filled('name') && $request->input('name') !== $category->name) {
                $updateData['slug'] = ProductCategory::generateUniqueSlug(
                    $request->input('name'),
                    $business->id,
                    $id
                );
            }

            // Update category
            $category->update($updateData);

            // Load relationships
            $category->load(['parent', 'children']);

            $this->loggingService->logAudit(
                'category_updated',
                'Category updated successfully',
                [
                    'category_id' => $category->id,
                    'category_name' => $category->name,
                    'business_id' => $business->id,
                    'tenant_id' => tenant()?->id,
                    'updated_fields' => array_keys($updateData),
                ],
                auth()->id()
            );

            return $this->successResponse(
                $this->transformCategory($category),
                'Category updated successfully'
            );

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update category',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'category_id' => $id,
                    'update_data' => $request->only(['name', 'parent_id']),
                ]
            );

            return $this->serverErrorResponse('Failed to update category');
        }
    }

    /**
     * Remove the specified category.
     *
     * @group Business Categories
     *
     * @authenticated
     *
     * @urlParam id string required The category ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Category deleted successfully",
     *   "data": null
     * }
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            $category = ProductCategory::where('business_id', $business->id)
                ->where('id', $id)
                ->first();

            if (! $category) {
                return $this->errorResponse('Category not found', 404);
            }

            // Check if category has products
            if ($category->hasProducts()) {
                return $this->errorResponse(
                    'Cannot delete category that contains products. Please move or delete products first.',
                    422
                );
            }

            // Check if category has children
            if ($category->hasChildren()) {
                return $this->errorResponse(
                    'Cannot delete category that has subcategories. Please move or delete subcategories first.',
                    422
                );
            }

            $categoryName = $category->name;

            // Delete the category
            $category->delete();

            $this->loggingService->logAudit(
                'category_deleted',
                'Category deleted successfully',
                [
                    'category_id' => $id,
                    'category_name' => $categoryName,
                    'business_id' => $business->id,
                    'tenant_id' => tenant()?->id,
                ],
                auth()->id()
            );

            return $this->successResponse(
                null,
                'Category deleted successfully'
            );

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to delete category',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'category_id' => $id,
                ]
            );

            return $this->serverErrorResponse('Failed to delete category');
        }
    }

    // Helper Methods

    /**
     * Apply filters to the query.
     */
    private function applyFilters($query, Request $request): void
    {
        if ($request->filled('parent_id')) {
            $query->where('parent_id', $request->input('parent_id'));
        }

        if ($request->filled('is_active')) {
            $query->where('is_active', $request->input('is_active'));
        }

        if ($request->filled('with_products')) {
            if ($request->input('with_products')) {
                $query->withProducts();
            } else {
                $query->doesntHave('products');
            }
        }
    }

    /**
     * Get tree structure for categories.
     */
    private function getTreeStructure($business): JsonResponse
    {
        $categories = ProductCategory::where('business_id', $business->id)
            ->with(['parent', 'children'])
            ->ordered()
            ->get();

        $tree = ProductCategory::buildTree($categories);

        $transformedTree = $tree->map(function ($category) {
            return $category->getTreeStructure();
        });

        return $this->successResponse([
            'tree' => $transformedTree,
            'total_categories' => $categories->count(),
        ], 'Category tree retrieved successfully');
    }

    /**
     * Transform category data for API response.
     */
    private function transformCategory(ProductCategory $category, bool $detailed = false): array
    {
        $data = [
            'id' => $category->id,
            'name' => $category->name,
            'slug' => $category->slug,
            'description' => $category->description,
            'image_url' => $category->image_url,
            'is_active' => $category->is_active,
            'display_order' => $category->display_order,
            'is_root' => $category->isRoot(),
            'has_children' => $category->hasChildren(),
            'has_products' => $category->hasProducts(),
            'products_count' => $category->products()->count(),
            'children_count' => $category->children()->count(),
            'depth' => $category->getDepth(),
            'full_path' => $category->getFullPath(),
            'parent' => $category->parent ? [
                'id' => $category->parent->id,
                'name' => $category->parent->name,
                'slug' => $category->parent->slug,
            ] : null,
            'children' => $category->children->map(function ($child) {
                return [
                    'id' => $child->id,
                    'name' => $child->name,
                    'slug' => $child->slug,
                    'is_active' => $child->is_active,
                    'products_count' => $child->products()->count(),
                ];
            }),
            'created_at' => $category->created_at->toISOString(),
        ];

        if ($detailed) {
            $data = array_merge($data, [
                'breadcrumb' => $category->getBreadcrumb(),
                'total_products_count' => $category->getTotalProductsCount(),
                'updated_at' => $category->updated_at->toISOString(),
            ]);
        }

        return $data;
    }
}
