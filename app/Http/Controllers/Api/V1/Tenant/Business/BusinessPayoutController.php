<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Business;

use App\Enums\Financial\PayoutMethodType;
use App\Http\Controllers\Controller;
use App\Models\Financial\Payout;
use App\Services\Financial\PayoutManagementService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\Rules\Enum;

/**
 * Business Payout Controller
 *
 * Handles payout requests and management for businesses.
 */
class BusinessPayoutController extends Controller
{
    use \App\Traits\ApiResponseTrait;

    public function __construct(
        private readonly PayoutManagementService $payoutService
    ) {}

    /**
     * Request a payout.
     *
     * @group Business Payouts
     *
     * @authenticated
     *
     * @bodyParam amount number required The payout amount.
     * @bodyParam payment_method string required The payment method (bank_transfer, wallet).
     * @bodyParam bank_details object required Bank account details for bank transfers.
     * @bodyParam bank_details.account_number string required Bank account number.
     * @bodyParam bank_details.bank_code string required Bank code.
     * @bodyParam bank_details.account_name string required Account holder name.
     * @bodyParam notes string optional Additional notes for the payout.
     *
     * @response 201 {
     *   "success": true,
     *   "data": {
     *     "id": "payout-uuid",
     *     "amount": 10000.00,
     *     "currency": "NGN",
     *     "transaction_fee": 200.00,
     *     "status": "pending",
     *     "payment_method": "bank_transfer",
     *     "reference": "PO_ABC123_20241201",
     *     "balance_before": 15000.00,
     *     "balance_after": 5000.00,
     *     "created_at": "2024-12-01T12:00:00.000000Z"
     *   },
     *   "message": "Payout requested successfully"
     * }
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'amount' => 'required|numeric|min:100',
            'payment_method' => ['required', new Enum(PayoutMethodType::class)],
            'bank_details' => 'required_if:payment_method,bank_transfer|array',
            'bank_details.account_number' => 'required_if:payment_method,bank_transfer|string',
            'bank_details.bank_code' => 'required_if:payment_method,bank_transfer|string',
            'bank_details.account_name' => 'required_if:payment_method,bank_transfer|string',
            'notes' => 'sometimes|string|max:500',
        ]);

        try {
            $business = auth()->user()->business;

            if (! $business) {
                return $this->errorResponse('Business not found', 404);
            }

            $payout = $this->payoutService->requestBusinessPayout(
                $business->id,
                $request->input('amount'),
                PayoutMethodType::from($request->input('payment_method')),
                $request->input('bank_details', []),
                $request->input('notes')
            );

            return $this->successResponse($payout, 'Payout requested successfully', 201);

        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 400);
        }
    }

    /**
     * Get payout history.
     *
     * @group Business Payouts
     *
     * @authenticated
     *
     * @queryParam limit integer The number of payouts to retrieve (default: 50).
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "payouts": [
     *       {
     *         "id": "payout-uuid",
     *         "amount": 10000.00,
     *         "currency": "NGN",
     *         "transaction_fee": 200.00,
     *         "status": "paid",
     *         "payment_method": "bank_transfer",
     *         "reference": "PO_ABC123_20241201",
     *         "processed_at": "2024-12-01T14:00:00.000000Z",
     *         "created_at": "2024-12-01T12:00:00.000000Z"
     *       }
     *     ],
     *     "summary": {
     *       "total_payouts": 5,
     *       "total_amount": 50000.00,
     *       "total_fees": 1000.00,
     *       "successful_payouts": 4,
     *       "pending_payouts": 1,
     *       "failed_payouts": 0
     *     }
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        $request->validate([
            'limit' => 'sometimes|integer|min:1|max:100',
        ]);

        try {
            $business = auth()->user()->business;

            if (! $business) {
                return $this->errorResponse('Business not found', 404);
            }

            $history = $this->payoutService->getPayoutHistory(
                $business->id,
                'App\Models\Business',
                $request->input('limit', 50)
            );

            return $this->successResponse($history, 'Payout history retrieved successfully');

        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve payout history', 500);
        }
    }

    /**
     * Get specific payout details.
     *
     * @group Business Payouts
     *
     * @authenticated
     *
     * @urlParam payout string required The payout ID.
     *
     * @response 200 {
     *   "success": true,
     *   "data": {
     *     "id": "payout-uuid",
     *     "amount": 10000.00,
     *     "currency": "NGN",
     *     "transaction_fee": 200.00,
     *     "status": "paid",
     *     "payment_method": "bank_transfer",
     *     "bank_details": {
     *       "account_number": "**********",
     *       "bank_code": "044",
     *       "account_name": "Business Account"
     *     },
     *     "reference": "PO_ABC123_20241201",
     *     "balance_before": 15000.00,
     *     "balance_after": 5000.00,
     *     "notes": "Monthly payout",
     *     "processed_at": "2024-12-01T14:00:00.000000Z",
     *     "created_at": "2024-12-01T12:00:00.000000Z"
     *   }
     * }
     */
    public function show(string $payout): JsonResponse
    {
        try {
            $business = auth()->user()->business;

            if (! $business) {
                return $this->errorResponse('Business not found', 404);
            }

            $payoutModel = Payout::where('id', $payout)
                ->where('payable_id', $business->id)
                ->where('payable_type', 'App\Models\Business')
                ->firstOrFail();

            return $this->successResponse($payoutModel, 'Payout details retrieved successfully');

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->errorResponse('Payout not found', 404);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve payout details', 500);
        }
    }
}
