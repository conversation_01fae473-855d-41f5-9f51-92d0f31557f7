<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Business;

use App\Exceptions\BusinessLogicException;
use App\Http\Controllers\Controller;
use App\Models\Business\Product;
use App\Services\Business\BusinessService;
use App\Services\System\LoggingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Inventory Management Controller
 *
 * Handles tenant-scoped inventory management for businesses.
 * Manages stock levels, low stock alerts, and inventory movements.
 */
class BusinessInventoryController extends Controller
{
    use \App\Traits\ApiResponseTrait, \App\Traits\QueryHandlerTrait;

    public function __construct(
        private readonly BusinessService $businessService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get inventory overview.
     *
     * @group Inventory Management
     *
     * @authenticated
     *
     * @queryParam page integer Page number for pagination. Example: 1
     * @queryParam per_page integer Items per page (max 100). Example: 15
     * @queryParam search string Search products by name or SKU. Example: burger
     * @queryParam low_stock boolean Filter low stock items. Example: true
     * @queryParam out_of_stock boolean Filter out of stock items. Example: false
     * @queryParam category_id string Filter by category. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     * @queryParam sort_by string Sort by field. Example: quantity
     * @queryParam sort_direction string Sort direction (asc, desc). Example: asc
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Inventory retrieved successfully",
     *   "data": {
     *     "summary": {
     *       "total_products": 150,
     *       "low_stock_items": 12,
     *       "out_of_stock_items": 3,
     *       "total_value": 125000
     *     },
     *     "data": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *         "name": "Classic Burger",
     *         "sku": "BURGER-001",
     *         "current_stock": 25,
     *         "low_stock_threshold": 10,
     *         "cost_price": 800,
     *         "selling_price": 1500,
     *         "stock_value": 20000,
     *         "status": "in_stock",
     *         "last_updated": "2024-01-15T10:30:00Z"
     *       }
     *     ],
     *     "current_page": 1,
     *     "per_page": 15,
     *     "total": 150
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            $query = Product::where('business_id', $business->id)
                ->with(['category:id,name']);

            // Calculate summary statistics
            $summary = $this->getInventorySummary($business->id);

            $result = $this->handleQuery($query, $request, [
                'searchFields' => [
                    'name',
                    'sku',
                    'barcode',
                ],
                'sortFields' => [
                    'name',
                    'quantity',
                    'cost_price',
                    'price',
                    'updated_at',
                ],
                'filters' => [
                    'category_id' => ['type' => 'exact'],
                    'low_stock' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            if (filter_var($value, FILTER_VALIDATE_BOOLEAN)) {
                                $query->whereColumn('quantity', '<=', 'low_stock_threshold');
                            }
                        },
                    ],
                    'out_of_stock' => [
                        'type' => 'custom',
                        'callback' => function ($query, $value) {
                            if (filter_var($value, FILTER_VALIDATE_BOOLEAN)) {
                                $query->where('quantity', '<=', 0);
                            }
                        },
                    ],
                ],
                'message' => 'Inventory retrieved successfully',
                'entityName' => 'products',
                'transformer' => [$this, 'transformInventoryItem'],
            ]);

            // Add summary to response
            if (is_array($result->getData(true))) {
                $data = $result->getData(true);
                $data['data']['summary'] = $summary;

                return response()->json($data, $result->getStatusCode());
            }

            return $result;

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve inventory',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve inventory');
        }
    }

    /**
     * Update product stock.
     *
     * @group Inventory Management
     *
     * @authenticated
     *
     * @urlParam product string required Product ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam quantity integer required New stock quantity. Example: 50
     * @bodyParam adjustment_type string required Type of adjustment (set, add, subtract). Example: set
     * @bodyParam reason string Reason for adjustment. Example: Stock count correction
     * @bodyParam cost_price number Update cost price. Example: 800
     * @bodyParam low_stock_threshold integer Update low stock threshold. Example: 10
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Stock updated successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "Classic Burger",
     *     "previous_quantity": 25,
     *     "new_quantity": 50,
     *     "adjustment": 25,
     *     "adjustment_type": "add",
     *     "reason": "Stock count correction",
     *     "updated_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function updateStock(Request $request, string $product): JsonResponse
    {
        $request->validate([
            'quantity' => 'required|integer|min:0',
            'adjustment_type' => 'required|string|in:set,add,subtract',
            'reason' => 'sometimes|string|max:255',
            'cost_price' => 'sometimes|numeric|min:0',
            'low_stock_threshold' => 'sometimes|integer|min:0',
        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();

            $productModel = Product::where('business_id', $business->id)
                ->where('id', $product)
                ->firstOrFail();

            $previousQuantity = $productModel->quantity;
            $newQuantity = $this->calculateNewQuantity(
                $previousQuantity,
                $request->quantity,
                $request->adjustment_type
            );

            // Update product
            $updateData = ['quantity' => $newQuantity];

            if ($request->has('cost_price')) {
                $updateData['cost_price'] = $request->cost_price;
            }

            if ($request->has('low_stock_threshold')) {
                $updateData['low_stock_threshold'] = $request->low_stock_threshold;
            }

            $productModel->update($updateData);

            // Log inventory movement (placeholder for future inventory_movements table)
            $this->loggingService->logInfo(
                'Inventory updated',
                [
                    'tenant_id' => tenant()?->id,
                    'business_id' => $business->id,
                    'product_id' => $productModel->id,
                    'previous_quantity' => $previousQuantity,
                    'new_quantity' => $newQuantity,
                    'adjustment_type' => $request->adjustment_type,
                    'reason' => $request->reason,
                    'user_id' => auth()->id(),
                ]
            );

            return $this->successResponse([
                'id' => $productModel->id,
                'name' => $productModel->name,
                'previous_quantity' => $previousQuantity,
                'new_quantity' => $newQuantity,
                'adjustment' => $newQuantity - $previousQuantity,
                'adjustment_type' => $request->adjustment_type,
                'reason' => $request->reason,
                'updated_at' => $productModel->updated_at->toISOString(),
            ], 'Stock updated successfully');

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Product not found');

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to update stock',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'product_id' => $product,
                ]
            );

            return $this->serverErrorResponse('Failed to update stock');
        }
    }

    // Helper Methods

    /**
     * Get inventory summary statistics.
     */
    private function getInventorySummary(string $businessId): array
    {
        $products = Product::where('business_id', $businessId);

        return [
            'total_products' => $products->count(),
            'low_stock_items' => $products->clone()->whereColumn('quantity', '<=', 'low_stock_threshold')->count(),
            'out_of_stock_items' => $products->clone()->where('quantity', '<=', 0)->count(),
            'total_value' => $products->clone()->selectRaw('SUM(quantity * cost_price) as total')->value('total') ?? 0,
        ];
    }

    /**
     * Calculate new quantity based on adjustment type.
     */
    private function calculateNewQuantity(int $current, int $adjustment, string $type): int
    {
        return match ($type) {
            'set' => $adjustment,
            'add' => $current + $adjustment,
            'subtract' => max(0, $current - $adjustment),
            default => $current,
        };
    }

    /**
     * Transform inventory item for API response.
     */
    public function transformInventoryItem(Product $product): array
    {
        $stockValue = $product->quantity * ($product->cost_price ?? 0);

        $status = match (true) {
            $product->quantity <= 0 => 'out_of_stock',
            $product->quantity <= ($product->low_stock_threshold ?? 0) => 'low_stock',
            default => 'in_stock'
        };

        return [
            'id' => $product->id,
            'name' => $product->name,
            'sku' => $product->sku,
            'barcode' => $product->barcode,
            'current_stock' => $product->quantity,
            'low_stock_threshold' => $product->low_stock_threshold ?? 0,
            'cost_price' => $product->cost_price ?? 0,
            'selling_price' => $product->getEffectivePrice(),
            'stock_value' => $stockValue,
            'status' => $status,
            'category' => $product->category?->name,
            'last_updated' => $product->updated_at->toISOString(),
        ];
    }
}
