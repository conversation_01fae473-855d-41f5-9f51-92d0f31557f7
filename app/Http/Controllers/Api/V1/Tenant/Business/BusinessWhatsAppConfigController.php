<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Business;

use App\Http\Controllers\Controller;
use App\Http\Requests\WhatsAppConfigRequest;
use App\Http\Resources\WhatsAppConfigResource;
use App\Models\System\TenantWhatsAppConfig;
use App\Services\WhatsApp\TenantWhatsAppService;
use App\Services\WhatsApp\WhatsAppBusinessApiService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * @group WhatsApp Configuration
 *
 * APIs for managing WhatsApp Business API configuration for tenant businesses
 */
class BusinessWhatsAppConfigController extends Controller
{
    use \App\Traits\ApiResponseTrait;

    public function __construct(
        private readonly TenantWhatsAppService $whatsAppService,
        private readonly WhatsAppBusinessApiService $apiService
    ) {}

    /**
     * Get WhatsApp Configuration
     *
     * Get the current WhatsApp Business API configuration for the tenant.
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "WhatsApp configuration retrieved successfully",
     *   "data": {
     *     "config": {
     *       "id": "uuid",
     *       "phone_number": "+*************",
     *       "display_phone_number": "+234 ************",
     *       "is_verified": true,
     *       "verification_status": "verified",
     *       "business_profile": {
     *         "name": "My Business",
     *         "description": "We deliver great food"
     *       },
     *       "settings": {
     *         "auto_reply": {
     *           "enabled": true,
     *           "message": "Thank you for contacting us!"
     *         }
     *       }
     *     },
     *     "setup_required": false,
     *     "webhook_url": "https://api.deliverynexus.com/api/v1/whatsapp/webhook"
     *   }
     * }
     * @response 200 {
     *   "success": true,
     *   "message": "WhatsApp configuration retrieved successfully",
     *   "data": {
     *     "config": null,
     *     "setup_required": true,
     *     "webhook_url": "https://api.deliverynexus.com/api/v1/whatsapp/webhook"
     *   }
     * }
     */
    public function show(): JsonResponse
    {
        $config = TenantWhatsAppConfig::where('tenant_id', tenant()->id)->first();

        return $this->successResponse([
            'config' => $config ? new WhatsAppConfigResource($config) : null,
            'setup_required' => ! $config || ! $config->is_verified,
            'webhook_url' => url('/api/v1/whatsapp/webhook'),
        ], 'WhatsApp configuration retrieved successfully');
    }

    /**
     * Create/Update WhatsApp Configuration
     *
     * Create or update WhatsApp Business API configuration for the tenant.
     *
     * @authenticated
     *
     * @bodyParam phone_number string required The WhatsApp Business phone number. Example: +*************
     * @bodyParam display_phone_number string optional Formatted display phone number. Example: +234 ************
     * @bodyParam business_account_id string required WhatsApp Business Account ID. Example: ***************
     * @bodyParam access_token string required WhatsApp Business API access token. Example: EAABsBCS1234...
     * @bodyParam api_version string optional API version to use. Example: v18.0
     * @bodyParam business_profile object optional Business profile information.
     * @bodyParam business_profile.name string optional Business name. Example: My Restaurant
     * @bodyParam business_profile.description string optional Business description. Example: We deliver great food
     * @bodyParam settings object optional WhatsApp settings configuration.
     *
     * @response 201 {
     *   "success": true,
     *   "message": "WhatsApp configuration saved. Verification process initiated.",
     *   "data": {
     *     "id": "uuid",
     *     "phone_number": "+*************",
     *     "display_phone_number": "+234 ************",
     *     "is_verified": false,
     *     "verification_status": "pending",
     *     "webhook_verify_token": "abc123def456",
     *     "webhook_url": "https://api.deliverynexus.com/api/v1/whatsapp/webhook"
     *   }
     * }
     */
    public function store(WhatsAppConfigRequest $request): JsonResponse
    {
        $tenant = tenant();

        $config = $this->whatsAppService->initializeTenantWhatsApp(
            $tenant,
            $request->validated()
        );

        // Try to verify the configuration with WhatsApp API
        try {
            $this->verifyWhatsAppConfiguration($config);
        } catch (\Exception $e) {
            // Log error but don't fail the request
            // Configuration is saved, verification can be retried later
        }

        return $this->successResponse(
            new WhatsAppConfigResource($config),
            'WhatsApp configuration saved. Verification process initiated.',
            201
        );
    }

    /**
     * Update WhatsApp Settings
     *
     * Update WhatsApp settings and preferences for the tenant.
     *
     * @authenticated
     *
     * @bodyParam settings object required WhatsApp settings configuration.
     * @bodyParam settings.auto_reply object optional Auto-reply settings.
     * @bodyParam settings.auto_reply.enabled boolean optional Enable auto-reply. Example: true
     * @bodyParam settings.auto_reply.message string optional Auto-reply message. Example: Thank you for contacting us!
     * @bodyParam settings.auto_reply.business_hours_only boolean optional Only auto-reply during business hours. Example: true
     * @bodyParam settings.order_notifications object optional Order notification settings.
     * @bodyParam settings.order_notifications.enabled boolean optional Enable order notifications. Example: true
     * @bodyParam settings.order_notifications.statuses array optional Order statuses to notify about. Example: ["confirmed", "ready", "delivered"]
     *
     * @response 200 {
     *   "success": true,
     *   "message": "WhatsApp settings updated successfully",
     *   "data": {
     *     "settings": {
     *       "auto_reply": {
     *         "enabled": true,
     *         "message": "Thank you for contacting us!",
     *         "business_hours_only": true
     *       },
     *       "order_notifications": {
     *         "enabled": true,
     *         "statuses": ["confirmed", "ready", "delivered"]
     *       }
     *     }
     *   }
     * }
     */
    public function updateSettings(Request $request): JsonResponse
    {
        $request->validate([
            'settings' => 'required|array',
            'settings.auto_reply' => 'sometimes|array',
            'settings.auto_reply.enabled' => 'sometimes|boolean',
            'settings.auto_reply.message' => 'sometimes|string|max:1000',
            'settings.auto_reply.business_hours_only' => 'sometimes|boolean',
            'settings.order_notifications' => 'sometimes|array',
            'settings.order_notifications.enabled' => 'sometimes|boolean',
            'settings.order_notifications.statuses' => 'sometimes|array',
            'settings.customer_support' => 'sometimes|array',
            'settings.catalog_sharing' => 'sometimes|array',
        ]);

        $config = TenantWhatsAppConfig::where('tenant_id', tenant()->id)->firstOrFail();

        // Merge new settings with existing ones
        $currentSettings = $config->settings ?? TenantWhatsAppConfig::getDefaultSettings();
        $newSettings = array_merge_recursive($currentSettings, $request->input('settings'));

        $config->update(['settings' => $newSettings]);

        return $this->successResponse([
            'settings' => $newSettings,
        ], 'WhatsApp settings updated successfully');
    }

    /**
     * Test WhatsApp Configuration
     *
     * Send a test message to verify WhatsApp configuration is working.
     *
     * @authenticated
     *
     * @bodyParam phone_number string required Phone number to send test message to. Example: +*************
     * @bodyParam message string optional Custom test message. Example: This is a test message from our WhatsApp integration.
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Test message sent successfully",
     *   "data": {
     *     "sent_to": "+*************",
     *     "message": "Test message from My Business",
     *     "timestamp": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function test(Request $request): JsonResponse
    {
        $request->validate([
            'phone_number' => 'required|string',
            'message' => 'sometimes|string|max:1000',
        ]);

        $config = TenantWhatsAppConfig::where('tenant_id', tenant()->id)->firstOrFail();

        if (! $config->isConfigured()) {
            return $this->errorResponse('WhatsApp is not properly configured', 400);
        }

        $phoneNumber = $request->input('phone_number');
        $message = $request->input('message', 'Test message from '.tenant()->business->business_name);

        $success = $this->whatsAppService->sendMessage(tenant()->id, $phoneNumber, $message);

        if ($success) {
            return $this->successResponse([
                'sent_to' => $phoneNumber,
                'message' => $message,
                'timestamp' => now()->toISOString(),
            ], 'Test message sent successfully');
        }

        return $this->errorResponse('Failed to send test message', 500);
    }

    /**
     * Delete WhatsApp Configuration
     *
     * Remove WhatsApp configuration for the tenant.
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "WhatsApp configuration deleted successfully"
     * }
     */
    public function destroy(): JsonResponse
    {
        $config = TenantWhatsAppConfig::where('tenant_id', tenant()->id)->first();

        if ($config) {
            $config->delete();
        }

        return $this->successResponse(null, 'WhatsApp configuration deleted successfully');
    }

    /**
     * Verify WhatsApp configuration with the API.
     */
    private function verifyWhatsAppConfiguration(TenantWhatsAppConfig $config): void
    {
        try {
            $profile = $this->apiService
                ->withTenantConfig($config)
                ->getBusinessProfile();

            $config->update([
                'is_verified' => true,
                'verification_status' => 'verified',
                'verified_at' => now(),
                'business_profile' => $profile,
            ]);

        } catch (\Exception $e) {
            $config->update([
                'verification_status' => 'failed',
            ]);

            throw $e;
        }
    }
}
