<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Business;

use App\Enums\ProductCollectionType;
use App\Exceptions\BusinessLogicException;
use App\Http\Controllers\Controller;
use App\Models\Business\Product;
use App\Models\Business\ProductCollection;
use App\Services\Business\BusinessService;
use App\Services\System\LoggingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

/**
 * Product Collection Management Controller
 *
 * Handles tenant-scoped product collection management for businesses.
 * Manages product collections, seasonal catalogs, and product assignments.
 */
class BusinessProductCollectionController extends Controller
{
    use \App\Traits\ApiResponseTrait, \App\Traits\QueryHandlerTrait;

    public function __construct(
        private readonly BusinessService $businessService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get product collections.
     *
     * @group Product Collections
     *
     * @authenticated
     *
     * @queryParam page integer Page number for pagination. Example: 1
     * @queryParam per_page integer Items per page (max 100). Example: 15
     * @queryParam search string Search collections by name or description. Example: summer
     * @queryParam type string Filter by collection type. Example: seasonal
     * @queryParam is_active boolean Filter by active status. Example: true
     * @queryParam sort_by string Sort by field. Example: display_order
     * @queryParam sort_direction string Sort direction (asc, desc). Example: asc
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Product collections retrieved successfully",
     *   "data": {
     *     "data": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *         "name": "Summer Collection",
     *         "description": "Summer specials available until August",
     *         "type": "time_based",
     *         "is_active": true,
     *         "display_order": 1,
     *         "products_count": 12,
     *         "created_at": "2024-01-15T10:30:00Z"
     *       }
     *     ],
     *     "current_page": 1,
     *     "per_page": 15,
     *     "total": 5
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            $query = ProductCollection::where('business_id', $business->id)
                ->withCount('products');

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'name',
                    'description',
                ],
                'sortFields' => [
                    'name',
                    'display_order',
                    'created_at',
                ],
                'filters' => [
                    'type' => ['type' => 'exact'],
                    'is_active' => ['type' => 'boolean'],
                ],
                'message' => 'Product collections retrieved successfully',
                'entityName' => 'collections',
                'transformer' => [$this, 'transformCollection'],
            ]);

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve product collections',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve product collections');
        }
    }

    /**
     * Get specific product collection details.
     *
     * @group Product Collections
     *
     * @authenticated
     *
     * @urlParam collection string required Collection ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Product collection retrieved successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "Summer Collection",
     *     "description": "Summer specials available until August",
     *     "type": "time_based",
     *     "is_active": true,
     *     "active_start_time": "2024-01-15T06:00:00Z",
     *     "active_end_time": "2024-01-15T11:00:00Z",
     *     "display_order": 1,
     *     "products": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *         "name": "Pancakes",
     *         "price": 800,
     *         "display_order": 1
     *       }
     *     ],
     *     "created_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function show(string $collection): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            $productCollection = ProductCollection::where('business_id', $business->id)
                ->where('id', $collection)
                ->with(['products' => function ($query) {
                    $query->orderBy('product_collection_products.display_order', 'asc');
                }])
                ->firstOrFail();

            return $this->successResponse(
                $this->transformCollection($productCollection, true),
                'Product collection retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Product collection not found');

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve product collection',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'collection_id' => $collection,
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve product collection');
        }
    }

    /**
     * Create new product collection.
     *
     * @group Product Collections
     *
     * @authenticated
     *
     * @bodyParam name string required Collection name. Example: Summer Collection
     * @bodyParam description string Collection description. Example: Summer specials available until August
     * @bodyParam type string required Collection type. Example: time_based
     * @bodyParam is_active boolean Collection active status. Example: true
     * @bodyParam active_start_time string Start time for time-based collections. Example: 2024-01-15T06:00:00Z
     * @bodyParam active_end_time string End time for time-based collections. Example: 2024-01-15T11:00:00Z
     * @bodyParam display_order integer Display order. Example: 1
     * @bodyParam image_url string Collection image URL. Example: https://example.com/image.jpg
     * @bodyParam products array Array of product IDs to add to collection. Example: ["019723aa-3202-70dd-a0c1-3565681dd87b"]
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Product collection created successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "Summer Collection",
     *     "description": "Summer specials available until August",
     *     "type": "time_based",
     *     "is_active": true,
     *     "display_order": 1,
     *     "products_count": 5,
     *     "created_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'sometimes|string|max:1000',
            'type' => 'required|string|in:regular,seasonal,time_based,special',
            'is_active' => 'sometimes|boolean',
            'active_start_time' => 'sometimes|date',
            'active_end_time' => 'sometimes|date|after:active_start_time',
            'display_order' => 'sometimes|integer|min:0',
            'image_url' => 'sometimes|url',
            'products' => 'sometimes|array',
            'products.*' => 'uuid|exists:products,id',
        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();

            // Generate unique slug
            $slug = Str::slug($request->input('name'));
            $originalSlug = $slug;
            $counter = 1;

            while (ProductCollection::where('business_id', $business->id)
                ->where('slug', $slug)
                ->exists()) {
                $slug = $originalSlug.'-'.$counter;
                $counter++;
            }

            // Get next display order if not provided
            $displayOrder = $request->input('display_order');
            if ($displayOrder === null) {
                $displayOrder = ProductCollection::where('business_id', $business->id)->max('display_order') + 1;
            }

            // Create collection
            $collection = ProductCollection::create([
                'business_id' => $business->id,
                'name' => $request->name,
                'description' => $request->description,
                'slug' => $slug,
                'type' => ProductCollectionType::from($request->type),
                'is_active' => $request->input('is_active', true),
                'active_start_time' => $request->active_start_time,
                'active_end_time' => $request->active_end_time,
                'display_order' => $displayOrder,
                'image_url' => $request->image_url,
            ]);

            // Add products to collection if provided
            if ($request->has('products') && is_array($request->products)) {
                $products = [];
                foreach ($request->products as $index => $productId) {
                    $products[$productId] = ['display_order' => $index + 1];
                }
                $collection->products()->attach($products);
            }

            $this->loggingService->logInfo(
                'Product collection created successfully',
                [
                    'tenant_id' => tenant()?->id,
                    'business_id' => $business->id,
                    'collection_id' => $collection->id,
                    'user_id' => auth()->id(),
                ]
            );

            return $this->successResponse(
                $this->transformCollection($collection->load('products')),
                'Product collection created successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create product collection',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'request_data' => $request->all(),
                ]
            );

            return $this->serverErrorResponse('Failed to create product collection');
        }
    }

    // Helper Methods

    /**
     * Transform collection for API response.
     */
    public function transformCollection(ProductCollection $collection, bool $detailed = false): array
    {
        $data = [
            'id' => $collection->id,
            'name' => $collection->name,
            'description' => $collection->description,
            'slug' => $collection->slug,
            'type' => $collection->type->value,
            'is_active' => $collection->is_active,
            'active_start_time' => $collection->active_start_time?->toISOString(),
            'active_end_time' => $collection->active_end_time?->toISOString(),
            'display_order' => $collection->display_order,
            'image_url' => $collection->image_url,
            'products_count' => $collection->products?->count() ?? $collection->products_count ?? 0,
            'created_at' => $collection->created_at->toISOString(),
        ];

        if ($detailed && $collection->relationLoaded('products')) {
            $data['products'] = $collection->products->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'price' => $product->getEffectivePrice(),
                    'display_order' => $product->pivot->display_order ?? 0,
                ];
            })->toArray();
        }

        return $data;
    }
}
