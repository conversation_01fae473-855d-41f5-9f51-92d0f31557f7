<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Business;

use App\Exceptions\BusinessLogicException;
use App\Http\Controllers\Controller;
use App\Models\Business\Product;
use App\Models\Business\ProductBundle;
use App\Services\Business\BusinessService;
use App\Services\System\LoggingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Product Bundle Management Controller
 *
 * Handles tenant-scoped product bundle management for businesses.
 * Manages bundle creation, updates, and product assignments.
 */
class BusinessProductBundleController extends Controller
{
    use \App\Traits\ApiResponseTrait, \App\Traits\QueryHandlerTrait;

    public function __construct(
        private readonly BusinessService $businessService,
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get product bundles.
     *
     * @group Product Bundles
     *
     * @authenticated
     *
     * @queryParam page integer Page number for pagination. Example: 1
     * @queryParam per_page integer Items per page (max 100). Example: 15
     * @queryParam search string Search bundles by name or description. Example: combo
     * @queryParam is_active boolean Filter by active status. Example: true
     * @queryParam sort_by string Sort by field. Example: name
     * @queryParam sort_direction string Sort direction (asc, desc). Example: asc
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Product bundles retrieved successfully",
     *   "data": {
     *     "data": [
     *       {
     *         "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *         "name": "Combo Meal",
     *         "description": "Main dish with sides and drink",
     *         "price": 2500,
     *         "discount_amount": 500,
     *         "is_active": true,
     *         "products_count": 3,
     *         "created_at": "2024-01-15T10:30:00Z"
     *       }
     *     ],
     *     "current_page": 1,
     *     "per_page": 15,
     *     "total": 10
     *   }
     * }
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            $query = ProductBundle::where('business_id', $business->id)
                ->withCount('bundleProducts');

            return $this->handleQuery($query, $request, [
                'searchFields' => [
                    'name',
                    'description',
                ],
                'sortFields' => [
                    'name',
                    'price',
                    'discount_amount',
                    'created_at',
                ],
                'filters' => [
                    'is_active' => ['type' => 'boolean'],
                ],
                'message' => 'Product bundles retrieved successfully',
                'entityName' => 'bundles',
                'transformer' => [$this, 'transformBundle'],
            ]);

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve product bundles',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve product bundles');
        }
    }

    /**
     * Get specific product bundle details.
     *
     * @group Product Bundles
     *
     * @authenticated
     *
     * @urlParam bundle string required Bundle ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Product bundle retrieved successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "Combo Meal",
     *     "description": "Main dish with sides and drink",
     *     "price": 2500,
     *     "discount_amount": 500,
     *     "is_active": true,
     *     "products": [
     *       {
     *         "product_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *         "product_name": "Burger",
     *         "quantity": 1,
     *         "unit_price": 1500
     *       }
     *     ],
     *     "created_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function show(string $bundle): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            $productBundle = ProductBundle::where('business_id', $business->id)
                ->where('id', $bundle)
                ->with(['bundleProducts.product', 'bundleProducts.variant'])
                ->firstOrFail();

            return $this->successResponse(
                $this->transformBundle($productBundle, true),
                'Product bundle retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Product bundle not found');

        } catch (BusinessLogicException $e) {
            return $this->errorResponse(
                $e->getMessage(),
                422,
                null,
                $e->getErrorCode()
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to retrieve product bundle',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'bundle_id' => $bundle,
                ]
            );

            return $this->serverErrorResponse('Failed to retrieve product bundle');
        }
    }

    /**
     * Create new product bundle.
     *
     * @group Product Bundles
     *
     * @authenticated
     *
     * @bodyParam name string required Bundle name. Example: Combo Meal
     * @bodyParam description string Bundle description. Example: Main dish with sides and drink
     * @bodyParam price number required Bundle price. Example: 2500
     * @bodyParam discount_amount number Discount amount. Example: 500
     * @bodyParam is_active boolean Bundle active status. Example: true
     * @bodyParam products array required Array of products in bundle. Example: [{"product_id": "123", "quantity": 1}]
     * @bodyParam products.*.product_id string required Product ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87b
     * @bodyParam products.*.variant_id string Product variant ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87c
     * @bodyParam products.*.quantity integer required Product quantity. Example: 1
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Product bundle created successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *     "name": "Combo Meal",
     *     "description": "Main dish with sides and drink",
     *     "price": 2500,
     *     "discount_amount": 500,
     *     "is_active": true,
     *     "products_count": 3,
     *     "created_at": "2024-01-15T10:30:00Z"
     *   }
     * }
     */
    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'description' => 'sometimes|string|max:1000',
            'price' => 'required|numeric|min:0',
            'discount_amount' => 'sometimes|numeric|min:0',
            'is_active' => 'sometimes|boolean',
            'products' => 'required|array|min:1',
            'products.*.product_id' => 'required|uuid|exists:products,id',
            'products.*.variant_id' => 'sometimes|uuid|exists:product_variants,id',
            'products.*.quantity' => 'required|integer|min:1',
        ]);

        try {
            $business = $this->businessService->getCurrentBusiness();

            // Create bundle
            $bundle = ProductBundle::create([
                'tenant_id' => tenant()?->id,
                'business_id' => $business->id,
                'name' => $request->name,
                'description' => $request->description,
                'price' => $request->price,
                'discount_amount' => $request->input('discount_amount', 0),
                'is_active' => $request->input('is_active', true),
            ]);

            // Add products to bundle
            foreach ($request->products as $productData) {
                $bundle->bundleProducts()->create([
                    'tenant_id' => tenant()?->id,
                    'product_id' => $productData['product_id'],
                    'variant_id' => $productData['variant_id'] ?? null,
                    'quantity' => $productData['quantity'],
                ]);
            }

            $this->loggingService->logInfo(
                'Product bundle created successfully',
                [
                    'tenant_id' => tenant()?->id,
                    'business_id' => $business->id,
                    'bundle_id' => $bundle->id,
                    'user_id' => auth()->id(),
                ]
            );

            return $this->successResponse(
                $this->transformBundle($bundle->load('bundleProducts')),
                'Product bundle created successfully',
                201
            );

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'Failed to create product bundle',
                $e,
                [
                    'tenant_id' => tenant()?->id,
                    'user_id' => auth()->id(),
                    'request_data' => $request->all(),
                ]
            );

            return $this->serverErrorResponse('Failed to create product bundle');
        }
    }

    // Helper Methods

    /**
     * Transform bundle for API response.
     */
    public function transformBundle(ProductBundle $bundle, bool $detailed = false): array
    {
        $data = [
            'id' => $bundle->id,
            'name' => $bundle->name,
            'description' => $bundle->description,
            'price' => $bundle->price,
            'discount_amount' => $bundle->discount_amount,
            'is_active' => $bundle->is_active,
            'products_count' => $bundle->bundleProducts?->count() ?? $bundle->bundle_products_count ?? 0,
            'created_at' => $bundle->created_at->toISOString(),
        ];

        if ($detailed && $bundle->relationLoaded('bundleProducts')) {
            $data['products'] = $bundle->bundleProducts->map(function ($bundleProduct) {
                return [
                    'product_id' => $bundleProduct->product_id,
                    'product_name' => $bundleProduct->product?->name,
                    'variant_id' => $bundleProduct->variant_id,
                    'variant_name' => $bundleProduct->variant?->name,
                    'quantity' => $bundleProduct->quantity,
                    'unit_price' => $bundleProduct->variant
                        ? $bundleProduct->variant->getEffectivePrice()
                        : $bundleProduct->product?->getEffectivePrice(),
                ];
            })->toArray();
        }

        return $data;
    }
}
