<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Business;

use App\Http\Controllers\Controller;
use App\Services\Business\BusinessService;
use App\Services\Business\ProductCatalogService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Validation\ValidationException;

/**
 * @group Product Catalog Management
 *
 * APIs for managing branch-specific product catalog customizations
 */
class BusinessProductCatalogController extends Controller
{
    use \App\Traits\ApiResponseTrait, \App\Traits\QueryHandlerTrait;

    public function __construct(
        private readonly BusinessService $businessService,
        private readonly ProductCatalogService $productCatalogService
    ) {}

    /**
     * Get product collections for a specific branch.
     *
     * @group Product Catalog Management
     *
     * @authenticated
     *
     * @urlParam branchId string required The branch ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Branch product collections retrieved successfully",
     *   "data": [
     *     {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87a",
     *       "name": "Breakfast Menu",
     *       "description": "Morning specials available until 11 AM",
     *       "type": "time_based",
     *       "is_active": true,
     *       "display_order": 1,
     *       "is_branch_specific": false,
     *       "branch_customization": {
     *         "is_active": true,
     *         "active_start_time": "2024-01-15T06:00:00Z",
     *         "active_end_time": "2024-01-15T11:00:00Z",
     *         "display_order": 2,
     *         "custom_settings": {}
     *       }
     *     }
     *   ]
     * }
     */
    public function getBranchMenuCollections(Request $request, string $branchId): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            // Validate that the branch belongs to the current business
            $branch = $business->branches()->findOrFail($branchId);

            $collections = $this->productCatalogService->getProductCollectionsForBranch($branchId);

            $transformedCollections = $collections->map(function ($collection) {
                $customization = $collection->branchCustomizations->first();

                return [
                    'id' => $collection->id,
                    'name' => $collection->name,
                    'description' => $collection->description,
                    'type' => $collection->type->value,
                    'is_active' => $collection->is_active,
                    'display_order' => $collection->display_order,
                    'is_branch_specific' => $collection->isBranchSpecific(),
                    'branch_customization' => $customization ? [
                        'id' => $customization->id,
                        'is_active' => $customization->is_active,
                        'active_start_time' => $customization->active_start_time?->toISOString(),
                        'active_end_time' => $customization->active_end_time?->toISOString(),
                        'display_order' => $customization->display_order,
                        'custom_settings' => $customization->custom_settings,
                    ] : null,
                ];
            });

            return $this->successResponse(
                $transformedCollections,
                'Branch product collections retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Branch not found');
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve branch product collections', 500);
        }
    }

    /**
     * Get products for a specific branch with overrides applied.
     *
     * @group Menu Branch Management
     *
     * @authenticated
     *
     * @urlParam branchId string required The branch ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @queryParam menu_collection_id string Filter by menu collection ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87b
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Branch products retrieved successfully",
     *   "data": [
     *     {
     *       "id": "019723aa-3202-70dd-a0c1-3565681dd87c",
     *       "name": "Grilled Chicken",
     *       "description": "Tender grilled chicken breast",
     *       "price": 1500,
     *       "stock_quantity": 50,
     *       "is_available": true,
     *       "branch_price": 1400,
     *       "branch_stock": 45,
     *       "branch_is_available": true,
     *       "branch_description": "Special branch description",
     *       "has_branch_override": true
     *     }
     *   ]
     * }
     */
    public function getBranchProducts(Request $request, string $branchId): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            // Validate that the branch belongs to the current business
            $branch = $business->branches()->findOrFail($branchId);

            $menuCollectionId = $request->query('menu_collection_id');
            $products = $this->productCatalogService->getProductsForBranch($branchId, $menuCollectionId);

            $transformedProducts = $products->map(function ($product) {
                $hasOverride = $product->branchOverrides->isNotEmpty();

                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'description' => $product->description,
                    'price' => $product->price,
                    'stock_quantity' => $product->stock_quantity,
                    'is_available' => $product->is_available,
                    'branch_price' => $product->branch_price,
                    'branch_stock' => $product->branch_stock,
                    'branch_is_available' => $product->branch_is_available,
                    'branch_description' => $product->branch_description,
                    'branch_custom_attributes' => $product->branch_custom_attributes,
                    'has_branch_override' => $hasOverride,
                ];
            });

            return $this->successResponse(
                $transformedProducts,
                'Branch products retrieved successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Branch not found');
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to retrieve branch products', 500);
        }
    }

    /**
     * Create a branch-specific menu customization.
     *
     * @group Menu Branch Management
     *
     * @authenticated
     *
     * @urlParam branchId string required The branch ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam menu_collection_id string required Menu collection ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87b
     * @bodyParam is_active boolean Customization active status. Example: true
     * @bodyParam active_start_time string Start time for customization. Example: 2024-01-15T06:00:00Z
     * @bodyParam active_end_time string End time for customization. Example: 2024-01-15T11:00:00Z
     * @bodyParam display_order integer Display order override. Example: 2
     * @bodyParam custom_settings object Custom settings for the branch. Example: {"pricing_adjustment": 0.1}
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Branch menu customization created successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87d",
     *     "menu_collection_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *     "is_active": true,
     *     "active_start_time": "2024-01-15T06:00:00Z",
     *     "active_end_time": "2024-01-15T11:00:00Z",
     *     "display_order": 2,
     *     "custom_settings": {"pricing_adjustment": 0.1}
     *   }
     * }
     */
    public function createBranchCustomization(Request $request, string $branchId): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            // Validate that the branch belongs to the current business
            $branch = $business->branches()->findOrFail($branchId);

            $request->validate([
                'menu_collection_id' => 'required|uuid|exists:menu_collections,id',
                'is_active' => 'sometimes|boolean',
                'active_start_time' => 'sometimes|nullable|date',
                'active_end_time' => 'sometimes|nullable|date|after:active_start_time',
                'display_order' => 'sometimes|nullable|integer|min:0',
                'custom_settings' => 'sometimes|array',
            ]);

            $customization = $this->productCatalogService->createBranchCustomization(
                $branchId,
                $request->input('menu_collection_id'),
                $request->only(['is_active', 'active_start_time', 'active_end_time', 'display_order', 'custom_settings'])
            );

            return $this->successResponse(
                [
                    'id' => $customization->id,
                    'menu_collection_id' => $customization->menu_collection_id,
                    'is_active' => $customization->is_active,
                    'active_start_time' => $customization->active_start_time?->toISOString(),
                    'active_end_time' => $customization->active_end_time?->toISOString(),
                    'display_order' => $customization->display_order,
                    'custom_settings' => $customization->custom_settings,
                ],
                'Branch menu customization created successfully',
                201
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Branch not found');
        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to create branch menu customization', 500);
        }
    }

    /**
     * Update a branch-specific menu customization.
     *
     * @group Menu Branch Management
     *
     * @authenticated
     *
     * @urlParam customizationId string required The customization ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87d
     *
     * @bodyParam is_active boolean Customization active status. Example: false
     * @bodyParam active_start_time string Start time for customization. Example: 2024-01-15T07:00:00Z
     * @bodyParam active_end_time string End time for customization. Example: 2024-01-15T12:00:00Z
     * @bodyParam display_order integer Display order override. Example: 3
     * @bodyParam custom_settings object Custom settings for the branch. Example: {"pricing_adjustment": 0.15}
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Branch menu customization updated successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87d",
     *     "menu_collection_id": "019723aa-3202-70dd-a0c1-3565681dd87b",
     *     "is_active": false,
     *     "active_start_time": "2024-01-15T07:00:00Z",
     *     "active_end_time": "2024-01-15T12:00:00Z",
     *     "display_order": 3,
     *     "custom_settings": {"pricing_adjustment": 0.15}
     *   }
     * }
     */
    public function updateBranchCustomization(Request $request, string $customizationId): JsonResponse
    {
        try {
            $request->validate([
                'is_active' => 'sometimes|boolean',
                'active_start_time' => 'sometimes|nullable|date',
                'active_end_time' => 'sometimes|nullable|date|after:active_start_time',
                'display_order' => 'sometimes|nullable|integer|min:0',
                'custom_settings' => 'sometimes|array',
            ]);

            $customization = $this->productCatalogService->updateBranchCustomization(
                $customizationId,
                $request->only(['is_active', 'active_start_time', 'active_end_time', 'display_order', 'custom_settings'])
            );

            return $this->successResponse(
                [
                    'id' => $customization->id,
                    'menu_collection_id' => $customization->menu_collection_id,
                    'is_active' => $customization->is_active,
                    'active_start_time' => $customization->active_start_time?->toISOString(),
                    'active_end_time' => $customization->active_end_time?->toISOString(),
                    'display_order' => $customization->display_order,
                    'custom_settings' => $customization->custom_settings,
                ],
                'Branch menu customization updated successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Customization not found');
        } catch (ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to update branch menu customization', 500);
        }
    }

    /**
     * Delete a branch-specific menu customization.
     *
     * @group Menu Branch Management
     *
     * @authenticated
     *
     * @urlParam customizationId string required The customization ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87d
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Branch menu customization deleted successfully"
     * }
     */
    public function deleteBranchCustomization(string $customizationId): JsonResponse
    {
        try {
            $this->productCatalogService->deleteBranchCustomization($customizationId);

            return $this->successResponse(
                null,
                'Branch menu customization deleted successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Customization not found');
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to delete branch menu customization', 500);
        }
    }

    /**
     * Create a branch-specific product override.
     *
     * @group Menu Branch Management
     *
     * @authenticated
     *
     * @urlParam branchId string required The branch ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     *
     * @bodyParam product_id string required Product ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87c
     * @bodyParam is_available boolean Product availability override. Example: false
     * @bodyParam price_override number Product price override. Example: 1400.50
     * @bodyParam stock_override integer Product stock override. Example: 45
     * @bodyParam description_override string Product description override. Example: "Special branch description"
     * @bodyParam custom_attributes object Custom attributes for the product. Example: {"special_note": "Limited time offer"}
     *
     * @response 201 {
     *   "success": true,
     *   "message": "Branch product override created successfully",
     *   "data": {
     *     "id": "019723aa-3202-70dd-a0c1-3565681dd87e",
     *     "product_id": "019723aa-3202-70dd-a0c1-3565681dd87c",
     *     "is_available": false,
     *     "price_override": 1400.50,
     *     "stock_override": 45,
     *     "description_override": "Special branch description",
     *     "custom_attributes": {"special_note": "Limited time offer"}
     *   }
     * }
     */
    public function createProductOverride(Request $request, string $branchId): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            // Validate that the branch belongs to the current business
            $branch = $business->branches()->findOrFail($branchId);

            $request->validate([
                'product_id' => 'required|uuid|exists:products,id',
                'is_available' => 'sometimes|nullable|boolean',
                'price_override' => 'sometimes|nullable|numeric|min:0',
                'stock_override' => 'sometimes|nullable|integer|min:0',
                'description_override' => 'sometimes|nullable|string|max:1000',
                'custom_attributes' => 'sometimes|array',
            ]);

            $override = $this->productCatalogService->createProductOverride(
                $branchId,
                $request->input('product_id'),
                $request->only(['is_available', 'price_override', 'stock_override', 'description_override', 'custom_attributes'])
            );

            return $this->successResponse(
                [
                    'id' => $override->id,
                    'product_id' => $override->product_id,
                    'is_available' => $override->is_available,
                    'price_override' => $override->price_override,
                    'stock_override' => $override->stock_override,
                    'description_override' => $override->description_override,
                    'custom_attributes' => $override->custom_attributes,
                ],
                'Branch product override created successfully',
                201
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Branch not found');
        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (ValidationException $e) {
            return $this->validationErrorResponse($e->errors());
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to create branch product override', 500);
        }
    }

    /**
     * Copy menu customizations from one branch to another.
     *
     * @group Menu Branch Management
     *
     * @authenticated
     *
     * @urlParam sourceBranchId string required The source branch ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87a
     * @urlParam targetBranchId string required The target branch ID. Example: 019723aa-3202-70dd-a0c1-3565681dd87b
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Menu customizations copied successfully",
     *   "data": {
     *     "copied_count": 5
     *   }
     * }
     */
    public function copyBranchCustomizations(string $sourceBranchId, string $targetBranchId): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            // Validate that both branches belong to the current business
            $business->branches()->findOrFail($sourceBranchId);
            $business->branches()->findOrFail($targetBranchId);

            $copiedCount = $this->productCatalogService->copyBranchCustomizations($sourceBranchId, $targetBranchId);

            return $this->successResponse(
                ['copied_count' => $copiedCount],
                'Menu customizations copied successfully'
            );

        } catch (\Illuminate\Database\Eloquent\ModelNotFoundException $e) {
            return $this->notFoundResponse('Branch not found');
        } catch (\InvalidArgumentException $e) {
            return $this->errorResponse($e->getMessage(), 422);
        } catch (\Exception $e) {
            return $this->errorResponse('Failed to copy menu customizations', 500);
        }
    }
}
