<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant\Business;

use App\Http\Controllers\Controller;
use App\Http\Requests\Business\GenerateQrCodeRequest;
use App\Services\Business\BusinessService;
use App\Services\Business\QrCodeService;
use App\Traits\ApiResponseTrait;
use App\Traits\QueryHandlerTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * QR Code Management Controller
 *
 * Handles QR code generation and management for business menus.
 */
class BusinessQrCodeController extends Controller
{
    use ApiResponseTrait, QueryHandlerTrait;

    public function __construct(
        private readonly QrCodeService $qrCodeService,
        private readonly BusinessService $businessService
    ) {}

    /**
     * Generate QR code for business menu.
     *
     * @group QR Code Management
     *
     * @authenticated
     *
     * @bodyParam style string The QR code style (classic, branded, minimal). Example: branded
     * @bodyParam size integer The QR code size in pixels (200-800). Example: 400
     * @bodyParam format string The output format (png, svg, pdf). Example: png
     * @bodyParam table_number string Optional table number for restaurant context. Example: T-12
     * @bodyParam campaign string Optional campaign name for tracking. Example: summer_menu
     */
    public function generateMenuQrCode(GenerateQrCodeRequest $request): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            $options = [
                'style' => $request->input('style', 'classic'),
                'size' => $request->input('size', 300),
                'format' => $request->input('format', 'png'),
                'table_number' => $request->input('table_number'),
                'campaign' => $request->input('campaign', 'menu_qr'),
            ];

            $result = $this->qrCodeService->generateMenuQrCode($business, $options);

            return $this->successResponse($result, 'QR code generated successfully');

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Failed to generate QR code: '.$e->getMessage(),
                500
            );
        }
    }

    /**
     * Generate QR code for specific category.
     *
     * @group QR Code Management
     *
     * @authenticated
     *
     * @urlParam categoryId string required The category ID. Example: 01234567-89ab-cdef-0123-456789abcdef
     *
     * @bodyParam size integer The QR code size in pixels (200-800). Example: 300
     * @bodyParam format string The output format (png, svg). Example: png
     * @bodyParam table_number string Optional table number. Example: T-12
     */
    public function generateCategoryQrCode(
        string $categoryId,
        GenerateQrCodeRequest $request
    ): JsonResponse {
        try {
            $business = $this->businessService->getCurrentBusiness();

            $category = $business->categories()
                ->where('id', $categoryId)
                ->first();

            if (! $category) {
                return $this->errorResponse('Category not found', 404);
            }

            $options = [
                'size' => $request->input('size', 300),
                'format' => $request->input('format', 'png'),
                'table_number' => $request->input('table_number'),
                'campaign' => $request->input('campaign', 'category_qr'),
            ];

            $result = $this->qrCodeService->generateCategoryQrCode(
                $business,
                $category,
                $options
            );

            return $this->successResponse($result, 'Category QR code generated successfully');

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Failed to generate category QR code: '.$e->getMessage(),
                500
            );
        }
    }

    /**
     * Get QR code history for business.
     *
     * @group QR Code Management
     *
     * @authenticated
     *
     * @queryParam page integer Page number for pagination. Example: 1
     * @queryParam per_page integer Items per page (max 50). Example: 15
     * @queryParam type string Filter by QR code type (menu, category). Example: menu
     */
    public function getQrCodeHistory(Request $request): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            // For now, return empty array as we haven't implemented QR code history storage
            // This would be implemented in Phase 2 with proper database tracking

            $history = [
                'data' => [],
                'meta' => [
                    'current_page' => 1,
                    'per_page' => 15,
                    'total' => 0,
                    'last_page' => 1,
                ],
            ];

            return $this->successResponse($history, 'QR code history retrieved successfully');

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Failed to retrieve QR code history: '.$e->getMessage(),
                500
            );
        }
    }

    /**
     * Download QR code in specified format.
     *
     * @group QR Code Management
     *
     * @authenticated
     *
     * @urlParam qrCodeId string required The QR code ID. Example: qr-menu-classic-123-1234567890
     *
     * @queryParam format string Download format (png, svg, pdf). Example: pdf
     */
    public function downloadQrCode(string $qrCodeId, Request $request): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();
            $format = $request->query('format', 'png');

            // Validate QR code belongs to business
            if (! str_contains($qrCodeId, (string) $business->id)) {
                return $this->errorResponse('QR code not found', 404);
            }

            // For now, return download URL
            // In full implementation, this would handle file streaming
            $downloadUrl = asset("storage/qr-codes/{$business->tenant_id}/{$qrCodeId}.{$format}");

            return $this->successResponse([
                'download_url' => $downloadUrl,
                'filename' => "{$qrCodeId}.{$format}",
                'format' => $format,
            ], 'Download link generated successfully');

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Failed to generate download link: '.$e->getMessage(),
                500
            );
        }
    }

    /**
     * Get QR code analytics.
     *
     * @group QR Code Management
     *
     * @authenticated
     *
     * @queryParam period string Analytics period (7d, 30d, 90d). Example: 30d
     * @queryParam qr_code_id string Optional specific QR code ID. Example: qr-menu-classic-123
     */
    public function getQrCodeAnalytics(Request $request): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();
            $period = $request->query('period', '30d');
            $qrCodeId = $request->query('qr_code_id');

            // For now, return mock analytics data
            // This would be implemented with proper tracking in Phase 2
            $analytics = [
                'total_scans' => 0,
                'unique_visitors' => 0,
                'conversion_rate' => 0.0,
                'top_scanning_times' => [],
                'device_breakdown' => [
                    'mobile' => 0,
                    'tablet' => 0,
                    'desktop' => 0,
                ],
                'location_breakdown' => [],
                'period' => $period,
            ];

            return $this->successResponse($analytics, 'QR code analytics retrieved successfully');

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Failed to retrieve QR code analytics: '.$e->getMessage(),
                500
            );
        }
    }

    /**
     * Bulk generate QR codes for all categories.
     *
     * @group QR Code Management
     *
     * @authenticated
     *
     * @bodyParam size integer The QR code size in pixels (200-800). Example: 300
     * @bodyParam format string The output format (png, svg). Example: png
     * @bodyParam include_menu boolean Whether to include main menu QR code. Example: true
     */
    public function bulkGenerateQrCodes(GenerateQrCodeRequest $request): JsonResponse
    {
        try {
            $business = $this->businessService->getCurrentBusiness();

            $options = [
                'size' => $request->input('size', 300),
                'format' => $request->input('format', 'png'),
                'campaign' => 'bulk_generation',
            ];

            $results = [];

            // Generate main menu QR code if requested
            if ($request->boolean('include_menu', true)) {
                $results['menu'] = $this->qrCodeService->generateMenuQrCode($business, $options);
            }

            // Generate QR codes for all categories
            $categories = $business->categories()->get();
            $results['categories'] = [];

            foreach ($categories as $category) {
                $results['categories'][$category->id] = $this->qrCodeService->generateCategoryQrCode(
                    $business,
                    $category,
                    $options
                );
            }

            return $this->successResponse([
                'generated_count' => count($results['categories']) + (isset($results['menu']) ? 1 : 0),
                'results' => $results,
            ], 'Bulk QR code generation completed successfully');

        } catch (\Exception $e) {
            return $this->errorResponse(
                'Failed to bulk generate QR codes: '.$e->getMessage(),
                500
            );
        }
    }
}
