<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Tenant;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\User\ChangePasswordRequest;
use App\Http\Requests\Api\V1\User\DeactivateAccountRequest;
use App\Http\Requests\Api\V1\User\UpdatePreferencesRequest;
use App\Http\Requests\Api\V1\User\UpdateProfileRequest;
use App\Services\User\UserProfileService;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Tenant User Profile Controller
 *
 * Handles user profile operations for tenant domain users.
 * This includes business owners, staff, delivery providers, and their teams.
 */
class TenantUserProfileController extends Controller
{
    use ApiResponseTrait;

    public function __construct(
        private readonly UserProfileService $userProfileService
    ) {}

    /**
     * Get user profile for tenant users only.
     */
    public function show(Request $request): JsonResponse
    {
        $user = $request->user();

        // Ensure this is a tenant user (has tenant_id)
        if ($user->tenant_id === null) {
            return $this->forbiddenResponse('This endpoint is for tenant users only. Please use the central domain.');
        }

        // Validate tenant access
        if (! $this->validateTenantAccess($user)) {
            return $this->forbiddenResponse('Access denied to this tenant');
        }

        $profileResource = $this->userProfileService->getProfile($user, 'tenant');

        return $this->successResponse($profileResource, 'Tenant user profile retrieved successfully');
    }

    /**
     * Update user profile for tenant users only.
     */
    public function update(UpdateProfileRequest $request): JsonResponse
    {
        $user = $request->user();

        // Ensure this is a tenant user (has tenant_id)
        if ($user->tenant_id === null) {
            return $this->forbiddenResponse('This endpoint is for tenant users only. Please use the central domain.');
        }

        // Validate tenant access
        if (! $this->validateTenantAccess($user)) {
            return $this->forbiddenResponse('Access denied to this tenant');
        }

        $data = $request->validated();

        $updatedUser = $this->userProfileService->updateProfile($user, $data);
        $profileResource = $this->userProfileService->getProfile($updatedUser, 'tenant');

        return $this->successResponse($profileResource, 'Tenant user profile updated successfully');
    }

    /**
     * Change user password for tenant users only.
     */
    public function changePassword(ChangePasswordRequest $request): JsonResponse
    {
        $user = $request->user();

        // Ensure this is a tenant user (has tenant_id)
        if ($user->tenant_id === null) {
            return $this->forbiddenResponse('This endpoint is for tenant users only. Please use the central domain.');
        }

        // Validate tenant access
        if (! $this->validateTenantAccess($user)) {
            return $this->forbiddenResponse('Access denied to this tenant');
        }

        $data = $request->validated();

        $this->userProfileService->changePassword(
            $user,
            $data['current_password'],
            $data['new_password']
        );

        return $this->successResponse(null, 'Password changed successfully');
    }

    /**
     * Get user preferences for tenant users only.
     */
    public function getPreferences(Request $request): JsonResponse
    {
        $user = $request->user();

        // Ensure this is a tenant user (has tenant_id)
        if ($user->tenant_id === null) {
            return $this->forbiddenResponse('This endpoint is for tenant users only. Please use the central domain.');
        }

        // Validate tenant access
        if (! $this->validateTenantAccess($user)) {
            return $this->forbiddenResponse('Access denied to this tenant');
        }

        $preferences = $this->userProfileService->getUserPreferences($user);

        return $this->successResponse($preferences, 'Tenant user preferences retrieved successfully');
    }

    /**
     * Update user preferences for tenant users only.
     */
    public function updatePreferences(UpdatePreferencesRequest $request): JsonResponse
    {
        $user = $request->user();

        // Ensure this is a tenant user (has tenant_id)
        if ($user->tenant_id === null) {
            return $this->forbiddenResponse('This endpoint is for tenant users only. Please use the central domain.');
        }

        // Validate tenant access
        if (! $this->validateTenantAccess($user)) {
            return $this->forbiddenResponse('Access denied to this tenant');
        }

        $preferences = $request->validated();

        $updatedPreferences = $this->userProfileService->updateUserPreferences($user, $preferences);

        return $this->successResponse($updatedPreferences, 'Tenant user preferences updated successfully');
    }

    /**
     * Deactivate user account for tenant users only.
     */
    public function deactivate(DeactivateAccountRequest $request): JsonResponse
    {
        $user = $request->user();

        // Ensure this is a tenant user (has tenant_id)
        if ($user->tenant_id === null) {
            return $this->forbiddenResponse('This endpoint is for tenant users only. Please use the central domain.');
        }

        // Validate tenant access
        if (! $this->validateTenantAccess($user)) {
            return $this->forbiddenResponse('Access denied to this tenant');
        }

        $data = $request->validated();

        $this->userProfileService->deactivateAccount(
            $user,
            $data['password'],
            $data['reason'] ?? null
        );

        return $this->successResponse(null, 'Tenant user account deactivated successfully');
    }

    /**
     * Validate that the user has access to the current tenant.
     */
    private function validateTenantAccess($user): bool
    {
        $tenant = tenant();

        if (! $tenant) {
            return false;
        }

        // Check if user belongs to this tenant
        return $user->tenant_id === $tenant->id;
    }
}
