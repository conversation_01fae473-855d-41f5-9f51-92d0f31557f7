<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Services\System\LoggingService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * API Documentation Controller
 *
 * Provides dynamic API documentation and examples based on user's subscription tier.
 * Includes endpoint details, usage limits, and code examples.
 */
class ApiDocumentationController extends Controller
{
    use \App\Traits\ApiResponseTrait;

    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get API documentation overview.
     *
     * @group API Documentation
     *
     * @authenticated
     *
     * @queryParam version string API version to get documentation for. Example: v1
     * @queryParam format string Documentation format (json, markdown). Example: json
     * @queryParam include_examples boolean Include code examples. Example: true
     *
     * @response 200 {
     *   "success": true,
     *   "message": "API documentation retrieved successfully",
     *   "data": {
     *     "api_version": "v1",
     *     "base_url": "https://api.deliverynexus.com/api/v1",
     *     "authentication": {
     *       "type": "Bearer Token",
     *       "header": "Authorization: Bearer {token}",
     *       "description": "Include your API token in the Authorization header"
     *     },
     *     "rate_limits": {
     *       "current_tier": "business",
     *       "requests_per_minute": 1000,
     *       "requests_per_hour": 50000,
     *       "burst_limit": 100
     *     },
     *     "available_endpoints": {
     *       "orders": {
     *         "base_path": "/orders",
     *         "methods": ["GET", "POST", "PUT", "DELETE"],
     *         "description": "Order management endpoints",
     *         "tier_required": "free"
     *       },
     *       "analytics": {
     *         "base_path": "/analytics",
     *         "methods": ["GET", "POST"],
     *         "description": "Business analytics and reporting",
     *         "tier_required": "business"
     *       }
     *     },
     *     "subscription_info": {
     *       "current_tier": "business",
     *       "features_available": ["bulk_operations", "advanced_analytics", "real_time_tracking"],
     *       "upgrade_benefits": ["custom_reports", "webhook_subscriptions", "priority_support"]
     *     }
     *   }
     * }
     */
    public function getApiDocumentation(Request $request): JsonResponse
    {
        $request->validate([
            'version' => 'sometimes|string|in:v1',
            'format' => 'sometimes|string|in:json,markdown',
            'include_examples' => 'sometimes|boolean',
        ]);

        try {
            $user = auth()->user();
            $version = $request->input('version', 'v1');
            $format = $request->input('format', 'json');
            $includeExamples = $request->boolean('include_examples', false);

            // Get user's subscription tier and limits
            $subscriptionInfo = $this->getUserSubscriptionInfo($user);
            $rateLimits = $this->getUserRateLimits($user);

            // Get available endpoints based on user's tier
            $availableEndpoints = $this->getAvailableEndpoints($subscriptionInfo['tier']);

            $documentation = [
                'api_version' => $version,
                'base_url' => config('app.url')."/api/{$version}",
                'authentication' => [
                    'type' => 'Bearer Token',
                    'header' => 'Authorization: Bearer {token}',
                    'description' => 'Include your API token in the Authorization header',
                    'token_location' => 'You can find your API token in your account settings',
                ],
                'rate_limits' => $rateLimits,
                'available_endpoints' => $availableEndpoints,
                'subscription_info' => $subscriptionInfo,
            ];

            // Add code examples if requested
            if ($includeExamples) {
                $documentation['code_examples'] = $this->getCodeExamples();
            }

            // Add common response formats
            $documentation['response_formats'] = $this->getResponseFormats();

            // Add error codes reference
            $documentation['error_codes'] = $this->getErrorCodes();

            // Convert to markdown if requested
            if ($format === 'markdown') {
                $documentation = $this->convertToMarkdown($documentation);
            }

            return $this->successResponse($documentation, 'API documentation retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve API documentation', $e, [
                'user_id' => auth()->id(),
                'version' => $request->input('version'),
                'format' => $request->input('format'),
            ]);

            return $this->errorResponse('Failed to retrieve API documentation', 500);
        }
    }

    /**
     * Get detailed information about a specific endpoint.
     *
     * @group API Documentation
     *
     * @authenticated
     *
     * @urlParam endpoint string required The endpoint path. Example: orders
     *
     * @queryParam method string HTTP method to get details for. Example: GET
     * @queryParam include_examples boolean Include code examples for this endpoint. Example: true
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Endpoint details retrieved successfully",
     *   "data": {
     *     "endpoint": "/api/v1/orders",
     *     "methods": {
     *       "GET": {
     *         "description": "Retrieve a list of orders",
     *         "parameters": {
     *           "query": {
     *             "page": {"type": "integer", "description": "Page number", "example": 1},
     *             "per_page": {"type": "integer", "description": "Items per page", "example": 15}
     *           }
     *         },
     *         "responses": {
     *           "200": {"description": "Success", "example": "..."},
     *           "401": {"description": "Unauthorized", "example": "..."}
     *         },
     *         "rate_limit": "100 requests per minute",
     *         "tier_required": "free"
     *       }
     *     },
     *     "code_examples": {
     *       "curl": "curl -H 'Authorization: Bearer {token}' https://api.example.com/api/v1/orders",
     *       "javascript": "fetch('/api/v1/orders', { headers: { 'Authorization': 'Bearer ' + token } })",
     *       "php": "$response = Http::withToken($token)->get('/api/v1/orders');"
     *     }
     *   }
     * }
     */
    public function getEndpointDetails(string $endpoint, Request $request): JsonResponse
    {
        $request->validate([
            'method' => 'sometimes|string|in:GET,POST,PUT,PATCH,DELETE',
            'include_examples' => 'sometimes|boolean',
        ]);

        try {
            $user = auth()->user();
            $method = $request->input('method');
            $includeExamples = $request->boolean('include_examples', true);

            // Check if user has access to this endpoint
            $subscriptionInfo = $this->getUserSubscriptionInfo($user);
            $endpointInfo = $this->getEndpointInfo($endpoint, $subscriptionInfo['tier']);

            if (! $endpointInfo) {
                return $this->errorResponse('Endpoint not found or not accessible with your subscription tier', 404);
            }

            $details = [
                'endpoint' => "/api/v1/{$endpoint}",
                'methods' => $endpointInfo['methods'],
                'description' => $endpointInfo['description'],
                'tier_required' => $endpointInfo['tier_required'],
                'rate_limits' => $endpointInfo['rate_limits'] ?? null,
            ];

            // Add code examples if requested
            if ($includeExamples) {
                $details['code_examples'] = $this->getEndpointCodeExamples($endpoint, $method);
            }

            return $this->successResponse($details, 'Endpoint details retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve endpoint details', $e, [
                'user_id' => auth()->id(),
                'endpoint' => $endpoint,
                'method' => $request->input('method'),
            ]);

            return $this->errorResponse('Failed to retrieve endpoint details', 500);
        }
    }

    /**
     * Get subscription features and tier information.
     *
     * @group API Documentation
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Subscription features retrieved successfully",
     *   "data": {
     *     "current_tier": "business",
     *     "tier_features": {
     *       "free": {
     *         "name": "Free",
     *         "price": 0,
     *         "features": ["Basic API access", "100 requests/hour", "Email support"],
     *         "endpoints": ["orders", "products", "customers"]
     *       },
     *       "business": {
     *         "name": "Business",
     *         "price": 20000,
     *         "features": ["Advanced analytics", "Real-time tracking", "1000 requests/minute"],
     *         "endpoints": ["orders", "products", "customers", "analytics", "tracking"]
     *       }
     *     },
     *     "upgrade_options": {
     *       "next_tier": "enterprise",
     *       "benefits": ["Custom reports", "Webhook subscriptions", "Priority support"],
     *       "price_difference": 20000
     *     }
     *   }
     * }
     */
    public function getSubscriptionFeatures(): JsonResponse
    {
        try {
            $user = auth()->user();
            $subscriptionInfo = $this->getUserSubscriptionInfo($user);

            $tierFeatures = $this->getAllTierFeatures();
            $upgradeOptions = $this->getUpgradeOptions($subscriptionInfo['tier']);

            $features = [
                'current_tier' => $subscriptionInfo['tier'],
                'tier_features' => $tierFeatures,
                'upgrade_options' => $upgradeOptions,
                'feature_comparison' => $this->getFeatureComparison(),
            ];

            return $this->successResponse($features, 'Subscription features retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve subscription features', $e, [
                'user_id' => auth()->id(),
            ]);

            return $this->errorResponse('Failed to retrieve subscription features', 500);
        }
    }

    /**
     * Get user's current API usage limits and statistics.
     *
     * @group API Documentation
     *
     * @authenticated
     *
     * @response 200 {
     *   "success": true,
     *   "message": "Usage limits retrieved successfully",
     *   "data": {
     *     "current_usage": {
     *       "requests_today": 1250,
     *       "requests_this_hour": 45,
     *       "requests_this_minute": 2
     *     },
     *     "limits": {
     *       "requests_per_minute": 1000,
     *       "requests_per_hour": 50000,
     *       "requests_per_day": 1000000,
     *       "burst_limit": 100
     *     },
     *     "usage_percentage": {
     *       "daily": 0.125,
     *       "hourly": 0.09,
     *       "minute": 0.2
     *     },
     *     "reset_times": {
     *       "minute_reset": "2024-01-15T14:31:00Z",
     *       "hour_reset": "2024-01-15T15:00:00Z",
     *       "day_reset": "2024-01-16T00:00:00Z"
     *     }
     *   }
     * }
     */
    public function getUsageLimits(): JsonResponse
    {
        try {
            $user = auth()->user();

            // Get current usage statistics
            $currentUsage = $this->getCurrentUsage($user);
            $limits = $this->getUserRateLimits($user);

            // Calculate usage percentages
            $usagePercentage = [
                'daily' => $limits['requests_per_day'] > 0 ? $currentUsage['requests_today'] / $limits['requests_per_day'] : 0,
                'hourly' => $limits['requests_per_hour'] > 0 ? $currentUsage['requests_this_hour'] / $limits['requests_per_hour'] : 0,
                'minute' => $limits['requests_per_minute'] > 0 ? $currentUsage['requests_this_minute'] / $limits['requests_per_minute'] : 0,
            ];

            // Calculate reset times
            $now = now();
            $resetTimes = [
                'minute_reset' => $now->copy()->addMinute()->startOfMinute()->toISOString(),
                'hour_reset' => $now->copy()->addHour()->startOfHour()->toISOString(),
                'day_reset' => $now->copy()->addDay()->startOfDay()->toISOString(),
            ];

            $usage = [
                'current_usage' => $currentUsage,
                'limits' => $limits,
                'usage_percentage' => $usagePercentage,
                'reset_times' => $resetTimes,
                'warnings' => $this->getUsageWarnings($usagePercentage),
            ];

            return $this->successResponse($usage, 'Usage limits retrieved successfully');

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to retrieve usage limits', $e, [
                'user_id' => auth()->id(),
            ]);

            return $this->errorResponse('Failed to retrieve usage limits', 500);
        }
    }

    // Helper methods for API documentation

    /**
     * Get user's subscription information.
     */
    private function getUserSubscriptionInfo($user): array
    {
        // This would get actual subscription info from the user/business
        // For now, return placeholder data
        return [
            'tier' => 'business',
            'features_available' => [
                'bulk_operations',
                'advanced_analytics',
                'real_time_tracking',
                'export_functionality',
            ],
            'upgrade_benefits' => [
                'custom_reports',
                'webhook_subscriptions',
                'priority_support',
                'dedicated_account_manager',
            ],
        ];
    }

    /**
     * Get user's rate limits based on subscription.
     */
    private function getUserRateLimits($user): array
    {
        // This would get actual rate limits from config/subscription
        // For now, return placeholder data based on business tier
        return [
            'current_tier' => 'business',
            'requests_per_minute' => 1000,
            'requests_per_hour' => 50000,
            'requests_per_day' => 1000000,
            'burst_limit' => 100,
        ];
    }

    /**
     * Get available endpoints based on subscription tier.
     */
    private function getAvailableEndpoints(string $tier): array
    {
        $allEndpoints = [
            'orders' => [
                'base_path' => '/orders',
                'methods' => ['GET', 'POST', 'PUT', 'DELETE'],
                'description' => 'Order management endpoints',
                'tier_required' => 'free',
            ],
            'products' => [
                'base_path' => '/products',
                'methods' => ['GET', 'POST', 'PUT', 'DELETE'],
                'description' => 'Product catalog management',
                'tier_required' => 'free',
            ],
            'customers' => [
                'base_path' => '/customers',
                'methods' => ['GET', 'POST', 'PUT'],
                'description' => 'Customer management',
                'tier_required' => 'free',
            ],
            'analytics' => [
                'base_path' => '/analytics',
                'methods' => ['GET', 'POST'],
                'description' => 'Business analytics and reporting',
                'tier_required' => 'business',
            ],
            'tracking' => [
                'base_path' => '/tracking',
                'methods' => ['GET'],
                'description' => 'Real-time delivery tracking',
                'tier_required' => 'business',
            ],
            'webhooks' => [
                'base_path' => '/webhooks',
                'methods' => ['GET', 'POST', 'PUT', 'DELETE'],
                'description' => 'Webhook subscription management',
                'tier_required' => 'enterprise',
            ],
            'reports' => [
                'base_path' => '/reports',
                'methods' => ['GET', 'POST'],
                'description' => 'Custom report generation',
                'tier_required' => 'enterprise',
            ],
        ];

        $tierHierarchy = ['free', 'starter', 'business', 'enterprise'];
        $userTierIndex = array_search($tier, $tierHierarchy);

        $availableEndpoints = [];
        foreach ($allEndpoints as $key => $endpoint) {
            $requiredTierIndex = array_search($endpoint['tier_required'], $tierHierarchy);
            if ($userTierIndex >= $requiredTierIndex) {
                $availableEndpoints[$key] = $endpoint;
            }
        }

        return $availableEndpoints;
    }

    /**
     * Get code examples for different programming languages.
     */
    private function getCodeExamples(): array
    {
        return [
            'curl' => [
                'description' => 'cURL command line examples',
                'examples' => [
                    'get_orders' => "curl -H 'Authorization: Bearer {token}' https://api.deliverynexus.com/api/v1/orders",
                    'create_order' => "curl -X POST -H 'Authorization: Bearer {token}' -H 'Content-Type: application/json' -d '{\"customer_id\":\"123\"}' https://api.deliverynexus.com/api/v1/orders",
                ],
            ],
            'javascript' => [
                'description' => 'JavaScript/Node.js examples',
                'examples' => [
                    'get_orders' => "fetch('/api/v1/orders', { headers: { 'Authorization': 'Bearer ' + token } })",
                    'create_order' => "fetch('/api/v1/orders', { method: 'POST', headers: { 'Authorization': 'Bearer ' + token, 'Content-Type': 'application/json' }, body: JSON.stringify({customer_id: '123'}) })",
                ],
            ],
            'php' => [
                'description' => 'PHP examples using Laravel HTTP client',
                'examples' => [
                    'get_orders' => "\$response = Http::withToken(\$token)->get('/api/v1/orders');",
                    'create_order' => "\$response = Http::withToken(\$token)->post('/api/v1/orders', ['customer_id' => '123']);",
                ],
            ],
            'python' => [
                'description' => 'Python examples using requests library',
                'examples' => [
                    'get_orders' => "response = requests.get('/api/v1/orders', headers={'Authorization': f'Bearer {token}'})",
                    'create_order' => "response = requests.post('/api/v1/orders', headers={'Authorization': f'Bearer {token}'}, json={'customer_id': '123'})",
                ],
            ],
        ];
    }

    /**
     * Get common response formats.
     */
    private function getResponseFormats(): array
    {
        return [
            'success_response' => [
                'structure' => [
                    'success' => 'boolean',
                    'message' => 'string',
                    'data' => 'object|array',
                ],
                'example' => [
                    'success' => true,
                    'message' => 'Orders retrieved successfully',
                    'data' => ['orders' => []],
                ],
            ],
            'error_response' => [
                'structure' => [
                    'success' => 'boolean',
                    'message' => 'string',
                    'errors' => 'object|null',
                    'error_code' => 'string|null',
                ],
                'example' => [
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => ['email' => ['The email field is required.']],
                    'error_code' => 'VALIDATION_ERROR',
                ],
            ],
            'paginated_response' => [
                'structure' => [
                    'success' => 'boolean',
                    'message' => 'string',
                    'data' => 'array',
                    'meta' => 'object',
                ],
                'example' => [
                    'success' => true,
                    'message' => 'Orders retrieved successfully',
                    'data' => [],
                    'meta' => [
                        'current_page' => 1,
                        'per_page' => 15,
                        'total' => 100,
                        'last_page' => 7,
                    ],
                ],
            ],
        ];
    }

    /**
     * Get error codes reference.
     */
    private function getErrorCodes(): array
    {
        return [
            'VALIDATION_ERROR' => [
                'code' => 422,
                'description' => 'Request validation failed',
                'example' => 'Required field missing or invalid format',
            ],
            'AUTHENTICATION_ERROR' => [
                'code' => 401,
                'description' => 'Authentication required or invalid token',
                'example' => 'Missing or expired API token',
            ],
            'AUTHORIZATION_ERROR' => [
                'code' => 403,
                'description' => 'Insufficient permissions for this action',
                'example' => 'Feature requires higher subscription tier',
            ],
            'RATE_LIMIT_EXCEEDED' => [
                'code' => 429,
                'description' => 'API rate limit exceeded',
                'example' => 'Too many requests, please try again later',
            ],
            'RESOURCE_NOT_FOUND' => [
                'code' => 404,
                'description' => 'Requested resource not found',
                'example' => 'Order with specified ID does not exist',
            ],
            'SERVER_ERROR' => [
                'code' => 500,
                'description' => 'Internal server error',
                'example' => 'Unexpected error occurred, please contact support',
            ],
        ];
    }

    /**
     * Convert documentation to markdown format.
     */
    private function convertToMarkdown(array $documentation): string
    {
        // This would convert the documentation array to markdown format
        // For now, return a simple markdown representation
        return "# API Documentation\n\n".
               "Base URL: {$documentation['base_url']}\n\n".
               "## Authentication\n\n".
               "Type: {$documentation['authentication']['type']}\n\n".
               "## Rate Limits\n\n".
               "- Requests per minute: {$documentation['rate_limits']['requests_per_minute']}\n".
               "- Requests per hour: {$documentation['rate_limits']['requests_per_hour']}\n\n".
               "## Available Endpoints\n\n".
               implode("\n", array_map(function ($endpoint, $key) {
                   return "- **{$key}**: {$endpoint['description']}";
               }, $documentation['available_endpoints'], array_keys($documentation['available_endpoints'])));
    }

    /**
     * Get detailed endpoint information.
     */
    private function getEndpointInfo(string $endpoint, string $userTier): ?array
    {
        $availableEndpoints = $this->getAvailableEndpoints($userTier);

        return $availableEndpoints[$endpoint] ?? null;
    }

    /**
     * Get code examples for specific endpoint.
     */
    private function getEndpointCodeExamples(string $endpoint, ?string $method): array
    {
        $baseExamples = $this->getCodeExamples();

        // Customize examples for specific endpoint
        $customExamples = [];
        foreach ($baseExamples as $language => $languageData) {
            $customExamples[$language] = [
                'description' => $languageData['description'],
                'example' => str_replace('/orders', "/{$endpoint}", $languageData['examples']['get_orders'] ?? ''),
            ];
        }

        return $customExamples;
    }

    /**
     * Get all tier features for comparison.
     */
    private function getAllTierFeatures(): array
    {
        return [
            'free' => [
                'name' => 'Free',
                'price' => 0,
                'features' => [
                    'Basic API access',
                    '100 requests/hour',
                    'Email support',
                    'Basic order management',
                ],
                'endpoints' => ['orders', 'products', 'customers'],
            ],
            'starter' => [
                'name' => 'Starter',
                'price' => 10000,
                'features' => [
                    'Enhanced API access',
                    '500 requests/hour',
                    'Priority email support',
                    'Basic analytics',
                ],
                'endpoints' => ['orders', 'products', 'customers', 'basic-analytics'],
            ],
            'business' => [
                'name' => 'Business',
                'price' => 20000,
                'features' => [
                    'Advanced analytics',
                    'Real-time tracking',
                    '1000 requests/minute',
                    'Bulk operations',
                    'Export functionality',
                ],
                'endpoints' => ['orders', 'products', 'customers', 'analytics', 'tracking'],
            ],
            'enterprise' => [
                'name' => 'Enterprise',
                'price' => 40000,
                'features' => [
                    'Custom reports',
                    'Webhook subscriptions',
                    'Unlimited requests',
                    'Priority support',
                    'Dedicated account manager',
                ],
                'endpoints' => ['all'],
            ],
        ];
    }

    /**
     * Get upgrade options for current tier.
     */
    private function getUpgradeOptions(string $currentTier): ?array
    {
        $tierOrder = ['free', 'starter', 'business', 'enterprise'];
        $currentIndex = array_search($currentTier, $tierOrder);

        if ($currentIndex === false || $currentIndex >= count($tierOrder) - 1) {
            return null;
        }

        $nextTier = $tierOrder[$currentIndex + 1];
        $allFeatures = $this->getAllTierFeatures();

        return [
            'next_tier' => $nextTier,
            'benefits' => array_diff($allFeatures[$nextTier]['features'], $allFeatures[$currentTier]['features']),
            'price_difference' => $allFeatures[$nextTier]['price'] - $allFeatures[$currentTier]['price'],
        ];
    }

    /**
     * Get feature comparison matrix.
     */
    private function getFeatureComparison(): array
    {
        return [
            'api_access' => ['free' => 'Basic', 'starter' => 'Enhanced', 'business' => 'Advanced', 'enterprise' => 'Unlimited'],
            'rate_limits' => ['free' => '100/hour', 'starter' => '500/hour', 'business' => '1000/minute', 'enterprise' => 'Unlimited'],
            'analytics' => ['free' => '❌', 'starter' => 'Basic', 'business' => 'Advanced', 'enterprise' => 'Custom'],
            'tracking' => ['free' => '❌', 'starter' => '❌', 'business' => '✅', 'enterprise' => '✅'],
            'webhooks' => ['free' => '❌', 'starter' => '❌', 'business' => '❌', 'enterprise' => '✅'],
            'support' => ['free' => 'Email', 'starter' => 'Priority Email', 'business' => 'Phone + Email', 'enterprise' => 'Dedicated Manager'],
        ];
    }

    /**
     * Get current usage statistics.
     */
    private function getCurrentUsage($user): array
    {
        // This would get actual usage from the API usage tracking service
        // For now, return placeholder data
        return [
            'requests_today' => 1250,
            'requests_this_hour' => 45,
            'requests_this_minute' => 2,
        ];
    }

    /**
     * Get usage warnings based on percentage.
     */
    private function getUsageWarnings(array $usagePercentage): array
    {
        $warnings = [];

        if ($usagePercentage['daily'] > 0.8) {
            $warnings[] = 'You are approaching your daily request limit';
        }

        if ($usagePercentage['hourly'] > 0.9) {
            $warnings[] = 'You are approaching your hourly request limit';
        }

        if ($usagePercentage['minute'] > 0.8) {
            $warnings[] = 'You are approaching your per-minute request limit';
        }

        return $warnings;
    }
}
