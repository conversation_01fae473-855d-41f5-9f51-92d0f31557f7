<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Shared;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\UnifiedKycRequest;
use App\Http\Requests\Api\V1\VerificationComponentRequest;
use App\Services\User\KycWorkflowService;
use App\Traits\ApiResponseTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * Unified KYC Controller
 *
 * Handles all KYC verification through QoreID integration.
 * Replaces the three-tier KYC system with a unified approach.
 */
class UnifiedKycController extends Controller
{
    use ApiResponseTrait;

    public function __construct(
        private KycWorkflowService $kycWorkflowService
    ) {}

    /**
     * Initiate comprehensive KYC verification.
     */
    public function initiateVerification(UnifiedKycRequest $request): JsonResponse
    {
        try {
            $user = $request->user();
            $data = $request->validated();

            $result = $this->kycWorkflowService->initiateUnifiedKyc($user, $data);

            return $this->successResponse($result, 'KYC verification initiated successfully');

        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                $e->getCode() ?: 500
            );
        }
    }

    /**
     * Verify individual component (BVN, NIN, Bank Account).
     */
    public function verifyComponent(VerificationComponentRequest $request): JsonResponse
    {
        try {
            $user = $request->user();
            $data = $request->validated();
            $type = $data['type'];

            $result = $this->kycWorkflowService->processVerificationComponent($user, $type, $data);

            return $this->successResponse($result, ucfirst($type).' verification completed');

        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                $e->getCode() ?: 500
            );
        }
    }

    /**
     * Get user's KYC status and progress.
     */
    public function getStatus(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $status = $this->kycWorkflowService->getUnifiedKycStatus($user);

            return $this->successResponse($status, 'KYC status retrieved successfully');

        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                $e->getCode() ?: 500
            );
        }
    }

    /**
     * Generate comprehensive KYC report.
     */
    public function generateReport(Request $request): JsonResponse
    {
        try {
            $user = $request->user();
            $report = $this->kycWorkflowService->generateUnifiedKycReport($user);

            return $this->successResponse($report, 'KYC report generated successfully');

        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                $e->getCode() ?: 500
            );
        }
    }

    /**
     * Get available verification methods.
     */
    public function getVerificationMethods(Request $request): JsonResponse
    {
        try {
            $methods = [
                'comprehensive' => [
                    'name' => 'Comprehensive KYC',
                    'description' => 'Complete verification including BVN, NIN, and bank account',
                    'required_fields' => ['bvn', 'nin', 'account_number', 'bank_code'],
                    'estimated_time' => '2-5 minutes',
                    'cost' => 'Low',
                ],
                'bvn' => [
                    'name' => 'BVN Verification',
                    'description' => 'Bank Verification Number verification',
                    'required_fields' => ['bvn'],
                    'estimated_time' => '30 seconds',
                    'cost' => 'Very Low',
                ],
                'nin' => [
                    'name' => 'NIN Verification',
                    'description' => 'National Identification Number verification',
                    'required_fields' => ['nin'],
                    'estimated_time' => '30 seconds',
                    'cost' => 'Very Low',
                ],
                'bank_account' => [
                    'name' => 'Bank Account Verification',
                    'description' => 'Bank account ownership verification',
                    'required_fields' => ['account_number', 'bank_code'],
                    'estimated_time' => '15 seconds',
                    'cost' => 'Free',
                ],
            ];

            return $this->successResponse($methods, 'Verification methods retrieved successfully');

        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                $e->getCode() ?: 500
            );
        }
    }

    /**
     * Get supported banks for verification.
     */
    public function getSupportedBanks(Request $request): JsonResponse
    {
        try {
            // This would typically come from a service or config
            $banks = [
                ['code' => '011', 'name' => 'First Bank of Nigeria'],
                ['code' => '044', 'name' => 'Access Bank'],
                ['code' => '058', 'name' => 'Guaranty Trust Bank'],
                ['code' => '033', 'name' => 'United Bank for Africa'],
                ['code' => '057', 'name' => 'Zenith Bank'],
                ['code' => '070', 'name' => 'Fidelity Bank'],
                ['code' => '076', 'name' => 'Polaris Bank'],
                ['code' => '082', 'name' => 'Keystone Bank'],
                ['code' => '084', 'name' => 'Enterprise Bank'],
                ['code' => '221', 'name' => 'Stanbic IBTC Bank'],
                ['code' => '232', 'name' => 'Sterling Bank'],
                ['code' => '215', 'name' => 'Unity Bank'],
                ['code' => '035', 'name' => 'Wema Bank'],
                ['code' => '032', 'name' => 'Union Bank of Nigeria'],
                ['code' => '214', 'name' => 'First City Monument Bank'],
                ['code' => '050', 'name' => 'Ecobank Nigeria'],
                ['code' => '101', 'name' => 'ProvidusBank'],
                ['code' => '100', 'name' => 'SunTrust Bank'],
                ['code' => '102', 'name' => 'Titan Trust Bank'],
                ['code' => '103', 'name' => 'Globus Bank'],
                ['code' => '090175', 'name' => 'Rubies MFB'],
                ['code' => '090267', 'name' => 'Kuda Bank'],
                ['code' => '999991', 'name' => 'PalmPay'],
                ['code' => '999992', 'name' => 'Opay'],
            ];

            return $this->successResponse($banks, 'Supported banks retrieved successfully');

        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                $e->getCode() ?: 500
            );
        }
    }

    /**
     * Get verification pricing information.
     */
    public function getPricing(Request $request): JsonResponse
    {
        try {
            $pricing = [
                'comprehensive' => [
                    'cost' => '₦400-600',
                    'description' => 'Complete verification package',
                    'includes' => ['BVN verification', 'NIN verification', 'Bank account verification', 'Risk assessment'],
                ],
                'individual' => [
                    'bvn' => ['cost' => '₦50-100', 'description' => 'BVN verification only'],
                    'nin' => ['cost' => '₦50-100', 'description' => 'NIN verification only'],
                    'bank_account' => ['cost' => 'Free', 'description' => 'Bank account verification only'],
                ],
                'volume_discounts' => [
                    '1000+' => '10% discount',
                    '5000+' => '20% discount',
                    '10000+' => '30% discount',
                ],
                'notes' => [
                    'Prices are estimates and may vary based on verification complexity',
                    'Volume discounts apply to monthly usage',
                    'Failed verifications are not charged',
                ],
            ];

            return $this->successResponse($pricing, 'Pricing information retrieved successfully');

        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                $e->getCode() ?: 500
            );
        }
    }

    /**
     * Get verification statistics.
     */
    public function getStatistics(Request $request): JsonResponse
    {
        try {
            $user = $request->user();

            // This would typically come from analytics service
            $statistics = [
                'user_verifications' => [
                    'total_attempts' => 0,
                    'successful_verifications' => 0,
                    'failed_verifications' => 0,
                    'success_rate' => '0%',
                ],
                'verification_breakdown' => [
                    'bvn' => ['attempted' => 0, 'successful' => 0],
                    'nin' => ['attempted' => 0, 'successful' => 0],
                    'bank_account' => ['attempted' => 0, 'successful' => 0],
                ],
                'last_verification' => null,
                'average_verification_time' => '45 seconds',
            ];

            return $this->successResponse($statistics, 'Verification statistics retrieved successfully');

        } catch (\Exception $e) {
            return $this->errorResponse(
                $e->getMessage(),
                $e->getCode() ?: 500
            );
        }
    }
}
