<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Auth;

use App\Helpers\RateLimitHelper;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\V1\Auth\LoginRequest;
use App\Http\Requests\Auth\BusinessOnboardingRequest;
use App\Http\Requests\Auth\ProviderOnboardingRequest;
use App\Http\Requests\Auth\RegisterRequest;
use App\Http\Requests\Auth\SelectAccountTypeRequest;
use App\Http\Requests\Auth\VerifyEmailRequest;
use App\Http\Requests\Auth\VerifyPhoneRequest;
use App\Http\Resources\Api\V1\AuthResource;
use App\Http\Resources\Api\V1\UserResource;
use App\Services\System\LoggingService;
use App\Services\User\AuthService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

/**
 * Web Authentication Controller
 *
 * Handles NextJS dashboard authentication:
 * - Cookie-based session management
 * - CSRF protection ready
 * - Shorter token expiration (8 hours)
 * - Business-focused features
 */
class WebAuthController extends Controller
{
    use \App\Traits\ApiResponseTrait;

    public function __construct(
        private AuthService $authService,
        private LoggingService $loggingService
    ) {}

    /**
     * Smart login for web dashboard (detects central vs tenant context).
     * Uses Laravel Sanctum's built-in stateful authentication.
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $rateLimit = RateLimitHelper::checkLoginAttempts($request);

        if (! $rateLimit['allowed']) {
            return $this->rateLimitResponse('Too many login attempts', $rateLimit['retry_after']);
        }

        try {
            // Check if we're in a tenant context
            $tenant = tenant();

            if ($tenant) {
                // Tenant domain login
                $result = $this->authService->tenantLogin(
                    identifier: $request->email,
                    password: $request->password,
                    tenant: $tenant
                );

                $context = 'tenant_web';
                $message = 'Tenant login successful';
            } else {
                // Central domain login
                $result = $this->authService->centralLogin(
                    identifier: $request->email,
                    password: $request->password
                );

                // Only allow platform admins on central web dashboard
                if ($result['user']->getUserType() === 'customer') {
                    return $this->forbiddenResponse('Customers must use the mobile app');
                }

                $context = 'central_web';
                $message = 'Central login successful';
            }

            // Use Laravel's Auth facade for stateful authentication
            Auth::login($result['user']);

            $authData = [
                'user' => $result['user'],
                'tenant' => $result['tenant'] ?? null,
                'context' => $context,
            ];

            RateLimitHelper::clear(RateLimitHelper::getAuthKey($request, 'login'));

            // Sanctum handles cookies automatically for stateful domains
            return $this->successResponse(
                new AuthResource($authData),
                $message
            );

        } catch (\Throwable $e) {
            RateLimitHelper::hit(RateLimitHelper::getAuthKey($request, 'login'));

            if ($e instanceof \App\Exceptions\AuthenticationException) {
                return $this->unauthorizedResponse($e->getMessage());
            }

            $this->loggingService->logException($e, ['action' => 'web_login']);

            return $this->serverErrorResponse('Login failed');
        }
    }

    /**
     * Logout user from web dashboard.
     */
    public function logout(Request $request): JsonResponse
    {
        try {
            // Use Laravel's Auth facade for stateful logout
            Auth::logout();

            // Invalidate the session
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return $this->successResponse(null, 'Logged out successfully');

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, ['action' => 'web_logout']);

            return $this->serverErrorResponse('Logout failed');
        }
    }

    /**
     * Get current authenticated user for web dashboard.
     */
    public function me(Request $request): JsonResponse
    {
        try {
            $user = $request->user()->load(['tenant', 'roles', 'abilities']);

            // Ensure user has access to web dashboard
            if ($user->getUserType() === 'customer') {
                return $this->forbiddenResponse('Access denied');
            }

            return $this->successResponse(new UserResource($user));

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, ['action' => 'web_me']);

            return $this->serverErrorResponse('Failed to get user data');
        }
    }

    /**
     * Refresh session for web dashboard.
     * For stateful authentication, session refresh is handled automatically by Laravel.
     */
    public function refresh(Request $request): JsonResponse
    {
        try {
            $user = $request->user();

            // For stateful authentication, just return current user data
            $authData = [
                'user' => $user->load(['tenant', 'roles']),
                'context' => 'web',
            ];

            return $this->successResponse(
                new AuthResource($authData),
                'Session refreshed successfully'
            );

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, ['action' => 'web_refresh']);

            return $this->serverErrorResponse('Session refresh failed');
        }
    }

    /**
     * Set httpOnly cookie with token.
     */
    private function setCookieToken(JsonResponse $response, string $token): JsonResponse
    {
        return $response->withCookie(cookie(
            name: 'auth_token',
            value: $token,
            minutes: 480, // 8 hours
            path: '/',
            domain: null,
            secure: app()->environment('production'),
            httpOnly: true,
            raw: false,
            sameSite: 'Strict'
        ));
    }

    /**
     * Send password reset code.
     */
    public function sendPasswordReset(Request $request): JsonResponse
    {
        $request->validate([
            'identifier' => 'required|string',
        ]);

        $rateLimit = RateLimitHelper::checkPasswordResetAttempts($request->identifier);

        if (! $rateLimit['allowed']) {
            return $this->rateLimitResponse('Too many password reset attempts', $rateLimit['retry_after']);
        }

        try {
            $this->authService->sendPasswordReset($request->identifier);

            return $this->successResponse(
                null,
                'Password reset instructions sent successfully'
            );

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, ['action' => 'web_password_reset']);

            return $this->serverErrorResponse('Failed to send password reset');
        }
    }

    /**
     * Reset password with token.
     */
    public function resetPassword(Request $request): JsonResponse
    {
        $request->validate([
            'identifier' => 'required|string',
            'token' => 'required|string',
            'password' => 'required|string|min:8|confirmed',
        ]);

        $rateLimit = RateLimitHelper::checkPasswordResetAttempts($request->email);

        if (! $rateLimit['allowed']) {
            return $this->rateLimitResponse('Too many password reset attempts', $rateLimit['retry_after']);
        }

        try {
            $this->authService->resetPassword(
                $request->identifier,
                $request->token,
                $request->password
            );

            RateLimitHelper::clearEmailLimits($request->email);

            return $this->successResponse(
                null,
                'Password reset successfully'
            );

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, ['action' => 'web_password_confirm']);

            return $this->serverErrorResponse('Failed to reset password');
        }
    }

    /**
     * Register a new user (Phase 1: Simple email + password).
     */
    public function register(RegisterRequest $request): JsonResponse
    {
        try {
            $result = $this->authService->registerSimple($request->validated());

            $response = $this->successResponse(
                data: [
                    'user' => $result['user'],
                    'onboarding_status' => 'pending_account_type_selection',
                    'next_step' => '/auth/web/select-account-type',
                ],
                message: 'Registration successful! Please select your account type to continue.',
                statusCode: 201
            );

            // Set httpOnly cookie for web security
            return $this->setCookieToken($response, $result['token']);

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, ['action' => 'web_register']);

            return $this->serverErrorResponse('Registration failed');
        }
    }

    /**
     * Select account type (Phase 2).
     */
    public function selectAccountType(SelectAccountTypeRequest $request): JsonResponse
    {
        try {
            $result = $this->authService->selectAccountType(
                user: $request->user(),
                accountType: $request->getAccountType()
            );

            return $this->successResponse(
                data: [
                    'user' => $result['user'],
                    'account_type' => $result['account_type'],
                    'onboarding_status' => $result['onboarding_status'],
                    'next_step' => $result['next_step'] ?? null,
                ],
                message: $result['message']
            );

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, ['action' => 'web_select_account_type']);

            return $this->serverErrorResponse('Account type selection failed');
        }
    }

    /**
     * Business onboarding (Phase 3).
     */
    public function onboardBusiness(BusinessOnboardingRequest $request): JsonResponse
    {
        try {
            $result = $this->authService->onboardBusiness(
                user: $request->user(),
                data: $request->validated()
            );

            return $this->successResponse(
                data: [
                    'user' => $result['user'],
                    'business' => $result['business'],
                    'tenant' => $result['tenant'],
                    'tenant_url' => $result['tenant_url'],
                    'onboarding_status' => 'business_onboarded',
                ],
                message: 'Business onboarding completed! You can now access your business dashboard.',
                statusCode: 201
            );

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, ['action' => 'web_onboard_business']);

            return $this->serverErrorResponse('Business onboarding failed');
        }
    }

    /**
     * Provider onboarding (Phase 3).
     */
    public function onboardProvider(ProviderOnboardingRequest $request): JsonResponse
    {
        try {
            $result = $this->authService->onboardProvider(
                user: $request->user(),
                data: $request->validated()
            );

            return $this->successResponse(
                data: [
                    'user' => $result['user'],
                    'provider' => $result['provider'],
                    'tenant' => $result['tenant'],
                    'tenant_url' => $result['tenant_url'],
                    'onboarding_status' => 'provider_onboarded',
                ],
                message: 'Provider onboarding completed! You can now access your provider dashboard.',
                statusCode: 201
            );

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, ['action' => 'web_onboard_provider']);

            return $this->serverErrorResponse('Provider onboarding failed');
        }
    }

    /**
     * Logout from all devices.
     */
    public function logoutFromAllDevices(Request $request): JsonResponse
    {
        try {
            // For stateful authentication, logout from current session
            Auth::logout();

            // Invalidate session
            $request->session()->invalidate();
            $request->session()->regenerateToken();

            return $this->successResponse(
                null,
                'Logged out from all devices successfully'
            );

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, ['action' => 'web_logout_all']);

            return $this->serverErrorResponse('Logout failed');
        }
    }

    /**
     * Send email verification code.
     */
    public function sendEmailVerification(Request $request): JsonResponse
    {
        try {
            $this->authService->sendEmailVerification($request->user());

            return $this->successResponse(
                null,
                'Email verification code sent successfully'
            );

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, ['action' => 'web_send_email_verification']);

            return $this->serverErrorResponse('Failed to send email verification');
        }
    }

    /**
     * Verify email with code.
     */
    public function verifyEmail(VerifyEmailRequest $request): JsonResponse
    {
        try {
            $this->authService->verifyEmail(
                $request->user(),
                $request->validated('code')
            );

            return $this->successResponse(
                null,
                'Email verified successfully'
            );

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, ['action' => 'web_verify_email']);

            return $this->serverErrorResponse('Email verification failed');
        }
    }

    /**
     * Send phone verification code.
     */
    public function sendPhoneVerification(Request $request): JsonResponse
    {
        try {
            $this->authService->sendPhoneVerification($request->user());

            return $this->successResponse(
                null,
                'Phone verification code sent successfully'
            );

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, ['action' => 'web_send_phone_verification']);

            return $this->serverErrorResponse('Failed to send phone verification');
        }
    }

    /**
     * Verify phone with code.
     */
    public function verifyPhone(VerifyPhoneRequest $request): JsonResponse
    {
        try {
            $this->authService->verifyPhone(
                $request->user(),
                $request->validated('code')
            );

            return $this->successResponse(
                null,
                'Phone verified successfully'
            );

        } catch (\Throwable $e) {
            $this->loggingService->logException($e, ['action' => 'web_verify_phone']);

            return $this->serverErrorResponse('Phone verification failed');
        }
    }
}
