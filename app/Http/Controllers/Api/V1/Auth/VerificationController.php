<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api\V1\Auth;

use App\Helpers\RateLimitHelper;
use App\Helpers\ValidationHelper;
use App\Http\Controllers\Controller;
use App\Services\System\LoggingService;
use App\Services\User\VerificationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

/**
 * Verification Controller
 *
 * Handles email and phone verification using OTPs:
 * - Send verification codes
 * - Verify OTPs
 * - Resend codes with rate limiting
 */
class VerificationController extends Controller
{
    use \App\Traits\ApiResponseTrait;

    public function __construct(
        private VerificationService $verificationService,
        private LoggingService $loggingService
    ) {}

    /**
     * Send email verification OTP.
     */
    public function sendEmailVerification(Request $request): JsonResponse
    {
        $rateLimitKey = 'email_verification:'.$request->user()->id;

        if (RateLimitHelper::tooManyAttempts($rateLimitKey, 3)) {
            return $this->rateLimitResponse(
                'Too many verification attempts',
                RateLimitHelper::availableIn($rateLimitKey)
            );
        }

        try {
            $sent = $this->verificationService->sendEmailVerification($request->user());

            if ($sent) {
                RateLimitHelper::clear($rateLimitKey);

                return $this->successResponse([
                    'message' => 'Verification code sent to your email',
                    'expires_in' => 900, // 15 minutes
                ]);
            }

            return $this->serverErrorResponse('Failed to send verification code');

        } catch (\Throwable $e) {
            RateLimitHelper::hit($rateLimitKey);

            if ($e instanceof \App\Exceptions\BusinessLogicException) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage(),
                    'error_code' => $e->getErrorCode(),
                    'timestamp' => now()->toISOString(),
                ], $e->getCode());
            }

            $this->loggingService->logException($e, ['action' => 'send_email_verification']);

            return $this->serverErrorResponse('Failed to send verification code');
        }
    }

    /**
     * Verify email OTP.
     */
    public function verifyEmail(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), ValidationHelper::getOtpRules());

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors()->toArray());
        }

        $rateLimitKey = 'verify_email:'.$request->user()->id;

        if (RateLimitHelper::tooManyAttempts($rateLimitKey, 5)) {
            return $this->rateLimitResponse(
                'Too many verification attempts',
                RateLimitHelper::availableIn($rateLimitKey)
            );
        }

        try {
            $verified = $this->verificationService->verifyEmailOtp(
                $request->user(),
                $request->otp
            );

            if ($verified) {
                RateLimitHelper::clear($rateLimitKey);

                return $this->successResponse([
                    'message' => 'Email verified successfully',
                    'user' => [
                        'email_verified_at' => $request->user()->fresh()->email_verified_at,
                    ],
                ]);
            }

            return $this->serverErrorResponse('Email verification failed');

        } catch (\Throwable $e) {
            RateLimitHelper::hit($rateLimitKey);

            if ($e instanceof \App\Exceptions\BusinessLogicException) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage(),
                    'error_code' => $e->getErrorCode(),
                    'timestamp' => now()->toISOString(),
                ], $e->getCode());
            }

            $this->loggingService->logException($e, ['action' => 'verify_email']);

            return $this->serverErrorResponse('Email verification failed');
        }
    }

    /**
     * Send phone verification OTP.
     */
    public function sendPhoneVerification(Request $request): JsonResponse
    {
        $rateLimitKey = 'phone_verification:'.$request->user()->id;

        if (RateLimitHelper::tooManyAttempts($rateLimitKey, 3)) {
            return $this->rateLimitResponse(
                'Too many verification attempts',
                RateLimitHelper::availableIn($rateLimitKey)
            );
        }

        try {
            $sent = $this->verificationService->sendPhoneVerification($request->user());

            if ($sent) {
                RateLimitHelper::clear($rateLimitKey);

                return $this->successResponse([
                    'message' => 'Verification code sent to your phone',
                    'expires_in' => 900, // 15 minutes
                ]);
            }

            return $this->serverErrorResponse('Failed to send verification code');

        } catch (\Throwable $e) {
            RateLimitHelper::hit($rateLimitKey);

            if ($e instanceof \App\Exceptions\BusinessLogicException) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage(),
                    'error_code' => $e->getErrorCode(),
                    'timestamp' => now()->toISOString(),
                ], $e->getCode());
            }

            $this->loggingService->logException($e, ['action' => 'send_phone_verification']);

            return $this->serverErrorResponse('Failed to send verification code');
        }
    }

    /**
     * Verify phone OTP.
     */
    public function verifyPhone(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), ValidationHelper::getOtpRules());

        if ($validator->fails()) {
            return $this->validationErrorResponse($validator->errors()->toArray());
        }

        $rateLimitKey = 'verify_phone:'.$request->user()->id;

        if (RateLimitHelper::tooManyAttempts($rateLimitKey, 5)) {
            return $this->rateLimitResponse(
                'Too many verification attempts',
                RateLimitHelper::availableIn($rateLimitKey)
            );
        }

        try {
            $verified = $this->verificationService->verifyPhoneOtp(
                $request->user(),
                $request->otp
            );

            if ($verified) {
                RateLimitHelper::clear($rateLimitKey);

                return $this->successResponse([
                    'message' => 'Phone verified successfully',
                    'user' => [
                        'phone_verified_at' => $request->user()->fresh()->phone_verified_at,
                    ],
                ]);
            }

            return $this->serverErrorResponse('Phone verification failed');

        } catch (\Throwable $e) {
            RateLimitHelper::hit($rateLimitKey);

            if ($e instanceof \App\Exceptions\BusinessLogicException) {
                return response()->json([
                    'success' => false,
                    'message' => $e->getMessage(),
                    'error_code' => $e->getErrorCode(),
                    'timestamp' => now()->toISOString(),
                ], $e->getCode());
            }

            $this->loggingService->logException($e, ['action' => 'verify_phone']);

            return $this->serverErrorResponse('Phone verification failed');
        }
    }

    /**
     * Get verification status for current user.
     */
    public function getVerificationStatus(Request $request): JsonResponse
    {
        $user = $request->user();

        return $this->successResponse([
            'email_verified' => $user->hasVerifiedEmail(),
            'phone_verified' => $user->hasVerifiedPhone(),
            'fully_verified' => $user->isFullyVerified(),
            'email_verified_at' => $user->email_verified_at?->toISOString(),
            'phone_verified_at' => $user->phone_verified_at?->toISOString(),
        ]);
    }
}
