<?php

declare(strict_types=1);

namespace App\Listeners\Tenant;

use App\Events\Tenant\TenantStaffAdded;
use App\Events\Tenant\TenantStatusChanged;
use App\Events\Tenant\TenantTeamMemberStatusChanged;
use App\Notifications\Tenant\TenantStaffAddedNotification;
use App\Notifications\Tenant\TenantStatusChangedNotification;
use App\Notifications\Tenant\TenantTeamMemberStatusChangedNotification;
use App\Services\System\LoggingService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

/**
 * Unified Tenant Management Notification Listener
 *
 * Handles notifications for both business and provider management events.
 * Replaces separate business and provider notification listeners.
 */
class SendTenantManagementNotification implements ShouldQueue
{
    use InteractsWithQueue;

    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Handle tenant staff added events
     */
    public function handleTenantStaffAdded(TenantStaffAdded $event): void
    {
        try {
            // Send notification to the new staff member
            $event->staffMember->notify(new TenantStaffAddedNotification(
                $event->staffMember,
                $event->tenant,
                $event->addedBy,
                $event->role,
                $event->temporaryPassword
            ));

            $this->loggingService->logInfo('Tenant staff added notification sent', [
                'staff_member_id' => $event->staffMember->id,
                'tenant_type' => $event->getTenantType(),
                'tenant_id' => $event->getTenantId(),
                'added_by_id' => $event->addedBy->id,
                'role' => $event->role,
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send tenant staff added notification', $e, [
                'staff_member_id' => $event->staffMember->id,
                'tenant_type' => $event->getTenantType(),
                'tenant_id' => $event->getTenantId(),
            ]);
            throw $e;
        }
    }

    /**
     * Handle tenant team member status changed events
     */
    public function handleTenantTeamMemberStatusChanged(TenantTeamMemberStatusChanged $event): void
    {
        try {
            // Send notification to the team member
            $event->teamMember->notify(new TenantTeamMemberStatusChangedNotification(
                $event->teamMember,
                $event->tenant,
                $event->changedBy,
                $event->action,
                $event->newRole,
                $event->message
            ));

            $this->loggingService->logInfo('Tenant team member status change notification sent', [
                'team_member_id' => $event->teamMember->id,
                'tenant_type' => $event->getTenantType(),
                'tenant_id' => $event->getTenantId(),
                'action' => $event->action,
                'changed_by_id' => $event->changedBy->id,
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send tenant team member status change notification', $e, [
                'team_member_id' => $event->teamMember->id,
                'tenant_type' => $event->getTenantType(),
                'tenant_id' => $event->getTenantId(),
                'action' => $event->action,
            ]);
            throw $e;
        }
    }

    /**
     * Handle tenant status changed events
     */
    public function handleTenantStatusChanged(TenantStatusChanged $event): void
    {
        try {
            // Get tenant owner
            $owner = $event->getTenantOwner();
            if (! $owner) {
                $this->loggingService->logWarning('Tenant status changed but no owner found', [
                    'tenant_type' => $event->getTenantType(),
                    'tenant_id' => $event->getTenantId(),
                ]);

                return;
            }

            // Send notification to the tenant owner
            $owner->notify(new TenantStatusChangedNotification(
                $event->tenant,
                $event->oldStatus,
                $event->newStatus,
                $event->reason,
                $event->changedBy
            ));

            $this->loggingService->logInfo('Tenant status change notification sent', [
                'tenant_type' => $event->getTenantType(),
                'tenant_id' => $event->getTenantId(),
                'owner_id' => $owner->id,
                'old_status' => $event->oldStatus,
                'new_status' => $event->newStatus,
                'changed_by_id' => $event->changedBy?->id,
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send tenant status change notification', $e, [
                'tenant_type' => $event->getTenantType(),
                'tenant_id' => $event->getTenantId(),
                'old_status' => $event->oldStatus,
                'new_status' => $event->newStatus,
            ]);
            throw $e;
        }
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return ['notification', 'tenant_management', 'unified'];
    }
}
