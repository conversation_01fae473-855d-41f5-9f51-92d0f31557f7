<?php

declare(strict_types=1);

namespace App\Listeners\User;

use App\Events\User\UserCreatedByAdmin;
use App\Events\User\UserRegistered;
use App\Notifications\User\UserCredentialsNotification;
use App\Notifications\User\WelcomeUser;
use App\Services\Communication\NotificationDecisionEngine;
use App\Services\System\LoggingService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

/**
 * Send User Welcome Notification Listener
 *
 * Handles UserRegistered events and sends welcome notifications
 * Implements refined SMS strategy: Email + Push only (NO SMS for welcome)
 */
class SendUserWelcomeNotification implements ShouldQueue
{
    use InteractsWithQueue;

    public function __construct(
        private LoggingService $loggingService,
        private NotificationDecisionEngine $decisionEngine
    ) {}

    /**
     * Handle the event.
     */
    public function handle(UserRegistered $event): void
    {
        try {
            $user = $event->user;

            // Send welcome notification (Email + Push only, NO SMS)
            $user->notify(new WelcomeUser($user->account_type ?? 'customer'));

            $this->loggingService->logInfo('User welcome notification sent', [
                'user_id' => $user->id,
                'email' => $user->email,
                'registration_method' => $event->registrationMethod,
                'referral_code' => $event->referralCode,
                'channels' => 'email+push_only', // No SMS for welcome
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send user welcome notification', $e, [
                'user_id' => $event->user->id,
                'registration_method' => $event->registrationMethod,
            ]);

            // Re-throw to trigger job retry
            throw $e;
        }
    }

    /**
     * Determine the time at which the job should timeout.
     */
    public function retryUntil(): \DateTime
    {
        return now()->addMinutes(5);
    }

    /**
     * Get the tags that should be assigned to the job.
     */
    public function tags(): array
    {
        return ['notification', 'user_welcome'];
    }

    /**
     * Handle user created by admin events.
     */
    public function handleUserCreatedByAdmin(UserCreatedByAdmin $event): void
    {
        try {
            $user = $event->user;

            // Send credentials notification
            $user->notify(new UserCredentialsNotification(
                $event->temporaryPassword,
                $event->createdBy
            ));

            $this->loggingService->logInfo('User credentials notification sent', [
                'user_id' => $user->id,
                'email' => $user->email,
                'created_by_id' => $event->createdBy->id,
                'channels' => 'email+database',
            ]);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send user credentials notification', $e, [
                'user_id' => $event->user->id,
                'created_by_id' => $event->createdBy->id,
            ]);

            // Re-throw to trigger job retry
            throw $e;
        }
    }
}
