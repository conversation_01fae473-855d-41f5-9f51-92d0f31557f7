<?php

declare(strict_types=1);

namespace App\Services\User;

use App\Enums\User\KycLevel;
use App\Exceptions\BusinessLogicException;
use App\Models\User\User;
use App\Services\Core\LoggingService;
use App\Services\Notification\NotificationService;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

/**
 * KYC Workflow Service
 *
 * Orchestrates the complete KYC verification workflow across all tiers.
 * Manages progression from Basic → Intermediate → Advanced KYC levels.
 */
class KycWorkflowService
{
    public function __construct(
        private UnifiedKycService $unifiedKycService,
        private NotificationService $notificationService,
        private LoggingService $loggingService
    ) {}

    /**
     * Initiate KYC process for a user targeting a specific level.
     */
    public function initiateKycProcess(User $user, string $targetLevel): bool
    {
        try {
            $targetKycLevel = KycLevel::from($targetLevel);

            // Validate target level
            if (! $this->canUpgradeToLevel($user, $targetKycLevel)) {
                throw new BusinessLogicException(
                    'Cannot upgrade to target KYC level',
                    'INVALID_KYC_UPGRADE',
                    Response::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            return DB::transaction(function () use ($user, $targetKycLevel) {
                // Create KYC workflow record
                $workflowData = [
                    'user_id' => $user->id,
                    'current_level' => $user->kyc_level?->value ?? 'none',
                    'target_level' => $targetKycLevel->value,
                    'status' => 'initiated',
                    'initiated_at' => now(),
                    'steps_required' => $targetKycLevel->getRequiredDocuments(),
                    'steps_completed' => [],
                ];

                $this->logKycProgress($user, 'kyc_workflow_initiated', [
                    'target_level' => $targetKycLevel->value,
                    'current_level' => $user->kyc_level?->value ?? 'none',
                    'steps_required' => count($workflowData['steps_required']),
                ]);

                // Send initiation notification
                $this->notificationService->sendKycWorkflowInitiated($user, $targetKycLevel);

                return true;
            });

        } catch (\Exception $e) {
            $this->loggingService->logError('KYC workflow initiation failed', $e, [
                'user_id' => $user->id,
                'target_level' => $targetLevel,
            ]);

            if ($e instanceof BusinessLogicException) {
                throw $e;
            }

            throw new BusinessLogicException(
                'Failed to initiate KYC process',
                'KYC_INITIATION_FAILED',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Process a verification step in the KYC workflow (legacy method - use unified KYC instead).
     *
     * @deprecated Use initiateUnifiedKyc() or processVerificationComponent() instead
     */
    public function processVerificationStep(User $user, string $step, array $data): bool
    {
        try {
            // Map legacy steps to unified verification components
            $componentMapping = [
                'bvn_verification' => 'bvn',
                'nin_verification' => 'nin',
                'bank_account_verification' => 'bank_account',
            ];

            if (isset($componentMapping[$step])) {
                $result = $this->unifiedKycService->verifyComponent($user, $componentMapping[$step], $data);

                if ($result['success']) {
                    $this->logKycProgress($user, 'verification_step_completed', [
                        'step' => $step,
                        'unified_component' => $componentMapping[$step],
                        'verification_score' => $result['data']['verification_score'] ?? 0,
                    ]);

                    return true;
                }

                return false;
            }

            // For non-unified steps, log deprecation warning
            $this->loggingService->logWarning('Legacy verification step used', [
                'user_id' => $user->id,
                'step' => $step,
                'message' => 'This verification step should be migrated to unified KYC',
            ]);

            throw new BusinessLogicException(
                "Legacy verification step '{$step}' is no longer supported. Use unified KYC verification instead.",
                'LEGACY_VERIFICATION_DEPRECATED',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );

        } catch (\Exception $e) {
            $this->loggingService->logError('Verification step processing failed', $e, [
                'user_id' => $user->id,
                'step' => $step,
                'data_keys' => array_keys($data),
            ]);

            if ($e instanceof BusinessLogicException) {
                throw $e;
            }

            throw new BusinessLogicException(
                'Failed to process verification step',
                'VERIFICATION_STEP_FAILED',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Update KYC status based on verification results.
     */
    public function updateKycStatus(User $user, string $status, array $metadata = []): bool
    {
        try {
            return DB::transaction(function () use ($user, $status, $metadata) {
                // Update user's KYC status if provided in metadata
                if (isset($metadata['kyc_level'])) {
                    $kycLevel = KycLevel::from($metadata['kyc_level']);
                    $user->update(['kyc_level' => $kycLevel]);
                }

                $this->logKycProgress($user, 'kyc_status_updated', array_merge([
                    'status' => $status,
                    'previous_level' => $user->getOriginal('kyc_level'),
                    'new_level' => $user->kyc_level?->value,
                ], $metadata));

                // Send status update notification
                $this->notificationService->sendKycStatusUpdate($user, $status, $metadata);

                return true;
            });

        } catch (\Exception $e) {
            $this->loggingService->logError('KYC status update failed', $e, [
                'user_id' => $user->id,
                'status' => $status,
                'metadata' => $metadata,
            ]);

            return false;
        }
    }

    /**
     * Get KYC requirements for a specific level.
     */
    public function getKycRequirements(User $user, string $level): array
    {
        try {
            $kycLevel = KycLevel::from($level);
            $currentLevel = $user->kyc_level ?? KycLevel::BASIC;

            $requirements = [
                'target_level' => $kycLevel->value,
                'current_level' => $currentLevel->value,
                'can_upgrade' => $this->canUpgradeToLevel($user, $kycLevel),
                'required_documents' => $kycLevel->getRequiredDocuments(),
                'verification_methods' => $kycLevel->getVerificationMethods(),
                'estimated_time' => $this->getEstimatedCompletionTime($kycLevel),
                'benefits' => $this->getKycLevelBenefits($kycLevel),
                'next_steps' => $this->getNextSteps($user, $kycLevel),
            ];

            // Add progress information
            $requirements['progress'] = $this->getKycProgress($user, $kycLevel);

            return $requirements;

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to get KYC requirements', $e, [
                'user_id' => $user->id,
                'level' => $level,
            ]);

            throw new BusinessLogicException(
                'Failed to retrieve KYC requirements',
                'KYC_REQUIREMENTS_ERROR',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Generate comprehensive KYC report for user (legacy method).
     *
     * @deprecated Use UnifiedKycService::generateKycReport() instead
     */
    public function generateKycReport(User $user): array
    {
        try {
            // Delegate to unified KYC service
            return $this->unifiedKycService->generateKycReport($user);

        } catch (\Exception $e) {
            $this->loggingService->logError('KYC report generation failed', $e, [
                'user_id' => $user->id,
            ]);

            throw new BusinessLogicException(
                'Failed to generate KYC report',
                'KYC_REPORT_ERROR',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Schedule manual review for user (legacy method).
     *
     * @deprecated Manual review is now automated through QoreID risk assessment
     */
    public function scheduleManualReview(User $user, array $documents): bool
    {
        try {
            $this->loggingService->logWarning('Manual review requested but deprecated', [
                'user_id' => $user->id,
                'documents_count' => count($documents),
                'message' => 'Manual review is now automated through QoreID risk assessment',
            ]);

            // Manual review is no longer needed with QoreID's automated risk assessment
            return true;

        } catch (\Exception $e) {
            $this->loggingService->logError('Manual review scheduling failed', $e, [
                'user_id' => $user->id,
                'documents_count' => count($documents),
            ]);

            return false;
        }
    }

    /**
     * Check if user can upgrade to target KYC level.
     */
    private function canUpgradeToLevel(User $user, KycLevel $targetLevel): bool
    {
        $currentLevel = $user->kyc_level ?? KycLevel::BASIC;

        // Can only upgrade to next level or stay at current level
        return $targetLevel->getAccessLevel() <= ($currentLevel->getAccessLevel() + 1);
    }

    /**
     * Check overall KYC progress and update level if complete.
     */
    private function checkKycProgress(User $user): void
    {
        // Use unified KYC service to get current status
        $status = $this->unifiedKycService->getKycStatus($user);

        if ($status['completion_status']['is_complete']) {
            $this->updateKycStatus($user, 'completed', [
                'kyc_level' => $status['current_kyc_level'],
                'verification_score' => $status['overall_score'],
                'risk_level' => $status['risk_level'],
            ]);
        }
    }

    /**
     * Get intermediate KYC progress (legacy method).
     *
     * @deprecated Use UnifiedKycService::getKycStatus() instead
     */
    private function getIntermediateProgress(User $user): array
    {
        $status = $this->unifiedKycService->getKycStatus($user);

        return [
            'kyc_level' => KycLevel::INTERMEDIATE->value,
            'completion_percentage' => $status['completion_status']['completion_percentage'],
            'completed_steps' => $status['completion_status']['completed_count'],
            'total_steps' => $status['completion_status']['required_count'],
            'is_complete' => $status['completion_status']['is_complete'] && $status['current_kyc_level'] === 'intermediate',
        ];
    }

    /**
     * Get advanced KYC progress (legacy method).
     *
     * @deprecated Use UnifiedKycService::getKycStatus() instead
     */
    private function getAdvancedProgress(User $user): array
    {
        $status = $this->unifiedKycService->getKycStatus($user);

        return [
            'kyc_level' => KycLevel::ADVANCED->value,
            'completion_percentage' => $status['completion_status']['completion_percentage'],
            'completed_steps' => $status['completion_status']['completed_count'],
            'total_steps' => $status['completion_status']['required_count'],
            'is_complete' => $status['completion_status']['is_complete'] && $status['current_kyc_level'] === 'advanced',
        ];
    }

    /**
     * Get KYC progress for specific level (legacy method).
     *
     * @deprecated Use UnifiedKycService::getKycStatus() instead
     */
    private function getKycProgress(User $user, KycLevel $level): array
    {
        $status = $this->unifiedKycService->getKycStatus($user);

        return [
            'kyc_level' => $level->value,
            'completion_percentage' => $status['completion_status']['completion_percentage'],
            'completed_steps' => $status['completion_status']['completed_count'],
            'total_steps' => $status['completion_status']['required_count'],
            'is_complete' => $status['completion_status']['is_complete'] && $status['current_kyc_level'] === $level->value,
        ];
    }

    /**
     * Get estimated completion time for KYC level.
     */
    private function getEstimatedCompletionTime(KycLevel $level): string
    {
        return match ($level) {
            KycLevel::BASIC => '5-10 minutes',
            KycLevel::INTERMEDIATE => '1-2 hours',
            KycLevel::ADVANCED => '1-3 business days',
        };
    }

    /**
     * Get benefits of KYC level.
     */
    private function getKycLevelBenefits(KycLevel $level): array
    {
        return match ($level) {
            KycLevel::BASIC => [
                'Basic platform access',
                'Standard delivery services',
                'Basic customer support',
            ],
            KycLevel::INTERMEDIATE => [
                'Interstate delivery access',
                'Higher transaction limits',
                'Priority customer support',
                'Advanced analytics',
            ],
            KycLevel::ADVANCED => [
                'Enterprise features',
                'Unlimited transaction limits',
                'Dedicated account manager',
                'Custom integrations',
                'White-label options',
            ],
        };
    }

    /**
     * Get next steps for user to complete KYC level (legacy method).
     *
     * @deprecated Use UnifiedKycService::getKycStatus() instead
     */
    private function getNextSteps(User $user, KycLevel $level): array
    {
        $status = $this->unifiedKycService->getKycStatus($user);

        return $status['next_steps'] ?? [];
    }

    /**
     * Log KYC progress for audit trail.
     */
    private function logKycProgress(User $user, string $action, array $metadata = []): void
    {
        $this->loggingService->logAuth($action, array_merge([
            'user_id' => $user->id,
            'email' => $user->email,
        ], $metadata), $user->id);
    }

    /**
     * Initiate unified KYC verification using QoreID.
     */
    public function initiateUnifiedKyc(User $user, array $data): array
    {
        try {
            // Use the unified KYC service for comprehensive verification
            $result = $this->unifiedKycService->initiateKycVerification($user, $data);

            if ($result['success']) {
                // Update user status
                $user->update(['kyc_status' => 'completed']);

                $this->logKycProgress($user, 'unified_kyc_completed', [
                    'verification_score' => $result['verification_score'],
                    'risk_level' => $result['risk_level'],
                    'kyc_level' => $result['kyc_level'],
                    'qoreid_reference' => $result['qoreid_reference'],
                ]);
            }

            return $result;

        } catch (\Exception $e) {
            $this->loggingService->logError('Unified KYC verification failed', $e, [
                'user_id' => $user->id,
                'data_keys' => array_keys($data),
            ]);

            throw $e;
        }
    }

    /**
     * Process individual verification component.
     */
    public function processVerificationComponent(User $user, string $type, array $data): array
    {
        try {
            return $this->unifiedKycService->verifyComponent($user, $type, $data);
        } catch (\Exception $e) {
            $this->loggingService->logError('Component verification failed', $e, [
                'user_id' => $user->id,
                'type' => $type,
                'data_keys' => array_keys($data),
            ]);

            throw $e;
        }
    }

    /**
     * Get unified KYC status.
     */
    public function getUnifiedKycStatus(User $user): array
    {
        return $this->unifiedKycService->getKycStatus($user);
    }

    /**
     * Generate unified KYC report.
     */
    public function generateUnifiedKycReport(User $user): array
    {
        return $this->unifiedKycService->generateKycReport($user);
    }
}
