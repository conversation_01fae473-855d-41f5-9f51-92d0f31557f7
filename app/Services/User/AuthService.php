<?php

declare(strict_types=1);

namespace App\Services\User;

use App\Enums\Delivery\DeliveryProviderStatus;
use App\Enums\Financial\SubscriptionTargetType;
use App\Enums\System\TenantStatus;
use App\Events\User\UserRegistered;
use App\Exceptions\AuthenticationException;
use App\Exceptions\BusinessLogicException;
use App\Exceptions\TenantException;
use App\Models\System\Tenant;
use App\Models\User\User;
use App\Services\Communication\HybridSmsService;
use App\Services\Communication\NotificationService;
use App\Services\System\LoggingService;
use Carbon\Carbon;
use Illuminate\Auth\Events\Login;
use Illuminate\Auth\Events\PasswordReset;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;

class AuthService
{
    public function __construct(
        private readonly LoggingService $loggingService,
        private readonly NotificationService $notificationService,
        private readonly HybridSmsService $smsService,
        private readonly TeamInvitationService $teamInvitationService,
        private readonly VerificationService $verificationService
    ) {}

    /**
     * Phase 1: Simple user registration (email + password only).
     */
    public function registerSimple(array $data): array
    {
        // Check if user already exists
        $existingUser = User::where('email', $data['email'])->first();

        if ($existingUser) {
            throw new AuthenticationException(
                'User already exists with this email',
                'USER_EXISTS'
            );
        }

        // Create user (no roles assigned yet - pending onboarding)
        $user = User::create([
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'email' => $data['email'],
            'phone_number' => $data['phone_number'] ?? null,
            'password' => Hash::make($data['password']),
            'timezone' => $data['timezone'] ?? 'Africa/Lagos',
            'email_verified_at' => null,
            'phone_verified_at' => null,
            'onboarding_status' => 'pending_account_type_selection',
        ]);

        // Initialize notification preferences
        $this->notificationService->initializeUserPreferences($user);

        // Dispatch UserRegistered event
        UserRegistered::dispatch($user, 'simple', 'customer');

        // Log registration
        $this->loggingService->logAuth('user_registered_simple', [
            'user_id' => $user->id,
            'email' => $user->email,
        ], $user->id);

        // Generate token
        $token = $user->createToken('auth_token')->plainTextToken;

        return [
            'user' => $user,
            'token' => $token,
        ];
    }

    /**
     * Phase 2: Select account type and assign appropriate role.
     */
    public function selectAccountType(User $user, string $accountType): array
    {
        // Validate user is in correct onboarding state
        if ($user->onboarding_status !== 'pending_account_type_selection') {
            throw new BusinessLogicException(
                'User has already completed account type selection',
                'INVALID_ONBOARDING_STATE'
            );
        }

        $nextStep = null;
        $onboardingStatus = 'completed';
        $message = 'Account type selected successfully!';

        switch ($accountType) {
            case 'customer':
                // Assign customer role immediately
                \Silber\Bouncer\BouncerFacade::role()->firstOrCreate(['name' => 'customer']);
                $user->assign('customer');
                $onboardingStatus = 'completed';
                $message = 'Welcome! Your customer account is ready to use.';
                break;

            case 'business':
                $onboardingStatus = 'pending_business_onboarding';
                $nextStep = '/auth/onboard/business';
                $message = 'Please complete your business setup to continue.';
                break;

            case 'delivery_provider':
                $onboardingStatus = 'pending_provider_onboarding';
                $nextStep = '/auth/onboard/provider';
                $message = 'Please complete your delivery provider setup to continue.';
                break;

            default:
                throw new BusinessLogicException(
                    'Invalid account type',
                    'INVALID_ACCOUNT_TYPE'
                );
        }

        // Update user onboarding status
        $user->update([
            'onboarding_status' => $onboardingStatus,
        ]);

        // Log account type selection
        $this->loggingService->logAuth('account_type_selected', [
            'user_id' => $user->id,
            'account_type' => $accountType,
            'onboarding_status' => $onboardingStatus,
        ], $user->id);

        return [
            'user' => $user->load('roles'),
            'account_type' => $accountType,
            'onboarding_status' => $onboardingStatus,
            'next_step' => $nextStep,
            'message' => $message,
        ];
    }

    /**
     * Phase 3: Business onboarding (creates tenant + business with minimal data).
     */
    public function onboardBusiness(User $user, array $data): array
    {
        // Validate user is in correct onboarding state
        if ($user->onboarding_status !== 'pending_business_onboarding') {
            throw new BusinessLogicException(
                'User is not in business onboarding state',
                'INVALID_ONBOARDING_STATE'
            );
        }

        // Use transaction only in non-testing environments to avoid conflicts with RefreshDatabase
        $callback = function () use ($user, $data) {
            // Create tenant
            $tenant = Tenant::create([
                'name' => $data['business_name'],
                'tenant_type' => SubscriptionTargetType::BUSINESS,
                'status' => TenantStatus::ACTIVE,
                'created_by' => $user->id,
            ]);

            // Update user with tenant
            $user->update([
                'tenant_id' => $tenant->id,
                'onboarding_status' => 'completed',
            ]);

            // Create business with minimal data
            $business = \App\Models\Business\Business::create([
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'business_name' => $data['business_name'],
                'business_type' => $data['business_type'],
                'description' => $data['description'] ?? null,
                'country_id' => $data['country_id'],
                'subdomain' => $data['subdomain'],
                'contact_email' => $data['contact_email'] ?? $user->email,
                'contact_phone' => $data['contact_phone'] ?? $user->phone_number,
                'status' => \App\Enums\Business\BusinessStatus::PENDING_VERIFICATION,
                'global_auto_accept_orders' => false,
                'accepts_cash_on_delivery' => false,
                'allows_pickup' => false,
            ]);

            // Assign business owner role
            \Silber\Bouncer\BouncerFacade::role()->firstOrCreate(['name' => 'business-owner']);
            $user->assign('business-owner');

            // Log business onboarding
            $this->loggingService->logAuth('business_onboarded', [
                'user_id' => $user->id,
                'business_id' => $business->id,
                'tenant_id' => $tenant->id,
            ], $user->id);

            return [
                'user' => $user->load('roles'),
                'business' => $business->load(['tenant', 'country']),
                'tenant' => $tenant,
                'tenant_url' => "https://{$data['subdomain']}.deliverynexus.ng",
            ];
        };

        return app()->environment('testing') ? $callback() : DB::transaction($callback);
    }

    /**
     * Phase 3: Provider onboarding (creates tenant + provider with minimal data).
     */
    public function onboardProvider(User $user, array $data): array
    {
        // Validate user is in correct onboarding state
        if ($user->onboarding_status !== 'pending_provider_onboarding') {
            throw new BusinessLogicException(
                'User is not in provider onboarding state',
                'INVALID_ONBOARDING_STATE'
            );
        }

        $callback = function () use ($user, $data) {
            // Create tenant
            $tenant = Tenant::create([
                'name' => $data['provider_name'],
                'tenant_type' => SubscriptionTargetType::PROVIDER,
                'status' => TenantStatus::ACTIVE,
                'created_by' => $user->id,
            ]);

            // Update user with tenant
            $user->update([
                'tenant_id' => $tenant->id,
                'onboarding_status' => 'completed',
            ]);

            // Create delivery provider with minimal data
            $provider = \App\Models\Delivery\DeliveryProvider::create([
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'company_name' => $data['provider_name'],
                'description' => $data['description'] ?? null,
                'country_id' => $data['country_id'],
                'contact_email' => $data['contact_email'] ?? $user->email,
                'contact_phone' => $data['contact_phone'] ?? $user->phone_number,
                'status' => DeliveryProviderStatus::PENDING_VERIFICATION,
            ]);

            // Assign delivery provider owner role
            \Silber\Bouncer\BouncerFacade::role()->firstOrCreate(['name' => 'delivery-provider-owner']);
            $user->assign('delivery-provider-owner');

            // Log provider onboarding
            $this->loggingService->logAuth('provider_onboarded', [
                'user_id' => $user->id,
                'provider_id' => $provider->id,
                'tenant_id' => $tenant->id,
            ], $user->id);

            return [
                'user' => $user->load('roles'),
                'provider' => $provider->load(['tenant', 'country']),
                'tenant' => $tenant,
                'tenant_url' => "https://{$data['subdomain']}.deliverynexus.ng",
            ];
        };

        return app()->environment('testing') ? $callback() : DB::transaction($callback);
    }

    /**
     * Register business with owner (creates user + tenant + business).
     */
    public function registerBusinessWithOwner(array $data, bool $isStateful = false): array
    {
        $callback = function () use ($data) {
            $userData = $data['user'] ?? $data;
            $businessData = $data['business'] ?? $data;

            // Check if user already exists
            $existingUser = User::where('email', $userData['email'])->first();
            if ($existingUser) {
                throw new AuthenticationException(
                    'User already exists with this email',
                    'USER_EXISTS'
                );
            }

            // Create user
            $user = User::create([
                'first_name' => $userData['first_name'],
                'last_name' => $userData['last_name'],
                'email' => $userData['email'],
                'phone_number' => $userData['phone_number'] ?? null,
                'password' => Hash::make($userData['password']),
                'timezone' => $userData['timezone'] ?? 'Africa/Lagos',
            ]);

            // Create tenant
            $tenant = Tenant::create([
                'name' => $businessData['business_name'],
                'tenant_type' => SubscriptionTargetType::BUSINESS,
                'status' => TenantStatus::ACTIVE,
                'created_by' => $user->id,
            ]);

            // Update user with tenant
            $user->update(['tenant_id' => $tenant->id]);

            // Create business
            $business = \App\Models\Business\Business::create([
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'business_name' => $businessData['business_name'],
                'business_type' => $businessData['business_type'],
                'description' => $businessData['description'] ?? null,
                'country_id' => $businessData['country_id'],
                'state_id' => $businessData['state_id'] ?? null,
                'subdomain' => $businessData['subdomain'],
                'contact_email' => $businessData['contact_email'] ?? $userData['email'],
                'contact_phone' => $businessData['contact_phone'] ?? $userData['phone_number'],
                'status' => \App\Enums\Business\BusinessStatus::PENDING_VERIFICATION,
            ]);

            // Create address if provided
            if (! empty($businessData['address'])) {
                $address = \App\Models\User\Address::create([
                    'addressable_type' => \App\Models\Business\Business::class,
                    'addressable_id' => $business->id,
                    'label' => 'business',
                    'street_address' => $businessData['address']['address_line_1'],
                    'city_name_string' => $businessData['address']['city'],
                    'state_province_string' => $businessData['address']['state'] ?? null,
                    'postal_code' => $businessData['address']['postal_code'] ?? null,
                    'country_id' => $businessData['country_id'],
                    'latitude' => $businessData['address']['latitude'] ?? null,
                    'longitude' => $businessData['address']['longitude'] ?? null,
                ]);

                $business->update(['primary_address_id' => $address->id]);
            }

            // Assign business owner role
            \Silber\Bouncer\BouncerFacade::role()->firstOrCreate(['name' => 'business-owner']);
            $user->assign('business-owner');

            // Initialize notification preferences
            $this->notificationService->initializeUserPreferences($user);

            // Dispatch UserRegistered event
            UserRegistered::dispatch($user, 'business_with_owner', 'business_owner');

            // Log registration
            $this->loggingService->logAuth('business_registered', [
                'user_id' => $user->id,
                'business_id' => $business->id,
                'tenant_id' => $tenant->id,
            ], $user->id);

            // Generate token
            $token = $user->createToken('auth_token')->plainTextToken;

            return [
                'user' => $user->load('roles'),
                'business' => $business->load(['tenant', 'country', 'primaryAddress']),
                'tenant' => $tenant,
                'token' => $token,
            ];
        };

        return app()->environment('testing') ? $callback() : DB::transaction($callback);
    }

    /**
     * Authenticate user and return token.
     */
    public function login(string $identifier, string $password, ?string $tenantId = null): array
    {
        // Find user by email or phone
        $user = User::where('email', $identifier)
            ->orWhere('phone_number', $identifier)
            ->first();

        if (! $user || ! Hash::check($password, $user->password)) {
            throw new AuthenticationException(
                'Invalid credentials',
                'INVALID_CREDENTIALS'
            );
        }

        // Check if user is active
        if (! $user->is_active) {
            throw new AuthenticationException(
                'Account is deactivated',
                'ACCOUNT_DEACTIVATED'
            );
        }

        // Validate tenant context if provided
        $tenant = null;
        if ($tenantId) {
            $tenant = Tenant::find($tenantId);
            if (! $tenant) {
                throw new TenantException('Invalid tenant', 'INVALID_TENANT');
            }

            // Check if tenant is active
            if (! $tenant->is_active) {
                throw new TenantException('Tenant is not active', 'TENANT_INACTIVE');
            }
        }

        // Log successful login
        $this->loggingService->logAuth('user_login', [
            'user_id' => $user->id,
            'email' => $user->email,
            'tenant_id' => $tenantId,
        ], $user->id);

        // Update last login timestamp
        $user->update(['last_login_at' => now()]);

        // Generate new token
        $token = $user->createToken('auth_token')->plainTextToken;

        return [
            'user' => $user->load('roles'),
            'token' => $token,
            'tenant' => $tenant,
        ];
    }

    /**
     * Register delivery provider with owner (creates user + tenant + provider).
     */
    public function registerProviderWithOwner(array $data, bool $isStateful = false): array
    {
        $callback = function () use ($data) {
            $userData = $data['user'] ?? $data;
            $providerData = $data['provider'] ?? $data;

            // Check if user already exists
            $existingUser = User::where('email', $userData['email'])->first();
            if ($existingUser) {
                throw new AuthenticationException(
                    'User already exists with this email',
                    'USER_EXISTS'
                );
            }

            // Create user
            $user = User::create([
                'first_name' => $userData['first_name'],
                'last_name' => $userData['last_name'],
                'email' => $userData['email'],
                'phone_number' => $userData['phone_number'] ?? null,
                'password' => Hash::make($userData['password']),
                'timezone' => $userData['timezone'] ?? 'Africa/Lagos',
            ]);

            // Create tenant
            $tenant = Tenant::create([
                'name' => $providerData['provider_name'],
                'tenant_type' => SubscriptionTargetType::PROVIDER,
                'status' => TenantStatus::ACTIVE,
                'created_by' => $user->id,
            ]);

            // Update user with tenant
            $user->update(['tenant_id' => $tenant->id]);

            // Create delivery provider
            $provider = \App\Models\Delivery\DeliveryProvider::create([
                'tenant_id' => $tenant->id,
                'user_id' => $user->id,
                'company_name' => $providerData['provider_name'],
                'description' => $providerData['description'] ?? null,
                'country_id' => $providerData['country_id'],
                'state_id' => $providerData['state_id'] ?? null,
                'contact_email' => $providerData['contact_email'] ?? $userData['email'],
                'contact_phone' => $providerData['contact_phone'] ?? $userData['phone_number'],
                'status' => DeliveryProviderStatus::PENDING_VERIFICATION,
            ]);

            // Create address if provided
            if (! empty($providerData['address'])) {
                $address = \App\Models\User\Address::create([
                    'addressable_type' => \App\Models\Delivery\DeliveryProvider::class,
                    'addressable_id' => $provider->id,
                    'label' => 'provider',
                    'street_address' => $providerData['address']['address_line_1'],
                    'city_name_string' => $providerData['address']['city'],
                    'state_province_string' => $providerData['address']['state'] ?? null,
                    'postal_code' => $providerData['address']['postal_code'] ?? null,
                    'country_id' => $providerData['country_id'],
                    'latitude' => $providerData['address']['latitude'] ?? null,
                    'longitude' => $providerData['address']['longitude'] ?? null,
                ]);

                $provider->update(['primary_address_id' => $address->id]);
            }

            // Assign delivery provider owner role
            \Silber\Bouncer\BouncerFacade::role()->firstOrCreate(['name' => 'delivery-provider-owner']);
            $user->assign('delivery-provider-owner');

            // Initialize notification preferences
            $this->notificationService->initializeUserPreferences($user);

            // Dispatch UserRegistered event
            UserRegistered::dispatch($user, 'provider_with_owner', 'delivery_provider');

            // Log registration
            $this->loggingService->logAuth('provider_registered', [
                'user_id' => $user->id,
                'provider_id' => $provider->id,
                'tenant_id' => $tenant->id,
            ], $user->id);

            // Generate token
            $token = $user->createToken('auth_token')->plainTextToken;

            return [
                'user' => $user->load('roles'),
                'provider' => $provider->load(['tenant', 'country', 'primaryAddress']),
                'tenant' => $tenant,
                'token' => $token,
            ];
        };

        return app()->environment('testing') ? $callback() : DB::transaction($callback);
    }

    /**
     * Register from staff invitation.
     */
    public function registerFromInvitation(array $data): array
    {
        $callback = function () use ($data) {
            // Use TeamInvitationService to accept invitation
            $result = $this->teamInvitationService->acceptInvitation(
                $data['invitation_token'],
                $data['password']
            );

            $user = $result['user'];
            $invitation = $result['invitation'];

            // Dispatch UserRegistered event
            UserRegistered::dispatch($user, 'invitation', $invitation->role);

            // Log registration
            $this->loggingService->logAuth('invitation_registered', [
                'user_id' => $user->id,
                'invitation_id' => $invitation->id,
                'tenant_id' => $invitation->tenant_id,
                'role' => $invitation->role,
            ], $user->id);

            // Generate token
            $token = $user->createToken('auth_token')->plainTextToken;

            return [
                'user' => $user->load('roles'),
                'tenant' => $invitation->tenant,
                'role' => $invitation->role,
                'token' => $token,
                'team_member' => $result['team_member'],
            ];
        };

        return app()->environment('testing') ? $callback() : DB::transaction($callback);
    }

    /**
     * Register platform admin (super admin only).
     */
    public function registerAdmin(array $data): array
    {
        // Check if user already exists
        $existingUser = User::where('email', $data['email'])->first();
        if ($existingUser) {
            throw new AuthenticationException(
                'User already exists with this email',
                'USER_EXISTS'
            );
        }

        // Create admin user
        $user = User::create([
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'email' => $data['email'],
            'phone_number' => $data['phone_number'] ?? null,
            'password' => Hash::make($data['password']),
            'timezone' => $data['timezone'] ?? 'Africa/Lagos',
            'email_verified_at' => now(), // Auto-verify admin emails
        ]);

        // Assign admin role
        \Silber\Bouncer\BouncerFacade::role()->firstOrCreate(['name' => $data['role']]);
        $user->assign($data['role']);

        // Dispatch UserRegistered event
        UserRegistered::dispatch($user, 'admin', $data['role']);

        // Log registration
        $this->loggingService->logAuth('admin_registered', [
            'user_id' => $user->id,
            'role' => $data['role'],
        ], $user->id);

        // Generate token
        $token = $user->createToken('auth_token')->plainTextToken;

        return [
            'user' => $user->load('roles'),
            'token' => $token,
        ];
    }

    /**
     * Central domain login - Allows central users and tenant owners.
     * Used by customers, platform admins, and tenant owners on api.deliverynexus.com
     */
    public function centralLogin(string $identifier, string $password): array
    {
        // Find user by email or phone
        $user = User::where('email', $identifier)
            ->orWhere('phone_number', $identifier)
            ->first();

        if (! $user || ! Hash::check($password, $user->password)) {
            throw new AuthenticationException(
                'Invalid credentials',
                'INVALID_CREDENTIALS'
            );
        }

        // Check if user is active
        if (! $user->is_active) {
            throw new AuthenticationException(
                'Account is deactivated',
                'ACCOUNT_DEACTIVATED'
            );
        }

        // Allow both central users AND tenant owners on central domain
        $allowedOnCentral = $user->tenant_id === null ||
                           $user->hasAnyRole(['business-owner', 'provider-owner']);

        if (! $allowedOnCentral) {
            throw new AuthenticationException(
                'Please use your tenant domain to login.',
                'USE_TENANT_DOMAIN'
            );
        }

        // Dispatch Laravel's Login event
        event(new Login('web', $user, false));

        // Log successful login
        $this->loggingService->logAuth('central_login', [
            'user_id' => $user->id,
            'email' => $user->email,
            'context' => 'central',
            'has_tenant' => $user->tenant_id !== null,
        ], $user->id);

        // Generate new token
        $token = $user->createToken('auth_token')->plainTextToken;

        return [
            'user' => $user->load('roles'),
            'token' => $token,
        ];
    }

    /**
     * Tenant-specific login with automatic tenant validation.
     */
    public function tenantLogin(string $identifier, string $password, $tenant): array
    {
        // Find user by email or phone
        $user = User::where('email', $identifier)
            ->orWhere('phone_number', $identifier)
            ->first();

        if (! $user || ! Hash::check($password, $user->password)) {
            throw new AuthenticationException(
                'Invalid credentials',
                'INVALID_CREDENTIALS'
            );
        }

        // Check if user is active
        if (! $user->is_active) {
            throw new AuthenticationException(
                'Account is deactivated',
                'ACCOUNT_DEACTIVATED'
            );
        }

        // Verify user belongs to this tenant
        if ($user->tenant_id !== $tenant->id) {
            throw new AuthenticationException(
                'User not authorized for this tenant',
                'TENANT_ACCESS_DENIED'
            );
        }

        // Check if tenant is active
        if ($tenant->status !== TenantStatus::ACTIVE) {
            throw new TenantException('Tenant is not active', 'TENANT_INACTIVE');
        }

        // Dispatch Laravel's Login event
        event(new Login('web', $user, false));

        // Log successful login
        $this->loggingService->logAuth('tenant_login', [
            'user_id' => $user->id,
            'tenant_id' => $tenant->id,
        ], $user->id);

        // Generate new token
        $token = $user->createToken('auth_token')->plainTextToken;

        return [
            'user' => $user->load('roles'),
            'token' => $token,
            'tenant' => $tenant,
        ];
    }

    /**
     * Logout user by revoking current token.
     */
    public function logout(User $user, ?string $tokenId = null): bool
    {
        if ($tokenId) {
            // Revoke specific token by ID
            $token = $user->tokens()->where('id', $tokenId)->first();
            if ($token) {
                $token->delete();

                return true;
            }

            return false;
        }

        // Get the current token from the request
        $currentToken = $user->currentAccessToken();

        if ($currentToken) {
            // Delete the current token
            $currentToken->delete();
        } else {
            // In testing environment, currentAccessToken() might not work
            // So we'll revoke the most recent token
            $latestToken = $user->tokens()->latest()->first();
            if ($latestToken) {
                $latestToken->delete();
            }
        }

        return true;
    }

    /**
     * Logout from all devices by revoking all tokens.
     */
    public function logoutFromAllDevices(User $user): bool
    {
        $user->tokens()->delete();

        return true;
    }

    /**
     * Refresh user token.
     */
    public function refreshToken(User $user): string
    {
        // Revoke current token
        $user->currentAccessToken()?->delete();

        // Generate new token
        return $user->createToken('auth_token')->plainTextToken;
    }

    /**
     * Create token with detailed information for different contexts.
     */
    public function createToken(User $user, string $deviceName = 'API Token', string $context = 'web'): array
    {
        // Set token expiration based on context
        $expiresIn = match ($context) {
            'mobile' => 60 * 24 * 7, // 7 days for mobile
            'web' => 60 * 24, // 1 day for web
            default => 60 * 24, // Default 1 day
        };

        // Create token with abilities based on user roles
        $abilities = $this->getUserAbilities($user);
        $token = $user->createToken($deviceName, $abilities, now()->addMinutes($expiresIn));

        return [
            'token' => $token->plainTextToken,
            'token_type' => 'Bearer',
            'expires_in' => $expiresIn * 60, // Convert to seconds
            'abilities' => $abilities,
        ];
    }

    /**
     * Get user abilities based on their roles.
     */
    private function getUserAbilities(User $user): array
    {
        $abilities = ['*']; // Default abilities

        // Add role-specific abilities
        if ($user->isA('customer')) {
            $abilities = array_merge($abilities, [
                'orders:create',
                'orders:view',
                'profile:update',
            ]);
        }

        if ($user->isA('business_owner')) {
            $abilities = array_merge($abilities, [
                'business:manage',
                'orders:manage',
                'products:manage',
            ]);
        }

        if ($user->isA('delivery_provider')) {
            $abilities = array_merge($abilities, [
                'deliveries:manage',
                'location:update',
            ]);
        }

        if ($user->isA('admin')) {
            $abilities = ['*']; // Admin has all abilities
        }

        return array_unique($abilities);
    }

    /**
     * Verify if user has required permissions for tenant operations.
     */
    public function verifyTenantAccess(User $user, string $tenantId, array $permissions = []): bool
    {
        // All users (customers) can access any active tenant
        $tenant = Tenant::find($tenantId);
        if (! $tenant || ! $tenant->is_active) {
            return false;
        }

        // Check specific permissions if provided
        if (! empty($permissions)) {
            foreach ($permissions as $permission) {
                if (! $user->can($permission)) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Get user's accessible tenants.
     */
    public function getUserAccessibleTenants(User $user): array
    {
        // All users (customers) can access all active tenants
        return Tenant::where('is_active', true)->get()->toArray();
    }

    /**
     * Check if user can interact with a specific tenant.
     */
    public function canUserInteractWithTenant(User $user, string $tenantId): bool
    {
        // All users (customers) can interact with any active tenant
        $tenant = Tenant::find($tenantId);

        return $tenant && $tenant->is_active;
    }

    /**
     * Send email verification code.
     */
    public function sendEmailVerification(User $user): bool
    {
        if ($user->hasVerifiedEmail()) {
            throw new BusinessLogicException(
                'Email is already verified',
                'EMAIL_ALREADY_VERIFIED',
                422
            );
        }

        $code = str_pad((string) random_int(100000, 999999), 6, '0', STR_PAD_LEFT);
        $key = "email_verification:{$user->id}";

        // Store verification code in cache for 15 minutes
        Cache::put($key, $code, 900);

        // Send email verification notification
        $this->notificationService->sendEmailVerificationCode($user, $code, 15);

        $this->loggingService->logAuth('email_verification_sent', [
            'user_id' => $user->id,
            'email' => $user->email,
        ], $user->id);

        return true;
    }

    /**
     * Verify email with code.
     */
    public function verifyEmail(User $user, string $code): bool
    {
        $key = "email_verification:{$user->id}";
        $storedCode = Cache::get($key);

        if (! $storedCode || $storedCode !== $code) {
            throw new BusinessLogicException(
                'Invalid or expired verification code',
                'INVALID_VERIFICATION_CODE',
                422
            );
        }

        // Update user email verification
        $user->update(['email_verified_at' => now()]);

        // Remove verification code from cache
        Cache::forget($key);

        $this->loggingService->logAuth('email_verified', [
            'user_id' => $user->id,
            'email' => $user->email,
        ], $user->id);

        return true;
    }

    /**
     * Send phone verification code via SMS.
     */
    public function sendPhoneVerification(User $user): bool
    {
        return $this->verificationService->sendPhoneVerification($user);
    }

    /**
     * Verify phone with code.
     */
    public function verifyPhone(User $user, string $code): bool
    {
        return $this->verificationService->verifyPhoneOtp($user, $code);
    }

    /**
     * Send password reset code.
     */
    public function sendPasswordReset(string $identifier): bool
    {
        // Find user by email or phone
        $user = User::where('email', $identifier)
            ->orWhere('phone_number', $identifier)
            ->first();

        if (! $user) {
            // Don't reveal if user exists or not for security
            return true;
        }

        $token = Str::random(64);
        $key = "password_reset:{$user->id}";

        // Generate 6-digit code for easier user input
        $code = str_pad((string) random_int(100000, 999999), 6, '0', STR_PAD_LEFT);

        // Store reset code in cache for 30 minutes
        Cache::put($key, $code, 1800);

        // Store in database for additional security
        DB::table('password_reset_tokens')->updateOrInsert(
            ['email' => $user->email],
            [
                'email' => $user->email,
                'token' => Hash::make($code),
                'created_at' => now(),
            ]
        );

        // Send password reset notification
        $this->notificationService->sendPasswordResetCode($user, $code, 30);

        $this->loggingService->logAuth('password_reset_sent', [
            'user_id' => $user->id,
            'identifier' => $identifier,
        ], $user->id);

        return true;
    }

    /**
     * Reset password with token.
     */
    public function resetPassword(string $identifier, string $token, string $newPassword): bool
    {
        // Find user by email or phone
        $user = User::where('email', $identifier)
            ->orWhere('phone_number', $identifier)
            ->first();

        if (! $user) {
            throw new AuthenticationException(
                'Invalid reset token',
                'INVALID_RESET_TOKEN'
            );
        }

        $key = "password_reset:{$user->id}";
        $storedToken = Cache::get($key);

        // Check cache first, then database
        $validToken = false;
        if ($storedToken && $storedToken === $token) {
            $validToken = true;
        } else {
            // Check database
            $resetRecord = DB::table('password_reset_tokens')
                ->where('email', $user->email)
                ->first();

            if ($resetRecord && Hash::check($token, $resetRecord->token)) {
                // Check if token is not expired (60 minutes)
                $createdAt = Carbon::parse($resetRecord->created_at);
                if ($createdAt->addHour()->isFuture()) {
                    $validToken = true;
                }
            }
        }

        if (! $validToken) {
            throw new AuthenticationException(
                'Invalid or expired reset token',
                'INVALID_RESET_TOKEN'
            );
        }

        // Update password
        $user->update(['password' => Hash::make($newPassword)]);

        // Dispatch Laravel's PasswordReset event
        event(new PasswordReset($user));

        // Clean up reset tokens
        Cache::forget($key);
        DB::table('password_reset_tokens')->where('email', $user->email)->delete();

        // Revoke all existing tokens for security
        $user->tokens()->delete();

        $this->loggingService->logAuth('password_reset_completed', [
            'user_id' => $user->id,
            'identifier' => $identifier,
        ], $user->id);

        return true;
    }
}
