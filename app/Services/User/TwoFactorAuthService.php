<?php

declare(strict_types=1);

namespace App\Services\User;

use App\Exceptions\BusinessLogicException;
use App\Models\User\User;
use App\Models\User\Verification;
use App\Services\Communication\NotificationService;
use App\Services\System\LoggingService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use PragmaRX\Google2FA\Google2FA;
use Symfony\Component\HttpFoundation\Response;

/**
 * Two-Factor Authentication Service
 *
 * Handles TOTP-based 2FA using Google Authenticator and backup codes.
 * Integrates with existing Verification model and notification system.
 */
class TwoFactorAuthService
{
    private Google2FA $google2fa;

    public function __construct(
        private readonly LoggingService $loggingService,
        private readonly NotificationService $notificationService
    ) {
        $this->google2fa = new Google2FA;
    }

    /**
     * Enable two-factor authentication for user.
     */
    public function enableTwoFactor(User $user): array
    {
        if ($user->two_factor_secret) {
            throw new BusinessLogicException(
                'Two-factor authentication is already enabled',
                'TWO_FACTOR_ALREADY_ENABLED',
                Response::HTTP_CONFLICT
            );
        }

        return DB::transaction(function () use ($user) {
            // Generate secret key
            $secret = $this->google2fa->generateSecretKey();

            // Generate QR code URL
            $qrCodeUrl = $this->google2fa->getQRCodeUrl(
                config('app.name'),
                $user->email,
                $secret
            );

            // Store secret temporarily (not confirmed yet)
            $user->update([
                'two_factor_secret' => encrypt($secret),
                'two_factor_confirmed_at' => null,
            ]);

            // Generate backup codes
            $backupCodes = $this->generateBackupCodes($user);

            // Create verification record for setup
            Verification::create([
                'verifiable_type' => User::class,
                'verifiable_id' => $user->id,
                'verification_type' => 'two_factor_setup',
                'status' => \App\Enums\User\VerificationStatus::PENDING,
                'code_expires_at' => now()->addMinutes(30),
                'max_attempts' => 3,
                'attempts' => 0,
                'verification_data' => [
                    'secret' => $secret,
                    'backup_codes' => $backupCodes,
                ],
            ]);

            $this->loggingService->logAuth('two_factor_setup_initiated', [
                'user_id' => $user->id,
            ], $user->id);

            return [
                'secret' => $secret,
                'qr_code_url' => $qrCodeUrl,
                'backup_codes' => $backupCodes,
                'manual_entry_key' => $secret,
            ];
        });
    }

    /**
     * Verify two-factor setup with TOTP code.
     */
    public function verifyTwoFactorSetup(User $user, string $code): bool
    {
        if (! $user->two_factor_secret) {
            throw new BusinessLogicException(
                'Two-factor authentication setup not initiated',
                'TWO_FACTOR_NOT_INITIATED',
                Response::HTTP_BAD_REQUEST
            );
        }

        if ($user->two_factor_confirmed_at) {
            throw new BusinessLogicException(
                'Two-factor authentication is already confirmed',
                'TWO_FACTOR_ALREADY_CONFIRMED',
                Response::HTTP_CONFLICT
            );
        }

        return DB::transaction(function () use ($user, $code) {
            $secret = decrypt($user->two_factor_secret);

            // Verify TOTP code
            $valid = $this->google2fa->verifyKey($secret, $code);

            if (! $valid) {
                $this->loggingService->logAuth('two_factor_setup_failed', [
                    'user_id' => $user->id,
                    'reason' => 'invalid_code',
                ], $user->id);

                throw new BusinessLogicException(
                    'Invalid verification code',
                    'INVALID_TWO_FACTOR_CODE',
                    Response::HTTP_UNPROCESSABLE_ENTITY
                );
            }

            // Confirm 2FA setup
            $user->update([
                'two_factor_confirmed_at' => now(),
            ]);

            // Update verification record
            $verification = Verification::where('verifiable_type', User::class)
                ->where('verifiable_id', $user->id)
                ->where('verification_type', 'two_factor_setup')
                ->where('status', \App\Enums\User\VerificationStatus::PENDING)
                ->first();

            if ($verification) {
                $verification->update([
                    'status' => \App\Enums\User\VerificationStatus::APPROVED,
                    'verified_at' => now(),
                ]);
            }

            $this->loggingService->logAuth('two_factor_enabled', [
                'user_id' => $user->id,
            ], $user->id);

            // Send confirmation notification
            $this->notificationService->sendToUser(
                $user,
                new \App\Notifications\User\TwoFactorEnabledNotification
            );

            return true;
        });
    }

    /**
     * Generate backup codes for user.
     */
    public function generateBackupCodes(User $user): array
    {
        $codes = [];
        for ($i = 0; $i < 8; $i++) {
            $codes[] = strtoupper(substr(str_shuffle('ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789'), 0, 8));
        }

        // Store hashed backup codes
        $hashedCodes = array_map(fn ($code) => Hash::make($code), $codes);

        $user->update([
            'two_factor_backup_codes' => encrypt(json_encode($hashedCodes)),
        ]);

        return $codes;
    }

    /**
     * Verify two-factor code for login.
     */
    public function verifyTwoFactorLogin(User $user, string $code): bool
    {
        if (! $user->hasTwoFactorEnabled()) {
            throw new BusinessLogicException(
                'Two-factor authentication is not enabled',
                'TWO_FACTOR_NOT_ENABLED',
                Response::HTTP_BAD_REQUEST
            );
        }

        // Check rate limiting
        $rateLimitKey = "2fa_attempts:{$user->id}";
        $attempts = Cache::get($rateLimitKey, 0);

        if ($attempts >= 5) {
            throw new BusinessLogicException(
                'Too many failed attempts. Please try again later.',
                'TWO_FACTOR_RATE_LIMITED',
                Response::HTTP_TOO_MANY_REQUESTS
            );
        }

        $secret = decrypt($user->two_factor_secret);

        // Try TOTP verification first
        $valid = $this->google2fa->verifyKey($secret, $code);

        // If TOTP fails, try backup codes
        if (! $valid) {
            $valid = $this->verifyBackupCode($user, $code);
        }

        if (! $valid) {
            // Increment failed attempts
            Cache::put($rateLimitKey, $attempts + 1, 300); // 5 minutes

            $this->loggingService->logAuth('two_factor_login_failed', [
                'user_id' => $user->id,
                'attempts' => $attempts + 1,
            ], $user->id);

            throw new BusinessLogicException(
                'Invalid two-factor authentication code',
                'INVALID_TWO_FACTOR_CODE',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        // Clear rate limiting on success
        Cache::forget($rateLimitKey);

        $this->loggingService->logAuth('two_factor_login_success', [
            'user_id' => $user->id,
        ], $user->id);

        return true;
    }

    /**
     * Verify backup code.
     */
    private function verifyBackupCode(User $user, string $code): bool
    {
        if (! $user->two_factor_backup_codes) {
            return false;
        }

        $backupCodes = json_decode(decrypt($user->two_factor_backup_codes), true);

        foreach ($backupCodes as $index => $hashedCode) {
            if (Hash::check($code, $hashedCode)) {
                // Remove used backup code
                unset($backupCodes[$index]);
                $user->update([
                    'two_factor_backup_codes' => encrypt(json_encode(array_values($backupCodes))),
                ]);

                $this->loggingService->logAuth('backup_code_used', [
                    'user_id' => $user->id,
                    'remaining_codes' => count($backupCodes),
                ], $user->id);

                return true;
            }
        }

        return false;
    }

    /**
     * Disable two-factor authentication.
     */
    public function disableTwoFactor(User $user, string $password): bool
    {
        if (! Hash::check($password, $user->password)) {
            throw new BusinessLogicException(
                'Invalid password',
                'INVALID_PASSWORD',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        if (! $user->hasTwoFactorEnabled()) {
            throw new BusinessLogicException(
                'Two-factor authentication is not enabled',
                'TWO_FACTOR_NOT_ENABLED',
                Response::HTTP_BAD_REQUEST
            );
        }

        return DB::transaction(function () use ($user) {
            $user->update([
                'two_factor_secret' => null,
                'two_factor_confirmed_at' => null,
                'two_factor_backup_codes' => null,
            ]);

            $this->loggingService->logAuth('two_factor_disabled', [
                'user_id' => $user->id,
            ], $user->id);

            // Send notification
            $this->notificationService->sendToUser(
                $user,
                new \App\Notifications\User\TwoFactorDisabledNotification
            );

            return true;
        });
    }

    /**
     * Get two-factor authentication status.
     */
    public function getTwoFactorStatus(User $user): array
    {
        $backupCodesCount = 0;
        if ($user->two_factor_backup_codes) {
            $backupCodes = json_decode(decrypt($user->two_factor_backup_codes), true);
            $backupCodesCount = count($backupCodes);
        }

        return [
            'enabled' => $user->hasTwoFactorEnabled(),
            'confirmed' => ! is_null($user->two_factor_confirmed_at),
            'backup_codes_count' => $backupCodesCount,
            'setup_pending' => ! is_null($user->two_factor_secret) && is_null($user->two_factor_confirmed_at),
        ];
    }
}
