<?php

declare(strict_types=1);

namespace App\Services\User;

use App\Exceptions\BusinessLogicException;
use App\Helpers\OtpHelper;
use App\Models\User\User;
use App\Services\Communication\HybridSmsService;
use App\Services\Communication\NotificationService;
use App\Services\System\LoggingService;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Hash;

/**
 * Verification Service
 *
 * Handles email and phone verification using OTPs:
 * - Generate and send OTPs
 * - Verify OTPs
 * - Password reset tokens
 * - Rate limiting
 */
class VerificationService
{
    public function __construct(
        private NotificationService $notificationService,
        private HybridSmsService $smsService,
        private LoggingService $loggingService
    ) {}

    /**
     * Send email verification OTP.
     */
    public function sendEmailVerification(User $user): bool
    {
        if ($user->hasVerifiedEmail()) {
            throw new BusinessLogicException(
                'Email is already verified',
                'EMAIL_ALREADY_VERIFIED',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        // Use OtpHelper for generation and rate limiting
        $result = OtpHelper::generateEmailVerification($user->email);

        if (! $result['success']) {
            throw new BusinessLogicException($result['error']);
        }

        $otp = $result['otp'];

        // Send email verification code using NotificationService
        try {
            $this->notificationService->sendEmailVerificationCode($user, $otp, 15);

            $this->loggingService->logAuth('email_verification_sent', [
                'user_id' => $user->id,
                'email' => $user->email,
            ], $user->id);

            return true;
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send email verification', $e, [
                'user_id' => $user->id,
                'email' => $user->email,
            ]);

            return false;
        }
    }

    /**
     * Verify email OTP.
     */
    public function verifyEmailOtp(User $user, string $otp): bool
    {
        if (! OtpHelper::verifyEmailVerification($user->email, $otp)) {
            $this->loggingService->logAuth('email_verification_failed', [
                'user_id' => $user->id,
                'email' => $user->email,
                'reason' => 'invalid_otp',
            ], $user->id);

            throw new BusinessLogicException(
                'Invalid or expired verification code',
                'INVALID_VERIFICATION_CODE',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        // Mark email as verified
        $user->markEmailAsVerified();

        $this->loggingService->logAuth('email_verified', [
            'user_id' => $user->id,
            'email' => $user->email,
        ], $user->id);

        return true;
    }

    /**
     * Send phone verification OTP.
     */
    public function sendPhoneVerification(User $user): bool
    {
        if (! $user->phone_number) {
            throw new BusinessLogicException(
                'User does not have a phone number',
                'NO_PHONE_NUMBER',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        if ($user->hasVerifiedPhone()) {
            throw new BusinessLogicException(
                'Phone number is already verified',
                'PHONE_ALREADY_VERIFIED',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        // Use OtpHelper for generation and rate limiting
        $result = OtpHelper::generatePhoneVerification($user->phone_number);

        if (! $result['success']) {
            throw new BusinessLogicException($result['error']);
        }

        $otp = $result['otp'];

        // Send SMS
        $message = "Your DeliveryNexus verification code is: {$otp}. Valid for 15 minutes.";
        $sent = $this->smsService->sendMessage($user->phone_number, $message, $user);

        if ($sent) {
            $this->loggingService->logAuth('phone_verification_sent', [
                'user_id' => $user->id,
                'phone' => $user->phone_number,
            ], $user->id);
        }

        return $sent;
    }

    /**
     * Verify phone OTP.
     */
    public function verifyPhoneOtp(User $user, string $otp): bool
    {
        if (! OtpHelper::verifyPhoneVerification($user->phone_number, $otp)) {
            $this->loggingService->logAuth('phone_verification_failed', [
                'user_id' => $user->id,
                'phone' => $user->phone_number,
                'reason' => 'invalid_otp',
            ], $user->id);

            throw new BusinessLogicException(
                'Invalid or expired verification code',
                'INVALID_VERIFICATION_CODE',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }

        // Mark phone as verified
        $user->markPhoneAsVerified();

        $this->loggingService->logAuth('phone_verified', [
            'user_id' => $user->id,
            'phone' => $user->phone_number,
        ], $user->id);

        return true;
    }

    /**
     * Send password reset OTP.
     */
    public function sendPasswordResetOtp(string $email): bool
    {
        $user = User::where('email', $email)->first();

        if (! $user) {
            // Don't reveal if email exists
            return true;
        }

        // Use OtpHelper for generation and rate limiting
        $result = OtpHelper::generatePasswordReset($email);

        if (! $result['success']) {
            throw new BusinessLogicException($result['error']);
        }

        $otp = $result['otp'];

        // Send password reset code using NotificationService
        try {
            $this->notificationService->sendPasswordResetCode($user, $otp, 30);

            $this->loggingService->logAuth('password_reset_sent', [
                'user_id' => $user->id,
                'email' => $email,
            ], $user->id);

            return true;
        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to send password reset', $e, [
                'user_id' => $user->id,
                'email' => $email,
            ]);

            return false;
        }
    }

    /**
     * Verify password reset OTP and reset password.
     */
    public function resetPasswordWithOtp(string $email, string $otp, string $newPassword): bool
    {
        $user = User::where('email', $email)->first();

        if (! $user) {
            throw new BusinessLogicException('Invalid reset request');
        }

        if (! OtpHelper::verifyPasswordReset($email, $otp)) {
            $this->loggingService->logAuth('password_reset_failed', [
                'user_id' => $user->id,
                'email' => $email,
                'reason' => 'invalid_otp',
            ], $user->id);

            throw new BusinessLogicException('Invalid or expired reset code');
        }

        // Update password
        $user->update(['password' => Hash::make($newPassword)]);

        // Revoke all existing tokens
        $user->tokens()->delete();

        // OTP is automatically cleared by OtpHelper::verifyPasswordReset

        $this->loggingService->logAuth('password_reset_completed', [
            'user_id' => $user->id,
            'email' => $email,
        ], $user->id);

        return true;
    }

    /**
     * Change password for authenticated user.
     */
    public function changePassword(User $user, string $currentPassword, string $newPassword): bool
    {
        if (! Hash::check($currentPassword, $user->password)) {
            throw new BusinessLogicException('Current password is incorrect');
        }

        // Update password
        $user->update(['password' => Hash::make($newPassword)]);

        // Revoke all tokens except current one
        $currentTokenId = $user->currentAccessToken()?->id;
        $user->tokens()->where('id', '!=', $currentTokenId)->delete();

        $this->loggingService->logAuth('password_changed', [
            'user_id' => $user->id,
        ], $user->id);

        return true;
    }

    /**
     * Get OTP expiration time.
     */
    public function getOtpExpirationTime(string $type, string $identifier): ?int
    {
        $cacheKey = match ($type) {
            'email' => "email_otp:{$identifier}",
            'phone' => "phone_otp:{$identifier}",
            'reset' => "reset_otp:{$identifier}",
            default => null,
        };

        if (! $cacheKey || ! Cache::has($cacheKey)) {
            return null;
        }

        // Get remaining TTL in seconds
        return Cache::store()->getRedis()->ttl(Cache::store()->getPrefix().$cacheKey);
    }
}
