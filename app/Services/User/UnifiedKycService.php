<?php

declare(strict_types=1);

namespace App\Services\User;

use App\Enums\User\KycLevel;
use App\Exceptions\BusinessLogicException;
use App\Models\User\BankAccountVerification;
use App\Models\User\IdentityVerification;
use App\Models\User\User;
use App\Services\Core\LoggingService;
use App\Services\External\QoreIdService;
use App\Services\Notification\NotificationService;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\DB;

/**
 * Unified KYC Service
 *
 * Replaces the three-tier KYC system with a unified QoreID-based approach.
 * Provides simplified, cost-effective, and comprehensive verification.
 */
class UnifiedKycService
{
    public function __construct(
        private QoreIdService $qoreIdService,
        private NotificationService $notificationService,
        private LoggingService $loggingService
    ) {}

    /**
     * Initiate comprehensive KYC verification.
     */
    public function initiateKycVerification(User $user, array $data): array
    {
        try {
            return DB::transaction(function () use ($user, $data) {
                // Prepare verification data
                $verificationData = $this->prepareVerificationData($user, $data);

                // Call QoreID comprehensive verification
                $result = $this->qoreIdService->comprehensiveKyc($verificationData);

                if ($result['success']) {
                    // Store verification results
                    $this->storeVerificationResults($user, $result);

                    // Update user KYC level based on verification score
                    $this->updateKycLevel($user, $result);

                    // Send notification
                    $this->notificationService->sendKycVerificationInitiated($user, $result);

                    $this->loggingService->logInfo('Unified KYC verification initiated', [
                        'user_id' => $user->id,
                        'verification_score' => $result['verification_score'] ?? 0,
                        'risk_level' => $result['risk_level'] ?? 'unknown',
                        'qoreid_reference' => $result['qoreid_reference'] ?? null,
                    ]);

                    return [
                        'success' => true,
                        'verification_score' => $result['verification_score'] ?? 0,
                        'risk_level' => $result['risk_level'] ?? 'unknown',
                        'kyc_level' => $user->fresh()->kyc_level?->value,
                        'reference' => $result['reference'],
                        'qoreid_reference' => $result['qoreid_reference'] ?? null,
                        'message' => 'KYC verification completed successfully',
                    ];
                } else {
                    throw new BusinessLogicException(
                        $result['message'] ?? 'KYC verification failed',
                        'KYC_VERIFICATION_FAILED',
                        Response::HTTP_UNPROCESSABLE_ENTITY
                    );
                }
            });
        } catch (\Exception $e) {
            $this->loggingService->logError('Unified KYC verification failed', $e, [
                'user_id' => $user->id,
                'data_keys' => array_keys($data),
            ]);

            if ($e instanceof BusinessLogicException) {
                throw $e;
            }

            throw new BusinessLogicException(
                'KYC verification process failed',
                'KYC_VERIFICATION_ERROR',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Verify individual component (BVN, NIN, Bank Account).
     */
    public function verifyComponent(User $user, string $type, array $data): array
    {
        try {
            $result = match ($type) {
                'bvn' => $this->verifyBvn($user, $data['bvn'], $data['customer_data'] ?? []),
                'nin' => $this->verifyNin($user, $data['nin'], $data['customer_data'] ?? []),
                'bank_account' => $this->verifyBankAccount($user, $data['account_number'], $data['bank_code']),
                default => throw new BusinessLogicException(
                    "Unknown verification type: {$type}",
                    'UNKNOWN_VERIFICATION_TYPE',
                    Response::HTTP_UNPROCESSABLE_ENTITY
                ),
            };

            if ($result['success']) {
                // Store individual verification result
                $this->storeIndividualVerification($user, $type, $result);

                // Check if user qualifies for KYC level upgrade
                $this->checkKycLevelUpgrade($user);
            }

            return $result;

        } catch (\Exception $e) {
            $this->loggingService->logError('Component verification failed', $e, [
                'user_id' => $user->id,
                'type' => $type,
                'data_keys' => array_keys($data),
            ]);

            if ($e instanceof BusinessLogicException) {
                throw $e;
            }

            throw new BusinessLogicException(
                'Component verification failed',
                'COMPONENT_VERIFICATION_ERROR',
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }

    /**
     * Get user's KYC status and progress.
     */
    public function getKycStatus(User $user): array
    {
        $verifications = $this->getUserVerifications($user);
        $kycLevel = $user->kyc_level ?? KycLevel::BASIC;

        // Calculate overall verification score
        $overallScore = $this->calculateOverallScore($verifications);

        // Determine completion status
        $completionStatus = $this->getCompletionStatus($verifications, $kycLevel);

        return [
            'user_id' => $user->id,
            'current_kyc_level' => $kycLevel->value,
            'overall_score' => $overallScore,
            'risk_level' => $this->determineRiskLevel($overallScore),
            'completion_status' => $completionStatus,
            'verifications' => $verifications,
            'next_steps' => $this->getNextSteps($user, $verifications),
            'benefits' => $kycLevel->getBenefits(),
            'last_updated' => $user->updated_at->toISOString(),
        ];
    }

    /**
     * Generate comprehensive KYC report.
     */
    public function generateKycReport(User $user): array
    {
        $status = $this->getKycStatus($user);
        $verificationHistory = $this->getVerificationHistory($user);

        return [
            'user_id' => $user->id,
            'generated_at' => now()->toISOString(),
            'current_status' => $status,
            'verification_history' => $verificationHistory,
            'compliance_summary' => [
                'kyc_compliant' => $status['overall_score'] >= 70,
                'risk_level' => $status['risk_level'],
                'last_verification' => $verificationHistory[0]['created_at'] ?? null,
                'verification_count' => count($verificationHistory),
            ],
            'recommendations' => $this->generateRecommendations($user, $status),
        ];
    }

    /**
     * Verify BVN using QoreID.
     */
    private function verifyBvn(User $user, string $bvn, array $customerData = []): array
    {
        // Add user data for matching
        $customerData = array_merge([
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'phone_number' => $user->phone_number,
        ], $customerData);

        return $this->qoreIdService->verifyBvn($bvn, $customerData);
    }

    /**
     * Verify NIN using QoreID.
     */
    private function verifyNin(User $user, string $nin, array $customerData = []): array
    {
        // Add user data for matching
        $customerData = array_merge([
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'phone_number' => $user->phone_number,
        ], $customerData);

        return $this->qoreIdService->verifyNin($nin, $customerData);
    }

    /**
     * Verify bank account using QoreID.
     */
    private function verifyBankAccount(User $user, string $accountNumber, string $bankCode): array
    {
        return $this->qoreIdService->verifyBankAccount($accountNumber, $bankCode);
    }

    /**
     * Prepare verification data for QoreID.
     */
    private function prepareVerificationData(User $user, array $data): array
    {
        return [
            'user_id' => $user->id,
            'first_name' => $user->first_name,
            'last_name' => $user->last_name,
            'email' => $user->email,
            'phone_number' => $user->phone_number,
            'date_of_birth' => $user->date_of_birth?->format('Y-m-d'),
            'bvn' => $data['bvn'] ?? null,
            'nin' => $data['nin'] ?? null,
            'account_number' => $data['account_number'] ?? null,
            'bank_code' => $data['bank_code'] ?? null,
            'verification_level' => $data['target_level'] ?? 'comprehensive',
        ];
    }

    /**
     * Store comprehensive verification results.
     */
    private function storeVerificationResults(User $user, array $result): void
    {
        $verificationData = $result['data'] ?? [];

        // Store BVN verification if present
        if (isset($verificationData['bvn_verification'])) {
            IdentityVerification::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'verification_type' => 'bvn',
                ],
                [
                    'verification_status' => $verificationData['bvn_verification']['status'] === 'verified' ? 'verified' : 'failed',
                    'verification_data' => $verificationData['bvn_verification'],
                    'external_reference' => $result['qoreid_reference'] ?? null,
                    'verified_at' => $verificationData['bvn_verification']['status'] === 'verified' ? now() : null,
                ]
            );
        }

        // Store NIN verification if present
        if (isset($verificationData['nin_verification'])) {
            IdentityVerification::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'verification_type' => 'nin',
                ],
                [
                    'verification_status' => $verificationData['nin_verification']['status'] === 'verified' ? 'verified' : 'failed',
                    'verification_data' => $verificationData['nin_verification'],
                    'external_reference' => $result['qoreid_reference'] ?? null,
                    'verified_at' => $verificationData['nin_verification']['status'] === 'verified' ? now() : null,
                ]
            );
        }

        // Store bank verification if present
        if (isset($verificationData['bank_verification'])) {
            BankAccountVerification::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'account_number' => $verificationData['bank_verification']['account_number'] ?? '',
                ],
                [
                    'verification_status' => $verificationData['bank_verification']['status'] === 'verified' ? 'verified' : 'failed',
                    'account_name' => $verificationData['bank_verification']['account_name'] ?? null,
                    'verification_data' => $verificationData['bank_verification'],
                    'paystack_reference' => $result['qoreid_reference'] ?? null,
                    'verified_at' => $verificationData['bank_verification']['status'] === 'verified' ? now() : null,
                ]
            );
        }
    }

    /**
     * Store individual verification result.
     */
    private function storeIndividualVerification(User $user, string $type, array $result): void
    {
        if ($type === 'bank_account') {
            BankAccountVerification::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'account_number' => $result['data']['account_number'] ?? '',
                ],
                [
                    'verification_status' => 'verified',
                    'account_name' => $result['data']['account_name'] ?? null,
                    'verification_data' => $result['data'],
                    'paystack_reference' => $result['qoreid_reference'] ?? null,
                    'verified_at' => now(),
                ]
            );
        } else {
            IdentityVerification::updateOrCreate(
                [
                    'user_id' => $user->id,
                    'verification_type' => $type,
                ],
                [
                    'verification_status' => 'verified',
                    'verification_data' => $result['data'],
                    'external_reference' => $result['qoreid_reference'] ?? null,
                    'verified_at' => now(),
                ]
            );
        }
    }

    /**
     * Update user KYC level based on verification score.
     */
    private function updateKycLevel(User $user, array $result): void
    {
        $score = $result['verification_score'] ?? 0;
        $riskLevel = $result['risk_level'] ?? 'high';

        // Determine KYC level based on score and risk
        $newKycLevel = match (true) {
            $score >= 90 && $riskLevel === 'low' => KycLevel::ADVANCED,
            $score >= 70 && in_array($riskLevel, ['low', 'medium']) => KycLevel::INTERMEDIATE,
            $score >= 50 => KycLevel::BASIC,
            default => null,
        };

        if ($newKycLevel && (! $user->kyc_level || $newKycLevel->getAccessLevel() > $user->kyc_level->getAccessLevel())) {
            $user->update(['kyc_level' => $newKycLevel]);

            $this->loggingService->logInfo('User KYC level updated', [
                'user_id' => $user->id,
                'old_level' => $user->getOriginal('kyc_level'),
                'new_level' => $newKycLevel->value,
                'verification_score' => $score,
                'risk_level' => $riskLevel,
            ]);
        }
    }

    /**
     * Check if user qualifies for KYC level upgrade.
     */
    private function checkKycLevelUpgrade(User $user): void
    {
        $verifications = $this->getUserVerifications($user);
        $overallScore = $this->calculateOverallScore($verifications);

        // Mock result for upgrade check
        $mockResult = [
            'verification_score' => $overallScore,
            'risk_level' => $this->determineRiskLevel($overallScore),
        ];

        $this->updateKycLevel($user, $mockResult);
    }

    /**
     * Get user's verification records.
     */
    private function getUserVerifications(User $user): array
    {
        $verifications = [];

        // Get identity verifications
        $identityVerifications = IdentityVerification::where('user_id', $user->id)->get();
        foreach ($identityVerifications as $verification) {
            $verifications[$verification->verification_type] = [
                'type' => $verification->verification_type,
                'status' => $verification->verification_status,
                'verified_at' => $verification->verified_at?->toISOString(),
                'score' => $verification->verification_data['verification_score'] ?? 0,
            ];
        }

        // Get bank verifications
        $bankVerifications = BankAccountVerification::where('user_id', $user->id)
            ->where('verification_status', 'verified')
            ->get();

        if ($bankVerifications->isNotEmpty()) {
            $verifications['bank_account'] = [
                'type' => 'bank_account',
                'status' => 'verified',
                'verified_at' => $bankVerifications->first()->verified_at?->toISOString(),
                'score' => 100, // Bank verification is binary
                'count' => $bankVerifications->count(),
            ];
        }

        return $verifications;
    }

    /**
     * Calculate overall verification score.
     */
    private function calculateOverallScore(array $verifications): int
    {
        if (empty($verifications)) {
            return 0;
        }

        $totalScore = 0;
        $count = 0;

        foreach ($verifications as $verification) {
            if ($verification['status'] === 'verified') {
                $totalScore += $verification['score'];
                $count++;
            }
        }

        return $count > 0 ? (int) ($totalScore / $count) : 0;
    }

    /**
     * Determine risk level based on score.
     */
    private function determineRiskLevel(int $score): string
    {
        return match (true) {
            $score >= 85 => 'low',
            $score >= 65 => 'medium',
            default => 'high',
        };
    }

    /**
     * Get completion status.
     */
    private function getCompletionStatus(array $verifications, KycLevel $kycLevel): array
    {
        $requiredVerifications = match ($kycLevel) {
            KycLevel::BASIC => ['email', 'phone'],
            KycLevel::INTERMEDIATE => ['email', 'phone', 'bank_account'],
            KycLevel::ADVANCED => ['email', 'phone', 'bank_account', 'bvn', 'nin'],
        };

        $completed = array_intersect_key($verifications, array_flip($requiredVerifications));
        $completionPercentage = count($completed) / count($requiredVerifications) * 100;

        return [
            'required_count' => count($requiredVerifications),
            'completed_count' => count($completed),
            'completion_percentage' => round($completionPercentage, 2),
            'is_complete' => $completionPercentage === 100.0,
        ];
    }

    /**
     * Get next steps for user.
     */
    private function getNextSteps(User $user, array $verifications): array
    {
        $nextSteps = [];

        if (! isset($verifications['bvn'])) {
            $nextSteps[] = [
                'step' => 'bvn_verification',
                'title' => 'Verify BVN',
                'description' => 'Verify your Bank Verification Number for enhanced security',
                'priority' => 'high',
            ];
        }

        if (! isset($verifications['nin'])) {
            $nextSteps[] = [
                'step' => 'nin_verification',
                'title' => 'Verify NIN',
                'description' => 'Verify your National Identification Number',
                'priority' => 'high',
            ];
        }

        if (! isset($verifications['bank_account'])) {
            $nextSteps[] = [
                'step' => 'bank_verification',
                'title' => 'Verify Bank Account',
                'description' => 'Verify your bank account for secure transactions',
                'priority' => 'medium',
            ];
        }

        return array_slice($nextSteps, 0, 3); // Return top 3 next steps
    }

    /**
     * Get verification history.
     */
    private function getVerificationHistory(User $user): array
    {
        $history = [];

        // Get identity verification history
        $identityVerifications = IdentityVerification::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();

        foreach ($identityVerifications as $verification) {
            $history[] = [
                'type' => $verification->verification_type,
                'status' => $verification->verification_status,
                'created_at' => $verification->created_at->toISOString(),
                'verified_at' => $verification->verified_at?->toISOString(),
            ];
        }

        // Get bank verification history
        $bankVerifications = BankAccountVerification::where('user_id', $user->id)
            ->orderBy('created_at', 'desc')
            ->get();

        foreach ($bankVerifications as $verification) {
            $history[] = [
                'type' => 'bank_account',
                'status' => $verification->verification_status,
                'created_at' => $verification->created_at->toISOString(),
                'verified_at' => $verification->verified_at?->toISOString(),
            ];
        }

        // Sort by creation date
        usort($history, fn ($a, $b) => strcmp($b['created_at'], $a['created_at']));

        return $history;
    }

    /**
     * Generate recommendations.
     */
    private function generateRecommendations(User $user, array $status): array
    {
        $recommendations = [];

        if ($status['overall_score'] < 70) {
            $recommendations[] = 'Complete additional verification steps to improve your verification score';
        }

        if ($status['risk_level'] === 'high') {
            $recommendations[] = 'Verify additional identity documents to reduce risk level';
        }

        if ($status['current_kyc_level'] !== 'advanced') {
            $recommendations[] = 'Upgrade to Advanced KYC for access to premium features';
        }

        return $recommendations;
    }
}
