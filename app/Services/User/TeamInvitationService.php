<?php

declare(strict_types=1);

namespace App\Services\User;

use App\Enums\Business\TeamMemberRelationshipType;
use App\Enums\User\UserType;
use App\Events\User\TeamMemberInvited;
use App\Exceptions\BusinessLogicException;
use App\Models\Business\Business;
use App\Models\Business\BusinessTeamMember;
use App\Models\Delivery\DeliveryProvider;
use App\Models\Delivery\ProviderTeamMember;
use App\Models\User\TeamInvitation;
use App\Models\User\User;
use App\Services\Communication\NotificationService;
use App\Services\System\LoggingService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Symfony\Component\HttpFoundation\Response;

/**
 * Team Invitation Service
 *
 * Handles team member invitations for businesses and delivery providers
 * using token-based invitation system with proper event-driven architecture.
 */
class TeamInvitationService
{
    public function __construct(
        private readonly LoggingService $loggingService,
        private readonly NotificationService $notificationService
    ) {}

    /**
     * Send invitation to join business team.
     */
    public function inviteBusinessTeamMember(
        Business $business,
        array $invitationData,
        User $invitedBy
    ): TeamInvitation {
        return DB::transaction(function () use ($business, $invitationData, $invitedBy) {
            // Validate invitation data
            $this->validateInvitationData($invitationData);

            // Check if user already exists
            $existingUser = User::where('email', $invitationData['email'])->first();
            if ($existingUser) {
                // Check if already a team member
                $existingMember = BusinessTeamMember::where('business_id', $business->id)
                    ->where('user_id', $existingUser->id)
                    ->exists();

                if ($existingMember) {
                    throw new BusinessLogicException(
                        'User is already a team member',
                        'USER_ALREADY_TEAM_MEMBER',
                        Response::HTTP_CONFLICT
                    );
                }
            }

            // Check for existing pending invitation
            $existingInvitation = TeamInvitation::where('tenant_id', tenant()?->id)
                ->where('email', $invitationData['email'])
                ->where('status', TeamInvitation::STATUS_PENDING)
                ->first();

            if ($existingInvitation) {
                throw new BusinessLogicException(
                    'Invitation already sent to this email',
                    'INVITATION_ALREADY_SENT',
                    Response::HTTP_CONFLICT
                );
            }

            // Create invitation
            $invitation = TeamInvitation::createInvitation(
                tenant()?->id,
                $invitationData['email'],
                $invitationData['role'],
                $invitedBy,
                72, // 3 days expiration
                [
                    'business_id' => $business->id,
                    'business_name' => $business->business_name,
                    'first_name' => $invitationData['first_name'],
                    'last_name' => $invitationData['last_name'],
                    'phone_number' => $invitationData['phone_number'],
                    'invitation_type' => 'business_team',
                ]
            );

            // Dispatch event
            TeamMemberInvited::dispatch(
                $invitation,
                $business,
                $invitedBy,
                'business'
            );

            // Send notification
            $this->notificationService->sendTeamInvitation(
                $invitation,
                $business,
                $invitedBy
            );

            // Log invitation
            $this->loggingService->logInfo(
                'Business team member invited',
                [
                    'tenant_id' => tenant()?->id,
                    'business_id' => $business->id,
                    'invitation_id' => $invitation->id,
                    'invited_email' => $invitation->email,
                    'invited_by' => $invitedBy->id,
                    'role' => $invitation->role,
                ]
            );

            return $invitation;
        });
    }

    /**
     * Send invitation to join provider team.
     */
    public function inviteProviderTeamMember(
        DeliveryProvider $provider,
        array $invitationData,
        User $invitedBy
    ): TeamInvitation {
        return DB::transaction(function () use ($provider, $invitationData, $invitedBy) {
            // Validate invitation data
            $this->validateInvitationData($invitationData);

            // Check if user already exists
            $existingUser = User::where('email', $invitationData['email'])->first();
            if ($existingUser) {
                // Check if already a team member
                $existingMember = ProviderTeamMember::where('provider_id', $provider->id)
                    ->where('user_id', $existingUser->id)
                    ->exists();

                if ($existingMember) {
                    throw new BusinessLogicException(
                        'User is already a team member',
                        'USER_ALREADY_TEAM_MEMBER',
                        Response::HTTP_CONFLICT
                    );
                }
            }

            // Check for existing pending invitation
            $existingInvitation = TeamInvitation::where('tenant_id', tenant()?->id)
                ->where('email', $invitationData['email'])
                ->where('status', TeamInvitation::STATUS_PENDING)
                ->first();

            if ($existingInvitation) {
                throw new BusinessLogicException(
                    'Invitation already sent to this email',
                    'INVITATION_ALREADY_SENT',
                    Response::HTTP_CONFLICT
                );
            }

            // Create invitation
            $invitation = TeamInvitation::createInvitation(
                tenant()?->id,
                $invitationData['email'],
                $invitationData['role'],
                $invitedBy,
                72, // 3 days expiration
                [
                    'provider_id' => $provider->id,
                    'provider_name' => $provider->company_name,
                    'first_name' => $invitationData['first_name'],
                    'last_name' => $invitationData['last_name'],
                    'phone_number' => $invitationData['phone_number'],
                    'invitation_type' => 'provider_team',
                ]
            );

            // Dispatch event
            TeamMemberInvited::dispatch(
                $invitation,
                $provider,
                $invitedBy,
                'provider'
            );

            // Send notification
            $this->notificationService->sendTeamInvitation(
                $invitation,
                $provider,
                $invitedBy
            );

            // Log invitation
            $this->loggingService->logInfo(
                'Provider team member invited',
                [
                    'tenant_id' => tenant()?->id,
                    'provider_id' => $provider->id,
                    'invitation_id' => $invitation->id,
                    'invited_email' => $invitation->email,
                    'invited_by' => $invitedBy->id,
                    'role' => $invitation->role,
                ]
            );

            return $invitation;
        });
    }

    /**
     * Accept team invitation and create user account.
     */
    public function acceptInvitation(string $token, string $password): array
    {
        return DB::transaction(function () use ($token, $password) {
            // Find and validate invitation
            $invitation = TeamInvitation::findByToken($token);

            if (! $invitation) {
                throw new BusinessLogicException(
                    'Invalid or expired invitation token',
                    'INVALID_INVITATION_TOKEN',
                    Response::HTTP_NOT_FOUND
                );
            }

            // Check if user already exists
            $existingUser = User::where('email', $invitation->email)->first();
            if ($existingUser) {
                throw new BusinessLogicException(
                    'User account already exists for this email',
                    'USER_ALREADY_EXISTS',
                    Response::HTTP_CONFLICT
                );
            }

            // Create user account
            $user = User::create([
                'first_name' => $invitation->metadata['first_name'],
                'last_name' => $invitation->metadata['last_name'],
                'email' => $invitation->email,
                'phone_number' => $invitation->metadata['phone_number'],
                'password' => Hash::make($password),
                'user_type' => $this->getUserTypeFromInvitation($invitation),
                'tenant_id' => $invitation->tenant_id,
                'is_active' => true,
                'email_verified_at' => now(), // Auto-verify invited users
            ]);

            // Create team member relationship
            $this->createTeamMemberRelationship($invitation, $user);

            // Assign role using Bouncer
            $this->assignUserRole($user, $invitation);

            // Mark invitation as accepted
            $invitation->markAsAccepted($user);

            // Log acceptance
            $this->loggingService->logInfo(
                'Team invitation accepted',
                [
                    'tenant_id' => $invitation->tenant_id,
                    'invitation_id' => $invitation->id,
                    'user_id' => $user->id,
                    'email' => $invitation->email,
                    'role' => $invitation->role,
                ]
            );

            return [
                'user' => $user,
                'invitation' => $invitation,
                'team_member' => $this->getTeamMemberRecord($invitation, $user),
            ];
        });
    }

    /**
     * Cancel pending invitation.
     */
    public function cancelInvitation(string $invitationId, User $cancelledBy): bool
    {
        return DB::transaction(function () use ($invitationId, $cancelledBy) {
            $invitation = TeamInvitation::where('id', $invitationId)
                ->where('tenant_id', tenant()?->id)
                ->where('status', TeamInvitation::STATUS_PENDING)
                ->first();

            if (! $invitation) {
                throw new BusinessLogicException(
                    'Invitation not found or already processed',
                    'INVITATION_NOT_FOUND',
                    Response::HTTP_NOT_FOUND
                );
            }

            $invitation->markAsCancelled();

            $this->loggingService->logInfo(
                'Team invitation cancelled',
                [
                    'tenant_id' => $invitation->tenant_id,
                    'invitation_id' => $invitation->id,
                    'cancelled_by' => $cancelledBy->id,
                    'email' => $invitation->email,
                ]
            );

            return true;
        });
    }

    /**
     * Get pending invitations for current tenant.
     */
    public function getPendingInvitations(): array
    {
        return TeamInvitation::where('tenant_id', tenant()?->id)
            ->pending()
            ->with(['invitedBy'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->toArray();
    }

    /**
     * Resend invitation.
     */
    public function resendInvitation(string $invitationId, User $resentBy): TeamInvitation
    {
        return DB::transaction(function () use ($invitationId, $resentBy) {
            $invitation = TeamInvitation::where('id', $invitationId)
                ->where('tenant_id', tenant()?->id)
                ->where('status', TeamInvitation::STATUS_PENDING)
                ->first();

            if (! $invitation) {
                throw new BusinessLogicException(
                    'Invitation not found or already processed',
                    'INVITATION_NOT_FOUND',
                    Response::HTTP_NOT_FOUND
                );
            }

            // Extend expiration
            $invitation->update([
                'expires_at' => now()->addHours(72),
                'token' => TeamInvitation::generateToken(), // Generate new token
            ]);

            // Resend notification
            if ($invitation->metadata['invitation_type'] === 'business_team') {
                $business = Business::find($invitation->metadata['business_id']);
                if ($business) {
                    $this->notificationService->sendTeamInvitation($invitation, $business, $resentBy);
                }
            } else {
                $provider = DeliveryProvider::find($invitation->metadata['provider_id']);
                if ($provider) {
                    $this->notificationService->sendTeamInvitation($invitation, $provider, $resentBy);
                }
            }

            $this->loggingService->logInfo(
                'Team invitation resent',
                [
                    'tenant_id' => $invitation->tenant_id,
                    'invitation_id' => $invitation->id,
                    'resent_by' => $resentBy->id,
                    'email' => $invitation->email,
                ]
            );

            return $invitation;
        });
    }

    /**
     * Validate invitation data.
     */
    private function validateInvitationData(array $data): void
    {
        $required = ['first_name', 'last_name', 'email', 'phone_number', 'role'];

        foreach ($required as $field) {
            if (empty($data[$field])) {
                throw new BusinessLogicException(
                    "Field {$field} is required",
                    'MISSING_REQUIRED_FIELD',
                    Response::HTTP_UNPROCESSABLE_ENTITY
                );
            }
        }

        if (! filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            throw new BusinessLogicException(
                'Invalid email format',
                'INVALID_EMAIL_FORMAT',
                Response::HTTP_UNPROCESSABLE_ENTITY
            );
        }
    }

    /**
     * Get user type from invitation.
     */
    private function getUserTypeFromInvitation(TeamInvitation $invitation): UserType
    {
        return match ($invitation->metadata['invitation_type']) {
            'business_team' => UserType::BUSINESS_OWNER,
            'provider_team' => UserType::DELIVERY_DRIVER, // Use DELIVERY_DRIVER instead of non-existent DELIVERY_PROVIDER
            default => UserType::CUSTOMER,
        };
    }

    /**
     * Create team member relationship.
     */
    private function createTeamMemberRelationship(TeamInvitation $invitation, User $user): void
    {
        if ($invitation->metadata['invitation_type'] === 'business_team') {
            BusinessTeamMember::create([
                'tenant_id' => $invitation->tenant_id,
                'business_id' => $invitation->metadata['business_id'],
                'user_id' => $user->id,
                'role' => TeamMemberRelationshipType::from($invitation->role),
            ]);
        } else {
            ProviderTeamMember::create([
                'tenant_id' => $invitation->tenant_id,
                'provider_id' => $invitation->metadata['provider_id'],
                'user_id' => $user->id,
                'role' => TeamMemberRelationshipType::from($invitation->role),
            ]);
        }
    }

    /**
     * Assign user role using Bouncer.
     */
    private function assignUserRole(User $user, TeamInvitation $invitation): void
    {
        if ($invitation->metadata['invitation_type'] === 'business_team') {
            $bouncerRole = match ($invitation->role) {
                'admin' => 'business-admin',
                'manager' => 'business-manager',
                'staff' => 'business-staff',
                default => 'business-staff'
            };
        } else {
            $bouncerRole = match ($invitation->role) {
                'admin' => 'delivery-provider-admin',
                'manager' => 'delivery-manager',
                'driver' => 'delivery-driver',
                'support' => 'delivery-support',
                default => 'delivery-driver'
            };
        }

        $user->assign($bouncerRole);
    }

    /**
     * Get team member record.
     */
    private function getTeamMemberRecord(TeamInvitation $invitation, User $user)
    {
        if ($invitation->metadata['invitation_type'] === 'business_team') {
            return BusinessTeamMember::where('business_id', $invitation->metadata['business_id'])
                ->where('user_id', $user->id)
                ->first();
        } else {
            return ProviderTeamMember::where('provider_id', $invitation->metadata['provider_id'])
                ->where('user_id', $user->id)
                ->first();
        }
    }
}
