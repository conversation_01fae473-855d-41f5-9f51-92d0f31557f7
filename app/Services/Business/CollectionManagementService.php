<?php

declare(strict_types=1);

namespace App\Services\Business;

use App\Enums\Business\ProductCollectionType;
use App\Models\Business\ProductCategory;
use App\Models\Business\ProductCollection;
use App\Services\System\LoggingService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class CollectionManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all collections with detailed information.
     */
    public function getAllCollectionsWithDetails(): Collection
    {
        return ProductCollection::with([
            'business:id,name,slug',
            'businessBranch:id,name',
            'categories:id,product_collection_id,name',
            'products:id,name,price',
        ])
            ->withCount(['categories', 'products'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get collection statistics.
     */
    public function getCollectionStatistics(): array
    {
        return [
            'total_collections' => ProductCollection::count(),
            'active_collections' => ProductCollection::active()->count(),
            'collections_by_type' => ProductCollection::selectRaw('type, COUNT(*) as count')
                ->groupBy('type')
                ->pluck('count', 'type'),
            'collections_by_business' => ProductCollection::with('business:id,name')
                ->selectRaw('business_id, COUNT(*) as count')
                ->groupBy('business_id')
                ->orderByDesc('count')
                ->limit(10)
                ->get()
                ->map(function ($item) {
                    return [
                        'business' => $item->business->name ?? 'Unknown',
                        'count' => $item->count,
                    ];
                }),
            'scheduled_collections' => ProductCollection::whereNotNull('active_start_time')
                ->orWhereNotNull('active_end_time')
                ->count(),
            'collections_with_products' => ProductCollection::has('products')->count(),
            'average_products_per_collection' => $this->getAverageProductsPerCollection(),
        ];
    }

    /**
     * Create collection template.
     */
    public function createCollectionTemplate(array $data): ProductCollection
    {
        return DB::transaction(function () use ($data) {
            $collection = ProductCollection::create([
                'tenant_id' => $data['tenant_id'],
                'business_id' => $data['business_id'],
                'business_branch_id' => $data['business_branch_id'] ?? null,
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'slug' => $data['slug'],
                'type' => $data['type'] ?? ProductCollectionType::COLLECTION,
                'is_active' => $data['is_active'] ?? true,
                'active_start_time' => $data['active_start_time'] ?? null,
                'active_end_time' => $data['active_end_time'] ?? null,
                'display_order' => $data['display_order'] ?? 0,
                'image_url' => $data['image_url'] ?? null,
            ]);

            $this->loggingService->logInfo('Collection template created', [
                'collection_id' => $collection->id,
                'collection_name' => $collection->name,
                'business_id' => $collection->business_id,
                'admin_id' => auth()->id(),
            ]);

            return $collection;
        });
    }

    /**
     * Bulk update collection status.
     */
    public function bulkUpdateCollectionStatus(array $collectionIds, bool $isActive): array
    {
        $results = ['updated' => 0, 'failed' => 0, 'details' => []];

        DB::transaction(function () use ($collectionIds, $isActive, &$results) {
            foreach ($collectionIds as $collectionId) {
                try {
                    $collection = ProductCollection::findOrFail($collectionId);
                    $collection->update(['is_active' => $isActive]);

                    $results['updated']++;
                    $results['details'][$collectionId] = 'updated';
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['details'][$collectionId] = 'failed: '.$e->getMessage();
                }
            }

            $this->loggingService->logInfo('Bulk collection status update', [
                'collection_count' => count($collectionIds),
                'is_active' => $isActive,
                'updated_count' => $results['updated'],
                'failed_count' => $results['failed'],
                'admin_id' => auth()->id(),
            ]);
        });

        return $results;
    }

    /**
     * Get collection performance analytics.
     */
    public function getCollectionPerformance(string $collectionId): array
    {
        $collection = ProductCollection::with(['products', 'business'])->findOrFail($collectionId);

        // This would integrate with order analytics
        // For now, return mock performance data
        return [
            'collection' => $collection->only(['id', 'name', 'type']),
            'metrics' => [
                'total_products' => $collection->products->count(),
                'active_products' => $collection->products->where('is_available', true)->count(),
                'total_orders' => 245, // Mock data
                'total_revenue' => 125000, // Mock data
                'average_order_value' => 510, // Mock data
                'conversion_rate' => 3.2, // Mock data
            ],
            'top_products' => $collection->products->take(5)->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'price' => $product->price,
                    'orders_count' => rand(10, 50), // Mock data
                    'revenue' => rand(5000, 25000), // Mock data
                ];
            }),
            'performance_trend' => $this->getCollectionPerformanceTrend($collectionId),
        ];
    }

    /**
     * Search collections across tenants.
     */
    public function searchCollections(string $query, ?string $type = null): Collection
    {
        $queryBuilder = ProductCollection::with([
            'business:id,name',
            'businessBranch:id,name',
        ])
            ->where(function ($q) use ($query) {
                $q->where('name', 'ILIKE', "%{$query}%")
                    ->orWhere('description', 'ILIKE', "%{$query}%")
                    ->orWhere('slug', 'ILIKE', "%{$query}%")
                    ->orWhereHas('business', function ($businessQuery) use ($query) {
                        $businessQuery->where('name', 'ILIKE', "%{$query}%");
                    });
            });

        if ($type) {
            $queryBuilder->where('type', $type);
        }

        return $queryBuilder->limit(50)->get();
    }

    /**
     * Get collections by business.
     */
    public function getCollectionsByBusiness(string $businessId): Collection
    {
        return ProductCollection::where('business_id', $businessId)
            ->with(['categories', 'products'])
            ->withCount(['categories', 'products'])
            ->orderBy('display_order')
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get collections by type.
     */
    public function getCollectionsByType(ProductCollectionType $type): Collection
    {
        return ProductCollection::ofType($type)
            ->with(['business:id,name', 'businessBranch:id,name'])
            ->withCount(['categories', 'products'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Clone collection to another business.
     */
    public function cloneCollection(string $collectionId, string $targetBusinessId, string $targetTenantId): ProductCollection
    {
        return DB::transaction(function () use ($collectionId, $targetBusinessId, $targetTenantId) {
            $originalCollection = ProductCollection::with(['categories', 'products'])->findOrFail($collectionId);

            // Create new collection
            $newCollection = ProductCollection::create([
                'tenant_id' => $targetTenantId,
                'business_id' => $targetBusinessId,
                'name' => $originalCollection->name.' (Copy)',
                'description' => $originalCollection->description,
                'slug' => $originalCollection->slug.'-copy-'.time(),
                'type' => $originalCollection->type,
                'is_active' => false, // Start as inactive
                'display_order' => $originalCollection->display_order,
                'image_url' => $originalCollection->image_url,
            ]);

            // Clone categories
            foreach ($originalCollection->categories as $category) {
                ProductCategory::create([
                    'tenant_id' => $targetTenantId,
                    'business_id' => $targetBusinessId,
                    'product_collection_id' => $newCollection->id,
                    'parent_id' => null, // Simplified for now
                    'name' => $category->name,
                    'slug' => $category->slug.'-copy-'.time(),
                    'description' => $category->description,
                    'is_active' => $category->is_active,
                    'display_order' => $category->display_order,
                ]);
            }

            $this->loggingService->logInfo('Collection cloned', [
                'original_collection_id' => $collectionId,
                'new_collection_id' => $newCollection->id,
                'target_business_id' => $targetBusinessId,
                'admin_id' => auth()->id(),
            ]);

            return $newCollection->fresh(['categories']);
        });
    }

    /**
     * Get featured collections.
     */
    public function getFeaturedCollections(): Collection
    {
        return ProductCollection::active()
            ->where('type', ProductCollectionType::SPECIAL)
            ->with(['business:id,name', 'products:id,name,price,main_image_url'])
            ->withCount('products')
            ->orderBy('display_order')
            ->limit(10)
            ->get();
    }

    /**
     * Get seasonal collections.
     */
    public function getSeasonalCollections(): Collection
    {
        return ProductCollection::ofType(ProductCollectionType::SEASONAL)
            ->active()
            ->with(['business:id,name'])
            ->withCount(['products'])
            ->orderBy('active_start_time', 'desc')
            ->get();
    }

    /**
     * Update collection scheduling.
     */
    public function updateCollectionScheduling(string $collectionId, array $scheduleData): ProductCollection
    {
        return DB::transaction(function () use ($collectionId, $scheduleData) {
            $collection = ProductCollection::findOrFail($collectionId);

            $collection->update([
                'active_start_time' => $scheduleData['active_start_time'] ?? null,
                'active_end_time' => $scheduleData['active_end_time'] ?? null,
                'is_active' => $scheduleData['is_active'] ?? $collection->is_active,
            ]);

            $this->loggingService->logInfo('Collection scheduling updated', [
                'collection_id' => $collection->id,
                'schedule_data' => $scheduleData,
                'admin_id' => auth()->id(),
            ]);

            return $collection->fresh();
        });
    }

    /**
     * Get collection templates.
     */
    public function getCollectionTemplates(): array
    {
        return [
            'food_service' => [
                'name' => 'Food Service Collection',
                'categories' => ['Appetizers', 'Main Courses', 'Desserts', 'Beverages'],
                'type' => ProductCollectionType::COLLECTION,
            ],
            'retail_store' => [
                'name' => 'Retail Store Collection',
                'categories' => ['Electronics', 'Clothing', 'Home & Garden', 'Sports'],
                'type' => ProductCollectionType::COLLECTION,
            ],
            'seasonal_special' => [
                'name' => 'Seasonal Special',
                'categories' => ['Limited Time', 'Holiday Specials', 'Seasonal Items'],
                'type' => ProductCollectionType::SEASONAL,
            ],
            'promotional' => [
                'name' => 'Promotional Collection',
                'categories' => ['Flash Sale', 'Bundle Deals', 'Clearance'],
                'type' => ProductCollectionType::SPECIAL,
            ],
        ];
    }

    /**
     * Export collections data.
     */
    public function exportCollections(array $filters = [], string $format = 'csv'): string
    {
        $query = ProductCollection::with(['business:id,name', 'businessBranch:id,name'])
            ->withCount(['categories', 'products']);

        // Apply filters
        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        $collections = $query->orderBy('created_at')->get();

        // Generate filename
        $filename = 'collections_export_'.now()->format('Y_m_d_H_i_s').'.'.$format;
        $filepath = storage_path('app/exports/'.$filename);

        // Ensure directory exists
        if (! file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        if ($format === 'csv') {
            $this->exportToCsv($collections, $filepath);
        } elseif ($format === 'json') {
            $this->exportToJson($collections, $filepath);
        }

        return $filename;
    }

    /**
     * Get average products per collection.
     */
    private function getAverageProductsPerCollection(): float
    {
        $totalProducts = DB::table('product_collection_products')->count();
        $totalCollections = ProductCollection::count();

        return $totalCollections > 0 ? round($totalProducts / $totalCollections, 2) : 0;
    }

    /**
     * Get collection performance trend.
     */
    private function getCollectionPerformanceTrend(string $collectionId): array
    {
        // This would integrate with order analytics
        // For now, return mock trend data
        return [
            'daily_orders' => [
                '2024-01-01' => 12,
                '2024-01-02' => 15,
                '2024-01-03' => 18,
                '2024-01-04' => 22,
                '2024-01-05' => 19,
                '2024-01-06' => 25,
                '2024-01-07' => 28,
            ],
            'daily_revenue' => [
                '2024-01-01' => 6500,
                '2024-01-02' => 8200,
                '2024-01-03' => 9800,
                '2024-01-04' => 12100,
                '2024-01-05' => 10500,
                '2024-01-06' => 13800,
                '2024-01-07' => 15600,
            ],
        ];
    }

    /**
     * Export collections to CSV.
     */
    private function exportToCsv(Collection $collections, string $filepath): void
    {
        $file = fopen($filepath, 'w');

        // Write header
        fputcsv($file, [
            'ID', 'Name', 'Business', 'Branch', 'Type', 'Is Active',
            'Categories Count', 'Products Count', 'Display Order', 'Created At',
        ]);

        // Write data
        foreach ($collections as $collection) {
            fputcsv($file, [
                $collection->id,
                $collection->name,
                $collection->business?->name,
                $collection->businessBranch?->name,
                $collection->type->value,
                $collection->is_active ? 'Yes' : 'No',
                $collection->categories_count,
                $collection->products_count,
                $collection->display_order,
                $collection->created_at->toISOString(),
            ]);
        }

        fclose($file);
    }

    /**
     * Export collections to JSON.
     */
    private function exportToJson(Collection $collections, string $filepath): void
    {
        $data = $collections->map(function ($collection) {
            return [
                'id' => $collection->id,
                'name' => $collection->name,
                'business' => $collection->business?->name,
                'branch' => $collection->businessBranch?->name,
                'type' => $collection->type->value,
                'is_active' => $collection->is_active,
                'categories_count' => $collection->categories_count,
                'products_count' => $collection->products_count,
                'display_order' => $collection->display_order,
                'created_at' => $collection->created_at->toISOString(),
            ];
        });

        file_put_contents($filepath, json_encode($data, JSON_PRETTY_PRINT));
    }
}
