<?php

declare(strict_types=1);

namespace App\Services\Business;

use App\Enums\Business\PromotionType;
use App\Models\Business\Promotion;
use App\Services\System\LoggingService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

/**
 * Promotion Management Service
 *
 * Handles comprehensive promotion management including:
 * - Promotion CRUD operations
 * - Discount code generation and validation
 * - Usage tracking and analytics
 * - Fraud prevention
 * - Cross-business promotion coordination
 */
class PromotionManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get promotions with advanced filtering and analytics.
     */
    public function getPromotions(array $filters = []): Builder
    {
        $query = Promotion::with(['business:id,name', 'country:id,name'])
            ->withCount(['usages']);

        // Apply filters
        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        if (isset($filters['country_id'])) {
            $query->where('country_id', $filters['country_id']);
        }

        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['is_active'])) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['status'])) {
            $now = now();
            switch ($filters['status']) {
                case 'active':
                    $query->where('is_active', true)
                        ->where(function ($q) use ($now) {
                            $q->whereNull('starts_at')->orWhere('starts_at', '<=', $now);
                        })
                        ->where(function ($q) use ($now) {
                            $q->whereNull('expires_at')->orWhere('expires_at', '>=', $now);
                        });
                    break;
                case 'expired':
                    $query->where('expires_at', '<', $now);
                    break;
                case 'scheduled':
                    $query->where('starts_at', '>', $now);
                    break;
                case 'inactive':
                    $query->where('is_active', false);
                    break;
            }
        }

        if (isset($filters['search'])) {
            $query->search($filters['search']);
        }

        return $query;
    }

    /**
     * Create a new promotion.
     */
    public function createPromotion(array $data): Promotion
    {
        return DB::transaction(function () use ($data) {
            // Generate unique code if not provided
            if (empty($data['code']) && in_array($data['type'], ['percentage_discount', 'fixed_discount'])) {
                $data['code'] = $this->generateUniquePromotionCode();
            }

            // Validate promotion data
            $this->validatePromotionData($data);

            $promotion = Promotion::create([
                'tenant_id' => $data['tenant_id'] ?? null,
                'business_id' => $data['business_id'] ?? null,
                'country_id' => $data['country_id'] ?? null,
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'code' => $data['code'] ?? null,
                'type' => $data['type'],
                'discount_value' => $data['discount_value'] ?? null,
                'min_order_value' => $data['min_order_value'] ?? null,
                'max_discount_amount' => $data['max_discount_amount'] ?? null,
                'currency' => $data['currency'] ?? 'NGN',
                'usage_limit_per_user' => $data['usage_limit_per_user'] ?? null,
                'usage_limit_total' => $data['usage_limit_total'] ?? null,
                'starts_at' => $data['starts_at'] ?? now(),
                'expires_at' => $data['expires_at'] ?? null,
                'is_active' => $data['is_active'] ?? true,
            ]);

            $this->loggingService->logInfo('Promotion created', [
                'promotion_id' => $promotion->id,
                'promotion_name' => $promotion->name,
                'promotion_code' => $promotion->code,
                'type' => $promotion->type->value,
                'created_by' => auth()->id(),
            ]);

            return $promotion->fresh(['business', 'country']);
        });
    }

    /**
     * Update an existing promotion.
     */
    public function updatePromotion(string $promotionId, array $data): Promotion
    {
        return DB::transaction(function () use ($promotionId, $data) {
            $promotion = Promotion::findOrFail($promotionId);

            // Prevent editing active promotions with usage
            if ($promotion->usages_count > 0 && isset($data['discount_value'])) {
                throw new \InvalidArgumentException('Cannot modify discount value for promotions with existing usage');
            }

            // Validate updated data
            $this->validatePromotionData($data, $promotion);

            $oldData = $promotion->toArray();
            $promotion->update(array_filter($data, fn ($value) => $value !== null));

            $this->loggingService->logInfo('Promotion updated', [
                'promotion_id' => $promotion->id,
                'promotion_name' => $promotion->name,
                'changes' => array_diff_assoc($promotion->toArray(), $oldData),
                'updated_by' => auth()->id(),
            ]);

            return $promotion->fresh(['business', 'country']);
        });
    }

    /**
     * Delete a promotion.
     */
    public function deletePromotion(string $promotionId, ?string $reason = null): bool
    {
        return DB::transaction(function () use ($promotionId, $reason) {
            $promotion = Promotion::findOrFail($promotionId);

            // Check if promotion has been used
            if ($promotion->usages_count > 0) {
                throw new \InvalidArgumentException('Cannot delete promotion with existing usage. Deactivate instead.');
            }

            $promotionData = $promotion->toArray();
            $promotion->delete();

            $this->loggingService->logInfo('Promotion deleted', [
                'promotion_id' => $promotionId,
                'promotion_data' => $promotionData,
                'reason' => $reason,
                'deleted_by' => auth()->id(),
            ]);

            return true;
        });
    }

    /**
     * Activate/deactivate a promotion.
     */
    public function togglePromotionStatus(string $promotionId, bool $isActive, ?string $reason = null): Promotion
    {
        return DB::transaction(function () use ($promotionId, $isActive, $reason) {
            $promotion = Promotion::findOrFail($promotionId);

            $oldStatus = $promotion->is_active;
            $promotion->update(['is_active' => $isActive]);

            $this->loggingService->logInfo('Promotion status updated', [
                'promotion_id' => $promotion->id,
                'promotion_name' => $promotion->name,
                'old_status' => $oldStatus,
                'new_status' => $isActive,
                'reason' => $reason,
                'updated_by' => auth()->id(),
            ]);

            return $promotion->fresh();
        });
    }

    /**
     * Generate unique promotion code.
     */
    private function generateUniquePromotionCode(int $length = 8): string
    {
        do {
            $code = strtoupper(Str::random($length));
        } while (Promotion::where('code', $code)->exists());

        return $code;
    }

    /**
     * Get promotion analytics and insights.
     */
    public function getPromotionAnalytics(array $filters = []): array
    {
        $query = Promotion::with(['usages']);

        // Apply filters
        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $promotions = $query->get();

        return [
            'summary' => [
                'total_promotions' => $promotions->count(),
                'active_promotions' => $promotions->where('is_active', true)->count(),
                'expired_promotions' => $promotions->filter(fn ($p) => $p->expires_at && $p->expires_at->isPast())->count(),
                'total_usage_count' => $promotions->sum('usages_count'),
                'average_usage_per_promotion' => $promotions->avg('usages_count'),
            ],
            'by_type' => $this->getPromotionsByType($promotions),
            'top_performing' => $this->getTopPerformingPromotions($promotions),
            'usage_trends' => $this->getUsageTrends($filters),
            'fraud_indicators' => $this->getFraudIndicators($filters),
        ];
    }

    /**
     * Get promotion usage tracking.
     */
    public function getPromotionUsage(string $promotionId): array
    {
        $promotion = Promotion::with(['usages.user:id,first_name,last_name,email', 'usages.order:id,total_amount'])
            ->findOrFail($promotionId);

        $usages = $promotion->usages()->orderBy('used_at', 'desc')->get();

        return [
            'promotion' => $promotion->getSummary(),
            'usage_summary' => [
                'total_uses' => $usages->count(),
                'unique_users' => $usages->unique('user_id')->count(),
                'total_discount_given' => $usages->sum('discount_amount'),
                'average_discount' => $usages->avg('discount_amount'),
                'usage_by_day' => $this->getUsageByDay($usages),
            ],
            'recent_usages' => $usages->take(50)->map(function ($usage) {
                return [
                    'id' => $usage->id,
                    'user' => $usage->user ? [
                        'name' => $usage->user->first_name.' '.$usage->user->last_name,
                        'email' => $usage->user->email,
                    ] : null,
                    'order_total' => $usage->order?->total_amount,
                    'discount_amount' => $usage->discount_amount,
                    'used_at' => $usage->used_at,
                ];
            }),
        ];
    }

    /**
     * Detect and prevent promotion fraud.
     */
    public function detectPromotionFraud(string $promotionId): array
    {
        $promotion = Promotion::with(['usages.user'])->findOrFail($promotionId);
        $usages = $promotion->usages;

        $fraudIndicators = [];

        // Check for excessive usage by single users
        $userUsageCounts = $usages->groupBy('user_id')->map(fn ($group) => $group->count());
        $suspiciousUsers = $userUsageCounts->filter(fn ($count) => $promotion->usage_limit_per_user && $count > $promotion->usage_limit_per_user
        );

        if ($suspiciousUsers->isNotEmpty()) {
            $fraudIndicators[] = [
                'type' => 'excessive_user_usage',
                'severity' => 'high',
                'description' => 'Users exceeding per-user usage limits',
                'affected_users' => $suspiciousUsers->keys()->toArray(),
                'count' => $suspiciousUsers->count(),
            ];
        }

        // Check for rapid successive usage (potential bot activity)
        $rapidUsage = $usages->groupBy('user_id')->filter(function ($userUsages) {
            return $userUsages->count() > 1 &&
                   $userUsages->max('used_at')->diffInMinutes($userUsages->min('used_at')) < 5;
        });

        if ($rapidUsage->isNotEmpty()) {
            $fraudIndicators[] = [
                'type' => 'rapid_usage',
                'severity' => 'medium',
                'description' => 'Rapid successive usage detected',
                'affected_users' => $rapidUsage->keys()->toArray(),
                'count' => $rapidUsage->count(),
            ];
        }

        // Check for unusual usage patterns
        $unusualPatterns = $this->detectUnusualUsagePatterns($usages);
        if (! empty($unusualPatterns)) {
            $fraudIndicators = array_merge($fraudIndicators, $unusualPatterns);
        }

        return [
            'promotion_id' => $promotionId,
            'fraud_score' => $this->calculateFraudScore($fraudIndicators),
            'indicators' => $fraudIndicators,
            'recommendations' => $this->getFraudPreventionRecommendations($fraudIndicators),
        ];
    }

    /**
     * Bulk operations for promotions.
     */
    public function bulkPromotionOperations(array $promotionIds, string $action, array $options = []): array
    {
        $results = ['success' => [], 'failed' => []];

        DB::transaction(function () use ($promotionIds, $action, $options, &$results) {
            foreach ($promotionIds as $promotionId) {
                try {
                    switch ($action) {
                        case 'activate':
                            $this->togglePromotionStatus($promotionId, true, $options['reason'] ?? null);
                            break;
                        case 'deactivate':
                            $this->togglePromotionStatus($promotionId, false, $options['reason'] ?? null);
                            break;
                        case 'extend_expiry':
                            $this->extendPromotionExpiry($promotionId, $options['new_expiry_date']);
                            break;
                        case 'delete':
                            $this->deletePromotion($promotionId, $options['reason'] ?? null);
                            break;
                        default:
                            throw new \InvalidArgumentException("Invalid action: {$action}");
                    }

                    $results['success'][] = $promotionId;

                } catch (\Exception $e) {
                    $results['failed'][] = [
                        'promotion_id' => $promotionId,
                        'error' => $e->getMessage(),
                    ];

                    $this->loggingService->logError('Bulk promotion operation failed', $e, [
                        'promotion_id' => $promotionId,
                        'action' => $action,
                    ]);
                }
            }
        });

        return $results;
    }

    /**
     * Validate promotion data.
     */
    private function validatePromotionData(array $data, ?Promotion $existingPromotion = null): void
    {
        // Validate discount value based on type
        if (isset($data['type']) && isset($data['discount_value'])) {
            $type = is_string($data['type']) ? PromotionType::from($data['type']) : $data['type'];

            if ($type === PromotionType::PERCENTAGE_DISCOUNT && $data['discount_value'] > 100) {
                throw new \InvalidArgumentException('Percentage discount cannot exceed 100%');
            }

            if ($type === PromotionType::FIXED_DISCOUNT && $data['discount_value'] <= 0) {
                throw new \InvalidArgumentException('Fixed discount must be greater than 0');
            }
        }

        // Validate date ranges
        if (isset($data['starts_at']) && isset($data['expires_at'])) {
            if (strtotime($data['starts_at']) >= strtotime($data['expires_at'])) {
                throw new \InvalidArgumentException('Start date must be before expiry date');
            }
        }

        // Validate unique code
        if (isset($data['code']) && $data['code']) {
            $query = Promotion::where('code', $data['code']);
            if ($existingPromotion) {
                $query->where('id', '!=', $existingPromotion->id);
            }
            if ($query->exists()) {
                throw new \InvalidArgumentException('Promotion code already exists');
            }
        }
    }

    /**
     * Helper methods for analytics.
     */
    private function getPromotionsByType($promotions): array
    {
        return $promotions->groupBy('type')->map(function ($typePromotions, $type) {
            return [
                'type' => $type,
                'count' => $typePromotions->count(),
                'total_usage' => $typePromotions->sum('usages_count'),
            ];
        })->values()->toArray();
    }

    private function getTopPerformingPromotions($promotions): array
    {
        return $promotions->sortByDesc('usages_count')
            ->take(10)
            ->map(fn ($p) => $p->getSummary())
            ->values()
            ->toArray();
    }

    private function getUsageTrends(array $filters): array
    {
        // This would typically query usage data over time
        // For now, return mock data structure
        return [
            'daily_usage' => [],
            'weekly_usage' => [],
            'monthly_usage' => [],
        ];
    }

    private function getFraudIndicators(array $filters): array
    {
        // This would analyze patterns for fraud detection
        return [
            'suspicious_patterns' => 0,
            'blocked_attempts' => 0,
            'fraud_score' => 0,
        ];
    }

    private function getUsageByDay($usages): array
    {
        return $usages->groupBy(function ($usage) {
            return $usage->used_at->format('Y-m-d');
        })->map->count()->toArray();
    }

    private function detectUnusualUsagePatterns($usages): array
    {
        $patterns = [];

        // Check for usage outside business hours
        $afterHoursUsage = $usages->filter(function ($usage) {
            $hour = $usage->used_at->hour;

            return $hour < 6 || $hour > 22;
        });

        if ($afterHoursUsage->count() > $usages->count() * 0.3) {
            $patterns[] = [
                'type' => 'after_hours_usage',
                'severity' => 'low',
                'description' => 'High percentage of after-hours usage',
                'percentage' => round(($afterHoursUsage->count() / $usages->count()) * 100, 2),
            ];
        }

        return $patterns;
    }

    private function calculateFraudScore(array $indicators): float
    {
        $score = 0;
        foreach ($indicators as $indicator) {
            $score += match ($indicator['severity']) {
                'high' => 30,
                'medium' => 20,
                'low' => 10,
                default => 5,
            };
        }

        return min($score, 100);
    }

    private function getFraudPreventionRecommendations(array $indicators): array
    {
        $recommendations = [];

        foreach ($indicators as $indicator) {
            switch ($indicator['type']) {
                case 'excessive_user_usage':
                    $recommendations[] = 'Consider implementing stricter per-user limits';
                    break;
                case 'rapid_usage':
                    $recommendations[] = 'Implement rate limiting for promotion usage';
                    break;
                case 'after_hours_usage':
                    $recommendations[] = 'Monitor after-hours usage patterns';
                    break;
            }
        }

        return array_unique($recommendations);
    }

    private function extendPromotionExpiry(string $promotionId, string $newExpiryDate): Promotion
    {
        $promotion = Promotion::findOrFail($promotionId);

        $promotion->update(['expires_at' => $newExpiryDate]);

        $this->loggingService->logInfo('Promotion expiry extended', [
            'promotion_id' => $promotion->id,
            'new_expiry_date' => $newExpiryDate,
            'extended_by' => auth()->id(),
        ]);

        return $promotion->fresh();
    }
}
