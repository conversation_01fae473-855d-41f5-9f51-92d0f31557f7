<?php

declare(strict_types=1);

namespace App\Services\Business;

use App\Models\Business\Product;
use App\Models\Business\ProductCategory;
use App\Services\System\LoggingService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class ProductManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get comprehensive product statistics.
     */
    public function getProductStatistics(): array
    {
        return [
            'total_products' => Product::count(),
            'active_products' => Product::where('is_available', true)->count(),
            'inactive_products' => Product::where('is_available', false)->count(),
            'products_by_category' => Product::with('category:id,name')
                ->selectRaw('category_id, COUNT(*) as count')
                ->groupBy('category_id')
                ->get()
                ->pluck('count', 'category.name'),
            'featured_products' => Product::where('is_featured', true)->count(),
            'out_of_stock' => Product::where('quantity', 0)->count(),
            'low_stock' => Product::where('quantity', '>', 0)
                ->where('quantity', '<=', 10)
                ->count(),
            'unlimited_stock' => Product::where('quantity', -1)->count(),
            'products_today' => Product::whereDate('created_at', today())->count(),
            'products_this_week' => Product::whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek(),
            ])->count(),
            'products_this_month' => Product::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
            'average_price' => Product::where('is_available', true)->avg('price'),
            'price_ranges' => $this->getPriceRangeBreakdown(),
        ];
    }

    /**
     * Get product analytics.
     */
    public function getProductAnalytics(array $filters = []): array
    {
        $query = Product::query();

        // Apply filters
        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        if (isset($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $products = $query->get();

        return [
            'total_products' => $products->count(),
            'active_products' => $products->where('is_available', true)->count(),
            'average_price' => $products->where('is_available', true)->avg('price'),
            'price_distribution' => $this->getPriceDistribution($products),
            'category_breakdown' => $this->getCategoryBreakdown($products),
            'stock_analysis' => $this->getStockAnalysis($products),
            'daily_breakdown' => $this->getDailyProductBreakdown($products),
            'performance_metrics' => $this->getProductPerformanceMetrics($products),
        ];
    }

    /**
     * Get products requiring attention.
     */
    public function getProductsRequiringAttention(): array
    {
        return [
            'out_of_stock' => Product::where('quantity', 0)
                ->where('is_available', true)
                ->with(['business:id,name', 'category:id,name'])
                ->limit(20)
                ->get(),
            'low_stock' => Product::where('quantity', '>', 0)
                ->where('quantity', '<=', 10)
                ->where('is_available', true)
                ->with(['business:id,name', 'category:id,name'])
                ->orderBy('quantity')
                ->limit(20)
                ->get(),
            'inactive_products' => Product::where('is_available', false)
                ->where('created_at', '>=', now()->subDays(30))
                ->with(['business:id,name', 'category:id,name'])
                ->limit(20)
                ->get(),
            'missing_images' => Product::whereNull('main_image_url')
                ->where('is_available', true)
                ->with(['business:id,name', 'category:id,name'])
                ->limit(20)
                ->get(),
            'missing_descriptions' => Product::where(function ($query) {
                $query->whereNull('description')
                    ->orWhere('description', '');
            })
                ->where('is_available', true)
                ->with(['business:id,name', 'category:id,name'])
                ->limit(20)
                ->get(),
        ];
    }

    /**
     * Get product compliance issues.
     */
    public function getProductComplianceIssues(): array
    {
        return [
            'missing_required_fields' => $this->getProductsMissingRequiredFields(),
            'pricing_issues' => $this->getProductsWithPricingIssues(),
            'inventory_discrepancies' => $this->getInventoryDiscrepancies(),
            'duplicate_skus' => $this->getDuplicateSkus(),
            'category_violations' => $this->getCategoryViolations(),
        ];
    }

    /**
     * Update product status.
     */
    public function updateProductStatus(string $productId, bool $isActive, ?string $reason = null): Product
    {
        return DB::transaction(function () use ($productId, $isActive, $reason) {
            $product = Product::findOrFail($productId);

            $oldStatus = $product->is_available;
            $product->update(['is_available' => $isActive]);

            $this->loggingService->logInfo('Product status updated', [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'old_status' => $oldStatus ? 'active' : 'inactive',
                'new_status' => $isActive ? 'active' : 'inactive',
                'reason' => $reason,
                'admin_id' => auth()->id(),
            ]);

            return $product->fresh();
        });
    }

    /**
     * Bulk update product status.
     */
    public function bulkUpdateProductStatus(array $productIds, bool $isActive, ?string $reason = null): array
    {
        $results = ['updated' => 0, 'failed' => 0, 'details' => []];

        DB::transaction(function () use ($productIds, $isActive, $reason, &$results) {
            foreach ($productIds as $productId) {
                try {
                    $product = Product::findOrFail($productId);

                    $oldStatus = $product->is_available;
                    $product->update(['is_available' => $isActive]);

                    $results['updated']++;
                    $results['details'][$productId] = 'updated';

                    $this->loggingService->logInfo('Product status updated via bulk operation', [
                        'product_id' => $product->id,
                        'old_status' => $oldStatus ? 'active' : 'inactive',
                        'new_status' => $isActive ? 'active' : 'inactive',
                        'reason' => $reason,
                        'admin_id' => auth()->id(),
                    ]);

                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['details'][$productId] = 'error: '.$e->getMessage();
                }
            }
        });

        return $results;
    }

    /**
     * Get featured products management.
     */
    public function getFeaturedProductsManagement(): array
    {
        $featuredProducts = Product::where('is_featured', true)
            ->with(['business:id,name', 'category:id,name'])
            ->orderBy('created_at', 'desc')
            ->get();

        return [
            'featured_products' => $featuredProducts,
            'total_featured' => $featuredProducts->count(),
            'featured_by_category' => $featuredProducts->groupBy('category.name')
                ->map(fn ($group) => $group->count()),
            'featured_by_business' => $featuredProducts->groupBy('business.name')
                ->map(fn ($group) => $group->count()),
            'recommendations' => $this->getFeaturedProductRecommendations(),
        ];
    }

    /**
     * Export products data.
     */
    public function exportProducts(array $filters = [], string $format = 'csv'): string
    {
        $query = Product::with([
            'business:id,name',
            'category:id,name',
            'tenant:id,name',
        ]);

        // Apply filters
        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        if (isset($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        if (isset($filters['is_available'])) {
            $query->where('is_available', $filters['is_available']);
        }

        if (isset($filters['is_featured'])) {
            $query->where('is_featured', $filters['is_featured']);
        }

        if (isset($filters['stock_status'])) {
            switch ($filters['stock_status']) {
                case 'out_of_stock':
                    $query->where('quantity', 0);
                    break;
                case 'low_stock':
                    $query->where('quantity', '>', 0)->where('quantity', '<=', 10);
                    break;
                case 'in_stock':
                    $query->where('quantity', '>', 10);
                    break;
                case 'unlimited':
                    $query->where('quantity', -1);
                    break;
            }
        }

        $products = $query->orderBy('created_at', 'desc')->get();

        // Generate filename
        $filename = 'products_export_'.now()->format('Y_m_d_H_i_s').'.'.$format;
        $filepath = storage_path('app/exports/'.$filename);

        // Ensure directory exists
        if (! file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        if ($format === 'csv') {
            $this->exportToCsv($products, $filepath);
        } elseif ($format === 'json') {
            $this->exportToJson($products, $filepath);
        }

        return $filename;
    }

    /**
     * Get price range breakdown.
     */
    private function getPriceRangeBreakdown(): array
    {
        return [
            '0-1000' => Product::whereBetween('price', [0, 1000])->count(),
            '1000-5000' => Product::whereBetween('price', [1000, 5000])->count(),
            '5000-10000' => Product::whereBetween('price', [5000, 10000])->count(),
            '10000-25000' => Product::whereBetween('price', [10000, 25000])->count(),
            '25000+' => Product::where('price', '>', 25000)->count(),
        ];
    }

    /**
     * Get price distribution for collection.
     */
    private function getPriceDistribution(Collection $products): array
    {
        return [
            'min_price' => $products->min('price'),
            'max_price' => $products->max('price'),
            'average_price' => $products->avg('price'),
            'median_price' => $products->median('price'),
            'price_ranges' => [
                '0-1000' => $products->whereBetween('price', [0, 1000])->count(),
                '1000-5000' => $products->whereBetween('price', [1000, 5000])->count(),
                '5000-10000' => $products->whereBetween('price', [5000, 10000])->count(),
                '10000+' => $products->where('price', '>', 10000)->count(),
            ],
        ];
    }

    /**
     * Get category breakdown for collection.
     */
    private function getCategoryBreakdown(Collection $products): array
    {
        return $products->with('category')->groupBy('category.name')
            ->map(function ($categoryProducts) {
                return [
                    'count' => $categoryProducts->count(),
                    'active_count' => $categoryProducts->where('is_available', true)->count(),
                    'average_price' => $categoryProducts->avg('price'),
                ];
            })->toArray();
    }

    /**
     * Get stock analysis for collection.
     */
    private function getStockAnalysis(Collection $products): array
    {
        return [
            'total_products' => $products->count(),
            'in_stock' => $products->where('quantity', '>', 0)->count(),
            'out_of_stock' => $products->where('quantity', 0)->count(),
            'unlimited_stock' => $products->where('quantity', -1)->count(),
            'low_stock' => $products->where('quantity', '>', 0)
                ->where('quantity', '<=', 10)->count(),
            'average_stock' => $products->where('quantity', '>', 0)->avg('quantity'),
        ];
    }

    /**
     * Get daily product breakdown.
     */
    private function getDailyProductBreakdown(Collection $products): array
    {
        return $products->groupBy(function ($product) {
            return $product->created_at->format('Y-m-d');
        })->map(function ($dayProducts) {
            return [
                'count' => $dayProducts->count(),
                'active_count' => $dayProducts->where('is_available', true)->count(),
                'average_price' => $dayProducts->avg('price'),
            ];
        })->toArray();
    }

    /**
     * Get product performance metrics.
     */
    private function getProductPerformanceMetrics(Collection $products): array
    {
        return [
            'completion_rate' => $this->calculateProductCompletionRate($products),
            'image_coverage' => $this->calculateImageCoverage($products),
            'description_coverage' => $this->calculateDescriptionCoverage($products),
            'category_coverage' => $this->calculateCategoryCoverage($products),
        ];
    }

    /**
     * Calculate product completion rate.
     */
    private function calculateProductCompletionRate(Collection $products): float
    {
        $total = $products->count();
        if ($total === 0) {
            return 0;
        }

        $complete = $products->filter(function ($product) {
            return ! empty($product->name) &&
                   ! empty($product->description) &&
                   ! empty($product->main_image_url) &&
                   ! empty($product->category_id) &&
                   $product->price > 0;
        })->count();

        return round(($complete / $total) * 100, 2);
    }

    /**
     * Calculate image coverage.
     */
    private function calculateImageCoverage(Collection $products): float
    {
        $total = $products->count();
        if ($total === 0) {
            return 0;
        }

        $withImages = $products->whereNotNull('main_image_url')->count();

        return round(($withImages / $total) * 100, 2);
    }

    /**
     * Calculate description coverage.
     */
    private function calculateDescriptionCoverage(Collection $products): float
    {
        $total = $products->count();
        if ($total === 0) {
            return 0;
        }

        $withDescriptions = $products->filter(function ($product) {
            return ! empty($product->description);
        })->count();

        return round(($withDescriptions / $total) * 100, 2);
    }

    /**
     * Calculate category coverage.
     */
    private function calculateCategoryCoverage(Collection $products): float
    {
        $total = $products->count();
        if ($total === 0) {
            return 0;
        }

        $withCategories = $products->whereNotNull('category_id')->count();

        return round(($withCategories / $total) * 100, 2);
    }

    /**
     * Get products missing required fields.
     */
    private function getProductsMissingRequiredFields(): Collection
    {
        return Product::where(function ($query) {
            $query->whereNull('name')
                ->orWhereNull('price')
                ->orWhere('price', '<=', 0)
                ->orWhereNull('category_id');
        })->with(['business:id,name', 'category:id,name'])->limit(20)->get();
    }

    /**
     * Get products with pricing issues.
     */
    private function getProductsWithPricingIssues(): Collection
    {
        return Product::where(function ($query) {
            $query->where('price', '<=', 0)
                ->orWhere(function ($q) {
                    $q->whereNotNull('sale_price')
                        ->whereColumn('sale_price', '>=', 'price');
                })
                ->orWhere(function ($q) {
                    $q->whereNotNull('cost_price')
                        ->whereColumn('cost_price', '>', 'price');
                });
        })->with(['business:id,name'])->limit(20)->get();
    }

    /**
     * Get inventory discrepancies.
     */
    private function getInventoryDiscrepancies(): Collection
    {
        return Product::where('quantity', '<', 0)
            ->where('quantity', '!=', -1)
            ->with(['business:id,name'])
            ->limit(20)
            ->get();
    }

    /**
     * Get duplicate SKUs.
     */
    private function getDuplicateSkus(): Collection
    {
        return Product::whereNotNull('sku')
            ->selectRaw('sku, COUNT(*) as count')
            ->groupBy('sku')
            ->havingRaw('COUNT(*) > 1')
            ->with(['business:id,name'])
            ->limit(20)
            ->get();
    }

    /**
     * Get category violations.
     */
    private function getCategoryViolations(): Collection
    {
        return Product::whereNotNull('category_id')
            ->whereDoesntHave('category')
            ->with(['business:id,name'])
            ->limit(20)
            ->get();
    }

    /**
     * Get featured product recommendations.
     */
    private function getFeaturedProductRecommendations(): Collection
    {
        return Product::where('is_available', true)
            ->where('is_featured', false)
            ->whereNotNull('main_image_url')
            ->whereNotNull('description')
            ->where('price', '>', 0)
            ->orderBy('created_at', 'desc')
            ->limit(10)
            ->get();
    }

    /**
     * Export products to CSV.
     */
    private function exportToCsv(Collection $products, string $filepath): void
    {
        $file = fopen($filepath, 'w');

        // Write header
        fputcsv($file, [
            'Product ID', 'Name', 'Business', 'Category', 'Price', 'Sale Price',
            'SKU', 'Quantity', 'Weight', 'Status', 'Featured', 'Created At',
        ]);

        // Write data
        foreach ($products as $product) {
            fputcsv($file, [
                $product->id,
                $product->name,
                $product->business?->name ?? 'N/A',
                $product->category?->name ?? 'N/A',
                $product->price,
                $product->sale_price ?? 'N/A',
                $product->sku ?? 'N/A',
                $product->quantity === -1 ? 'Unlimited' : $product->quantity,
                $product->weight ?? 'N/A',
                $product->is_available ? 'Active' : 'Inactive',
                $product->is_featured ? 'Yes' : 'No',
                $product->created_at->toISOString(),
            ]);
        }

        fclose($file);
    }

    /**
     * Export products to JSON.
     */
    private function exportToJson(Collection $products, string $filepath): void
    {
        $data = $products->map(function ($product) {
            return [
                'product_id' => $product->id,
                'name' => $product->name,
                'business' => $product->business?->name,
                'category' => $product->category?->name,
                'price' => $product->price,
                'sale_price' => $product->sale_price,
                'sku' => $product->sku,
                'quantity' => $product->quantity,
                'weight' => $product->weight,
                'is_available' => $product->is_available,
                'is_featured' => $product->is_featured,
                'main_image_url' => $product->main_image_url,
                'created_at' => $product->created_at->toISOString(),
            ];
        });

        file_put_contents($filepath, json_encode($data, JSON_PRETTY_PRINT));
    }

    /**
     * Get product with detailed information.
     */
    public function getProductWithDetails(string $productId): Product
    {
        return Product::with([
            'business:id,name,tenant_id',
            'category:id,name',
            'productCollections:id,name',
            'variants:id,name,price,sku',
            'reviews:id,rating,comment,created_at',
            'orderItems:id,quantity,unit_price',
        ])->findOrFail($productId);
    }

    /**
     * Update product category.
     */
    public function updateProductCategory(string $productId, string $categoryId, ?string $reason = null): Product
    {
        return DB::transaction(function () use ($productId, $categoryId, $reason) {
            $product = Product::findOrFail($productId);

            // Validate category exists
            ProductCategory::findOrFail($categoryId);

            $oldCategoryId = $product->category_id;
            $product->update(['category_id' => $categoryId]);

            $this->loggingService->logInfo('Product category updated', [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'old_category_id' => $oldCategoryId,
                'new_category_id' => $categoryId,
                'reason' => $reason,
                'admin_id' => auth()->id(),
            ]);

            return $product->fresh(['category']);
        });
    }

    /**
     * Toggle product featured status.
     */
    public function toggleFeaturedStatus(string $productId, bool $isFeatured, ?string $reason = null): Product
    {
        return DB::transaction(function () use ($productId, $isFeatured, $reason) {
            $product = Product::findOrFail($productId);

            $oldStatus = $product->is_featured;
            $product->update(['is_featured' => $isFeatured]);

            $this->loggingService->logInfo('Product featured status updated', [
                'product_id' => $product->id,
                'product_name' => $product->name,
                'old_featured_status' => $oldStatus,
                'new_featured_status' => $isFeatured,
                'reason' => $reason,
                'admin_id' => auth()->id(),
            ]);

            return $product->fresh();
        });
    }

    /**
     * Bulk update products.
     */
    public function bulkUpdateProducts(array $productIds, string $action, array $options = []): array
    {
        $results = ['updated' => 0, 'failed' => 0, 'details' => []];

        DB::transaction(function () use ($productIds, $action, $options, &$results) {
            foreach ($productIds as $productId) {
                try {
                    switch ($action) {
                        case 'activate':
                            $this->updateProductStatus($productId, true, $options['reason'] ?? null);
                            break;
                        case 'deactivate':
                            $this->updateProductStatus($productId, false, $options['reason'] ?? null);
                            break;
                        case 'update_category':
                            $this->updateProductCategory($productId, $options['category_id'], $options['reason'] ?? null);
                            break;
                        case 'toggle_featured':
                            $this->toggleFeaturedStatus($productId, $options['is_featured'], $options['reason'] ?? null);
                            break;
                        case 'delete':
                            $product = Product::findOrFail($productId);
                            $product->delete();
                            break;
                        default:
                            throw new \InvalidArgumentException("Invalid action: {$action}");
                    }

                    $results['updated']++;
                    $results['details'][$productId] = 'success';

                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['details'][$productId] = 'error: '.$e->getMessage();
                }
            }
        });

        return $results;
    }

    /**
     * Get product inventory summary.
     */
    public function getProductInventorySummary(): array
    {
        return [
            'total_products' => Product::count(),
            'stock_levels' => [
                'in_stock' => Product::where('quantity', '>', 10)->count(),
                'low_stock' => Product::where('quantity', '>', 0)->where('quantity', '<=', 10)->count(),
                'out_of_stock' => Product::where('quantity', 0)->count(),
                'unlimited' => Product::where('quantity', -1)->count(),
            ],
            'value_analysis' => [
                'total_inventory_value' => Product::where('quantity', '>', 0)
                    ->selectRaw('SUM(quantity * price) as total')
                    ->value('total') ?? 0,
                'total_cost_value' => Product::where('quantity', '>', 0)
                    ->whereNotNull('cost_price')
                    ->selectRaw('SUM(quantity * cost_price) as total')
                    ->value('total') ?? 0,
            ],
            'top_value_products' => Product::where('quantity', '>', 0)
                ->selectRaw('*, (quantity * price) as inventory_value')
                ->orderByDesc('inventory_value')
                ->limit(10)
                ->get(['id', 'name', 'price', 'quantity']),
        ];
    }

    /**
     * Search products across tenants.
     */
    public function searchProducts(string $query, array $filters = []): Collection
    {
        $searchQuery = Product::where(function ($q) use ($query) {
            $q->where('name', 'ILIKE', "%{$query}%")
                ->orWhere('description', 'ILIKE', "%{$query}%")
                ->orWhere('sku', 'ILIKE', "%{$query}%");
        });

        // Apply filters
        if (isset($filters['business_id'])) {
            $searchQuery->where('business_id', $filters['business_id']);
        }

        if (isset($filters['category_id'])) {
            $searchQuery->where('category_id', $filters['category_id']);
        }

        if (isset($filters['is_available'])) {
            $searchQuery->where('is_available', $filters['is_available']);
        }

        if (isset($filters['is_featured'])) {
            $searchQuery->where('is_featured', $filters['is_featured']);
        }

        return $searchQuery->with([
            'business:id,name',
            'category:id,name',
        ])->limit(50)->get();
    }

    /**
     * Get product performance report.
     */
    public function getProductPerformanceReport(array $filters = []): array
    {
        $query = Product::with(['business:id,name', 'category:id,name']);

        // Apply filters
        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        if (isset($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        $products = $query->get();

        return [
            'summary' => [
                'total_products' => $products->count(),
                'active_products' => $products->where('is_available', true)->count(),
                'featured_products' => $products->where('is_featured', true)->count(),
                'average_price' => $products->avg('price'),
            ],
            'top_performers' => $products->where('is_available', true)
                ->sortByDesc('price')
                ->take(10)
                ->values(),
            'underperformers' => $products->where('is_available', false)
                ->sortBy('created_at')
                ->take(10)
                ->values(),
            'recommendations' => [
                'products_to_feature' => $this->getFeaturedProductRecommendations(),
                'products_needing_attention' => $this->getProductsRequiringAttention()['missing_images']->take(5),
            ],
        ];
    }
}
