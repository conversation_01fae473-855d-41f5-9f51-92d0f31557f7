<?php

declare(strict_types=1);

namespace App\Services\Business;

use App\Models\Business\Product;
use App\Models\Business\ProductCategory;
use Illuminate\Support\Facades\DB;

class CategoryManagementService
{
    /**
     * Get category with detailed information.
     */
    public function getCategoryWithDetails(string $categoryId): array
    {
        $category = ProductCategory::with([
            'business:id,business_name,tenant_id',
            'parent:id,name',
            'children:id,name,parent_id',
            'products' => function ($query) {
                $query->where('is_active', true)->limit(10);
            },
        ])->findOrFail($categoryId);

        return [
            'category' => $category,
            'statistics' => $this->getCategoryDetailStatistics($category),
            'usage_analytics' => $this->getCategoryUsageAnalytics($category),
        ];
    }

    /**
     * Update category status.
     */
    public function updateCategoryStatus(string $categoryId, bool $isActive, ?string $reason = null): ProductCategory
    {
        return DB::transaction(function () use ($categoryId, $isActive, $reason) {
            $category = ProductCategory::findOrFail($categoryId);

            $category->update(['is_active' => $isActive]);

            // If deactivating, also deactivate child categories
            if (! $isActive) {
                $this->deactivateChildCategories($categoryId);
            }

            // Log the status change
            activity()
                ->performedOn($category)
                ->withProperties([
                    'old_status' => ! $isActive,
                    'new_status' => $isActive,
                    'reason' => $reason,
                    'admin_id' => auth()->id(),
                ])
                ->log($isActive ? 'category_activated' : 'category_deactivated');

            return $category->fresh();
        });
    }

    /**
     * Get category analytics for a specific period.
     */
    public function getCategoryAnalytics(string $period): array
    {
        $periodDates = $this->getPeriodDates($period);

        return [
            'overview' => $this->getCategoryOverviewStats(),
            'popular_categories' => $this->getPopularCategories(10, $period),
            'category_performance' => $this->getCategoryPerformance($periodDates),
            'usage_trends' => $this->getCategoryUsageTrends($periodDates),
        ];
    }

    /**
     * Get popular categories.
     */
    public function getPopularCategories(int $limit, string $period): array
    {
        $periodDates = $this->getPeriodDates($period);

        return ProductCategory::select('product_categories.*')
            ->join('products', 'product_categories.id', '=', 'products.category_id')
            ->join('order_items', 'products.id', '=', 'order_items.product_id')
            ->join('orders', 'order_items.order_id', '=', 'orders.id')
            ->whereBetween('orders.created_at', [$periodDates['start'], $periodDates['end']])
            ->where('product_categories.is_active', true)
            ->groupBy('product_categories.id')
            ->orderByRaw('COUNT(order_items.id) DESC')
            ->limit($limit)
            ->with(['business:id,business_name'])
            ->get()
            ->map(function ($category) {
                return [
                    'category' => $category,
                    'order_count' => $this->getCategoryOrderCount($category->id),
                    'revenue' => $this->getCategoryRevenue($category->id),
                ];
            })
            ->toArray();
    }

    /**
     * Merge categories.
     */
    public function mergeCategories(string $sourceCategoryId, string $targetCategoryId, bool $deleteSource = true): array
    {
        return DB::transaction(function () use ($sourceCategoryId, $targetCategoryId, $deleteSource) {
            $sourceCategory = ProductCategory::findOrFail($sourceCategoryId);
            $targetCategory = ProductCategory::findOrFail($targetCategoryId);

            // Move all products from source to target category
            $movedProducts = Product::where('category_id', $sourceCategoryId)
                ->update(['category_id' => $targetCategoryId]);

            // Move child categories
            $movedChildren = ProductCategory::where('parent_id', $sourceCategoryId)
                ->update(['parent_id' => $targetCategoryId]);

            // Delete source category if requested
            if ($deleteSource) {
                $sourceCategory->delete();
            } else {
                $sourceCategory->update(['is_active' => false]);
            }

            // Log the merge operation
            activity()
                ->performedOn($targetCategory)
                ->withProperties([
                    'source_category_id' => $sourceCategoryId,
                    'source_category_name' => $sourceCategory->name,
                    'moved_products' => $movedProducts,
                    'moved_children' => $movedChildren,
                    'deleted_source' => $deleteSource,
                    'admin_id' => auth()->id(),
                ])
                ->log('categories_merged');

            return [
                'target_category' => $targetCategory->fresh(),
                'moved_products' => $movedProducts,
                'moved_children' => $movedChildren,
                'source_deleted' => $deleteSource,
            ];
        });
    }

    /**
     * Bulk update categories.
     */
    public function bulkUpdateCategories(array $categoryIds, string $action, ?string $reason = null): array
    {
        return DB::transaction(function () use ($categoryIds, $action, $reason) {
            $categories = ProductCategory::whereIn('id', $categoryIds)->get();
            $results = [];

            foreach ($categories as $category) {
                switch ($action) {
                    case 'activate':
                        $category->update(['is_active' => true]);
                        $results[] = ['id' => $category->id, 'action' => 'activated'];
                        break;
                    case 'deactivate':
                        $category->update(['is_active' => false]);
                        $this->deactivateChildCategories($category->id);
                        $results[] = ['id' => $category->id, 'action' => 'deactivated'];
                        break;
                    case 'delete':
                        if ($this->canDeleteCategory($category->id)) {
                            $category->delete();
                            $results[] = ['id' => $category->id, 'action' => 'deleted'];
                        } else {
                            $results[] = ['id' => $category->id, 'action' => 'failed', 'reason' => 'has_products'];
                        }
                        break;
                }
            }

            // Log bulk operation
            activity()
                ->withProperties([
                    'category_ids' => $categoryIds,
                    'action' => $action,
                    'reason' => $reason,
                    'results' => $results,
                    'admin_id' => auth()->id(),
                ])
                ->log('categories_bulk_updated');

            return $results;
        });
    }

    /**
     * Get category hierarchy.
     */
    public function getCategoryHierarchy(?string $businessId = null, ?string $tenantId = null): array
    {
        $query = ProductCategory::with(['children.children'])
            ->whereNull('parent_id')
            ->where('is_active', true);

        if ($businessId) {
            $query->where('business_id', $businessId);
        }

        if ($tenantId) {
            $query->where('tenant_id', $tenantId);
        }

        return $query->orderBy('display_order')
            ->orderBy('name')
            ->get()
            ->map(function ($category) {
                return $this->buildCategoryTree($category);
            })
            ->toArray();
    }

    /**
     * Get category statistics.
     */
    public function getCategoryStatistics(): array
    {
        return [
            'total_categories' => ProductCategory::count(),
            'active_categories' => ProductCategory::where('is_active', true)->count(),
            'categories_with_products' => ProductCategory::whereHas('products')->count(),
            'empty_categories' => ProductCategory::whereDoesntHave('products')->count(),
            'top_level_categories' => ProductCategory::whereNull('parent_id')->count(),
            'nested_categories' => ProductCategory::whereNotNull('parent_id')->count(),
        ];
    }

    /**
     * Private helper methods.
     */
    private function deactivateChildCategories(string $parentId): void
    {
        $children = ProductCategory::where('parent_id', $parentId)->get();

        foreach ($children as $child) {
            $child->update(['is_active' => false]);
            $this->deactivateChildCategories($child->id);
        }
    }

    private function canDeleteCategory(string $categoryId): bool
    {
        return ! Product::where('category_id', $categoryId)->exists() &&
               ! ProductCategory::where('parent_id', $categoryId)->exists();
    }

    private function buildCategoryTree(ProductCategory $category): array
    {
        return [
            'id' => $category->id,
            'name' => $category->name,
            'slug' => $category->slug,
            'description' => $category->description,
            'display_order' => $category->display_order,
            'product_count' => $category->products()->count(),
            'children' => $category->children->map(function ($child) {
                return $this->buildCategoryTree($child);
            })->toArray(),
        ];
    }

    private function getCategoryDetailStatistics(ProductCategory $category): array
    {
        return [
            'product_count' => $category->products()->count(),
            'active_product_count' => $category->products()->where('is_active', true)->count(),
            'child_category_count' => $category->children()->count(),
            'total_orders' => $this->getCategoryOrderCount($category->id),
            'total_revenue' => $this->getCategoryRevenue($category->id),
        ];
    }

    private function getCategoryUsageAnalytics(ProductCategory $category): array
    {
        // This would integrate with order analytics
        return [
            'monthly_orders' => 0,
            'monthly_revenue' => 0,
            'top_products' => [],
            'performance_trend' => [],
        ];
    }

    private function getCategoryOverviewStats(): array
    {
        return [
            'total_categories' => ProductCategory::count(),
            'active_categories' => ProductCategory::where('is_active', true)->count(),
            'categories_with_products' => ProductCategory::whereHas('products')->count(),
            'most_used_categories' => $this->getPopularCategories(5, 'month'),
        ];
    }

    private function getCategoryPerformance(array $periodDates): array
    {
        // This would calculate category performance metrics
        return [
            'top_performing' => [],
            'underperforming' => [],
            'growth_categories' => [],
        ];
    }

    private function getCategoryUsageTrends(array $periodDates): array
    {
        // This would track category usage over time
        return [
            'daily_usage' => [],
            'category_adoption' => [],
            'seasonal_trends' => [],
        ];
    }

    private function getCategoryOrderCount(string $categoryId): int
    {
        return DB::table('order_items')
            ->join('products', 'order_items.product_id', '=', 'products.id')
            ->where('products.category_id', $categoryId)
            ->count();
    }

    private function getCategoryRevenue(string $categoryId): float
    {
        return DB::table('order_items')
            ->join('products', 'order_items.product_id', '=', 'products.id')
            ->where('products.category_id', $categoryId)
            ->sum(DB::raw('order_items.quantity * order_items.unit_price'));
    }

    private function getPeriodDates(string $period): array
    {
        $now = now();

        return match ($period) {
            'today' => [
                'start' => $now->copy()->startOfDay(),
                'end' => $now->copy()->endOfDay(),
            ],
            'week' => [
                'start' => $now->copy()->startOfWeek(),
                'end' => $now->copy()->endOfWeek(),
            ],
            'month' => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
            'quarter' => [
                'start' => $now->copy()->startOfQuarter(),
                'end' => $now->copy()->endOfQuarter(),
            ],
            'year' => [
                'start' => $now->copy()->startOfYear(),
                'end' => $now->copy()->endOfYear(),
            ],
            default => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
        };
    }
}
