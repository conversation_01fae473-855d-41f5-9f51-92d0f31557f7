<?php

declare(strict_types=1);

namespace App\Services\Business;

use App\Models\Business\InventoryMovement;
use App\Models\Business\Product;
use App\Services\System\LoggingService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

/**
 * Inventory Management Service
 *
 * Handles comprehensive cross-tenant inventory management including:
 * - Cross-tenant inventory oversight
 * - Stock level monitoring and alerts
 * - Inventory movement tracking
 * - Low stock and out-of-stock analytics
 * - Bulk inventory operations
 * - Inventory valuation and reporting
 */
class InventoryManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService,
        private readonly InventoryService $inventoryService
    ) {}

    /**
     * Get inventory overview with advanced filtering.
     */
    public function getInventoryOverview(array $filters = []): Builder
    {
        $query = Product::with(['business:id,business_name', 'category:id,name'])
            ->select([
                'id',
                'tenant_id',
                'business_id',
                'name',
                'sku',
                'barcode',
                'quantity',
                'price',
                'cost_price',
                'category_id',
                'is_available',
                'created_at',
                'updated_at',
            ]);

        // Apply filters
        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        if (isset($filters['tenant_id'])) {
            $query->where('tenant_id', $filters['tenant_id']);
        }

        if (isset($filters['category_id'])) {
            $query->where('category_id', $filters['category_id']);
        }

        if (isset($filters['stock_status'])) {
            switch ($filters['stock_status']) {
                case 'in_stock':
                    $query->where('quantity', '>', 10);
                    break;
                case 'low_stock':
                    $query->where('quantity', '>', 0)->where('quantity', '<=', 10);
                    break;
                case 'out_of_stock':
                    $query->where('quantity', '<=', 0);
                    break;
                case 'unlimited':
                    $query->where('quantity', -1);
                    break;
            }
        }

        if (isset($filters['is_available'])) {
            $query->where('is_available', $filters['is_available']);
        }

        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('name', 'like', "%{$filters['search']}%")
                    ->orWhere('sku', 'like', "%{$filters['search']}%")
                    ->orWhere('barcode', 'like', "%{$filters['search']}%");
            });
        }

        return $query;
    }

    /**
     * Get comprehensive inventory analytics.
     */
    public function getInventoryAnalytics(array $filters = []): array
    {
        $query = Product::query();

        // Apply filters
        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        if (isset($filters['tenant_id'])) {
            $query->where('tenant_id', $filters['tenant_id']);
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $products = $query->get();

        return [
            'summary' => [
                'total_products' => $products->count(),
                'total_businesses' => $products->unique('business_id')->count(),
                'total_tenants' => $products->unique('tenant_id')->count(),
                'active_products' => $products->where('is_available', true)->count(),
                'inactive_products' => $products->where('is_available', false)->count(),
            ],
            'stock_levels' => [
                'in_stock' => $products->where('quantity', '>', 10)->count(),
                'low_stock' => $products->filter(fn ($p) => $p->quantity > 0 && $p->quantity <= 10)->count(),
                'out_of_stock' => $products->where('quantity', '<=', 0)->count(),
                'unlimited' => $products->where('quantity', -1)->count(),
            ],
            'value_analysis' => $this->getInventoryValueAnalysis($products),
            'movement_trends' => $this->getInventoryMovementTrends($filters),
            'top_businesses_by_inventory' => $this->getTopBusinessesByInventory($filters),
            'critical_alerts' => $this->getCriticalInventoryAlerts($filters),
        ];
    }

    /**
     * Get inventory movements with filtering.
     */
    public function getInventoryMovements(array $filters = []): Builder
    {
        $query = InventoryMovement::with([
            'product:id,name,sku',
            'business:id,business_name',
        ]);

        // Apply filters
        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        if (isset($filters['tenant_id'])) {
            $query->where('tenant_id', $filters['tenant_id']);
        }

        if (isset($filters['product_id'])) {
            $query->where('product_id', $filters['product_id']);
        }

        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        return $query;
    }

    /**
     * Get low stock alerts across all businesses.
     */
    public function getLowStockAlerts(array $filters = []): array
    {
        $query = Product::with(['business:id,business_name'])
            ->where('quantity', '>', 0)
            ->where('quantity', '<=', 10)
            ->where('is_available', true);

        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        if (isset($filters['tenant_id'])) {
            $query->where('tenant_id', $filters['tenant_id']);
        }

        $lowStockProducts = $query->orderBy('quantity', 'asc')->get();

        return [
            'total_alerts' => $lowStockProducts->count(),
            'critical_alerts' => $lowStockProducts->where('quantity', '<=', 5)->count(),
            'warning_alerts' => $lowStockProducts->filter(fn ($p) => $p->quantity > 5 && $p->quantity <= 10)->count(),
            'products' => $lowStockProducts->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'sku' => $product->sku,
                    'current_stock' => $product->quantity,
                    'business' => [
                        'id' => $product->business->id,
                        'name' => $product->business->business_name,
                    ],
                    'alert_level' => $product->quantity <= 5 ? 'critical' : 'warning',
                ];
            })->toArray(),
        ];
    }

    /**
     * Get out of stock products.
     */
    public function getOutOfStockProducts(array $filters = []): array
    {
        $query = Product::with(['business:id,business_name'])
            ->where('quantity', '<=', 0)
            ->where('quantity', '!=', -1); // Exclude unlimited stock

        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        if (isset($filters['tenant_id'])) {
            $query->where('tenant_id', $filters['tenant_id']);
        }

        $outOfStockProducts = $query->get();

        return [
            'total_count' => $outOfStockProducts->count(),
            'auto_disabled_count' => $outOfStockProducts->where('is_available', false)->count(),
            'still_active_count' => $outOfStockProducts->where('is_available', true)->count(),
            'products' => $outOfStockProducts->map(function ($product) {
                return [
                    'id' => $product->id,
                    'name' => $product->name,
                    'sku' => $product->sku,
                    'current_stock' => $product->quantity,
                    'is_available' => $product->is_available,
                    'business' => [
                        'id' => $product->business->id,
                        'name' => $product->business->business_name,
                    ],
                    'days_out_of_stock' => $this->getDaysOutOfStock($product),
                ];
            })->toArray(),
        ];
    }

    /**
     * Bulk inventory operations.
     */
    public function bulkInventoryOperations(array $productIds, string $action, array $options = []): array
    {
        $results = ['success' => [], 'failed' => []];

        DB::transaction(function () use ($productIds, $action, $options, &$results) {
            foreach ($productIds as $productId) {
                try {
                    $product = Product::findOrFail($productId);

                    switch ($action) {
                        case 'update_stock':
                            $this->inventoryService->updateStock(
                                $product,
                                $options['new_stock'],
                                auth()->user(),
                                $options['reason'] ?? 'Admin bulk update'
                            );
                            break;
                        case 'increase_stock':
                            $this->inventoryService->increaseStock(
                                $product,
                                $options['quantity'],
                                auth()->user(),
                                $options['reason'] ?? 'Admin bulk increase'
                            );
                            break;
                        case 'decrease_stock':
                            $this->inventoryService->reduceStock(
                                $product,
                                $options['quantity'],
                                $options['reason'] ?? 'Admin bulk decrease'
                            );
                            break;
                        case 'enable_product':
                            $product->update(['is_available' => true]);
                            break;
                        case 'disable_product':
                            $product->update(['is_available' => false]);
                            break;
                        default:
                            throw new \InvalidArgumentException("Invalid action: {$action}");
                    }

                    $results['success'][] = $productId;

                } catch (\Exception $e) {
                    $results['failed'][] = [
                        'product_id' => $productId,
                        'error' => $e->getMessage(),
                    ];

                    $this->loggingService->logError('Bulk inventory operation failed', $e, [
                        'product_id' => $productId,
                        'action' => $action,
                    ]);
                }
            }
        });

        return $results;
    }

    /**
     * Helper methods for analytics and calculations.
     */
    private function getInventoryValueAnalysis($products): array
    {
        $inStockProducts = $products->filter(fn ($p) => $p->quantity > 0 && $p->quantity !== -1);

        $totalInventoryValue = $inStockProducts->sum(fn ($p) => $p->quantity * $p->price);
        $totalCostValue = $inStockProducts->filter(fn ($p) => $p->cost_price)
            ->sum(fn ($p) => $p->quantity * $p->cost_price);

        return [
            'total_inventory_value' => round($totalInventoryValue, 2),
            'total_cost_value' => round($totalCostValue, 2),
            'potential_profit' => round($totalInventoryValue - $totalCostValue, 2),
            'average_product_value' => $inStockProducts->count() > 0
                ? round($totalInventoryValue / $inStockProducts->count(), 2)
                : 0,
            'total_units_in_stock' => $inStockProducts->sum('quantity'),
        ];
    }

    private function getInventoryMovementTrends(array $filters): array
    {
        $query = InventoryMovement::query();

        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        if (isset($filters['tenant_id'])) {
            $query->where('tenant_id', $filters['tenant_id']);
        }

        $movements = $query->where('created_at', '>=', now()->subDays(30))->get();

        return [
            'total_movements' => $movements->count(),
            'stock_in_movements' => $movements->where('quantity_change', '>', 0)->count(),
            'stock_out_movements' => $movements->where('quantity_change', '<', 0)->count(),
            'daily_movements' => $this->getDailyMovementTrends($movements),
            'movement_types' => $movements->groupBy('type')->map(fn ($group) => $group->count())->toArray(),
        ];
    }

    private function getTopBusinessesByInventory(array $filters): array
    {
        $query = Product::with('business:id,business_name')
            ->where('quantity', '>', 0)
            ->where('quantity', '!=', -1);

        if (isset($filters['tenant_id'])) {
            $query->where('tenant_id', $filters['tenant_id']);
        }

        return $query->select([
            'business_id',
            DB::raw('COUNT(*) as product_count'),
            DB::raw('SUM(quantity) as total_stock'),
            DB::raw('SUM(quantity * price) as total_value'),
        ])
            ->groupBy('business_id')
            ->orderBy('total_value', 'desc')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                return [
                    'business_id' => $item->business_id,
                    'business_name' => $item->business->business_name ?? 'Unknown',
                    'product_count' => $item->product_count,
                    'total_stock' => $item->total_stock,
                    'total_value' => round($item->total_value, 2),
                ];
            })
            ->toArray();
    }

    private function getCriticalInventoryAlerts(array $filters): array
    {
        $query = Product::with('business:id,business_name');

        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        if (isset($filters['tenant_id'])) {
            $query->where('tenant_id', $filters['tenant_id']);
        }

        $criticalProducts = $query->where(function ($q) {
            $q->where('quantity', '<=', 0)
                ->orWhere('quantity', '<=', 5);
        })->where('quantity', '!=', -1)->get();

        return [
            'total_critical_alerts' => $criticalProducts->count(),
            'out_of_stock_count' => $criticalProducts->where('quantity', '<=', 0)->count(),
            'critically_low_count' => $criticalProducts->filter(fn ($p) => $p->quantity > 0 && $p->quantity <= 5)->count(),
            'affected_businesses' => $criticalProducts->unique('business_id')->count(),
            'recent_alerts' => $criticalProducts->take(10)->map(function ($product) {
                return [
                    'product_id' => $product->id,
                    'product_name' => $product->name,
                    'current_stock' => $product->quantity,
                    'business_name' => $product->business->business_name ?? 'Unknown',
                    'alert_type' => $product->quantity <= 0 ? 'out_of_stock' : 'critically_low',
                ];
            })->toArray(),
        ];
    }

    private function getDaysOutOfStock(Product $product): int
    {
        // This would typically check inventory movements to find when stock reached 0
        // For now, return a placeholder calculation
        $lastMovement = InventoryMovement::where('product_id', $product->id)
            ->where('current_stock', '<=', 0)
            ->orderBy('created_at', 'desc')
            ->first();

        if ($lastMovement) {
            return (int) $lastMovement->created_at->diffInDays(now());
        }

        return 0;
    }

    private function getDailyMovementTrends($movements): array
    {
        return $movements->groupBy(function ($movement) {
            return $movement->created_at->format('Y-m-d');
        })->map(function ($dayMovements) {
            return [
                'total_movements' => $dayMovements->count(),
                'stock_in' => $dayMovements->where('quantity_change', '>', 0)->sum('quantity_change'),
                'stock_out' => abs($dayMovements->where('quantity_change', '<', 0)->sum('quantity_change')),
            ];
        })->toArray();
    }

    /**
     * Generate inventory report for specific business.
     */
    public function generateBusinessInventoryReport(string $businessId): array
    {
        $products = Product::where('business_id', $businessId)->get();
        $movements = InventoryMovement::where('business_id', $businessId)
            ->where('created_at', '>=', now()->subDays(30))
            ->get();

        return [
            'business_id' => $businessId,
            'report_date' => now()->toDateString(),
            'summary' => [
                'total_products' => $products->count(),
                'active_products' => $products->where('is_available', true)->count(),
                'total_stock_value' => $products->filter(fn ($p) => $p->quantity > 0 && $p->quantity !== -1)
                    ->sum(fn ($p) => $p->quantity * $p->price),
            ],
            'stock_distribution' => [
                'in_stock' => $products->where('quantity', '>', 10)->count(),
                'low_stock' => $products->filter(fn ($p) => $p->quantity > 0 && $p->quantity <= 10)->count(),
                'out_of_stock' => $products->where('quantity', '<=', 0)->count(),
                'unlimited' => $products->where('quantity', -1)->count(),
            ],
            'recent_movements' => [
                'total_movements' => $movements->count(),
                'stock_additions' => $movements->where('quantity_change', '>', 0)->sum('quantity_change'),
                'stock_reductions' => abs($movements->where('quantity_change', '<', 0)->sum('quantity_change')),
            ],
            'recommendations' => $this->generateInventoryRecommendations($products),
        ];
    }

    private function generateInventoryRecommendations($products): array
    {
        $recommendations = [];

        $outOfStock = $products->where('quantity', '<=', 0)->where('quantity', '!=', -1);
        if ($outOfStock->count() > 0) {
            $recommendations[] = "Restock {$outOfStock->count()} out-of-stock products";
        }

        $lowStock = $products->filter(fn ($p) => $p->quantity > 0 && $p->quantity <= 5);
        if ($lowStock->count() > 0) {
            $recommendations[] = "Monitor {$lowStock->count()} products with critically low stock";
        }

        $inactive = $products->where('is_available', false);
        if ($inactive->count() > 0) {
            $recommendations[] = "Review {$inactive->count()} inactive products for potential reactivation";
        }

        return $recommendations;
    }
}
