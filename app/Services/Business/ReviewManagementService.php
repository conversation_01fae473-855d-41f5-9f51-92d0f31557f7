<?php

declare(strict_types=1);

namespace App\Services\Business;

use App\Models\Business\ProductReview;
use App\Models\Delivery\Rating;
use App\Services\System\LoggingService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

/**
 * Review Management Service
 *
 * Handles comprehensive review and rating management including:
 * - Review moderation and approval workflows
 * - Fake review detection algorithms
 * - Review analytics and insights
 * - Business reputation management
 * - Review response management
 * - Review aggregation and scoring
 */
class ReviewManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all reviews with advanced filtering and analytics.
     */
    public function getReviews(array $filters = []): Builder
    {
        $query = ProductReview::with([
            'product:id,name,business_id',
            'customer:id,first_name,last_name,email',
            'business:id,business_name',
            'approvedBy:id,first_name,last_name',
        ]);

        // Apply filters
        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        if (isset($filters['product_id'])) {
            $query->where('product_id', $filters['product_id']);
        }

        if (isset($filters['customer_id'])) {
            $query->where('customer_id', $filters['customer_id']);
        }

        if (isset($filters['rating'])) {
            $query->where('rating', $filters['rating']);
        }

        if (isset($filters['min_rating'])) {
            $query->where('rating', '>=', $filters['min_rating']);
        }

        if (isset($filters['is_approved'])) {
            $query->where('is_approved', $filters['is_approved']);
        }

        if (isset($filters['is_verified_purchase'])) {
            $query->where('is_verified_purchase', $filters['is_verified_purchase']);
        }

        if (isset($filters['has_images'])) {
            if ($filters['has_images']) {
                $query->whereNotNull('images');
            } else {
                $query->whereNull('images');
            }
        }

        if (isset($filters['search'])) {
            $query->where(function ($q) use ($filters) {
                $q->where('review', 'like', "%{$filters['search']}%")
                    ->orWhereHas('product', function ($productQuery) use ($filters) {
                        $productQuery->where('name', 'like', "%{$filters['search']}%");
                    })
                    ->orWhereHas('customer', function ($customerQuery) use ($filters) {
                        $customerQuery->where('first_name', 'like', "%{$filters['search']}%")
                            ->orWhere('last_name', 'like', "%{$filters['search']}%");
                    });
            });
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        return $query;
    }

    /**
     * Approve a review.
     */
    public function approveReview(string $reviewId, ?string $reason = null): ProductReview
    {
        return DB::transaction(function () use ($reviewId, $reason) {
            $review = ProductReview::findOrFail($reviewId);

            if ($review->is_approved) {
                throw new \InvalidArgumentException('Review is already approved');
            }

            $review->update([
                'is_approved' => true,
                'approved_at' => now(),
                'approved_by' => auth()->id(),
            ]);

            $this->loggingService->logInfo('Review approved', [
                'review_id' => $review->id,
                'product_id' => $review->product_id,
                'customer_id' => $review->customer_id,
                'reason' => $reason,
                'approved_by' => auth()->id(),
            ]);

            return $review->fresh();
        });
    }

    /**
     * Reject/disapprove a review.
     */
    public function rejectReview(string $reviewId, ?string $reason = null): ProductReview
    {
        return DB::transaction(function () use ($reviewId, $reason) {
            $review = ProductReview::findOrFail($reviewId);

            $review->update([
                'is_approved' => false,
                'approved_at' => null,
                'approved_by' => null,
            ]);

            $this->loggingService->logInfo('Review rejected', [
                'review_id' => $review->id,
                'product_id' => $review->product_id,
                'customer_id' => $review->customer_id,
                'reason' => $reason,
                'rejected_by' => auth()->id(),
            ]);

            return $review->fresh();
        });
    }

    /**
     * Delete a review.
     */
    public function deleteReview(string $reviewId, ?string $reason = null): bool
    {
        return DB::transaction(function () use ($reviewId, $reason) {
            $review = ProductReview::findOrFail($reviewId);

            $reviewData = $review->toArray();
            $review->delete();

            $this->loggingService->logInfo('Review deleted', [
                'review_id' => $reviewId,
                'review_data' => $reviewData,
                'reason' => $reason,
                'deleted_by' => auth()->id(),
            ]);

            return true;
        });
    }

    /**
     * Get comprehensive review analytics.
     */
    public function getReviewAnalytics(array $filters = []): array
    {
        $query = ProductReview::query();

        // Apply filters
        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $reviews = $query->get();

        return [
            'summary' => [
                'total_reviews' => $reviews->count(),
                'approved_reviews' => $reviews->where('is_approved', true)->count(),
                'pending_reviews' => $reviews->where('is_approved', false)->count(),
                'verified_purchase_reviews' => $reviews->where('is_verified_purchase', true)->count(),
                'reviews_with_images' => $reviews->filter(fn ($r) => ! empty($r->images))->count(),
                'average_rating' => round($reviews->avg('rating'), 2),
            ],
            'rating_distribution' => $this->getRatingDistribution($reviews),
            'trends' => $this->getReviewTrends($filters),
            'top_reviewed_products' => $this->getTopReviewedProducts($filters),
            'review_quality_metrics' => $this->getReviewQualityMetrics($reviews),
            'fraud_indicators' => $this->getFraudIndicators($filters),
        ];
    }

    /**
     * Detect fake reviews using various algorithms.
     */
    public function detectFakeReviews(array $filters = []): array
    {
        $suspiciousReviews = [];

        // Algorithm 1: Multiple reviews from same IP/device (would need additional tracking)
        // Algorithm 2: Reviews with similar text patterns
        $textSimilarityReviews = $this->detectTextSimilarityFraud($filters);
        if (! empty($textSimilarityReviews)) {
            $suspiciousReviews['text_similarity'] = $textSimilarityReviews;
        }

        // Algorithm 3: Rapid review posting patterns
        $rapidPostingReviews = $this->detectRapidPostingFraud($filters);
        if (! empty($rapidPostingReviews)) {
            $suspiciousReviews['rapid_posting'] = $rapidPostingReviews;
        }

        // Algorithm 4: Unusual rating patterns
        $ratingPatternReviews = $this->detectRatingPatternFraud($filters);
        if (! empty($ratingPatternReviews)) {
            $suspiciousReviews['rating_patterns'] = $ratingPatternReviews;
        }

        // Algorithm 5: Reviews from unverified purchases with high ratings
        $unverifiedHighRatings = $this->detectUnverifiedHighRatingFraud($filters);
        if (! empty($unverifiedHighRatings)) {
            $suspiciousReviews['unverified_high_ratings'] = $unverifiedHighRatings;
        }

        return [
            'total_suspicious' => collect($suspiciousReviews)->flatten()->count(),
            'fraud_categories' => $suspiciousReviews,
            'recommendations' => $this->getFraudPreventionRecommendations($suspiciousReviews),
        ];
    }

    /**
     * Get business reputation summary.
     */
    public function getBusinessReputationSummary(string $businessId): array
    {
        $productReviews = ProductReview::where('business_id', $businessId)
            ->where('is_approved', true)
            ->get();

        $deliveryRatings = Rating::where('rateable_type', 'business')
            ->where('rateable_id', $businessId)
            ->get();

        return [
            'product_reviews' => [
                'total_count' => $productReviews->count(),
                'average_rating' => round($productReviews->avg('rating'), 2),
                'rating_distribution' => $this->getRatingDistribution($productReviews),
                'verified_percentage' => $productReviews->count() > 0
                    ? round(($productReviews->where('is_verified_purchase', true)->count() / $productReviews->count()) * 100, 2)
                    : 0,
            ],
            'delivery_ratings' => [
                'total_count' => $deliveryRatings->count(),
                'average_rating' => round($deliveryRatings->avg('rating'), 2),
                'rating_distribution' => $this->getRatingDistribution($deliveryRatings),
            ],
            'overall_reputation_score' => $this->calculateOverallReputationScore($productReviews, $deliveryRatings),
            'reputation_trend' => $this->getReputationTrend($businessId),
        ];
    }

    /**
     * Bulk operations for reviews.
     */
    public function bulkReviewOperations(array $reviewIds, string $action, array $options = []): array
    {
        $results = ['success' => [], 'failed' => []];

        DB::transaction(function () use ($reviewIds, $action, $options, &$results) {
            foreach ($reviewIds as $reviewId) {
                try {
                    switch ($action) {
                        case 'approve':
                            $this->approveReview($reviewId, $options['reason'] ?? null);
                            break;
                        case 'reject':
                            $this->rejectReview($reviewId, $options['reason'] ?? null);
                            break;
                        case 'delete':
                            $this->deleteReview($reviewId, $options['reason'] ?? null);
                            break;
                        default:
                            throw new \InvalidArgumentException("Invalid action: {$action}");
                    }

                    $results['success'][] = $reviewId;

                } catch (\Exception $e) {
                    $results['failed'][] = [
                        'review_id' => $reviewId,
                        'error' => $e->getMessage(),
                    ];

                    $this->loggingService->logError('Bulk review operation failed', $e, [
                        'review_id' => $reviewId,
                        'action' => $action,
                    ]);
                }
            }
        });

        return $results;
    }

    /**
     * Helper methods for analytics and fraud detection.
     */
    private function getRatingDistribution($reviews): array
    {
        $distribution = $reviews->groupBy('rating')->map->count()->toArray();

        // Fill missing ratings with 0
        for ($i = 1; $i <= 5; $i++) {
            if (! isset($distribution[$i])) {
                $distribution[$i] = 0;
            }
        }
        ksort($distribution);

        return $distribution;
    }

    private function getReviewTrends(array $filters): array
    {
        // This would typically analyze review trends over time
        return [
            'daily_reviews' => [],
            'weekly_reviews' => [],
            'monthly_reviews' => [],
            'rating_trends' => [],
        ];
    }

    private function getTopReviewedProducts(array $filters): array
    {
        $query = ProductReview::with('product:id,name')
            ->where('is_approved', true);

        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        return $query->select('product_id', DB::raw('COUNT(*) as review_count'), DB::raw('AVG(rating) as avg_rating'))
            ->groupBy('product_id')
            ->orderBy('review_count', 'desc')
            ->limit(10)
            ->get()
            ->toArray();
    }

    private function getReviewQualityMetrics($reviews): array
    {
        $totalReviews = $reviews->count();

        if ($totalReviews === 0) {
            return [
                'average_review_length' => 0,
                'reviews_with_text_percentage' => 0,
                'detailed_reviews_percentage' => 0,
                'quality_score' => 0,
            ];
        }

        $reviewsWithText = $reviews->filter(fn ($r) => ! empty($r->review));
        $detailedReviews = $reviews->filter(fn ($r) => ! empty($r->review) && strlen($r->review) > 50);

        return [
            'average_review_length' => round($reviewsWithText->avg(fn ($r) => strlen($r->review ?? '')), 2),
            'reviews_with_text_percentage' => round(($reviewsWithText->count() / $totalReviews) * 100, 2),
            'detailed_reviews_percentage' => round(($detailedReviews->count() / $totalReviews) * 100, 2),
            'quality_score' => $this->calculateQualityScore($reviews),
        ];
    }

    private function getFraudIndicators(array $filters): array
    {
        return [
            'suspicious_patterns' => 0,
            'flagged_reviews' => 0,
            'fraud_score' => 0,
        ];
    }

    private function detectTextSimilarityFraud(array $filters): array
    {
        // This would implement text similarity detection algorithms
        // For now, return empty array
        return [];
    }

    private function detectRapidPostingFraud(array $filters): array
    {
        $query = ProductReview::select(['customer_id', DB::raw('COUNT(*) as review_count')])
            ->where('created_at', '>=', now()->subDays(1))
            ->groupBy('customer_id')
            ->having('review_count', '>', 5);

        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        return $query->get()->toArray();
    }

    private function detectRatingPatternFraud(array $filters): array
    {
        // Detect customers who only give extreme ratings (1 or 5)
        $query = ProductReview::select([
            'customer_id',
            DB::raw('COUNT(*) as total_reviews'),
            DB::raw('SUM(CASE WHEN rating IN (1,5) THEN 1 ELSE 0 END) as extreme_ratings'),
        ])
            ->groupBy('customer_id')
            ->havingRaw('total_reviews >= 3 AND extreme_ratings / total_reviews > 0.8');

        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        return $query->get()->toArray();
    }

    private function detectUnverifiedHighRatingFraud(array $filters): array
    {
        $query = ProductReview::where('is_verified_purchase', false)
            ->where('rating', '>=', 4)
            ->where('is_approved', true);

        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        return $query->get()->toArray();
    }

    private function getFraudPreventionRecommendations(array $suspiciousReviews): array
    {
        $recommendations = [];

        if (isset($suspiciousReviews['rapid_posting'])) {
            $recommendations[] = 'Implement rate limiting for review submissions';
        }

        if (isset($suspiciousReviews['rating_patterns'])) {
            $recommendations[] = 'Monitor customers with extreme rating patterns';
        }

        if (isset($suspiciousReviews['unverified_high_ratings'])) {
            $recommendations[] = 'Require purchase verification for high ratings';
        }

        return $recommendations;
    }

    private function calculateOverallReputationScore($productReviews, $deliveryRatings): float
    {
        $productScore = $productReviews->count() > 0 ? $productReviews->avg('rating') : 0;
        $deliveryScore = $deliveryRatings->count() > 0 ? $deliveryRatings->avg('rating') : 0;

        // Weighted average (70% product reviews, 30% delivery ratings)
        return round(($productScore * 0.7) + ($deliveryScore * 0.3), 2);
    }

    private function getReputationTrend(string $businessId): array
    {
        // This would analyze reputation trends over time
        return [
            'trend_direction' => 'stable', // 'improving', 'declining', 'stable'
            'monthly_scores' => [],
            'improvement_areas' => [],
        ];
    }

    private function calculateQualityScore($reviews): float
    {
        if ($reviews->count() === 0) {
            return 0;
        }

        $verifiedPercentage = ($reviews->where('is_verified_purchase', true)->count() / $reviews->count()) * 100;
        $textPercentage = ($reviews->filter(fn ($r) => ! empty($r->review))->count() / $reviews->count()) * 100;
        $imagePercentage = ($reviews->filter(fn ($r) => ! empty($r->images))->count() / $reviews->count()) * 100;

        // Weighted quality score
        return round(($verifiedPercentage * 0.5) + ($textPercentage * 0.3) + ($imagePercentage * 0.2), 2);
    }
}
