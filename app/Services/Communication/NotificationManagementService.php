<?php

declare(strict_types=1);

namespace App\Services\Communication;

use App\Enums\User\NotificationChannel;
use App\Models\User\Notification;
use App\Models\User\NotificationType;
use App\Models\User\User;
use App\Services\System\LoggingService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class NotificationManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get comprehensive notification statistics.
     */
    public function getNotificationStatistics(): array
    {
        return [
            'total_notifications' => Notification::count(),
            'notifications_by_channel' => Notification::selectRaw('channel, COUNT(*) as count')
                ->groupBy('channel')
                ->pluck('count', 'channel'),
            'notifications_by_type' => Notification::selectRaw('type, COUNT(*) as count')
                ->groupBy('type')
                ->pluck('count', 'type'),
            'read_notifications' => Notification::whereNotNull('read_at')->count(),
            'unread_notifications' => Notification::whereNull('read_at')->count(),
            'sent_notifications' => Notification::whereNotNull('sent_at')->count(),
            'pending_notifications' => Notification::whereNull('sent_at')->count(),
            'notifications_today' => Notification::whereDate('created_at', today())->count(),
            'notifications_this_week' => Notification::whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek(),
            ])->count(),
            'notifications_this_month' => Notification::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
            'delivery_rate' => $this->calculateDeliveryRate(),
            'read_rate' => $this->calculateReadRate(),
        ];
    }

    /**
     * Get notification analytics.
     */
    public function getNotificationAnalytics(array $filters = []): array
    {
        $query = Notification::query();

        // Apply date filters
        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $notifications = $query->get();

        return [
            'total_notifications' => $notifications->count(),
            'delivery_rate' => $this->calculateDeliveryRate($notifications),
            'read_rate' => $this->calculateReadRate($notifications),
            'channel_performance' => $this->getChannelPerformance($notifications),
            'type_breakdown' => $this->getTypeBreakdown($notifications),
            'daily_breakdown' => $this->getDailyNotificationBreakdown($notifications),
            'hourly_distribution' => $this->getHourlyNotificationDistribution($notifications),
            'engagement_metrics' => $this->getEngagementMetrics($notifications),
        ];
    }

    /**
     * Get notification templates management.
     */
    public function getNotificationTemplates(): array
    {
        $templates = NotificationType::with(['notifications' => function ($query) {
            $query->selectRaw('type, COUNT(*) as usage_count')
                ->groupBy('type');
        }])->get();

        return [
            'templates' => $templates,
            'total_templates' => $templates->count(),
            'active_templates' => $templates->where('is_active', true)->count(),
            'template_usage' => $templates->mapWithKeys(function ($template) {
                return [$template->name => $template->notifications->sum('usage_count')];
            }),
            'most_used_templates' => $templates->sortByDesc(function ($template) {
                return $template->notifications->sum('usage_count');
            })->take(10)->values(),
        ];
    }

    /**
     * Send broadcast notification.
     */
    public function sendBroadcastNotification(array $data): array
    {
        return DB::transaction(function () use ($data) {
            $recipients = $this->getRecipients($data['target_audience'], $data['tenant_ids'] ?? []);

            $results = [
                'total_recipients' => $recipients->count(),
                'sent' => 0,
                'failed' => 0,
                'details' => [],
            ];

            foreach ($recipients as $user) {
                try {
                    // Create notification record
                    $notification = Notification::create([
                        'notifiable_type' => User::class,
                        'notifiable_id' => $user->id,
                        'type' => $data['type'],
                        'title' => $data['title'],
                        'message' => $data['message'],
                        'channel' => NotificationChannel::DATABASE,
                        'data' => [
                            'priority' => $data['priority'],
                            'channels' => $data['channels'],
                            'broadcast_id' => uniqid('broadcast_'),
                        ],
                        'sent_at' => now(),
                    ]);

                    // Send via specified channels
                    foreach ($data['channels'] as $channel) {
                        $this->sendViaChannel($user, $notification, $channel);
                    }

                    $results['sent']++;
                    $results['details'][$user->id] = 'sent';

                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['details'][$user->id] = 'error: '.$e->getMessage();
                }
            }

            $this->loggingService->logInfo('Broadcast notification sent', [
                'type' => $data['type'],
                'title' => $data['title'],
                'target_audience' => $data['target_audience'],
                'channels' => $data['channels'],
                'total_recipients' => $results['total_recipients'],
                'sent' => $results['sent'],
                'failed' => $results['failed'],
                'admin_id' => auth()->id(),
            ]);

            return $results;
        });
    }

    /**
     * Get notification delivery report.
     */
    public function getDeliveryReport(array $filters = []): array
    {
        $query = Notification::query();

        // Apply filters
        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['channel'])) {
            $query->where('channel', $filters['channel']);
        }

        $notifications = $query->get();

        return [
            'summary' => [
                'total_notifications' => $notifications->count(),
                'delivered' => $notifications->whereNotNull('sent_at')->count(),
                'pending' => $notifications->whereNull('sent_at')->count(),
                'read' => $notifications->whereNotNull('read_at')->count(),
                'delivery_rate' => $this->calculateDeliveryRate($notifications),
                'read_rate' => $this->calculateReadRate($notifications),
            ],
            'channel_breakdown' => $this->getChannelDeliveryBreakdown($notifications),
            'type_breakdown' => $this->getTypeDeliveryBreakdown($notifications),
            'failed_deliveries' => $this->getFailedDeliveries($notifications),
            'top_performing_types' => $this->getTopPerformingTypes($notifications),
        ];
    }

    /**
     * Bulk update notification status.
     */
    public function bulkUpdateNotificationStatus(array $notificationIds, string $action): array
    {
        $results = ['updated' => 0, 'failed' => 0, 'details' => []];

        DB::transaction(function () use ($notificationIds, $action, &$results) {
            foreach ($notificationIds as $notificationId) {
                try {
                    $notification = Notification::findOrFail($notificationId);

                    switch ($action) {
                        case 'mark_read':
                            if (! $notification->read_at) {
                                $notification->update(['read_at' => now()]);
                            }
                            break;
                        case 'mark_unread':
                            $notification->update(['read_at' => null]);
                            break;
                        case 'mark_sent':
                            if (! $notification->sent_at) {
                                $notification->update(['sent_at' => now()]);
                            }
                            break;
                        case 'delete':
                            $notification->delete();
                            break;
                        default:
                            throw new \InvalidArgumentException("Invalid action: {$action}");
                    }

                    $results['updated']++;
                    $results['details'][$notificationId] = 'success';

                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['details'][$notificationId] = 'error: '.$e->getMessage();
                }
            }
        });

        return $results;
    }

    /**
     * Export notifications data.
     */
    public function exportNotifications(array $filters = [], string $format = 'csv'): string
    {
        $query = Notification::with(['notifiable:id,first_name,last_name,email']);

        // Apply filters
        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['channel'])) {
            $query->where('channel', $filters['channel']);
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $notifications = $query->orderBy('created_at', 'desc')->get();

        // Generate filename
        $filename = 'notifications_export_'.now()->format('Y_m_d_H_i_s').'.'.$format;
        $filepath = storage_path('app/exports/'.$filename);

        // Ensure directory exists
        if (! file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        if ($format === 'csv') {
            $this->exportToCsv($notifications, $filepath);
        } elseif ($format === 'json') {
            $this->exportToJson($notifications, $filepath);
        }

        return $filename;
    }

    /**
     * Calculate delivery rate.
     */
    private function calculateDeliveryRate(?Collection $notifications = null): float
    {
        $notifications = $notifications ?? Notification::all();
        $total = $notifications->count();

        if ($total === 0) {
            return 0;
        }

        $delivered = $notifications->whereNotNull('sent_at')->count();

        return round(($delivered / $total) * 100, 2);
    }

    /**
     * Calculate read rate.
     */
    private function calculateReadRate(?Collection $notifications = null): float
    {
        $notifications = $notifications ?? Notification::all();
        $total = $notifications->count();

        if ($total === 0) {
            return 0;
        }

        $read = $notifications->whereNotNull('read_at')->count();

        return round(($read / $total) * 100, 2);
    }

    /**
     * Get channel performance breakdown.
     */
    private function getChannelPerformance(Collection $notifications): array
    {
        return $notifications->groupBy('channel')->map(function ($channelNotifications) {
            return [
                'total' => $channelNotifications->count(),
                'delivered' => $channelNotifications->whereNotNull('sent_at')->count(),
                'read' => $channelNotifications->whereNotNull('read_at')->count(),
                'delivery_rate' => $this->calculateDeliveryRate($channelNotifications),
                'read_rate' => $this->calculateReadRate($channelNotifications),
            ];
        })->toArray();
    }

    /**
     * Get type breakdown.
     */
    private function getTypeBreakdown(Collection $notifications): array
    {
        return $notifications->groupBy('type')->map(function ($typeNotifications) {
            return [
                'count' => $typeNotifications->count(),
                'read_rate' => $this->calculateReadRate($typeNotifications),
            ];
        })->toArray();
    }

    /**
     * Get daily notification breakdown.
     */
    private function getDailyNotificationBreakdown(Collection $notifications): array
    {
        return $notifications->groupBy(function ($notification) {
            return $notification->created_at->format('Y-m-d');
        })->map(function ($dayNotifications) {
            return [
                'count' => $dayNotifications->count(),
                'delivered' => $dayNotifications->whereNotNull('sent_at')->count(),
                'read' => $dayNotifications->whereNotNull('read_at')->count(),
            ];
        })->toArray();
    }

    /**
     * Get hourly notification distribution.
     */
    private function getHourlyNotificationDistribution(Collection $notifications): array
    {
        return $notifications->groupBy(function ($notification) {
            return $notification->created_at->format('H');
        })->map(fn ($hourNotifications) => $hourNotifications->count())->toArray();
    }

    /**
     * Get engagement metrics.
     */
    private function getEngagementMetrics(Collection $notifications): array
    {
        $totalSent = $notifications->whereNotNull('sent_at');
        $totalRead = $notifications->whereNotNull('read_at');

        return [
            'total_sent' => $totalSent->count(),
            'total_read' => $totalRead->count(),
            'engagement_rate' => $totalSent->count() > 0 ?
                round(($totalRead->count() / $totalSent->count()) * 100, 2) : 0,
            'average_read_time' => $this->calculateAverageReadTime($totalRead),
        ];
    }

    /**
     * Get recipients based on target audience.
     */
    private function getRecipients(string $targetAudience, array $tenantIds = []): Collection
    {
        $query = User::query();

        switch ($targetAudience) {
            case 'businesses':
                $query->whereHas('roles', function ($q) {
                    $q->where('name', 'business_owner');
                });
                break;
            case 'providers':
                $query->whereHas('roles', function ($q) {
                    $q->where('name', 'delivery_provider');
                });
                break;
            case 'customers':
                $query->whereHas('roles', function ($q) {
                    $q->where('name', 'customer');
                });
                break;
            case 'all':
                // No additional filtering
                break;
        }

        if (! empty($tenantIds)) {
            $query->whereIn('tenant_id', $tenantIds);
        }

        return $query->get();
    }

    /**
     * Send notification via specific channel.
     */
    private function sendViaChannel(User $user, Notification $notification, string $channel): void
    {
        // This would integrate with actual notification channels
        // For now, just log the attempt
        $this->loggingService->logInfo('Notification sent via channel', [
            'user_id' => $user->id,
            'notification_id' => $notification->id,
            'channel' => $channel,
        ]);
    }

    /**
     * Get channel delivery breakdown.
     */
    private function getChannelDeliveryBreakdown(Collection $notifications): array
    {
        return $notifications->groupBy('channel')->map(function ($channelNotifications) {
            return [
                'total' => $channelNotifications->count(),
                'delivered' => $channelNotifications->whereNotNull('sent_at')->count(),
                'failed' => $channelNotifications->whereNull('sent_at')->count(),
            ];
        })->toArray();
    }

    /**
     * Get type delivery breakdown.
     */
    private function getTypeDeliveryBreakdown(Collection $notifications): array
    {
        return $notifications->groupBy('type')->map(function ($typeNotifications) {
            return [
                'total' => $typeNotifications->count(),
                'delivered' => $typeNotifications->whereNotNull('sent_at')->count(),
                'read' => $typeNotifications->whereNotNull('read_at')->count(),
            ];
        })->toArray();
    }

    /**
     * Get failed deliveries.
     */
    private function getFailedDeliveries(Collection $notifications): Collection
    {
        return $notifications->whereNull('sent_at')
            ->where('created_at', '<', now()->subHours(1))
            ->take(20);
    }

    /**
     * Get top performing notification types.
     */
    private function getTopPerformingTypes(Collection $notifications): array
    {
        return $notifications->groupBy('type')
            ->map(function ($typeNotifications) {
                return [
                    'count' => $typeNotifications->count(),
                    'read_rate' => $this->calculateReadRate($typeNotifications),
                ];
            })
            ->sortByDesc('read_rate')
            ->take(10)
            ->toArray();
    }

    /**
     * Calculate average read time.
     */
    private function calculateAverageReadTime(Collection $readNotifications): float
    {
        if ($readNotifications->isEmpty()) {
            return 0;
        }

        $totalMinutes = $readNotifications->sum(function ($notification) {
            return $notification->created_at->diffInMinutes($notification->read_at);
        });

        return round($totalMinutes / $readNotifications->count(), 2);
    }

    /**
     * Export notifications to CSV.
     */
    private function exportToCsv(Collection $notifications, string $filepath): void
    {
        $file = fopen($filepath, 'w');

        // Write header
        fputcsv($file, [
            'Notification ID', 'Type', 'Title', 'Recipient', 'Channel',
            'Sent At', 'Read At', 'Created At',
        ]);

        // Write data
        foreach ($notifications as $notification) {
            fputcsv($file, [
                $notification->id,
                $notification->type,
                $notification->title ?? 'N/A',
                $notification->notifiable ?
                    $notification->notifiable->first_name.' '.$notification->notifiable->last_name : 'N/A',
                $notification->channel?->value ?? 'N/A',
                $notification->sent_at?->toISOString() ?? 'N/A',
                $notification->read_at?->toISOString() ?? 'N/A',
                $notification->created_at->toISOString(),
            ]);
        }

        fclose($file);
    }

    /**
     * Export notifications to JSON.
     */
    private function exportToJson(Collection $notifications, string $filepath): void
    {
        $data = $notifications->map(function ($notification) {
            return [
                'notification_id' => $notification->id,
                'type' => $notification->type,
                'title' => $notification->title,
                'message' => $notification->message,
                'recipient' => $notification->notifiable ? [
                    'name' => $notification->notifiable->first_name.' '.$notification->notifiable->last_name,
                    'email' => $notification->notifiable->email,
                ] : null,
                'channel' => $notification->channel?->value,
                'sent_at' => $notification->sent_at?->toISOString(),
                'read_at' => $notification->read_at?->toISOString(),
                'created_at' => $notification->created_at->toISOString(),
            ];
        });

        file_put_contents($filepath, json_encode($data, JSON_PRETTY_PRINT));
    }

    /**
     * Get notification delivery status.
     */
    public function getNotificationDeliveryStatus(?array $notificationIds = null, ?string $dateFrom = null, ?string $dateTo = null): array
    {
        $query = Notification::query();

        if ($notificationIds) {
            $query->whereIn('id', $notificationIds);
        }

        if ($dateFrom) {
            $query->where('created_at', '>=', $dateFrom);
        }

        if ($dateTo) {
            $query->where('created_at', '<=', $dateTo);
        }

        $notifications = $query->get();

        return [
            'total_notifications' => $notifications->count(),
            'delivered' => $notifications->whereNotNull('sent_at')->count(),
            'pending' => $notifications->whereNull('sent_at')->count(),
            'read' => $notifications->whereNotNull('read_at')->count(),
            'failed' => $notifications->whereNull('sent_at')
                ->where('created_at', '<', now()->subHours(1))
                ->count(),
            'delivery_rate' => $this->calculateDeliveryRate($notifications),
            'read_rate' => $this->calculateReadRate($notifications),
            'notifications' => $notifications->map(function ($notification) {
                return [
                    'id' => $notification->id,
                    'type' => $notification->type,
                    'channel' => $notification->channel?->value,
                    'sent_at' => $notification->sent_at,
                    'read_at' => $notification->read_at,
                    'status' => $notification->sent_at ? 'delivered' : 'pending',
                ];
            }),
        ];
    }

    /**
     * Resend failed notifications.
     */
    public function resendFailedNotifications(array $notificationIds, ?array $channels = null): array
    {
        $results = ['resent' => 0, 'failed' => 0, 'details' => []];

        DB::transaction(function () use ($notificationIds, $channels, &$results) {
            foreach ($notificationIds as $notificationId) {
                try {
                    $notification = Notification::findOrFail($notificationId);

                    // Only resend if not already sent
                    if ($notification->sent_at) {
                        $results['failed']++;
                        $results['details'][$notificationId] = 'already sent';

                        continue;
                    }

                    // Get user
                    $user = $notification->notifiable;
                    if (! $user) {
                        $results['failed']++;
                        $results['details'][$notificationId] = 'user not found';

                        continue;
                    }

                    // Use specified channels or default
                    $resendChannels = $channels ?? ['database', 'email'];

                    // Resend via specified channels
                    foreach ($resendChannels as $channel) {
                        $this->sendViaChannel($user, $notification, $channel);
                    }

                    // Update sent timestamp
                    $notification->update(['sent_at' => now()]);

                    $results['resent']++;
                    $results['details'][$notificationId] = 'resent';

                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['details'][$notificationId] = 'error: '.$e->getMessage();
                }
            }
        });

        return $results;
    }

    /**
     * Create notification template.
     */
    public function createNotificationTemplate(array $data): NotificationType
    {
        return NotificationType::create([
            'name' => $data['name'],
            'description' => $data['description'] ?? null,
            'available_channels' => $data['channels'],
            'template' => [
                'title' => $data['title'],
                'message' => $data['message'],
                'variables' => $data['variables'] ?? [],
            ],
            'is_active' => true,
            'priority' => $data['priority'] ?? 2,
            'category' => $data['type'],
        ]);
    }

    /**
     * Update notification template.
     */
    public function updateNotificationTemplate(string $templateId, array $data): NotificationType
    {
        $template = NotificationType::findOrFail($templateId);

        $updateData = array_filter([
            'name' => $data['name'] ?? null,
            'description' => $data['description'] ?? null,
            'available_channels' => $data['channels'] ?? null,
            'is_active' => $data['is_active'] ?? null,
            'priority' => $data['priority'] ?? null,
        ], fn ($value) => $value !== null);

        if (isset($data['title']) || isset($data['message']) || isset($data['variables'])) {
            $templateData = $template->template ?? [];

            if (isset($data['title'])) {
                $templateData['title'] = $data['title'];
            }

            if (isset($data['message'])) {
                $templateData['message'] = $data['message'];
            }

            if (isset($data['variables'])) {
                $templateData['variables'] = $data['variables'];
            }

            $updateData['template'] = $templateData;
        }

        $template->update($updateData);

        return $template->fresh() ?? $template;
    }
}
