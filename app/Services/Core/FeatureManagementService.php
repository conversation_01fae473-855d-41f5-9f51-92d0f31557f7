<?php

declare(strict_types=1);

namespace App\Services\Core;

use App\Models\Core\Feature;
use App\Models\Financial\PlanFeature;
use App\Models\Financial\SubscriptionPlan;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class FeatureManagementService
{
    /**
     * Get feature with detailed information.
     */
    public function getFeatureWithDetails(string $featureId): array
    {
        $feature = Feature::with([
            'planFeatures.plan:id,name,slug,target_type',
            'subscriptionPlans:id,name,slug,target_type',
        ])->findOrFail($featureId);

        return [
            'feature' => $feature,
            'usage_statistics' => $this->getFeatureUsageStatistics($feature),
            'plan_associations' => $this->getFeaturePlanAssociations($feature),
            'adoption_metrics' => $this->getFeatureAdoptionMetrics($feature),
        ];
    }

    /**
     * Create new feature.
     */
    public function createFeature(array $data): Feature
    {
        return DB::transaction(function () use ($data) {
            $feature = Feature::create([
                'name' => $data['name'],
                'slug' => $data['slug'] ?? Str::slug($data['name']),
                'description' => $data['description'],
                'target_type' => $data['target_type'],
            ]);

            // Log feature creation
            activity()
                ->performedOn($feature)
                ->withProperties([
                    'feature_data' => $data,
                    'admin_id' => auth()->id(),
                ])
                ->log('feature_created');

            return $feature;
        });
    }

    /**
     * Update feature.
     */
    public function updateFeature(string $featureId, array $data): Feature
    {
        return DB::transaction(function () use ($featureId, $data) {
            $feature = Feature::findOrFail($featureId);
            $originalData = $feature->toArray();

            $feature->update([
                'name' => $data['name'] ?? $feature->name,
                'slug' => $data['slug'] ?? $feature->slug,
                'description' => $data['description'] ?? $feature->description,
                'target_type' => $data['target_type'] ?? $feature->target_type,
            ]);

            // Log feature update
            activity()
                ->performedOn($feature)
                ->withProperties([
                    'original_data' => $originalData,
                    'updated_data' => $data,
                    'admin_id' => auth()->id(),
                ])
                ->log('feature_updated');

            return $feature->fresh();
        });
    }

    /**
     * Delete feature.
     */
    public function deleteFeature(string $featureId): void
    {
        DB::transaction(function () use ($featureId) {
            $feature = Feature::findOrFail($featureId);

            // Check if feature is used in any active plans
            $activePlanUsage = PlanFeature::whereHas('plan', function ($query) {
                $query->where('is_active', true);
            })->where('feature_id', $featureId)->count();

            if ($activePlanUsage > 0) {
                throw new \InvalidArgumentException(
                    "Cannot delete feature that is used in {$activePlanUsage} active plans"
                );
            }

            // Delete plan feature associations
            PlanFeature::where('feature_id', $featureId)->delete();

            // Log feature deletion
            activity()
                ->performedOn($feature)
                ->withProperties([
                    'feature_data' => $feature->toArray(),
                    'admin_id' => auth()->id(),
                ])
                ->log('feature_deleted');

            // Delete the feature
            $feature->delete();
        });
    }

    /**
     * Get feature analytics.
     */
    public function getFeatureAnalytics(string $period, ?string $targetType = null): array
    {
        $periodDates = $this->getPeriodDates($period);

        return [
            'feature_overview' => $this->getFeatureOverviewStats($targetType),
            'adoption_rates' => $this->getFeatureAdoption($targetType),
            'usage_trends' => $this->getFeatureUsageTrends($periodDates, $targetType),
            'plan_distribution' => $this->getFeaturePlanDistribution($targetType),
            'performance_metrics' => $this->getFeaturePerformanceMetrics($periodDates, $targetType),
        ];
    }

    /**
     * Get feature adoption rates.
     */
    public function getFeatureAdoption(?string $targetType = null): array
    {
        $query = Feature::with(['planFeatures.plan.userSubscriptions' => function ($q) {
            $q->where('status', 'active');
        }]);

        if ($targetType) {
            $query->where('target_type', $targetType);
        }

        $features = $query->get();

        return $features->map(function ($feature) {
            $totalSubscriptions = $feature->planFeatures
                ->flatMap(fn ($pf) => $pf->plan->userSubscriptions)
                ->count();

            $totalPossibleSubscriptions = DB::table('user_subscriptions')
                ->where('status', 'active')
                ->count();

            $adoptionRate = $totalPossibleSubscriptions > 0
                ? ($totalSubscriptions / $totalPossibleSubscriptions) * 100
                : 0;

            return [
                'feature' => [
                    'id' => $feature->id,
                    'name' => $feature->name,
                    'slug' => $feature->slug,
                    'target_type' => $feature->target_type,
                ],
                'adoption_rate' => round($adoptionRate, 2),
                'active_subscriptions' => $totalSubscriptions,
                'available_in_plans' => $feature->planFeatures->count(),
            ];
        })->sortByDesc('adoption_rate')->values()->toArray();
    }

    /**
     * Get feature usage by plan.
     */
    public function getFeatureUsageByPlan(): array
    {
        return SubscriptionPlan::with([
            'planFeatures.feature',
            'userSubscriptions' => function ($query) {
                $query->where('status', 'active');
            },
        ])->get()->map(function ($plan) {
            return [
                'plan' => [
                    'id' => $plan->id,
                    'name' => $plan->name,
                    'slug' => $plan->slug,
                    'target_type' => $plan->target_type,
                ],
                'active_subscriptions' => $plan->userSubscriptions->count(),
                'features' => $plan->planFeatures->map(function ($planFeature) {
                    return [
                        'feature' => [
                            'id' => $planFeature->feature->id,
                            'name' => $planFeature->feature->name,
                            'slug' => $planFeature->feature->slug,
                        ],
                        'limit' => $planFeature->limit,
                        'usage_percentage' => $this->calculateFeatureUsage($planFeature),
                    ];
                }),
            ];
        })->toArray();
    }

    /**
     * Bulk update features.
     */
    public function bulkUpdateFeatures(array $featureIds, string $action, array $data = []): array
    {
        return DB::transaction(function () use ($featureIds, $action, $data) {
            $features = Feature::whereIn('id', $featureIds)->get();
            $results = [];

            foreach ($features as $feature) {
                try {
                    switch ($action) {
                        case 'delete':
                            $this->deleteFeature($feature->id);
                            $results[] = ['id' => $feature->id, 'action' => 'deleted', 'status' => 'success'];
                            break;
                        case 'update_target_type':
                            $feature->update(['target_type' => $data['target_type']]);
                            $results[] = ['id' => $feature->id, 'action' => 'updated', 'status' => 'success'];
                            break;
                        default:
                            $results[] = ['id' => $feature->id, 'action' => $action, 'status' => 'failed', 'reason' => 'unknown_action'];
                    }
                } catch (\Exception $e) {
                    $results[] = ['id' => $feature->id, 'action' => $action, 'status' => 'failed', 'reason' => $e->getMessage()];
                }
            }

            // Log bulk operation
            activity()
                ->withProperties([
                    'feature_ids' => $featureIds,
                    'action' => $action,
                    'data' => $data,
                    'results' => $results,
                    'admin_id' => auth()->id(),
                ])
                ->log('features_bulk_updated');

            return $results;
        });
    }

    /**
     * Get feature statistics.
     */
    public function getFeatureStatistics(): array
    {
        return [
            'total_features' => Feature::count(),
            'business_features' => Feature::where('target_type', 'business')->count(),
            'provider_features' => Feature::where('target_type', 'provider')->count(),
            'shared_features' => Feature::where('target_type', 'both')->count(),
            'features_in_use' => Feature::whereHas('planFeatures')->count(),
            'unused_features' => Feature::whereDoesntHave('planFeatures')->count(),
            'most_adopted_features' => $this->getMostAdoptedFeatures(5),
            'least_adopted_features' => $this->getLeastAdoptedFeatures(5),
        ];
    }

    /**
     * Private helper methods.
     */
    private function getFeatureUsageStatistics(Feature $feature): array
    {
        $planCount = $feature->planFeatures->count();
        $activeSubscriptions = $feature->planFeatures
            ->flatMap(fn ($pf) => $pf->plan->userSubscriptions)
            ->where('status', 'active')
            ->count();

        return [
            'plans_using_feature' => $planCount,
            'active_subscriptions' => $activeSubscriptions,
            'total_usage_instances' => $this->getTotalFeatureUsage($feature->id),
            'average_usage_per_subscription' => $activeSubscriptions > 0
                ? $this->getTotalFeatureUsage($feature->id) / $activeSubscriptions
                : 0,
        ];
    }

    private function getFeaturePlanAssociations(Feature $feature): array
    {
        return $feature->planFeatures->map(function ($planFeature) {
            return [
                'plan' => [
                    'id' => $planFeature->plan->id,
                    'name' => $planFeature->plan->name,
                    'slug' => $planFeature->plan->slug,
                    'target_type' => $planFeature->plan->target_type,
                ],
                'limit' => $planFeature->limit,
                'active_subscriptions' => $planFeature->plan->userSubscriptions()
                    ->where('status', 'active')->count(),
            ];
        })->toArray();
    }

    private function getFeatureAdoptionMetrics(Feature $feature): array
    {
        // This would integrate with actual usage tracking
        return [
            'adoption_rate' => 0,
            'usage_growth' => 0,
            'user_satisfaction' => 0,
            'feature_requests' => 0,
        ];
    }

    private function getFeatureOverviewStats(?string $targetType): array
    {
        $query = Feature::query();

        if ($targetType) {
            $query->where('target_type', $targetType);
        }

        return [
            'total_features' => $query->count(),
            'features_in_use' => $query->whereHas('planFeatures')->count(),
            'unused_features' => $query->whereDoesntHave('planFeatures')->count(),
        ];
    }

    private function getFeatureUsageTrends(array $periodDates, ?string $targetType): array
    {
        // This would track feature usage over time
        return [
            'daily_usage' => [],
            'feature_adoption_trend' => [],
            'usage_patterns' => [],
        ];
    }

    private function getFeaturePlanDistribution(?string $targetType): array
    {
        // This would show how features are distributed across plans
        return [
            'features_per_plan' => [],
            'plan_feature_matrix' => [],
        ];
    }

    private function getFeaturePerformanceMetrics(array $periodDates, ?string $targetType): array
    {
        // This would calculate feature performance metrics
        return [
            'top_performing_features' => [],
            'underutilized_features' => [],
            'feature_satisfaction_scores' => [],
        ];
    }

    private function calculateFeatureUsage($planFeature): float
    {
        // This would calculate actual usage vs limit
        return 0.0;
    }

    private function getTotalFeatureUsage(string $featureId): int
    {
        // This would integrate with usage tracking system
        return 0;
    }

    private function getMostAdoptedFeatures(int $limit): array
    {
        return Feature::withCount(['planFeatures as plan_count'])
            ->orderByDesc('plan_count')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    private function getLeastAdoptedFeatures(int $limit): array
    {
        return Feature::withCount(['planFeatures as plan_count'])
            ->orderBy('plan_count')
            ->limit($limit)
            ->get()
            ->toArray();
    }

    private function getPeriodDates(string $period): array
    {
        $now = now();

        return match ($period) {
            'today' => [
                'start' => $now->copy()->startOfDay(),
                'end' => $now->copy()->endOfDay(),
            ],
            'week' => [
                'start' => $now->copy()->startOfWeek(),
                'end' => $now->copy()->endOfWeek(),
            ],
            'month' => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
            'quarter' => [
                'start' => $now->copy()->startOfQuarter(),
                'end' => $now->copy()->endOfQuarter(),
            ],
            'year' => [
                'start' => $now->copy()->startOfYear(),
                'end' => $now->copy()->endOfYear(),
            ],
            default => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
        };
    }
}
