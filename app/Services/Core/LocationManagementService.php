<?php

declare(strict_types=1);

namespace App\Services\Core;

use App\Models\Core\City;
use App\Models\Core\Country;
use App\Models\Core\State;
use App\Services\System\LoggingService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class LocationManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get location hierarchy with statistics.
     */
    public function getLocationHierarchy(): array
    {
        $countries = Country::with(['states.cities'])
            ->withCount(['states', 'addresses'])
            ->orderBy('name')
            ->get();

        return [
            'countries' => $countries,
            'statistics' => $this->getLocationStatistics(),
        ];
    }

    /**
     * Get country with detailed information.
     */
    public function getCountryWithDetails(string $countryId): array
    {
        $country = Country::with([
            'states.cities',
            'addresses',
        ])->findOrFail($countryId);

        return [
            'country' => $country,
            'statistics' => $this->getCountryStatistics($country),
            'states_summary' => $this->getStatesSummary($country),
        ];
    }

    /**
     * Create new country.
     */
    public function createCountry(array $data): Country
    {
        return DB::transaction(function () use ($data) {
            $country = Country::create([
                'name' => $data['name'],
                'code' => strtoupper($data['code']),
                'currency_code' => strtoupper($data['currency_code']),
                'phone_code' => $data['phone_code'],
                'timezone' => $data['timezone'] ?? null,
                'latitude' => $data['latitude'] ?? null,
                'longitude' => $data['longitude'] ?? null,
                'is_active' => $data['is_active'] ?? true,
            ]);

            // Log country creation
            activity()
                ->performedOn($country)
                ->withProperties([
                    'country_data' => $data,
                    'admin_id' => auth()->id(),
                ])
                ->log('country_created');

            $this->loggingService->logInfo('Country created', [
                'country_id' => $country->id,
                'name' => $country->name,
                'code' => $country->code,
                'admin_id' => auth()->id(),
            ]);

            return $country;
        });
    }

    /**
     * Update country.
     */
    public function updateCountry(string $countryId, array $data): Country
    {
        return DB::transaction(function () use ($countryId, $data) {
            $country = Country::findOrFail($countryId);

            $country->update(array_filter([
                'name' => $data['name'] ?? $country->name,
                'code' => isset($data['code']) ? strtoupper($data['code']) : $country->code,
                'currency_code' => isset($data['currency_code']) ? strtoupper($data['currency_code']) : $country->currency_code,
                'phone_code' => $data['phone_code'] ?? $country->phone_code,
                'timezone' => $data['timezone'] ?? $country->timezone,
                'latitude' => $data['latitude'] ?? $country->latitude,
                'longitude' => $data['longitude'] ?? $country->longitude,
                'is_active' => $data['is_active'] ?? $country->is_active,
            ]));

            // Log country update
            activity()
                ->performedOn($country)
                ->withProperties([
                    'updated_data' => $data,
                    'admin_id' => auth()->id(),
                ])
                ->log('country_updated');

            return $country->fresh();
        });
    }

    /**
     * Create new state.
     */
    public function createState(array $data): State
    {
        return DB::transaction(function () use ($data) {
            $state = State::create([
                'country_id' => $data['country_id'],
                'name' => $data['name'],
                'code' => strtoupper($data['code']),
                'timezone' => $data['timezone'] ?? null,
                'latitude' => $data['latitude'] ?? null,
                'longitude' => $data['longitude'] ?? null,
                'is_active' => $data['is_active'] ?? true,
            ]);

            // Log state creation
            activity()
                ->performedOn($state)
                ->withProperties([
                    'state_data' => $data,
                    'admin_id' => auth()->id(),
                ])
                ->log('state_created');

            return $state;
        });
    }

    /**
     * Update state.
     */
    public function updateState(string $stateId, array $data): State
    {
        return DB::transaction(function () use ($stateId, $data) {
            $state = State::findOrFail($stateId);

            $state->update(array_filter([
                'country_id' => $data['country_id'] ?? $state->country_id,
                'name' => $data['name'] ?? $state->name,
                'code' => isset($data['code']) ? strtoupper($data['code']) : $state->code,
                'timezone' => $data['timezone'] ?? $state->timezone,
                'latitude' => $data['latitude'] ?? $state->latitude,
                'longitude' => $data['longitude'] ?? $state->longitude,
                'is_active' => $data['is_active'] ?? $state->is_active,
            ]));

            // Log state update
            activity()
                ->performedOn($state)
                ->withProperties([
                    'updated_data' => $data,
                    'admin_id' => auth()->id(),
                ])
                ->log('state_updated');

            return $state->fresh();
        });
    }

    /**
     * Create new city.
     */
    public function createCity(array $data): City
    {
        return DB::transaction(function () use ($data) {
            $city = City::create([
                'country_id' => $data['country_id'],
                'state_id' => $data['state_id'] ?? null,
                'name' => $data['name'],
                'timezone' => $data['timezone'] ?? null,
                'latitude' => $data['latitude'] ?? null,
                'longitude' => $data['longitude'] ?? null,
                'is_active' => $data['is_active'] ?? true,
            ]);

            // Log city creation
            activity()
                ->performedOn($city)
                ->withProperties([
                    'city_data' => $data,
                    'admin_id' => auth()->id(),
                ])
                ->log('city_created');

            return $city;
        });
    }

    /**
     * Update city.
     */
    public function updateCity(string $cityId, array $data): City
    {
        return DB::transaction(function () use ($cityId, $data) {
            $city = City::findOrFail($cityId);

            $city->update(array_filter([
                'country_id' => $data['country_id'] ?? $city->country_id,
                'state_id' => $data['state_id'] ?? $city->state_id,
                'name' => $data['name'] ?? $city->name,
                'timezone' => $data['timezone'] ?? $city->timezone,
                'latitude' => $data['latitude'] ?? $city->latitude,
                'longitude' => $data['longitude'] ?? $city->longitude,
                'is_active' => $data['is_active'] ?? $city->is_active,
            ]));

            // Log city update
            activity()
                ->performedOn($city)
                ->withProperties([
                    'updated_data' => $data,
                    'admin_id' => auth()->id(),
                ])
                ->log('city_updated');

            return $city->fresh();
        });
    }

    /**
     * Get location statistics.
     */
    public function getLocationStatistics(): array
    {
        return [
            'total_countries' => Country::count(),
            'active_countries' => Country::where('is_active', true)->count(),
            'total_states' => State::count(),
            'active_states' => State::where('is_active', true)->count(),
            'total_cities' => City::count(),
            'active_cities' => City::where('is_active', true)->count(),
            'countries_with_coordinates' => Country::whereNotNull('latitude')->whereNotNull('longitude')->count(),
            'states_with_coordinates' => State::whereNotNull('latitude')->whereNotNull('longitude')->count(),
            'cities_with_coordinates' => City::whereNotNull('latitude')->whereNotNull('longitude')->count(),
        ];
    }

    /**
     * Search locations by query.
     */
    public function searchLocations(string $query, ?string $type = null): array
    {
        $results = [];

        if (! $type || $type === 'countries') {
            $results['countries'] = Country::where('name', 'ILIKE', "%{$query}%")
                ->orWhere('code', 'ILIKE', "%{$query}%")
                ->limit(10)
                ->get();
        }

        if (! $type || $type === 'states') {
            $results['states'] = State::with('country:id,name,code')
                ->where('name', 'ILIKE', "%{$query}%")
                ->orWhere('code', 'ILIKE', "%{$query}%")
                ->limit(10)
                ->get();
        }

        if (! $type || $type === 'cities') {
            $results['cities'] = City::with(['country:id,name,code', 'state:id,name,code'])
                ->where('name', 'ILIKE', "%{$query}%")
                ->limit(10)
                ->get();
        }

        return $results;
    }

    /**
     * Bulk import locations.
     */
    public function bulkImportLocations(array $locations, string $type): array
    {
        return DB::transaction(function () use ($locations, $type) {
            $results = ['success' => 0, 'failed' => 0, 'errors' => []];

            foreach ($locations as $index => $locationData) {
                try {
                    match ($type) {
                        'countries' => $this->createCountry($locationData),
                        'states' => $this->createState($locationData),
                        'cities' => $this->createCity($locationData),
                        default => throw new \InvalidArgumentException("Invalid location type: {$type}")
                    };
                    $results['success']++;
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = [
                        'index' => $index,
                        'data' => $locationData,
                        'error' => $e->getMessage(),
                    ];
                }
            }

            return $results;
        });
    }

    /**
     * Get country statistics.
     */
    private function getCountryStatistics(Country $country): array
    {
        return [
            'total_states' => $country->states()->count(),
            'active_states' => $country->states()->where('is_active', true)->count(),
            'total_cities' => $country->cities()->count(),
            'active_cities' => $country->cities()->where('is_active', true)->count(),
            'total_addresses' => $country->addresses()->count(),
        ];
    }

    /**
     * Get states summary for a country.
     */
    private function getStatesSummary(Country $country): Collection
    {
        return $country->states()
            ->withCount(['cities', 'addresses'])
            ->orderBy('name')
            ->get();
    }

    /**
     * Delete country.
     */
    public function deleteCountry(string $countryId): bool
    {
        return DB::transaction(function () use ($countryId) {
            $country = Country::findOrFail($countryId);

            // Check if country has dependencies
            if ($country->states()->exists() || $country->addresses()->exists()) {
                throw new \InvalidArgumentException('Cannot delete country with existing states or addresses');
            }

            // Log country deletion
            activity()
                ->performedOn($country)
                ->withProperties([
                    'country_name' => $country->name,
                    'admin_id' => auth()->id(),
                ])
                ->log('country_deleted');

            return $country->delete();
        });
    }

    /**
     * Delete state.
     */
    public function deleteState(string $stateId): bool
    {
        return DB::transaction(function () use ($stateId) {
            $state = State::findOrFail($stateId);

            // Check if state has dependencies
            if ($state->cities()->exists() || $state->addresses()->exists()) {
                throw new \InvalidArgumentException('Cannot delete state with existing cities or addresses');
            }

            // Log state deletion
            activity()
                ->performedOn($state)
                ->withProperties([
                    'state_name' => $state->name,
                    'admin_id' => auth()->id(),
                ])
                ->log('state_deleted');

            return $state->delete();
        });
    }

    /**
     * Delete city.
     */
    public function deleteCity(string $cityId): bool
    {
        return DB::transaction(function () use ($cityId) {
            $city = City::findOrFail($cityId);

            // Check if city has dependencies
            if ($city->addresses()->exists()) {
                throw new \InvalidArgumentException('Cannot delete city with existing addresses');
            }

            // Log city deletion
            activity()
                ->performedOn($city)
                ->withProperties([
                    'city_name' => $city->name,
                    'admin_id' => auth()->id(),
                ])
                ->log('city_deleted');

            return $city->delete();
        });
    }
}
