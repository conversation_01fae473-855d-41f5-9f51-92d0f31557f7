<?php

declare(strict_types=1);

namespace App\Services\Core;

use App\Models\Core\City;
use App\Models\Core\Country;
use App\Models\Core\State;
use App\Models\User\Address;
use App\Services\System\LoggingService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class AddressManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get address with detailed information.
     */
    public function getAddressWithDetails(string $addressId): array
    {
        $address = Address::with([
            'user:id,first_name,last_name,email',
            'country:id,name,code,currency_code',
            'state:id,name,code',
            'city:id,name',
            'addressable',
        ])->findOrFail($addressId);

        return [
            'address' => $address,
            'validation_status' => $this->validateAddress($address),
            'geocoding_status' => $this->getGeocodingStatus($address),
            'delivery_zones' => $this->getDeliveryZones($address),
        ];
    }

    /**
     * Validate address format and completeness.
     */
    public function validateAddress(Address $address): array
    {
        $issues = [];
        $score = 100;

        // Check required fields
        if (empty($address->street_address)) {
            $issues[] = 'Street address is required';
            $score -= 30;
        }

        if (empty($address->city_name_string) && ! $address->city_id) {
            $issues[] = 'City information is required';
            $score -= 25;
        }

        if (! $address->country_id) {
            $issues[] = 'Country is required';
            $score -= 25;
        }

        // Check postal code format
        if ($address->postal_code && ! $this->validatePostalCode($address->postal_code, $address->country->code ?? null)) {
            $issues[] = 'Invalid postal code format';
            $score -= 10;
        }

        // Check coordinates
        if (! $address->hasCoordinates()) {
            $issues[] = 'Missing GPS coordinates';
            $score -= 10;
        }

        return [
            'is_valid' => empty($issues),
            'score' => max(0, $score),
            'issues' => $issues,
            'completeness' => $this->calculateCompleteness($address),
        ];
    }

    /**
     * Geocode address to get coordinates.
     */
    public function geocodeAddress(string $addressId): array
    {
        $address = Address::findOrFail($addressId);

        try {
            // This would integrate with a geocoding service like Google Maps
            // For now, return mock coordinates
            $coordinates = $this->performGeocoding($address->full_address);

            if ($coordinates) {
                $address->update([
                    'latitude' => $coordinates['lat'],
                    'longitude' => $coordinates['lng'],
                ]);

                $this->loggingService->logInfo('Address geocoded successfully', [
                    'address_id' => $address->id,
                    'coordinates' => $coordinates,
                ]);

                return [
                    'success' => true,
                    'coordinates' => $coordinates,
                    'address' => $address->fresh(),
                ];
            }

            return [
                'success' => false,
                'error' => 'Unable to geocode address',
            ];

        } catch (\Exception $e) {
            $this->loggingService->logError('Address geocoding failed', $e, [
                'address_id' => $address->id,
            ]);

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Standardize address format.
     */
    public function standardizeAddress(string $addressId): Address
    {
        return DB::transaction(function () use ($addressId) {
            $address = Address::findOrFail($addressId);

            // Standardize street address
            $standardizedStreet = $this->standardizeStreetAddress($address->street_address);

            // Standardize postal code
            $standardizedPostal = $this->standardizePostalCode(
                $address->postal_code,
                $address->country->code ?? null
            );

            $address->update([
                'street_address' => $standardizedStreet,
                'postal_code' => $standardizedPostal,
            ]);

            $this->loggingService->logInfo('Address standardized', [
                'address_id' => $address->id,
                'admin_id' => auth()->id(),
            ]);

            return $address->fresh();
        });
    }

    /**
     * Bulk validate addresses.
     */
    public function bulkValidateAddresses(array $addressIds): array
    {
        $results = ['valid' => 0, 'invalid' => 0, 'details' => []];

        foreach ($addressIds as $addressId) {
            try {
                $address = Address::findOrFail($addressId);
                $validation = $this->validateAddress($address);

                $results['details'][$addressId] = $validation;

                if ($validation['is_valid']) {
                    $results['valid']++;
                } else {
                    $results['invalid']++;
                }
            } catch (\Exception $e) {
                $results['invalid']++;
                $results['details'][$addressId] = [
                    'is_valid' => false,
                    'error' => $e->getMessage(),
                ];
            }
        }

        return $results;
    }

    /**
     * Get address statistics.
     */
    public function getAddressStatistics(): array
    {
        return [
            'total_addresses' => Address::count(),
            'addresses_with_coordinates' => Address::whereNotNull('latitude')
                ->whereNotNull('longitude')
                ->count(),
            'default_addresses' => Address::where('is_default', true)->count(),
            'by_country' => Address::with('country:id,name')
                ->selectRaw('country_id, COUNT(*) as count')
                ->groupBy('country_id')
                ->orderByDesc('count')
                ->limit(10)
                ->get()
                ->map(function ($item) {
                    return [
                        'country' => $item->country->name ?? 'Unknown',
                        'count' => $item->count,
                    ];
                }),
            'by_addressable_type' => Address::selectRaw('addressable_type, COUNT(*) as count')
                ->groupBy('addressable_type')
                ->orderByDesc('count')
                ->pluck('count', 'addressable_type'),
            'validation_stats' => $this->getValidationStatistics(),
        ];
    }

    /**
     * Search addresses by query.
     */
    public function searchAddresses(string $query, ?string $addressableType = null): Collection
    {
        $queryBuilder = Address::with([
            'country:id,name,code',
            'state:id,name',
            'city:id,name',
            'user:id,first_name,last_name',
        ])
            ->where(function ($q) use ($query) {
                $q->where('street_address', 'ILIKE', "%{$query}%")
                    ->orWhere('city_name_string', 'ILIKE', "%{$query}%")
                    ->orWhere('state_province_string', 'ILIKE', "%{$query}%")
                    ->orWhere('postal_code', 'ILIKE', "%{$query}%")
                    ->orWhere('label', 'ILIKE', "%{$query}%");
            });

        if ($addressableType) {
            $queryBuilder->where('addressable_type', $addressableType);
        }

        return $queryBuilder->limit(50)->get();
    }

    /**
     * Get delivery zones for address.
     */
    public function getDeliveryZones(Address $address): array
    {
        // This would integrate with delivery zone logic
        // For now, return mock data
        return [
            'zones' => [
                [
                    'id' => 'zone_1',
                    'name' => 'Central Zone',
                    'delivery_fee' => 500,
                    'estimated_time' => '30-45 minutes',
                ],
            ],
            'is_deliverable' => true,
            'restrictions' => [],
        ];
    }

    /**
     * Export addresses for compliance.
     */
    public function exportAddresses(array $filters = [], string $format = 'csv'): string
    {
        $query = Address::with([
            'country:id,name,code',
            'state:id,name',
            'city:id,name',
            'user:id,first_name,last_name',
        ]);

        // Apply filters
        if (isset($filters['country_id'])) {
            $query->where('country_id', $filters['country_id']);
        }

        if (isset($filters['addressable_type'])) {
            $query->where('addressable_type', $filters['addressable_type']);
        }

        if (isset($filters['has_coordinates'])) {
            if ($filters['has_coordinates']) {
                $query->whereNotNull('latitude')->whereNotNull('longitude');
            } else {
                $query->where(function ($q) {
                    $q->whereNull('latitude')->orWhereNull('longitude');
                });
            }
        }

        $addresses = $query->orderBy('created_at')->get();

        // Generate filename
        $filename = 'addresses_export_'.now()->format('Y_m_d_H_i_s').'.'.$format;
        $filepath = storage_path('app/exports/'.$filename);

        // Ensure directory exists
        if (! file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        if ($format === 'csv') {
            $this->exportToCsv($addresses, $filepath);
        } elseif ($format === 'json') {
            $this->exportToJson($addresses, $filepath);
        }

        return $filename;
    }

    /**
     * Validate postal code format.
     */
    private function validatePostalCode(?string $postalCode, ?string $countryCode): bool
    {
        if (! $postalCode || ! $countryCode) {
            return true; // Allow empty postal codes
        }

        $patterns = [
            'US' => '/^\d{5}(-\d{4})?$/',
            'CA' => '/^[A-Z]\d[A-Z] \d[A-Z]\d$/',
            'GB' => '/^[A-Z]{1,2}\d[A-Z\d]? \d[A-Z]{2}$/',
            'NG' => '/^\d{6}$/',
            'DE' => '/^\d{5}$/',
            'FR' => '/^\d{5}$/',
        ];

        $pattern = $patterns[$countryCode] ?? '/^.+$/'; // Default: any non-empty string

        return preg_match($pattern, $postalCode) === 1;
    }

    /**
     * Calculate address completeness percentage.
     */
    private function calculateCompleteness(Address $address): int
    {
        $fields = [
            'street_address' => 25,
            'city_info' => 20, // city_id or city_name_string
            'country_id' => 20,
            'postal_code' => 15,
            'coordinates' => 10, // latitude and longitude
            'state_info' => 10, // state_id or state_province_string
        ];

        $score = 0;

        if (! empty($address->street_address)) {
            $score += $fields['street_address'];
        }

        if ($address->city_id || ! empty($address->city_name_string)) {
            $score += $fields['city_info'];
        }

        if ($address->country_id) {
            $score += $fields['country_id'];
        }

        if (! empty($address->postal_code)) {
            $score += $fields['postal_code'];
        }

        if ($address->hasCoordinates()) {
            $score += $fields['coordinates'];
        }

        if ($address->state_id || ! empty($address->state_province_string)) {
            $score += $fields['state_info'];
        }

        return $score;
    }

    /**
     * Get geocoding status for address.
     */
    private function getGeocodingStatus(Address $address): array
    {
        return [
            'has_coordinates' => $address->hasCoordinates(),
            'accuracy' => $address->hasCoordinates() ? 'high' : 'none',
            'last_geocoded' => $address->updated_at,
            'needs_update' => ! $address->hasCoordinates(),
        ];
    }

    /**
     * Perform geocoding (mock implementation).
     */
    private function performGeocoding(string $address): ?array
    {
        // This would integrate with Google Maps Geocoding API or similar
        // For now, return mock coordinates
        return [
            'lat' => 6.5244 + (rand(-1000, 1000) / 10000), // Lagos area with variation
            'lng' => 3.3792 + (rand(-1000, 1000) / 10000),
        ];
    }

    /**
     * Standardize street address.
     */
    private function standardizeStreetAddress(string $streetAddress): string
    {
        // Basic standardization rules
        $standardized = trim($streetAddress);

        // Standardize common abbreviations
        $replacements = [
            '/\bSt\.?\b/i' => 'Street',
            '/\bAve\.?\b/i' => 'Avenue',
            '/\bRd\.?\b/i' => 'Road',
            '/\bBlvd\.?\b/i' => 'Boulevard',
            '/\bDr\.?\b/i' => 'Drive',
            '/\bCt\.?\b/i' => 'Court',
            '/\bPl\.?\b/i' => 'Place',
        ];

        foreach ($replacements as $pattern => $replacement) {
            $standardized = preg_replace($pattern, $replacement, $standardized);
        }

        return $standardized;
    }

    /**
     * Standardize postal code.
     */
    private function standardizePostalCode(?string $postalCode, ?string $countryCode): ?string
    {
        if (! $postalCode) {
            return null;
        }

        $standardized = strtoupper(trim($postalCode));

        // Country-specific standardization
        switch ($countryCode) {
            case 'CA':
                // Canadian postal codes: A1A 1A1
                $standardized = preg_replace('/([A-Z]\d[A-Z])(\d[A-Z]\d)/', '$1 $2', $standardized);
                break;
            case 'GB':
                // UK postal codes: ensure space before last 3 characters
                if (strlen($standardized) > 3) {
                    $standardized = substr($standardized, 0, -3).' '.substr($standardized, -3);
                }
                break;
        }

        return $standardized;
    }

    /**
     * Get validation statistics.
     */
    private function getValidationStatistics(): array
    {
        $total = Address::count();
        $withCoordinates = Address::whereNotNull('latitude')->whereNotNull('longitude')->count();
        $withPostalCode = Address::whereNotNull('postal_code')->count();

        return [
            'total' => $total,
            'with_coordinates' => $withCoordinates,
            'with_postal_code' => $withPostalCode,
            'geocoding_rate' => $total > 0 ? round(($withCoordinates / $total) * 100, 2) : 0,
            'postal_code_rate' => $total > 0 ? round(($withPostalCode / $total) * 100, 2) : 0,
        ];
    }

    /**
     * Export addresses to CSV.
     */
    private function exportToCsv(Collection $addresses, string $filepath): void
    {
        $file = fopen($filepath, 'w');

        // Write header
        fputcsv($file, [
            'ID', 'Label', 'Street Address', 'City', 'State', 'Country',
            'Postal Code', 'Latitude', 'Longitude', 'Is Default',
            'Addressable Type', 'Addressable ID', 'User', 'Created At',
        ]);

        // Write data
        foreach ($addresses as $address) {
            fputcsv($file, [
                $address->id,
                $address->label,
                $address->street_address,
                $address->city_display_name,
                $address->state_display_name,
                $address->country?->name,
                $address->postal_code,
                $address->latitude,
                $address->longitude,
                $address->is_default ? 'Yes' : 'No',
                $address->addressable_type,
                $address->addressable_id,
                $address->user?->name,
                $address->created_at->toISOString(),
            ]);
        }

        fclose($file);
    }

    /**
     * Export addresses to JSON.
     */
    private function exportToJson(Collection $addresses, string $filepath): void
    {
        $data = $addresses->map(function ($address) {
            return [
                'id' => $address->id,
                'label' => $address->label,
                'street_address' => $address->street_address,
                'city' => $address->city_display_name,
                'state' => $address->state_display_name,
                'country' => $address->country?->name,
                'postal_code' => $address->postal_code,
                'latitude' => $address->latitude,
                'longitude' => $address->longitude,
                'is_default' => $address->is_default,
                'addressable_type' => $address->addressable_type,
                'addressable_id' => $address->addressable_id,
                'user' => $address->user?->name,
                'created_at' => $address->created_at->toISOString(),
            ];
        });

        file_put_contents($filepath, json_encode($data, JSON_PRETTY_PRINT));
    }
}
