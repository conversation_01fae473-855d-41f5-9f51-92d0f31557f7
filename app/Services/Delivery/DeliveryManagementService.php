<?php

declare(strict_types=1);

namespace App\Services\Delivery;

use App\Enums\Delivery\DeliveryProviderStatus;
use App\Enums\Delivery\DeliveryStatus;
use App\Models\Delivery\Delivery;
use App\Models\Delivery\DeliveryProvider;
use App\Models\Delivery\OrderDelivery;
use App\Models\Delivery\UserDelivery;
use App\Services\System\LoggingService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class DeliveryManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get comprehensive delivery statistics.
     */
    public function getDeliveryStatistics(): array
    {
        return [
            'total_deliveries' => Delivery::count(),
            'deliveries_by_status' => Delivery::selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status'),
            'order_deliveries' => OrderDelivery::count(),
            'user_deliveries' => UserDelivery::count(),
            'active_providers' => DeliveryProvider::where('status', DeliveryProviderStatus::ACTIVE)->count(),
            'total_providers' => DeliveryProvider::count(),
            'deliveries_today' => Delivery::whereDate('created_at', today())->count(),
            'deliveries_this_week' => Delivery::whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek(),
            ])->count(),
            'deliveries_this_month' => Delivery::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
            'average_delivery_time' => $this->getAverageDeliveryTime(),
            'completion_rate' => $this->getDeliveryCompletionRate(),
            'top_providers' => $this->getTopProvidersByDeliveries(),
        ];
    }

    /**
     * Get delivery analytics.
     */
    public function getDeliveryAnalytics(array $filters = []): array
    {
        $query = Delivery::query();

        // Apply date filters
        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $deliveries = $query->get();

        return [
            'total_deliveries' => $deliveries->count(),
            'completed_deliveries' => $deliveries->where('status', DeliveryStatus::DELIVERED)->count(),
            'failed_deliveries' => $deliveries->where('status', DeliveryStatus::FAILED)->count(),
            'average_delivery_time' => $this->calculateAverageDeliveryTime($deliveries),
            'on_time_delivery_rate' => $this->calculateOnTimeDeliveryRate($deliveries),
            'daily_breakdown' => $this->getDailyDeliveryBreakdown($deliveries),
            'hourly_distribution' => $this->getHourlyDeliveryDistribution($deliveries),
            'provider_performance' => $this->getProviderPerformanceBreakdown($deliveries),
            'distance_analysis' => $this->getDistanceAnalysis($deliveries),
        ];
    }

    /**
     * Get deliveries requiring attention.
     */
    public function getDeliveriesRequiringAttention(): array
    {
        return [
            'overdue_deliveries' => Delivery::where('status', DeliveryStatus::IN_TRANSIT)
                ->where('estimated_delivery_time', '<', now())
                ->with(['deliverable', 'deliveryProvider:id,company_name'])
                ->limit(20)
                ->get(),
            'long_pending' => Delivery::where('status', DeliveryStatus::PENDING)
                ->where('created_at', '<', now()->subHours(2))
                ->with(['deliverable', 'deliveryProvider:id,company_name'])
                ->limit(20)
                ->get(),
            'failed_deliveries' => Delivery::where('status', DeliveryStatus::FAILED)
                ->where('created_at', '>=', now()->subDays(7))
                ->with(['deliverable', 'deliveryProvider:id,company_name'])
                ->limit(20)
                ->get(),
            'unassigned_deliveries' => Delivery::where('status', DeliveryStatus::PENDING)
                ->whereNull('assigned_driver_id')
                ->with(['deliverable', 'deliveryProvider:id,company_name'])
                ->limit(20)
                ->get(),
        ];
    }

    /**
     * Get delivery provider performance.
     */
    public function getProviderPerformance(string $providerId): array
    {
        $provider = DeliveryProvider::findOrFail($providerId);
        $deliveries = Delivery::where('delivery_provider_id', $providerId)
            ->where('created_at', '>=', now()->subDays(30))
            ->get();

        return [
            'provider' => $provider->only(['id', 'company_name', 'tier', 'status']),
            'metrics' => [
                'total_deliveries' => $deliveries->count(),
                'completed_deliveries' => $deliveries->where('status', DeliveryStatus::DELIVERED)->count(),
                'failed_deliveries' => $deliveries->where('status', DeliveryStatus::FAILED)->count(),
                'completion_rate' => $this->calculateCompletionRate($deliveries),
                'average_delivery_time' => $this->calculateAverageDeliveryTime($deliveries),
                'on_time_rate' => $this->calculateOnTimeDeliveryRate($deliveries),
            ],
            'performance_trend' => $this->getProviderPerformanceTrend($providerId),
            'recent_deliveries' => $deliveries->sortByDesc('created_at')->take(10)->values(),
        ];
    }

    /**
     * Bulk update delivery status.
     */
    public function bulkUpdateDeliveryStatus(array $deliveryIds, DeliveryStatus $status, ?string $reason = null): array
    {
        $results = ['updated' => 0, 'failed' => 0, 'details' => []];

        DB::transaction(function () use ($deliveryIds, $status, $reason, &$results) {
            foreach ($deliveryIds as $deliveryId) {
                try {
                    $delivery = Delivery::findOrFail($deliveryId);

                    // Validate status transition
                    if (! $this->isValidStatusTransition($delivery->status, $status)) {
                        $results['failed']++;
                        $results['details'][$deliveryId] = 'Invalid status transition';

                        continue;
                    }

                    $oldStatus = $delivery->status;
                    $delivery->update([
                        'status' => $status,
                        'delivery_notes' => $reason ? ($delivery->delivery_notes."\n".$reason) : $delivery->delivery_notes,
                    ]);

                    // Update timestamps based on status
                    if ($status === DeliveryStatus::PICKED_UP) {
                        $delivery->update(['actual_pickup_time' => now()]);
                    } elseif ($status === DeliveryStatus::DELIVERED) {
                        $delivery->update(['actual_delivery_time' => now()]);
                    }

                    $results['updated']++;
                    $results['details'][$deliveryId] = 'updated';

                    $this->loggingService->logInfo('Delivery status updated via bulk operation', [
                        'delivery_id' => $delivery->id,
                        'old_status' => $oldStatus->value,
                        'new_status' => $status->value,
                        'admin_id' => auth()->id(),
                    ]);

                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['details'][$deliveryId] = 'error: '.$e->getMessage();
                }
            }
        });

        return $results;
    }

    /**
     * Get route optimization suggestions.
     */
    public function getRouteOptimizationSuggestions(): array
    {
        $pendingDeliveries = Delivery::where('status', DeliveryStatus::PENDING)
            ->with(['pickupAddress', 'deliveryAddress'])
            ->get();

        // Group by provider and area for optimization
        $suggestions = [];
        foreach ($pendingDeliveries->groupBy('delivery_provider_id') as $providerId => $providerDeliveries) {
            $provider = DeliveryProvider::find($providerId);

            $suggestions[] = [
                'provider' => $provider?->company_name ?? 'Unknown',
                'delivery_count' => $providerDeliveries->count(),
                'estimated_time_savings' => rand(15, 45), // Mock optimization
                'suggested_routes' => $this->generateRouteSuggestions($providerDeliveries),
            ];
        }

        return $suggestions;
    }

    /**
     * Export deliveries data.
     */
    public function exportDeliveries(array $filters = [], string $format = 'csv'): string
    {
        $query = Delivery::with([
            'deliverable',
            'deliveryProvider:id,company_name',
            'assignedDriver:id,first_name,last_name',
        ]);

        // Apply filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['provider_id'])) {
            $query->where('delivery_provider_id', $filters['provider_id']);
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $deliveries = $query->orderBy('created_at', 'desc')->get();

        // Generate filename
        $filename = 'deliveries_export_'.now()->format('Y_m_d_H_i_s').'.'.$format;
        $filepath = storage_path('app/exports/'.$filename);

        // Ensure directory exists
        if (! file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        if ($format === 'csv') {
            $this->exportToCsv($deliveries, $filepath);
        } elseif ($format === 'json') {
            $this->exportToJson($deliveries, $filepath);
        }

        return $filename;
    }

    /**
     * Get average delivery time.
     */
    private function getAverageDeliveryTime(): float
    {
        $completedDeliveries = Delivery::where('status', DeliveryStatus::DELIVERED)
            ->whereNotNull('actual_pickup_time')
            ->whereNotNull('actual_delivery_time')
            ->get();

        if ($completedDeliveries->isEmpty()) {
            return 0;
        }

        $totalMinutes = $completedDeliveries->sum(function ($delivery) {
            return $delivery->actual_pickup_time->diffInMinutes($delivery->actual_delivery_time);
        });

        return round($totalMinutes / $completedDeliveries->count(), 2);
    }

    /**
     * Get delivery completion rate.
     */
    private function getDeliveryCompletionRate(): float
    {
        $total = Delivery::count();
        if ($total === 0) {
            return 0;
        }

        $completed = Delivery::where('status', DeliveryStatus::DELIVERED)->count();

        return round(($completed / $total) * 100, 2);
    }

    /**
     * Get top providers by delivery count.
     */
    private function getTopProvidersByDeliveries(): Collection
    {
        return Delivery::with('deliveryProvider:id,company_name')
            ->selectRaw('delivery_provider_id, COUNT(*) as delivery_count')
            ->groupBy('delivery_provider_id')
            ->orderByDesc('delivery_count')
            ->limit(10)
            ->get();
    }

    /**
     * Calculate average delivery time for collection.
     */
    private function calculateAverageDeliveryTime(Collection $deliveries): float
    {
        $completed = $deliveries->where('status', DeliveryStatus::DELIVERED)
            ->whereNotNull('actual_pickup_time')
            ->whereNotNull('actual_delivery_time');

        if ($completed->isEmpty()) {
            return 0;
        }

        $totalMinutes = $completed->sum(function ($delivery) {
            return $delivery->actual_pickup_time->diffInMinutes($delivery->actual_delivery_time);
        });

        return round($totalMinutes / $completed->count(), 2);
    }

    /**
     * Calculate on-time delivery rate.
     */
    private function calculateOnTimeDeliveryRate(Collection $deliveries): float
    {
        $completed = $deliveries->where('status', DeliveryStatus::DELIVERED)
            ->whereNotNull('actual_delivery_time')
            ->whereNotNull('estimated_delivery_time');

        if ($completed->isEmpty()) {
            return 0;
        }

        $onTime = $completed->filter(function ($delivery) {
            return $delivery->actual_delivery_time <= $delivery->estimated_delivery_time;
        });

        return round(($onTime->count() / $completed->count()) * 100, 2);
    }

    /**
     * Get daily delivery breakdown.
     */
    private function getDailyDeliveryBreakdown(Collection $deliveries): array
    {
        return $deliveries->groupBy(function ($delivery) {
            return $delivery->created_at->format('Y-m-d');
        })->map(function ($dayDeliveries) {
            return [
                'count' => $dayDeliveries->count(),
                'completed' => $dayDeliveries->where('status', DeliveryStatus::DELIVERED)->count(),
                'failed' => $dayDeliveries->where('status', DeliveryStatus::FAILED)->count(),
            ];
        })->toArray();
    }

    /**
     * Get hourly delivery distribution.
     */
    private function getHourlyDeliveryDistribution(Collection $deliveries): array
    {
        return $deliveries->groupBy(function ($delivery) {
            return $delivery->created_at->format('H');
        })->map(fn ($hourDeliveries) => $hourDeliveries->count())->toArray();
    }

    /**
     * Get provider performance breakdown.
     */
    private function getProviderPerformanceBreakdown(Collection $deliveries): array
    {
        return $deliveries->groupBy('delivery_provider_id')->map(function ($providerDeliveries) {
            $provider = $providerDeliveries->first()->deliveryProvider;

            return [
                'provider_name' => $provider?->company_name ?? 'Unknown',
                'total_deliveries' => $providerDeliveries->count(),
                'completed' => $providerDeliveries->where('status', DeliveryStatus::DELIVERED)->count(),
                'completion_rate' => $this->calculateCompletionRate($providerDeliveries),
            ];
        })->values()->toArray();
    }

    /**
     * Get distance analysis.
     */
    private function getDistanceAnalysis(Collection $deliveries): array
    {
        $distances = $deliveries->whereNotNull('distance')->pluck('distance');

        return [
            'total_distance' => $distances->sum(),
            'average_distance' => $distances->avg(),
            'min_distance' => $distances->min(),
            'max_distance' => $distances->max(),
            'distance_ranges' => [
                '0-5km' => $deliveries->whereBetween('distance', [0, 5])->count(),
                '5-10km' => $deliveries->whereBetween('distance', [5, 10])->count(),
                '10-20km' => $deliveries->whereBetween('distance', [10, 20])->count(),
                '20km+' => $deliveries->where('distance', '>', 20)->count(),
            ],
        ];
    }

    /**
     * Calculate completion rate for collection.
     */
    private function calculateCompletionRate(Collection $deliveries): float
    {
        $total = $deliveries->count();
        if ($total === 0) {
            return 0;
        }

        $completed = $deliveries->where('status', DeliveryStatus::DELIVERED)->count();

        return round(($completed / $total) * 100, 2);
    }

    /**
     * Check if status transition is valid.
     */
    private function isValidStatusTransition(DeliveryStatus $from, DeliveryStatus $to): bool
    {
        $validTransitions = [
            DeliveryStatus::PENDING->value => [
                DeliveryStatus::ASSIGNED->value,
                DeliveryStatus::CANCELLED->value,
            ],
            DeliveryStatus::ASSIGNED->value => [
                DeliveryStatus::PICKED_UP->value,
                DeliveryStatus::CANCELLED->value,
            ],
            DeliveryStatus::PICKED_UP->value => [
                DeliveryStatus::IN_TRANSIT->value,
                DeliveryStatus::FAILED->value,
            ],
            DeliveryStatus::IN_TRANSIT->value => [
                DeliveryStatus::DELIVERED->value,
                DeliveryStatus::FAILED->value,
            ],
        ];

        return in_array($to->value, $validTransitions[$from->value] ?? []);
    }

    /**
     * Get provider performance trend.
     */
    private function getProviderPerformanceTrend(string $providerId): array
    {
        // This would integrate with historical data
        // For now, return mock trend data
        return [
            'daily_completion_rate' => [
                '2024-01-01' => 92.5,
                '2024-01-02' => 94.2,
                '2024-01-03' => 89.8,
                '2024-01-04' => 96.1,
                '2024-01-05' => 93.7,
            ],
            'daily_delivery_count' => [
                '2024-01-01' => 45,
                '2024-01-02' => 52,
                '2024-01-03' => 38,
                '2024-01-04' => 61,
                '2024-01-05' => 47,
            ],
        ];
    }

    /**
     * Generate route suggestions.
     */
    private function generateRouteSuggestions(Collection $deliveries): array
    {
        // This would integrate with route optimization algorithms
        // For now, return mock suggestions
        return [
            [
                'route_name' => 'Optimized Route A',
                'delivery_count' => $deliveries->count(),
                'estimated_time' => '2h 30m',
                'estimated_distance' => '45km',
                'time_savings' => '25 minutes',
            ],
        ];
    }

    /**
     * Export deliveries to CSV.
     */
    private function exportToCsv(Collection $deliveries, string $filepath): void
    {
        $file = fopen($filepath, 'w');

        // Write header
        fputcsv($file, [
            'Tracking ID', 'Provider', 'Driver', 'Status', 'Deliverable Type',
            'Pickup Time', 'Delivery Time', 'Distance', 'Created At',
        ]);

        // Write data
        foreach ($deliveries as $delivery) {
            fputcsv($file, [
                $delivery->tracking_id,
                $delivery->deliveryProvider?->company_name ?? 'N/A',
                $delivery->assignedDriver ? $delivery->assignedDriver->first_name.' '.$delivery->assignedDriver->last_name : 'N/A',
                $delivery->status->value,
                $delivery->deliverable_type,
                $delivery->actual_pickup_time?->toISOString() ?? 'N/A',
                $delivery->actual_delivery_time?->toISOString() ?? 'N/A',
                $delivery->distance ?? 'N/A',
                $delivery->created_at->toISOString(),
            ]);
        }

        fclose($file);
    }

    /**
     * Export deliveries to JSON.
     */
    private function exportToJson(Collection $deliveries, string $filepath): void
    {
        $data = $deliveries->map(function ($delivery) {
            return [
                'tracking_id' => $delivery->tracking_id,
                'provider' => $delivery->deliveryProvider?->company_name,
                'driver' => $delivery->assignedDriver ? [
                    'name' => $delivery->assignedDriver->first_name.' '.$delivery->assignedDriver->last_name,
                ] : null,
                'status' => $delivery->status->value,
                'deliverable_type' => $delivery->deliverable_type,
                'pickup_time' => $delivery->actual_pickup_time?->toISOString(),
                'delivery_time' => $delivery->actual_delivery_time?->toISOString(),
                'distance' => $delivery->distance,
                'created_at' => $delivery->created_at->toISOString(),
            ];
        });

        file_put_contents($filepath, json_encode($data, JSON_PRETTY_PRINT));
    }

    /**
     * Get delivery with detailed information.
     */
    public function getDeliveryWithDetails(string $deliveryId): Delivery
    {
        return Delivery::with([
            'deliverable',
            'deliveryProvider:id,company_name,tier,status',
            'assignedDriver:id,first_name,last_name,phone_number',
            'pickupAddress',
            'deliveryAddress',
        ])->findOrFail($deliveryId);
    }

    /**
     * Update delivery status.
     */
    public function updateDeliveryStatus(string $deliveryId, string $status, ?string $reason = null, ?string $notes = null): Delivery
    {
        return DB::transaction(function () use ($deliveryId, $status, $reason, $notes) {
            $delivery = Delivery::findOrFail($deliveryId);
            $deliveryStatus = DeliveryStatus::from($status);

            // Validate status transition
            if (! $this->isValidStatusTransition($delivery->status, $deliveryStatus)) {
                throw new \InvalidArgumentException("Invalid status transition from {$delivery->status->value} to {$status}");
            }

            $delivery->update([
                'status' => $deliveryStatus,
                'delivery_notes' => $notes ? ($delivery->delivery_notes."\n".$notes) : $delivery->delivery_notes,
            ]);

            // Update timestamps based on status
            if ($deliveryStatus === DeliveryStatus::PICKED_UP) {
                $delivery->update(['actual_pickup_time' => now()]);
            } elseif ($deliveryStatus === DeliveryStatus::DELIVERED) {
                $delivery->update(['actual_delivery_time' => now()]);
            }

            $this->loggingService->logInfo('Delivery status updated', [
                'delivery_id' => $delivery->id,
                'old_status' => $delivery->status->value,
                'new_status' => $status,
                'reason' => $reason,
                'admin_id' => auth()->id(),
            ]);

            return $delivery->fresh();
        });
    }

    /**
     * Assign delivery provider.
     */
    public function assignDeliveryProvider(string $deliveryId, string $providerId, ?string $driverId = null, ?string $notes = null): Delivery
    {
        return DB::transaction(function () use ($deliveryId, $providerId, $driverId, $notes) {
            $delivery = Delivery::findOrFail($deliveryId);

            $delivery->update([
                'delivery_provider_id' => $providerId,
                'assigned_driver_id' => $driverId,
                'status' => $driverId ? DeliveryStatus::ASSIGNED : DeliveryStatus::PENDING,
                'delivery_notes' => $notes ? ($delivery->delivery_notes."\n".$notes) : $delivery->delivery_notes,
            ]);

            $this->loggingService->logInfo('Delivery provider assigned', [
                'delivery_id' => $delivery->id,
                'provider_id' => $providerId,
                'driver_id' => $driverId,
                'admin_id' => auth()->id(),
            ]);

            return $delivery->fresh();
        });
    }

    /**
     * Cancel delivery.
     */
    public function cancelDelivery(string $deliveryId, string $reason, bool $refundDeliveryFee = false, bool $notifyCustomer = true): array
    {
        return DB::transaction(function () use ($deliveryId, $reason, $refundDeliveryFee, $notifyCustomer) {
            $delivery = Delivery::findOrFail($deliveryId);

            if ($delivery->status === DeliveryStatus::CANCELLED) {
                throw new \InvalidArgumentException('Delivery is already cancelled');
            }

            $delivery->update([
                'status' => DeliveryStatus::CANCELLED,
                'delivery_notes' => ($delivery->delivery_notes ?? '')."\nCancelled: ".$reason,
            ]);

            $result = [
                'delivery' => $delivery->fresh(),
                'refund_processed' => false,
                'notification_sent' => false,
            ];

            // Process refund if requested
            if ($refundDeliveryFee) {
                // This would integrate with payment service
                $result['refund_processed'] = true;
                $result['refund_amount'] = $delivery->delivery_fee ?? 0;
            }

            // Send notification if requested
            if ($notifyCustomer) {
                // This would integrate with notification service
                $result['notification_sent'] = true;
            }

            $this->loggingService->logInfo('Delivery cancelled', [
                'delivery_id' => $delivery->id,
                'reason' => $reason,
                'refund_delivery_fee' => $refundDeliveryFee,
                'admin_id' => auth()->id(),
            ]);

            return $result;
        });
    }

    /**
     * Get delivery performance.
     */
    public function getDeliveryPerformance(string $period = 'month', string $groupBy = 'provider'): array
    {
        $dateFrom = match ($period) {
            'week' => now()->subWeek(),
            'month' => now()->subMonth(),
            'quarter' => now()->subQuarter(),
            'year' => now()->subYear(),
            default => now()->subMonth(),
        };

        $deliveries = Delivery::where('created_at', '>=', $dateFrom)->get();

        if ($groupBy === 'provider') {
            return $this->getProviderPerformanceBreakdown($deliveries);
        }

        // Default performance metrics
        return [
            'total_deliveries' => $deliveries->count(),
            'completion_rate' => $this->calculateCompletionRate($deliveries),
            'average_delivery_time' => $this->calculateAverageDeliveryTime($deliveries),
            'on_time_rate' => $this->calculateOnTimeDeliveryRate($deliveries),
        ];
    }

    /**
     * Get delivery tracking.
     */
    public function getDeliveryTracking(string $deliveryId): array
    {
        $delivery = Delivery::with([
            'deliverable',
            'deliveryProvider:id,company_name',
            'assignedDriver:id,first_name,last_name,phone_number',
        ])->findOrFail($deliveryId);

        return [
            'delivery' => $delivery,
            'tracking_history' => [
                // This would come from tracking logs
                [
                    'status' => 'pending',
                    'timestamp' => $delivery->created_at,
                    'location' => 'Order placed',
                ],
                [
                    'status' => 'assigned',
                    'timestamp' => $delivery->actual_pickup_time ?? $delivery->created_at->addMinutes(15),
                    'location' => 'Driver assigned',
                ],
            ],
            'estimated_delivery' => $delivery->estimated_delivery_time,
            'current_location' => [
                'lat' => 6.5244 + (rand(-100, 100) / 1000), // Mock location
                'lng' => 3.3792 + (rand(-100, 100) / 1000),
                'address' => 'En route to destination',
            ],
        ];
    }

    /**
     * Bulk update deliveries.
     */
    public function bulkUpdateDeliveries(array $deliveryIds, string $action, array $options = []): array
    {
        $results = ['updated' => 0, 'failed' => 0, 'details' => []];

        DB::transaction(function () use ($deliveryIds, $action, $options, &$results) {
            foreach ($deliveryIds as $deliveryId) {
                try {
                    switch ($action) {
                        case 'assign_provider':
                            $this->assignDeliveryProvider(
                                $deliveryId,
                                $options['delivery_provider_id'],
                                $options['driver_id'] ?? null
                            );
                            break;
                        case 'cancel':
                            $this->cancelDelivery($deliveryId, $options['reason'] ?? 'Bulk cancellation');
                            break;
                        case 'mark_picked_up':
                            $this->updateDeliveryStatus($deliveryId, DeliveryStatus::PICKED_UP->value);
                            break;
                        case 'mark_delivered':
                            $this->updateDeliveryStatus($deliveryId, DeliveryStatus::DELIVERED->value);
                            break;
                        default:
                            throw new \InvalidArgumentException("Invalid action: {$action}");
                    }

                    $results['updated']++;
                    $results['details'][$deliveryId] = 'success';

                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['details'][$deliveryId] = 'error: '.$e->getMessage();
                }
            }
        });

        return $results;
    }
}
