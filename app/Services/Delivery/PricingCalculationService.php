<?php

declare(strict_types=1);

namespace App\Services\Delivery;

use App\Models\Delivery\DeliveryProvider;
use App\Services\Core\LoggingService;
use Carbon\Carbon;

/**
 * Pricing Calculation Service
 *
 * Enhanced pricing calculation service with upfront pricing, interstate
 * detection, vehicle-based pricing, and seasonal adjustments.
 */
class PricingCalculationService
{
    private array $interstateMultipliers;

    private array $vehicleMultipliers;

    private array $seasonalMultipliers;

    public function __construct(
        private LoggingService $loggingService,
        private PackageWeightEstimationService $weightEstimationService
    ) {
        $this->loadPricingConfiguration();
    }

    /**
     * Calculate upfront price for checkout.
     */
    public function calculateUpfrontPrice(array $orderData): array
    {
        try {
            $basePrice = $this->calculateBasePrice($orderData);

            // Apply various multipliers
            $interstateMultiplier = $this->getInterstateMultiplier(
                $orderData['pickup_state'] ?? '',
                $orderData['delivery_state'] ?? ''
            );

            $vehicleMultiplier = $this->getVehicleTypeMultiplier(
                $orderData['vehicle_type'] ?? 'motorcycle',
                $orderData['weight'] ?? 1.0
            );

            $seasonalMultiplier = $this->getSeasonalMultiplier(
                Carbon::parse($orderData['delivery_date'] ?? now())
            );

            $insurancePrice = $this->calculateInsurancePrice(
                $orderData['item_value'] ?? 0
            );

            // Calculate final price
            $deliveryPrice = $basePrice * $interstateMultiplier * $vehicleMultiplier * $seasonalMultiplier;
            $totalPrice = $deliveryPrice + $insurancePrice;

            // Apply provider-specific adjustments if provider is specified
            if (isset($orderData['provider_id'])) {
                $provider = DeliveryProvider::find($orderData['provider_id']);
                if ($provider) {
                    $totalPrice = $this->applyProviderMultiplier($provider, $totalPrice);
                }
            }

            $result = [
                'base_price' => round($basePrice, 2),
                'delivery_price' => round($deliveryPrice, 2),
                'insurance_price' => round($insurancePrice, 2),
                'total_price' => round($totalPrice, 2),
                'multipliers' => [
                    'interstate' => $interstateMultiplier,
                    'vehicle' => $vehicleMultiplier,
                    'seasonal' => $seasonalMultiplier,
                ],
                'breakdown' => [
                    'base_delivery' => round($basePrice, 2),
                    'interstate_adjustment' => round($basePrice * ($interstateMultiplier - 1), 2),
                    'vehicle_adjustment' => round($basePrice * $interstateMultiplier * ($vehicleMultiplier - 1), 2),
                    'seasonal_adjustment' => round($basePrice * $interstateMultiplier * $vehicleMultiplier * ($seasonalMultiplier - 1), 2),
                    'insurance' => round($insurancePrice, 2),
                ],
                'calculated_at' => now()->toISOString(),
            ];

            $this->loggingService->logInfo('Upfront price calculated', [
                'order_data' => array_except($orderData, ['sensitive_data']),
                'total_price' => $totalPrice,
                'multipliers' => $result['multipliers'],
            ]);

            return $result;

        } catch (\Exception $e) {
            $this->loggingService->logError('Upfront price calculation failed', $e, [
                'order_data' => array_except($orderData, ['sensitive_data']),
            ]);

            throw $e;
        }
    }

    /**
     * Get interstate pricing multiplier.
     */
    public function getInterstateMultiplier(string $fromState, string $toState): float
    {
        $fromState = strtolower(trim($fromState));
        $toState = strtolower(trim($toState));

        // Same state = intrastate
        if ($fromState === $toState) {
            return 1.0;
        }

        // Check for specific interstate route multipliers
        $routeKey = $fromState.'_to_'.$toState;
        $reverseRouteKey = $toState.'_to_'.$fromState;

        if (isset($this->interstateMultipliers[$routeKey])) {
            return $this->interstateMultipliers[$routeKey];
        }

        if (isset($this->interstateMultipliers[$reverseRouteKey])) {
            return $this->interstateMultipliers[$reverseRouteKey];
        }

        // Default interstate multiplier
        return $this->interstateMultipliers['default_interstate'] ?? 1.5;
    }

    /**
     * Get vehicle type multiplier based on weight.
     */
    public function getVehicleTypeMultiplier(string $vehicleType, float $weight): float
    {
        $baseMultiplier = $this->vehicleMultipliers[$vehicleType] ?? 1.0;

        // Apply weight-based adjustments
        $weightCategory = $this->weightEstimationService->getWeightCategory($weight);
        $weightAdjustment = match ($weightCategory) {
            'light' => 0.9,
            'medium' => 1.0,
            'heavy' => 1.1,
            'bulk' => 1.3,
            'freight' => 1.5,
            default => 1.0,
        };

        return $baseMultiplier * $weightAdjustment;
    }

    /**
     * Apply provider-specific pricing multiplier.
     */
    public function applyProviderMultiplier(DeliveryProvider $provider, float $basePrice): float
    {
        // Provider tier-based multipliers
        $tierMultiplier = match ($provider->tier?->value) {
            'individual' => 0.95,      // 5% discount for individuals
            'small_fleet' => 1.0,      // Base price
            'medium_fleet' => 1.05,    // 5% premium for medium fleets
            'enterprise_fleet' => 1.1, // 10% premium for enterprise
            default => 1.0,
        };

        // Provider rating-based adjustments
        $ratingMultiplier = 1.0;
        if ($provider->average_rating) {
            if ($provider->average_rating >= 4.5) {
                $ratingMultiplier = 1.05; // 5% premium for highly rated providers
            } elseif ($provider->average_rating < 3.0) {
                $ratingMultiplier = 0.9;  // 10% discount for low-rated providers
            }
        }

        // Service scope multiplier
        $scopeMultiplier = match ($provider->service_scope?->value) {
            'interstate' => 1.1,  // Premium for interstate specialists
            'both' => 1.05,       // Slight premium for full-service providers
            'intrastate' => 1.0,  // Base price for intrastate only
            default => 1.0,
        };

        return $basePrice * $tierMultiplier * $ratingMultiplier * $scopeMultiplier;
    }

    /**
     * Calculate insurance price based on item value.
     */
    public function calculateInsurancePrice(float $itemValue): float
    {
        if ($itemValue <= 0) {
            return 0;
        }

        // Insurance pricing tiers
        if ($itemValue <= 10000) {
            return 100; // ₦100 for items up to ₦10,000
        } elseif ($itemValue <= 50000) {
            return 200; // ₦200 for items up to ₦50,000
        } elseif ($itemValue <= 100000) {
            return 500; // ₦500 for items up to ₦100,000
        } elseif ($itemValue <= 500000) {
            return 1000; // ₦1,000 for items up to ₦500,000
        } else {
            // 0.5% of item value for high-value items
            return $itemValue * 0.005;
        }
    }

    /**
     * Get seasonal pricing multiplier.
     */
    public function getSeasonalMultiplier(Carbon $date): float
    {
        $month = $date->month;
        $dayOfWeek = $date->dayOfWeek;
        $hour = $date->hour;

        // Seasonal multipliers by month
        $monthMultiplier = $this->seasonalMultipliers['months'][$month] ?? 1.0;

        // Day of week multipliers
        $dayMultiplier = match ($dayOfWeek) {
            0, 6 => 1.1,  // Weekend premium (Sunday = 0, Saturday = 6)
            1 => 1.05,     // Monday slight premium
            5 => 1.05,     // Friday slight premium
            default => 1.0, // Regular weekdays
        };

        // Hour of day multipliers
        $hourMultiplier = match (true) {
            $hour >= 7 && $hour <= 9 => 1.2,   // Morning rush hour
            $hour >= 17 && $hour <= 19 => 1.2, // Evening rush hour
            $hour >= 12 && $hour <= 14 => 1.1, // Lunch time
            $hour >= 22 || $hour <= 6 => 1.3,  // Night/early morning premium
            default => 1.0,
        };

        return $monthMultiplier * $dayMultiplier * $hourMultiplier;
    }

    /**
     * Calculate base price using distance and weight.
     */
    private function calculateBasePrice(array $orderData): float
    {
        $distance = $orderData['distance'] ?? 10; // Default 10km if not provided
        $weight = $orderData['weight'] ?? 1.0;    // Default 1kg if not provided
        $vehicleType = $orderData['vehicle_type'] ?? 'motorcycle';

        // Base rate per km by vehicle type
        $baseRates = [
            'bicycle' => 50,
            'motorcycle' => 80,
            'car' => 120,
            'van' => 200,
            'truck' => 350,
        ];

        $baseRate = $baseRates[$vehicleType] ?? $baseRates['motorcycle'];
        $basePrice = $distance * $baseRate;

        // Apply weight multiplier
        $weightCategory = $this->weightEstimationService->getWeightCategory($weight);
        $weightMultiplier = match ($weightCategory) {
            'light' => 1.0,
            'medium' => 1.2,
            'heavy' => 1.5,
            'bulk' => 2.0,
            'freight' => 3.0,
            default => 1.0,
        };

        // Minimum price (equivalent to 2km)
        $minimumPrice = $baseRate * 2;

        return max($basePrice * $weightMultiplier, $minimumPrice);
    }

    /**
     * Load pricing configuration.
     */
    private function loadPricingConfiguration(): void
    {
        // Interstate route multipliers
        $this->interstateMultipliers = [
            'default_interstate' => 1.5,

            // Popular routes with specific multipliers
            'lagos_to_abuja' => 1.4,
            'lagos_to_kano' => 1.6,
            'lagos_to_port_harcourt' => 1.3,
            'abuja_to_kano' => 1.3,
            'abuja_to_port_harcourt' => 1.4,
            'kano_to_port_harcourt' => 1.5,

            // Regional multipliers
            'north_to_south' => 1.6,
            'east_to_west' => 1.4,
        ];

        // Vehicle type base multipliers
        $this->vehicleMultipliers = [
            'bicycle' => 0.8,
            'motorcycle' => 1.0,
            'car' => 1.3,
            'van' => 1.8,
            'truck' => 2.5,
        ];

        // Seasonal multipliers by month
        $this->seasonalMultipliers = [
            'months' => [
                1 => 1.0,   // January
                2 => 1.0,   // February
                3 => 1.05,  // March (dry season)
                4 => 1.1,   // April (dry season peak)
                5 => 1.15,  // May (rainy season start)
                6 => 1.2,   // June (rainy season)
                7 => 1.25,  // July (peak rainy season)
                8 => 1.2,   // August (rainy season)
                9 => 1.15,  // September (rainy season)
                10 => 1.1,  // October (rainy season end)
                11 => 1.05, // November (harmattan)
                12 => 1.15, // December (holiday season)
            ],
        ];
    }

    /**
     * Get pricing summary for analytics.
     */
    public function getPricingSummary(array $orderData): array
    {
        $pricing = $this->calculateUpfrontPrice($orderData);

        return [
            'total_price' => $pricing['total_price'],
            'delivery_price' => $pricing['delivery_price'],
            'insurance_price' => $pricing['insurance_price'],
            'savings_vs_standard' => $this->calculateSavings($pricing, $orderData),
            'price_factors' => [
                'distance_impact' => $this->calculateDistanceImpact($orderData),
                'weight_impact' => $this->calculateWeightImpact($orderData),
                'time_impact' => $this->calculateTimeImpact($orderData),
                'route_impact' => $this->calculateRouteImpact($orderData),
            ],
        ];
    }

    /**
     * Calculate savings compared to standard pricing.
     */
    private function calculateSavings(array $pricing, array $orderData): float
    {
        // Calculate what the price would be with standard multipliers
        $standardPrice = $this->calculateBasePrice($orderData) * 1.5; // Standard 50% markup
        $actualPrice = $pricing['total_price'];

        return max(0, $standardPrice - $actualPrice);
    }

    /**
     * Calculate distance impact on pricing.
     */
    private function calculateDistanceImpact(array $orderData): float
    {
        $distance = $orderData['distance'] ?? 10;

        if ($distance <= 5) {
            return 0.8;
        }      // Short distance discount
        if ($distance <= 15) {
            return 1.0;
        }     // Standard distance
        if ($distance <= 50) {
            return 1.2;
        }     // Long distance premium

        return 1.5;                          // Very long distance premium
    }

    /**
     * Calculate weight impact on pricing.
     */
    private function calculateWeightImpact(array $orderData): float
    {
        $weight = $orderData['weight'] ?? 1.0;

        return $this->weightEstimationService->getWeightCategory($weight) === 'light' ? 0.9 : 1.1;
    }

    /**
     * Calculate time impact on pricing.
     */
    private function calculateTimeImpact(array $orderData): float
    {
        $deliveryDate = Carbon::parse($orderData['delivery_date'] ?? now());

        return $this->getSeasonalMultiplier($deliveryDate);
    }

    /**
     * Calculate route impact on pricing.
     */
    private function calculateRouteImpact(array $orderData): float
    {
        return $this->getInterstateMultiplier(
            $orderData['pickup_state'] ?? '',
            $orderData['delivery_state'] ?? ''
        );
    }
}
