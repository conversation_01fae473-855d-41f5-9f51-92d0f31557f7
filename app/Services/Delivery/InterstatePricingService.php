<?php

declare(strict_types=1);

namespace App\Services\Delivery;

use App\Enums\Delivery\VehicleType;
use App\Helpers\CacheHelper;
use App\Models\Delivery\InterstatePricing;
use App\Models\Location\State;
use App\Services\Core\LoggingService;
use Illuminate\Support\Facades\DB;

/**
 * Interstate Pricing Service
 *
 * Handles interstate delivery pricing calculations, route management,
 * and pricing updates for state-to-state deliveries.
 */
class InterstatePricingService
{
    public function __construct(
        private LoggingService $loggingService
    ) {}

    /**
     * Get interstate pricing for a route.
     */
    public function getInterstatePricing(string $fromState, string $toState, string $vehicleType): ?array
    {
        try {
            $cacheKey = "interstate_pricing:{$fromState}:{$toState}:{$vehicleType}";

            // Check cache first
            $cached = CacheHelper::get($cacheKey);
            if ($cached) {
                return $cached;
            }

            $vehicleTypeEnum = VehicleType::tryFrom($vehicleType);
            if (! $vehicleTypeEnum) {
                return null;
            }

            $pricing = InterstatePricing::active()
                ->where('from_state_id', $fromState)
                ->where('to_state_id', $toState)
                ->where('vehicle_type', $vehicleTypeEnum)
                ->first();

            if (! $pricing) {
                // Try reverse route
                $pricing = InterstatePricing::active()
                    ->where('from_state_id', $toState)
                    ->where('to_state_id', $fromState)
                    ->where('vehicle_type', $vehicleTypeEnum)
                    ->first();
            }

            if (! $pricing) {
                return null;
            }

            $result = [
                'id' => $pricing->id,
                'from_state_id' => $pricing->from_state_id,
                'to_state_id' => $pricing->to_state_id,
                'vehicle_type' => $pricing->vehicle_type->value,
                'base_price' => $pricing->base_price,
                'price_per_km' => $pricing->price_per_km,
                'minimum_price' => $pricing->minimum_price,
                'maximum_price' => $pricing->maximum_price,
                'estimated_distance_km' => $pricing->estimated_distance_km,
                'estimated_duration_minutes' => $pricing->estimated_duration_minutes,
                'route_waypoints' => $pricing->route_waypoints,
                'operational_config' => $pricing->operational_config,
                'delivery_count' => $pricing->delivery_count,
                'average_actual_distance' => $pricing->average_actual_distance,
                'average_actual_duration' => $pricing->average_actual_duration,
            ];

            // Cache the result
            CacheHelper::put($cacheKey, $result, 3600); // Cache for 1 hour

            return $result;

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to get interstate pricing', $e, [
                'from_state' => $fromState,
                'to_state' => $toState,
                'vehicle_type' => $vehicleType,
            ]);

            return null;
        }
    }

    /**
     * Calculate interstate price for a given distance.
     */
    public function calculateInterstatePrice(float $distance, string $fromState, string $toState, string $vehicleType = 'motorcycle'): float
    {
        try {
            $pricing = $this->getInterstatePricing($fromState, $toState, $vehicleType);

            if (! $pricing) {
                // Fallback to default pricing calculation
                return $this->calculateFallbackPrice($distance, $vehicleType);
            }

            $price = $pricing['base_price'] + ($distance * $pricing['price_per_km']);

            // Apply minimum price
            $price = max($price, $pricing['minimum_price']);

            // Apply maximum price if set
            if ($pricing['maximum_price']) {
                $price = min($price, $pricing['maximum_price']);
            }

            return round($price, 2);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to calculate interstate price', $e, [
                'distance' => $distance,
                'from_state' => $fromState,
                'to_state' => $toState,
                'vehicle_type' => $vehicleType,
            ]);

            return $this->calculateFallbackPrice($distance, $vehicleType);
        }
    }

    /**
     * Get available interstate routes.
     */
    public function getInterstateRoutes(string $fromState, string $toState): array
    {
        try {
            $cacheKey = "interstate_routes:{$fromState}:{$toState}";

            // Check cache first
            $cached = CacheHelper::get($cacheKey);
            if ($cached) {
                return $cached;
            }

            $routes = InterstatePricing::active()
                ->where(function ($query) use ($fromState, $toState) {
                    $query->where('from_state_id', $fromState)
                        ->where('to_state_id', $toState);
                })
                ->orWhere(function ($query) use ($fromState, $toState) {
                    $query->where('from_state_id', $toState)
                        ->where('to_state_id', $fromState);
                })
                ->with(['fromState', 'toState'])
                ->get()
                ->map(function ($pricing) {
                    return [
                        'id' => $pricing->id,
                        'from_state' => [
                            'id' => $pricing->fromState->id,
                            'name' => $pricing->fromState->name,
                            'code' => $pricing->fromState->code,
                        ],
                        'to_state' => [
                            'id' => $pricing->toState->id,
                            'name' => $pricing->toState->name,
                            'code' => $pricing->toState->code,
                        ],
                        'vehicle_type' => $pricing->vehicle_type->value,
                        'estimated_distance_km' => $pricing->estimated_distance_km,
                        'estimated_duration_minutes' => $pricing->estimated_duration_minutes,
                        'base_price' => $pricing->base_price,
                        'delivery_count' => $pricing->delivery_count,
                    ];
                })
                ->toArray();

            // Cache the result
            CacheHelper::put($cacheKey, $routes, 1800); // Cache for 30 minutes

            return $routes;

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to get interstate routes', $e, [
                'from_state' => $fromState,
                'to_state' => $toState,
            ]);

            return [];
        }
    }

    /**
     * Update interstate pricing.
     */
    public function updateInterstatePricing(string $fromState, string $toState, array $pricing): bool
    {
        try {
            DB::beginTransaction();

            foreach ($pricing as $vehicleType => $priceData) {
                $vehicleTypeEnum = VehicleType::tryFrom($vehicleType);
                if (! $vehicleTypeEnum) {
                    continue;
                }

                InterstatePricing::updateOrCreate(
                    [
                        'from_state_id' => $fromState,
                        'to_state_id' => $toState,
                        'vehicle_type' => $vehicleTypeEnum,
                    ],
                    [
                        'base_price' => $priceData['base_price'],
                        'price_per_km' => $priceData['price_per_km'],
                        'minimum_price' => $priceData['minimum_price'],
                        'maximum_price' => $priceData['maximum_price'] ?? null,
                        'estimated_distance_km' => $priceData['estimated_distance_km'] ?? null,
                        'estimated_duration_minutes' => $priceData['estimated_duration_minutes'] ?? null,
                        'route_waypoints' => $priceData['route_waypoints'] ?? null,
                        'operational_config' => $priceData['operational_config'] ?? null,
                        'is_active' => $priceData['is_active'] ?? true,
                    ]
                );
            }

            DB::commit();

            // Clear related cache
            $this->clearInterstateCache($fromState, $toState);

            return true;

        } catch (\Exception $e) {
            DB::rollBack();

            $this->loggingService->logError('Failed to update interstate pricing', $e, [
                'from_state' => $fromState,
                'to_state' => $toState,
                'pricing_data' => $pricing,
            ]);

            return false;
        }
    }

    /**
     * Get popular interstate routes.
     */
    public function getPopularRoutes(int $limit = 10): array
    {
        try {
            $cacheKey = "popular_interstate_routes:{$limit}";

            // Check cache first
            $cached = CacheHelper::get($cacheKey);
            if ($cached) {
                return $cached;
            }

            $routes = InterstatePricing::active()
                ->with(['fromState', 'toState'])
                ->orderByDesc('delivery_count')
                ->limit($limit)
                ->get()
                ->map(function ($pricing) {
                    return [
                        'route' => $pricing->fromState->name.' → '.$pricing->toState->name,
                        'from_state' => $pricing->fromState->name,
                        'to_state' => $pricing->toState->name,
                        'delivery_count' => $pricing->delivery_count,
                        'average_distance' => $pricing->average_actual_distance,
                        'average_duration' => $pricing->average_actual_duration,
                        'base_price' => $pricing->base_price,
                        'vehicle_type' => $pricing->vehicle_type->getLabel(),
                    ];
                })
                ->toArray();

            // Cache the result
            CacheHelper::put($cacheKey, $routes, 3600); // Cache for 1 hour

            return $routes;

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to get popular routes', $e, [
                'limit' => $limit,
            ]);

            return [];
        }
    }

    /**
     * Calculate fallback price when no specific pricing exists.
     */
    private function calculateFallbackPrice(float $distance, string $vehicleType): float
    {
        $baseRates = [
            'motorcycle' => ['base' => 500, 'per_km' => 15],
            'bicycle' => ['base' => 300, 'per_km' => 10],
            'car' => ['base' => 800, 'per_km' => 25],
            'van' => ['base' => 1200, 'per_km' => 35],
            'truck' => ['base' => 2000, 'per_km' => 50],
        ];

        $rates = $baseRates[$vehicleType] ?? $baseRates['motorcycle'];

        return $rates['base'] + ($distance * $rates['per_km']);
    }

    /**
     * Clear interstate pricing cache.
     */
    private function clearInterstateCache(string $fromState, string $toState): void
    {
        $vehicleTypes = VehicleType::values();

        foreach ($vehicleTypes as $vehicleType) {
            CacheHelper::forget("interstate_pricing:{$fromState}:{$toState}:{$vehicleType}");
            CacheHelper::forget("interstate_pricing:{$toState}:{$fromState}:{$vehicleType}");
        }

        CacheHelper::forget("interstate_routes:{$fromState}:{$toState}");
        CacheHelper::forget("interstate_routes:{$toState}:{$fromState}");
        CacheHelper::forget('popular_interstate_routes:10');
    }
}
