<?php

declare(strict_types=1);

namespace App\Services\Delivery;

use App\Models\Delivery\DeliveryProvider;
use App\Models\User\Address;
use App\Services\Core\LoggingService;
use App\Services\Integration\GoogleMapsService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;

/**
 * Checkout Pricing Service
 *
 * Calculates upfront delivery pricing for customers during checkout.
 * Provides real-time pricing with provider matching and ETA calculations.
 */
class CheckoutPricingService
{
    public function __construct(
        private GoogleMapsService $googleMapsService,
        private PackageWeightEstimationService $weightEstimationService,
        private LoggingService $loggingService
    ) {}

    /**
     * Calculate delivery price for checkout.
     */
    public function calculateDeliveryPrice(Address $pickup, Address $delivery, array $packageData): array
    {
        try {
            $cacheKey = $this->generateCacheKey($pickup, $delivery, $packageData);

            // Check cache first
            $cached = Cache::get($cacheKey);
            if ($cached) {
                Log::info('Checkout pricing returned from cache', [
                    'pickup_city' => $pickup->city,
                    'delivery_city' => $delivery->city,
                    'service' => 'CheckoutPricingService',
                ]);

                return $cached;
            }

            // Detect delivery type (interstate/intrastate)
            $deliveryType = $this->detectDeliveryType($pickup, $delivery);

            // Calculate distance and duration
            $distanceData = $this->googleMapsService->calculateDistanceAndDuration(
                $pickup->getCoordinates(),
                $delivery->getCoordinates()
            );

            if (! $distanceData) {
                throw new \Exception('Unable to calculate distance between addresses');
            }

            // Get available providers
            $availableProviders = $this->getAvailableProviders($pickup, $delivery);

            if ($availableProviders->isEmpty()) {
                return [
                    'success' => false,
                    'message' => 'No delivery providers available for this route',
                    'delivery_type' => $deliveryType,
                    'distance' => $distanceData['distance'] ?? null,
                ];
            }

            // Estimate package weight
            $weightEstimation = $this->estimatePackageWeight($packageData);

            // Calculate pricing options
            $pricingOptions = $this->calculatePricingOptions(
                $distanceData,
                $weightEstimation,
                $availableProviders,
                $deliveryType,
                $packageData
            );

            $result = [
                'success' => true,
                'delivery_type' => $deliveryType,
                'distance' => $distanceData['distance'],
                'duration' => $distanceData['duration'],
                'weight_estimation' => $weightEstimation,
                'pricing_options' => $pricingOptions,
                'calculated_at' => now()->toISOString(),
            ];

            // Cache the result
            $this->cacheCalculation($cacheKey, $result);

            $this->loggingService->logInfo('Checkout pricing calculated successfully', [
                'pickup_city' => $pickup->city,
                'delivery_city' => $delivery->city,
                'delivery_type' => $deliveryType,
                'distance_km' => $distanceData['distance'],
                'options_count' => count($pricingOptions),
            ]);

            return $result;

        } catch (\Exception $e) {
            $this->loggingService->logError('Checkout pricing calculation failed', $e, [
                'pickup_city' => $pickup->city,
                'delivery_city' => $delivery->city,
                'package_data' => $packageData,
            ]);

            return [
                'success' => false,
                'message' => 'Unable to calculate delivery pricing at this time',
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Detect delivery type (interstate/intrastate).
     */
    public function detectDeliveryType(Address $pickup, Address $delivery): string
    {
        $pickupState = strtolower(trim($pickup->state));
        $deliveryState = strtolower(trim($delivery->state));

        return $pickupState === $deliveryState ? 'intrastate' : 'interstate';
    }

    /**
     * Get available providers for the route.
     */
    public function getAvailableProviders(Address $pickup, Address $delivery): Collection
    {
        $deliveryType = $this->detectDeliveryType($pickup, $delivery);

        $query = DeliveryProvider::query()
            ->where('status', 'active')
            ->where('is_verified', true);

        // Filter by service scope
        if ($deliveryType === 'interstate') {
            $query->whereIn('service_scope', ['interstate', 'both']);
        } else {
            $query->whereIn('service_scope', ['intrastate', 'both']);
        }

        // Filter by coverage areas (simplified for now)
        // In a more advanced system, this would check actual coverage zones
        $providers = $query->get();

        // Filter providers that can serve both pickup and delivery locations
        return $providers->filter(function ($provider) use ($pickup, $delivery) {
            return $this->providerCoversLocation($provider, $pickup) &&
                   $this->providerCoversLocation($provider, $delivery);
        });
    }

    /**
     * Calculate distance-based price.
     */
    public function calculateDistanceBasedPrice(float $distance, string $vehicleType): float
    {
        // Base pricing per km by vehicle type
        $basePricing = [
            'bicycle' => 50.0,      // ₦50 per km
            'motorcycle' => 80.0,   // ₦80 per km
            'car' => 120.0,         // ₦120 per km
            'van' => 200.0,         // ₦200 per km
            'truck' => 350.0,       // ₦350 per km
        ];

        $baseRate = $basePricing[$vehicleType] ?? $basePricing['motorcycle'];

        // Apply distance-based scaling
        $price = $distance * $baseRate;

        // Minimum price
        $minimumPrice = $basePricing[$vehicleType] * 2; // Minimum 2km equivalent

        return max($price, $minimumPrice);
    }

    /**
     * Apply time-based multiplier for delivery speed.
     */
    public function applyTimeBasedMultiplier(string $deliverySpeed): float
    {
        return match ($deliverySpeed) {
            'express' => 1.5,      // 50% premium for express
            'same_day' => 1.3,     // 30% premium for same day
            'next_day' => 1.1,     // 10% premium for next day
            'standard' => 1.0,     // Base price for standard
            'economy' => 0.8,      // 20% discount for economy
            default => 1.0,
        };
    }

    /**
     * Get estimated delivery time.
     */
    public function getEstimatedDeliveryTime(Address $pickup, Address $delivery, string $speed): Carbon
    {
        $baseTime = now();

        // Calculate travel time
        $distanceData = $this->googleMapsService->calculateDistanceAndDuration(
            $pickup->getCoordinates(),
            $delivery->getCoordinates()
        );

        $travelMinutes = $distanceData['duration'] ?? 60; // Default 1 hour if unknown

        // Add processing time based on delivery speed
        $processingTime = match ($speed) {
            'express' => 30,       // 30 minutes processing
            'same_day' => 120,     // 2 hours processing
            'next_day' => 480,     // 8 hours processing (next business day)
            'standard' => 720,     // 12 hours processing
            'economy' => 1440,     // 24 hours processing
            default => 720,
        };

        return $baseTime->addMinutes($travelMinutes + $processingTime);
    }

    /**
     * Cache pricing calculation.
     */
    public function cacheCalculation(string $key, array $result): void
    {
        // Cache for 15 minutes (pricing can change frequently)
        Cache::put($key, $result, 900);
    }

    /**
     * Estimate package weight from package data.
     */
    private function estimatePackageWeight(array $packageData): array
    {
        if (isset($packageData['items']) && is_array($packageData['items'])) {
            return $this->weightEstimationService->estimateTotalWeight($packageData['items']);
        }

        // Single item estimation
        $weight = $packageData['weight'] ?? null;
        $category = $packageData['category'] ?? 'general';

        if ($weight) {
            $estimatedWeight = (float) $weight;
        } else {
            $estimatedWeight = $this->weightEstimationService->estimateWeightByCategory($category);
        }

        $recommendedVehicle = $this->weightEstimationService->getRecommendedVehicle(
            $estimatedWeight,
            $packageData['dimensions'] ?? []
        );

        return [
            'total_weight' => $estimatedWeight,
            'recommended_vehicle' => $recommendedVehicle,
            'weight_category' => $this->weightEstimationService->getWeightCategory($estimatedWeight),
        ];
    }

    /**
     * Calculate pricing options for different delivery speeds and vehicles.
     */
    private function calculatePricingOptions(
        array $distanceData,
        array $weightEstimation,
        Collection $providers,
        string $deliveryType,
        array $packageData
    ): array {
        $options = [];
        $distance = $distanceData['distance'];
        $recommendedVehicle = $weightEstimation['recommended_vehicle'];

        // Define delivery speed options
        $speedOptions = [
            'express' => 'Express (2-4 hours)',
            'same_day' => 'Same Day (4-8 hours)',
            'next_day' => 'Next Day',
            'standard' => 'Standard (2-3 days)',
        ];

        // Add economy option for non-express deliveries
        if ($deliveryType === 'intrastate') {
            $speedOptions['economy'] = 'Economy (3-5 days)';
        }

        foreach ($speedOptions as $speed => $description) {
            $basePrice = $this->calculateDistanceBasedPrice($distance, $recommendedVehicle);
            $speedMultiplier = $this->applyTimeBasedMultiplier($speed);

            // Apply delivery type multiplier
            $typeMultiplier = $deliveryType === 'interstate' ? 1.5 : 1.0;

            // Apply weight category multiplier
            $weightMultiplier = $this->getWeightMultiplier($weightEstimation['weight_category']);

            $finalPrice = $basePrice * $speedMultiplier * $typeMultiplier * $weightMultiplier;

            // Add provider-specific pricing variations
            $providerPricing = $this->calculateProviderPricing($providers, $finalPrice);

            $estimatedDeliveryTime = $this->getEstimatedDeliveryTime(
                new Address, // Would use actual addresses
                new Address,
                $speed
            );

            $options[] = [
                'speed' => $speed,
                'description' => $description,
                'estimated_price' => round($finalPrice, 2),
                'price_range' => [
                    'min' => round($providerPricing['min'], 2),
                    'max' => round($providerPricing['max'], 2),
                ],
                'estimated_delivery_time' => $estimatedDeliveryTime->toISOString(),
                'delivery_window' => $this->getDeliveryWindow($speed),
                'available_providers' => $providerPricing['count'],
                'recommended_vehicle' => $recommendedVehicle,
                'features' => $this->getSpeedFeatures($speed),
            ];
        }

        return $options;
    }

    /**
     * Get weight-based pricing multiplier.
     */
    private function getWeightMultiplier(string $weightCategory): float
    {
        return match ($weightCategory) {
            'light' => 1.0,
            'medium' => 1.2,
            'heavy' => 1.5,
            'bulk' => 2.0,
            'freight' => 3.0,
            default => 1.0,
        };
    }

    /**
     * Calculate provider-specific pricing variations.
     */
    private function calculateProviderPricing(Collection $providers, float $basePrice): array
    {
        if ($providers->isEmpty()) {
            return ['min' => $basePrice, 'max' => $basePrice, 'count' => 0];
        }

        $prices = $providers->map(function ($provider) use ($basePrice) {
            // Apply provider-specific multipliers based on tier
            $multiplier = match ($provider->tier?->value) {
                'individual' => 0.9,
                'small_fleet' => 1.0,
                'medium_fleet' => 1.1,
                'enterprise_fleet' => 1.2,
                default => 1.0,
            };

            return $basePrice * $multiplier;
        });

        return [
            'min' => $prices->min(),
            'max' => $prices->max(),
            'count' => $providers->count(),
        ];
    }

    /**
     * Get delivery window description.
     */
    private function getDeliveryWindow(string $speed): string
    {
        return match ($speed) {
            'express' => '2-4 hours',
            'same_day' => '4-8 hours',
            'next_day' => 'Next business day',
            'standard' => '2-3 business days',
            'economy' => '3-5 business days',
            default => '2-3 business days',
        };
    }

    /**
     * Get features for delivery speed.
     */
    private function getSpeedFeatures(string $speed): array
    {
        $features = [
            'express' => ['Real-time tracking', 'Priority handling', 'SMS notifications'],
            'same_day' => ['Real-time tracking', 'SMS notifications'],
            'next_day' => ['Tracking updates', 'Email notifications'],
            'standard' => ['Basic tracking', 'Email notifications'],
            'economy' => ['Basic tracking'],
        ];

        return $features[$speed] ?? ['Basic tracking'];
    }

    /**
     * Check if provider covers a location.
     */
    private function providerCoversLocation(DeliveryProvider $provider, Address $address): bool
    {
        // Simplified coverage check - in reality, this would check against
        // the provider's actual coverage zones
        return true; // For now, assume all active providers cover all locations
    }

    /**
     * Generate cache key for pricing calculation.
     */
    private function generateCacheKey(Address $pickup, Address $delivery, array $packageData): string
    {
        $keyData = [
            'pickup' => $pickup->city.'_'.$pickup->state,
            'delivery' => $delivery->city.'_'.$delivery->state,
            'weight' => $packageData['weight'] ?? 'estimated',
            'category' => $packageData['category'] ?? 'general',
        ];

        return 'checkout_pricing:'.md5(json_encode($keyData));
    }
}
