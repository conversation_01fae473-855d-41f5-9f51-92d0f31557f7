<?php

declare(strict_types=1);

namespace App\Services\Delivery;

use App\Enums\Delivery\ZoneType;
use App\Models\Delivery\DeliveryZone;
use App\Models\Location\City;
use App\Models\Location\State;
use Illuminate\Support\Facades\DB;

class ZoneManagementService
{
    /**
     * Get zone with detailed information.
     */
    public function getZoneWithDetails(string $zoneId): array
    {
        $zone = DeliveryZone::with([
            'providers:id,business_name,service_scope',
        ])->findOrFail($zoneId);

        return [
            'zone' => $zone,
            'coverage_details' => $this->getZoneCoverageDetails($zone),
            'statistics' => $this->getZoneDetailStatistics($zone),
            'performance_metrics' => $this->getZonePerformanceMetrics($zone),
        ];
    }

    /**
     * Create new delivery zone.
     */
    public function createZone(array $data): DeliveryZone
    {
        return DB::transaction(function () use ($data) {
            // Validate zone data based on type
            $this->validateZoneData($data);

            $zone = DeliveryZone::create([
                'name' => $data['name'],
                'zone_type' => ZoneType::from($data['zone_type']),
                'states' => $data['states'] ?? null,
                'cities' => $data['cities'] ?? null,
                'polygon_coordinates' => $data['polygon_coordinates'] ?? null,
                'base_multiplier' => $data['base_multiplier'],
                'description' => $data['description'] ?? null,
                'is_active' => $data['is_active'] ?? true,
            ]);

            // Log zone creation
            activity()
                ->performedOn($zone)
                ->withProperties([
                    'zone_data' => $data,
                    'admin_id' => auth()->id(),
                ])
                ->log('delivery_zone_created');

            return $zone;
        });
    }

    /**
     * Update delivery zone.
     */
    public function updateZone(string $zoneId, array $data): DeliveryZone
    {
        return DB::transaction(function () use ($zoneId, $data) {
            $zone = DeliveryZone::findOrFail($zoneId);
            $originalData = $zone->toArray();

            // Validate updated data
            if (isset($data['zone_type'])) {
                $this->validateZoneData($data);
            }

            $zone->update([
                'name' => $data['name'] ?? $zone->name,
                'zone_type' => isset($data['zone_type']) ? ZoneType::from($data['zone_type']) : $zone->zone_type,
                'states' => $data['states'] ?? $zone->states,
                'cities' => $data['cities'] ?? $zone->cities,
                'polygon_coordinates' => $data['polygon_coordinates'] ?? $zone->polygon_coordinates,
                'base_multiplier' => $data['base_multiplier'] ?? $zone->base_multiplier,
                'description' => $data['description'] ?? $zone->description,
                'is_active' => $data['is_active'] ?? $zone->is_active,
            ]);

            // Log zone update
            activity()
                ->performedOn($zone)
                ->withProperties([
                    'original_data' => $originalData,
                    'updated_data' => $data,
                    'admin_id' => auth()->id(),
                ])
                ->log('delivery_zone_updated');

            return $zone->fresh();
        });
    }

    /**
     * Delete delivery zone.
     */
    public function deleteZone(string $zoneId): void
    {
        DB::transaction(function () use ($zoneId) {
            $zone = DeliveryZone::findOrFail($zoneId);

            // Check if zone is being used by active providers
            $activeProviders = $zone->providers()
                ->where('status', 'active')
                ->count();

            if ($activeProviders > 0) {
                throw new \InvalidArgumentException(
                    "Cannot delete zone that is used by {$activeProviders} active providers"
                );
            }

            // Log zone deletion
            activity()
                ->performedOn($zone)
                ->withProperties([
                    'zone_data' => $zone->toArray(),
                    'admin_id' => auth()->id(),
                ])
                ->log('delivery_zone_deleted');

            // Delete the zone
            $zone->delete();
        });
    }

    /**
     * Toggle zone active status.
     */
    public function toggleZoneStatus(string $zoneId, bool $isActive): DeliveryZone
    {
        return DB::transaction(function () use ($zoneId, $isActive) {
            $zone = DeliveryZone::findOrFail($zoneId);

            $zone->update(['is_active' => $isActive]);

            // Log status change
            activity()
                ->performedOn($zone)
                ->withProperties([
                    'old_status' => ! $isActive,
                    'new_status' => $isActive,
                    'admin_id' => auth()->id(),
                ])
                ->log($isActive ? 'zone_activated' : 'zone_deactivated');

            return $zone->fresh();
        });
    }

    /**
     * Get zone analytics.
     */
    public function getZoneAnalytics(string $period): array
    {
        $periodDates = $this->getPeriodDates($period);

        return [
            'zone_overview' => $this->getZoneOverviewStats(),
            'coverage_analysis' => $this->getZoneCoverageAnalysis(),
            'performance_metrics' => $this->getZonePerformanceAnalytics($periodDates),
            'utilization_stats' => $this->getZoneUtilizationStats($periodDates),
            'provider_distribution' => $this->getProviderDistributionByZone(),
        ];
    }

    /**
     * Get zone coverage map.
     */
    public function getZoneCoverage(): array
    {
        $zones = DeliveryZone::where('is_active', true)->get();

        return [
            'total_zones' => $zones->count(),
            'coverage_by_type' => $this->getCoverageByType($zones),
            'state_coverage' => $this->getStateCoverage($zones),
            'city_coverage' => $this->getCityCoverage($zones),
            'coverage_gaps' => $this->identifyCoverageGaps(),
        ];
    }

    /**
     * Test route coverage.
     */
    public function testRouteCoverage(string $fromStateId, string $toStateId, ?string $fromCityId = null, ?string $toCityId = null): array
    {
        $coveringZones = DeliveryZone::where('is_active', true)->get()
            ->filter(function ($zone) use ($fromStateId, $toStateId) {
                return $zone->canServeRoute($fromStateId, $toStateId);
            });

        $availableProviders = [];
        foreach ($coveringZones as $zone) {
            $providers = $zone->providers()
                ->where('status', 'active')
                ->get(['id', 'business_name', 'service_scope']);

            $availableProviders = array_merge($availableProviders, $providers->toArray());
        }

        return [
            'route_covered' => $coveringZones->isNotEmpty(),
            'covering_zones' => $coveringZones->map(function ($zone) {
                return [
                    'id' => $zone->id,
                    'name' => $zone->name,
                    'zone_type' => $zone->zone_type->value,
                    'base_multiplier' => $zone->base_multiplier,
                ];
            })->toArray(),
            'available_providers' => array_unique($availableProviders, SORT_REGULAR),
            'estimated_multiplier' => $coveringZones->min('base_multiplier') ?? 1.0,
            'route_details' => [
                'from_state' => State::find($fromStateId)?->name,
                'to_state' => State::find($toStateId)?->name,
                'from_city' => $fromCityId ? City::find($fromCityId)?->name : null,
                'to_city' => $toCityId ? City::find($toCityId)?->name : null,
            ],
        ];
    }

    /**
     * Get zone statistics overview.
     */
    public function getZoneStatistics(): array
    {
        return [
            'total_zones' => DeliveryZone::count(),
            'active_zones' => DeliveryZone::where('is_active', true)->count(),
            'zones_by_type' => $this->getZonesByType(),
            'zones_with_providers' => DeliveryZone::whereHas('providers')->count(),
            'unused_zones' => DeliveryZone::whereDoesntHave('providers')->count(),
            'average_multiplier' => DeliveryZone::avg('base_multiplier'),
            'coverage_statistics' => $this->getCoverageStatistics(),
        ];
    }

    /**
     * Private helper methods.
     */
    private function validateZoneData(array $data): void
    {
        $zoneType = ZoneType::from($data['zone_type']);

        switch ($zoneType) {
            case ZoneType::CITY:
                if (empty($data['cities'])) {
                    throw new \InvalidArgumentException('Cities are required for city zone type');
                }
                break;
            case ZoneType::STATE:
            case ZoneType::INTERSTATE:
                if (empty($data['states'])) {
                    throw new \InvalidArgumentException('States are required for state/interstate zone type');
                }
                break;
            case ZoneType::POLYGON:
                if (empty($data['polygon_coordinates'])) {
                    throw new \InvalidArgumentException('Polygon coordinates are required for polygon zone type');
                }
                break;
        }
    }

    private function getZoneCoverageDetails(DeliveryZone $zone): array
    {
        $details = [
            'zone_type' => $zone->zone_type->value,
            'coverage_area' => [],
        ];

        switch ($zone->zone_type) {
            case ZoneType::CITY:
                $details['coverage_area'] = City::whereIn('id', $zone->cities ?? [])
                    ->with('state:id,name')
                    ->get(['id', 'name', 'state_id'])
                    ->toArray();
                break;
            case ZoneType::STATE:
            case ZoneType::INTERSTATE:
                $details['coverage_area'] = State::whereIn('id', $zone->states ?? [])
                    ->get(['id', 'name', 'code'])
                    ->toArray();
                break;
            case ZoneType::POLYGON:
                $details['coverage_area'] = [
                    'coordinates' => $zone->polygon_coordinates,
                    'area_calculated' => $this->calculatePolygonArea($zone->polygon_coordinates),
                ];
                break;
        }

        return $details;
    }

    private function getZoneDetailStatistics(DeliveryZone $zone): array
    {
        return [
            'provider_count' => $zone->providers()->count(),
            'active_provider_count' => $zone->providers()->where('status', 'active')->count(),
            'total_deliveries' => $this->getZoneDeliveryCount($zone->id),
            'average_delivery_time' => $this->getAverageDeliveryTime($zone->id),
            'success_rate' => $this->getZoneSuccessRate($zone->id),
        ];
    }

    private function getZonePerformanceMetrics(DeliveryZone $zone): array
    {
        // This would integrate with delivery analytics
        return [
            'delivery_volume' => 0,
            'average_cost' => 0,
            'customer_satisfaction' => 0,
            'provider_utilization' => 0,
        ];
    }

    private function getZoneOverviewStats(): array
    {
        return [
            'total_zones' => DeliveryZone::count(),
            'active_zones' => DeliveryZone::where('is_active', true)->count(),
            'zones_by_type' => $this->getZonesByType(),
            'total_coverage_area' => $this->calculateTotalCoverageArea(),
        ];
    }

    private function getZonesByType(): array
    {
        return DeliveryZone::select('zone_type')
            ->selectRaw('COUNT(*) as count')
            ->groupBy('zone_type')
            ->pluck('count', 'zone_type')
            ->toArray();
    }

    private function getCoverageByType(object $zones): array
    {
        return $zones->groupBy('zone_type')
            ->map(fn ($group) => $group->count())
            ->toArray();
    }

    private function getStateCoverage(object $zones): array
    {
        $coveredStates = $zones->flatMap(fn ($zone) => $zone->states ?? [])
            ->unique()
            ->values();

        return [
            'covered_states' => $coveredStates->count(),
            'total_states' => State::count(),
            'coverage_percentage' => State::count() > 0
                ? ($coveredStates->count() / State::count()) * 100
                : 0,
        ];
    }

    private function getCityCoverage(object $zones): array
    {
        $coveredCities = $zones->flatMap(fn ($zone) => $zone->cities ?? [])
            ->unique()
            ->values();

        return [
            'covered_cities' => $coveredCities->count(),
            'total_cities' => City::count(),
            'coverage_percentage' => City::count() > 0
                ? ($coveredCities->count() / City::count()) * 100
                : 0,
        ];
    }

    private function identifyCoverageGaps(): array
    {
        // This would identify areas without delivery coverage
        return [
            'uncovered_states' => [],
            'uncovered_cities' => [],
            'low_coverage_areas' => [],
        ];
    }

    private function getZoneCoverageAnalysis(): array
    {
        return [
            'coverage_overlap' => $this->calculateCoverageOverlap(),
            'coverage_efficiency' => $this->calculateCoverageEfficiency(),
            'optimal_zones' => $this->identifyOptimalZones(),
        ];
    }

    private function getZonePerformanceAnalytics(array $periodDates): array
    {
        return [
            'top_performing_zones' => [],
            'underperforming_zones' => [],
            'zone_utilization_trends' => [],
        ];
    }

    private function getZoneUtilizationStats(array $periodDates): array
    {
        return [
            'high_utilization_zones' => [],
            'low_utilization_zones' => [],
            'utilization_trends' => [],
        ];
    }

    private function getProviderDistributionByZone(): array
    {
        return DeliveryZone::withCount('providers')
            ->orderByDesc('providers_count')
            ->get(['id', 'name', 'zone_type'])
            ->toArray();
    }

    private function getCoverageStatistics(): array
    {
        return [
            'total_coverage_area' => $this->calculateTotalCoverageArea(),
            'average_zone_size' => $this->calculateAverageZoneSize(),
            'coverage_density' => $this->calculateCoverageDensity(),
        ];
    }

    // Placeholder methods for complex calculations
    private function calculatePolygonArea(?array $coordinates): float
    {
        return 0.0; // Would implement polygon area calculation
    }

    private function getZoneDeliveryCount(string $zoneId): int
    {
        return 0; // Would integrate with delivery data
    }

    private function getAverageDeliveryTime(string $zoneId): float
    {
        return 0.0; // Would calculate from delivery data
    }

    private function getZoneSuccessRate(string $zoneId): float
    {
        return 0.0; // Would calculate from delivery success data
    }

    private function calculateTotalCoverageArea(): float
    {
        return 0.0; // Would calculate total coverage area
    }

    private function calculateCoverageOverlap(): float
    {
        return 0.0; // Would calculate zone overlap
    }

    private function calculateCoverageEfficiency(): float
    {
        return 0.0; // Would calculate coverage efficiency
    }

    private function identifyOptimalZones(): array
    {
        return []; // Would identify optimal zone configurations
    }

    private function calculateAverageZoneSize(): float
    {
        return 0.0; // Would calculate average zone size
    }

    private function calculateCoverageDensity(): float
    {
        return 0.0; // Would calculate coverage density
    }

    private function getPeriodDates(string $period): array
    {
        $now = now();

        return match ($period) {
            'today' => [
                'start' => $now->copy()->startOfDay(),
                'end' => $now->copy()->endOfDay(),
            ],
            'week' => [
                'start' => $now->copy()->startOfWeek(),
                'end' => $now->copy()->endOfWeek(),
            ],
            'month' => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
            'quarter' => [
                'start' => $now->copy()->startOfQuarter(),
                'end' => $now->copy()->endOfQuarter(),
            ],
            'year' => [
                'start' => $now->copy()->startOfYear(),
                'end' => $now->copy()->endOfYear(),
            ],
            default => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
        };
    }
}
