<?php

declare(strict_types=1);

namespace App\Services\Delivery;

use App\Models\Product\Product;
use App\Services\Core\LoggingService;
use Illuminate\Support\Facades\Log;

/**
 * Package Weight Estimation Service
 *
 * Provides intelligent weight estimation for packages based on categories,
 * products, and customer input validation for upfront pricing calculations.
 */
class PackageWeightEstimationService
{
    private array $categoryWeights;

    private array $weightLimits;

    private array $vehicleCapacities;

    private array $dimensionalFactors;

    public function __construct(
        private LoggingService $loggingService
    ) {
        $this->loadConfiguration();
    }

    /**
     * Estimate weight based on product category.
     */
    public function estimateWeightByCategory(string $category): float
    {
        $category = strtolower($category);

        if (! isset($this->categoryWeights[$category])) {
            Log::warning('Unknown category for weight estimation', [
                'category' => $category,
                'service' => 'PackageWeightEstimationService',
            ]);

            // Return default weight for unknown categories
            return $this->categoryWeights['general'] ?? 1.0;
        }

        $weightData = $this->categoryWeights[$category];

        // Return average of min and max for estimation
        return ($weightData['min'] + $weightData['max']) / 2;
    }

    /**
     * Estimate weight based on specific product.
     */
    public function estimateWeightByProduct(Product $product): float
    {
        // If product has specific weight, use it
        if ($product->weight && $product->weight > 0) {
            return $product->weight;
        }

        // If product has dimensions, calculate dimensional weight
        if ($product->length && $product->width && $product->height) {
            $dimensionalWeight = $this->calculateDimensionalWeight(
                $product->length,
                $product->width,
                $product->height
            );

            // Use the higher of category weight or dimensional weight
            $categoryWeight = $this->estimateWeightByCategory($product->category ?? 'general');

            return max($categoryWeight, $dimensionalWeight);
        }

        // Fall back to category-based estimation
        return $this->estimateWeightByCategory($product->category ?? 'general');
    }

    /**
     * Validate customer-provided weight against category expectations.
     */
    public function validateCustomerWeight(float $weight, string $category): bool
    {
        if ($weight <= 0) {
            return false;
        }

        $category = strtolower($category);

        if (! isset($this->weightLimits[$category])) {
            // Use general limits for unknown categories
            $category = 'general';
        }

        $limits = $this->weightLimits[$category];

        return $weight >= $limits['min'] && $weight <= $limits['max'];
    }

    /**
     * Get weight range for a specific category.
     */
    public function getWeightRange(string $category): array
    {
        $category = strtolower($category);

        if (! isset($this->categoryWeights[$category])) {
            $category = 'general';
        }

        return [
            'min' => $this->categoryWeights[$category]['min'],
            'max' => $this->categoryWeights[$category]['max'],
            'average' => ($this->categoryWeights[$category]['min'] + $this->categoryWeights[$category]['max']) / 2,
            'unit' => 'kg',
        ];
    }

    /**
     * Calculate dimensional weight based on package dimensions.
     */
    public function calculateDimensionalWeight(float $length, float $width, float $height): float
    {
        // Convert dimensions to meters if they're in centimeters
        if ($length > 10 || $width > 10 || $height > 10) {
            $length /= 100;
            $width /= 100;
            $height /= 100;
        }

        // Calculate volume in cubic meters
        $volume = $length * $width * $height;

        // Apply dimensional weight factor (kg per cubic meter)
        $dimensionalWeight = $volume * $this->dimensionalFactors['density_factor'];

        // Minimum dimensional weight
        return max($dimensionalWeight, $this->dimensionalFactors['minimum_weight']);
    }

    /**
     * Get recommended vehicle type based on weight and dimensions.
     */
    public function getRecommendedVehicle(float $weight, array $dimensions = []): string
    {
        // Calculate dimensional weight if dimensions provided
        $effectiveWeight = $weight;
        if (! empty($dimensions) && isset($dimensions['length'], $dimensions['width'], $dimensions['height'])) {
            $dimensionalWeight = $this->calculateDimensionalWeight(
                $dimensions['length'],
                $dimensions['width'],
                $dimensions['height']
            );
            $effectiveWeight = max($weight, $dimensionalWeight);
        }

        // Find appropriate vehicle based on weight capacity
        foreach ($this->vehicleCapacities as $vehicleType => $capacity) {
            if ($effectiveWeight <= $capacity['max_weight']) {
                // Also check dimensions if provided
                if (! empty($dimensions)) {
                    $maxDimension = max($dimensions['length'] ?? 0, $dimensions['width'] ?? 0, $dimensions['height'] ?? 0);
                    if ($maxDimension > ($capacity['max_dimension'] ?? PHP_FLOAT_MAX)) {
                        continue;
                    }
                }

                return $vehicleType;
            }
        }

        // If no vehicle can handle the weight, return the largest
        return array_key_last($this->vehicleCapacities);
    }

    /**
     * Get all available categories with their weight ranges.
     */
    public function getAvailableCategories(): array
    {
        $categories = [];

        foreach ($this->categoryWeights as $category => $weights) {
            $categories[$category] = [
                'name' => ucfirst(str_replace('_', ' ', $category)),
                'min_weight' => $weights['min'],
                'max_weight' => $weights['max'],
                'average_weight' => ($weights['min'] + $weights['max']) / 2,
                'description' => $this->getCategoryDescription($category),
            ];
        }

        return $categories;
    }

    /**
     * Estimate total weight for multiple items.
     */
    public function estimateTotalWeight(array $items): array
    {
        $totalWeight = 0;
        $itemBreakdown = [];
        $recommendedVehicles = [];

        foreach ($items as $item) {
            $itemWeight = 0;

            if (isset($item['product']) && $item['product'] instanceof Product) {
                $itemWeight = $this->estimateWeightByProduct($item['product']);
            } elseif (isset($item['category'])) {
                $itemWeight = $this->estimateWeightByCategory($item['category']);
            } elseif (isset($item['weight'])) {
                $itemWeight = (float) $item['weight'];
            }

            $quantity = $item['quantity'] ?? 1;
            $lineWeight = $itemWeight * $quantity;
            $totalWeight += $lineWeight;

            $itemBreakdown[] = [
                'item' => $item['name'] ?? 'Unknown item',
                'weight_per_unit' => $itemWeight,
                'quantity' => $quantity,
                'total_weight' => $lineWeight,
                'category' => $item['category'] ?? 'general',
            ];

            // Get recommended vehicle for this item
            $vehicleType = $this->getRecommendedVehicle($lineWeight, $item['dimensions'] ?? []);
            if (! in_array($vehicleType, $recommendedVehicles)) {
                $recommendedVehicles[] = $vehicleType;
            }
        }

        // Get overall recommended vehicle
        $overallVehicle = $this->getRecommendedVehicle($totalWeight);

        return [
            'total_weight' => round($totalWeight, 2),
            'item_breakdown' => $itemBreakdown,
            'recommended_vehicle' => $overallVehicle,
            'alternative_vehicles' => $recommendedVehicles,
            'weight_category' => $this->getWeightCategory($totalWeight),
        ];
    }

    /**
     * Get weight category for pricing purposes.
     */
    public function getWeightCategory(float $weight): string
    {
        if ($weight <= 1) {
            return 'light';
        }
        if ($weight <= 5) {
            return 'medium';
        }
        if ($weight <= 20) {
            return 'heavy';
        }
        if ($weight <= 50) {
            return 'bulk';
        }

        return 'freight';
    }

    /**
     * Validate package dimensions.
     */
    public function validateDimensions(array $dimensions): array
    {
        $errors = [];

        $required = ['length', 'width', 'height'];
        foreach ($required as $dimension) {
            if (! isset($dimensions[$dimension]) || $dimensions[$dimension] <= 0) {
                $errors[] = "Invalid {$dimension}: must be greater than 0";
            }
        }

        // Check maximum dimensions
        $maxDimension = 300; // 3 meters in centimeters
        foreach ($required as $dimension) {
            if (isset($dimensions[$dimension]) && $dimensions[$dimension] > $maxDimension) {
                $errors[] = "Invalid {$dimension}: maximum allowed is {$maxDimension}cm";
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
        ];
    }

    /**
     * Get category description for UI.
     */
    private function getCategoryDescription(string $category): string
    {
        $descriptions = [
            'electronics' => 'Phones, laptops, gadgets, and electronic devices',
            'clothing' => 'Apparel, shoes, accessories, and textiles',
            'food' => 'Packaged food items, beverages, and perishables',
            'documents' => 'Papers, books, files, and printed materials',
            'furniture' => 'Home and office furniture, large items',
            'cosmetics' => 'Beauty products, skincare, and personal care items',
            'books' => 'Books, magazines, and printed publications',
            'toys' => 'Children\'s toys, games, and recreational items',
            'automotive' => 'Car parts, accessories, and automotive supplies',
            'sports' => 'Sports equipment, fitness gear, and outdoor items',
            'jewelry' => 'Precious items, watches, and valuable accessories',
            'medical' => 'Medical supplies, pharmaceuticals, and health products',
            'general' => 'Miscellaneous items and general packages',
        ];

        return $descriptions[$category] ?? 'General category items';
    }

    /**
     * Load weight estimation configuration.
     */
    private function loadConfiguration(): void
    {
        // Category-based weight defaults (in kg)
        $this->categoryWeights = [
            'electronics' => ['min' => 0.1, 'max' => 10.0],
            'clothing' => ['min' => 0.1, 'max' => 3.0],
            'food' => ['min' => 0.1, 'max' => 5.0],
            'documents' => ['min' => 0.01, 'max' => 2.0],
            'furniture' => ['min' => 5.0, 'max' => 100.0],
            'cosmetics' => ['min' => 0.05, 'max' => 2.0],
            'books' => ['min' => 0.1, 'max' => 5.0],
            'toys' => ['min' => 0.1, 'max' => 10.0],
            'automotive' => ['min' => 0.5, 'max' => 50.0],
            'sports' => ['min' => 0.2, 'max' => 20.0],
            'jewelry' => ['min' => 0.01, 'max' => 1.0],
            'medical' => ['min' => 0.05, 'max' => 5.0],
            'general' => ['min' => 0.1, 'max' => 10.0],
        ];

        // Weight validation limits (more lenient for customer input)
        $this->weightLimits = [
            'electronics' => ['min' => 0.01, 'max' => 25.0],
            'clothing' => ['min' => 0.01, 'max' => 10.0],
            'food' => ['min' => 0.01, 'max' => 20.0],
            'documents' => ['min' => 0.001, 'max' => 5.0],
            'furniture' => ['min' => 1.0, 'max' => 200.0],
            'cosmetics' => ['min' => 0.01, 'max' => 5.0],
            'books' => ['min' => 0.01, 'max' => 15.0],
            'toys' => ['min' => 0.01, 'max' => 25.0],
            'automotive' => ['min' => 0.1, 'max' => 100.0],
            'sports' => ['min' => 0.05, 'max' => 50.0],
            'jewelry' => ['min' => 0.001, 'max' => 2.0],
            'medical' => ['min' => 0.01, 'max' => 15.0],
            'general' => ['min' => 0.01, 'max' => 50.0],
        ];

        // Vehicle weight capacities (in kg)
        $this->vehicleCapacities = [
            'bicycle' => ['max_weight' => 15.0, 'max_dimension' => 100],
            'motorcycle' => ['max_weight' => 50.0, 'max_dimension' => 150],
            'car' => ['max_weight' => 200.0, 'max_dimension' => 200],
            'van' => ['max_weight' => 1000.0, 'max_dimension' => 300],
            'truck' => ['max_weight' => 5000.0, 'max_dimension' => 500],
        ];

        // Dimensional weight calculation factors
        $this->dimensionalFactors = [
            'density_factor' => 200.0, // kg per cubic meter
            'minimum_weight' => 0.1, // minimum dimensional weight in kg
        ];
    }
}
