<?php

declare(strict_types=1);

namespace App\Services\Delivery;

use App\Helpers\CacheHelper;
use App\Models\Delivery\DeliveryProvider;
use App\Models\Delivery\DeliveryZone;
use App\Models\User\Address;
use App\Services\Integration\GoogleMapsService;
use App\Services\System\LoggingService;
use Illuminate\Database\Eloquent\Collection;

/**
 * Delivery Zone Detection Service
 *
 * Detects delivery zones and provider coverage for addresses.
 * Handles interstate detection, provider matching, and zone optimization.
 */
class DeliveryZoneDetectionService
{
    public function __construct(
        private LoggingService $loggingService,
        private GoogleMapsService $googleMapsService
    ) {}

    /**
     * Detect delivery zone for an address.
     */
    public function detectDeliveryZone(Address $address): ?DeliveryZone
    {
        try {
            $cacheKey = "delivery_zone:address:{$address->id}";

            // Check cache first
            $cached = CacheHelper::get($cacheKey);
            if ($cached) {
                return $cached;
            }

            // Find zones that cover this address
            $zones = DeliveryZone::active()
                ->where(function ($query) use ($address) {
                    // Check city coverage
                    if ($address->city_id) {
                        $query->orWhere(function ($q) use ($address) {
                            $q->whereJsonContains('cities', $address->city_id);
                        });
                    }

                    // Check state coverage
                    if ($address->state_id) {
                        $query->orWhere(function ($q) use ($address) {
                            $q->whereJsonContains('states', $address->state_id);
                        });
                    }
                })
                ->orderBy('zone_type')
                ->get();

            // Prioritize most specific zone
            $zone = $this->selectBestZone($zones, $address);

            // Cache the result
            if ($zone) {
                CacheHelper::put($cacheKey, $zone, 3600); // Cache for 1 hour
            }

            return $zone;

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to detect delivery zone', $e, [
                'address_id' => $address->id,
                'city_id' => $address->city_id,
                'state_id' => $address->state_id,
            ]);

            return null;
        }
    }

    /**
     * Check if delivery is interstate.
     */
    public function isInterstateDelivery(Address $pickup, Address $delivery): bool
    {
        if (! $pickup->state_id || ! $delivery->state_id) {
            return false;
        }

        return $pickup->state_id !== $delivery->state_id;
    }

    /**
     * Find providers that can cover the route.
     */
    public function findCoveringProviders(Address $pickup, Address $delivery): Collection
    {
        try {
            $isInterstate = $this->isInterstateDelivery($pickup, $delivery);

            $cacheKey = "covering_providers:{$pickup->state_id}:{$delivery->state_id}:".($isInterstate ? 'interstate' : 'intrastate');

            // Check cache first
            $cached = CacheHelper::get($cacheKey);
            if ($cached) {
                return $cached;
            }

            $query = DeliveryProvider::query()
                ->where('status', 'active')
                ->where('is_verified', true);

            // Filter by service scope
            if ($isInterstate) {
                $query->whereIn('service_scope', ['interstate', 'both']);
            } else {
                $query->whereIn('service_scope', ['intrastate', 'both']);
            }

            // Get providers with their service areas
            $providers = $query->with(['serviceAreas.zone'])->get();

            // Filter providers that actually cover the route
            $coveringProviders = $providers->filter(function ($provider) use ($pickup, $delivery, $isInterstate) {
                return $this->providerCoversRoute($provider, $pickup, $delivery, $isInterstate);
            });

            // Cache the result
            CacheHelper::put($cacheKey, $coveringProviders, 1800); // Cache for 30 minutes

            return $coveringProviders;

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to find covering providers', $e, [
                'pickup_state' => $pickup->state_id,
                'delivery_state' => $delivery->state_id,
            ]);

            return new Collection;
        }
    }

    /**
     * Calculate distance from zone center to address.
     */
    public function calculateZoneDistance(DeliveryZone $zone, Address $address): float
    {
        try {
            // For simple zones, calculate distance to state/city center
            if ($zone->zone_type->isSimpleZone()) {
                return $this->calculateSimpleZoneDistance($zone, $address);
            }

            // For polygon zones (future implementation)
            if ($zone->zone_type->value === 'polygon' && $zone->polygon_coordinates) {
                return $this->calculatePolygonZoneDistance($zone, $address);
            }

            return 0.0;

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to calculate zone distance', $e, [
                'zone_id' => $zone->id,
                'address_id' => $address->id,
            ]);

            return 0.0;
        }
    }

    /**
     * Get zone pricing multiplier for vehicle type.
     */
    public function getZonePricingMultiplier(DeliveryZone $zone, string $vehicleType): float
    {
        try {
            // Base multiplier from zone
            $baseMultiplier = $zone->base_multiplier;

            // Vehicle-specific adjustments from zone metadata
            $vehicleMultipliers = $zone->metadata['vehicle_multipliers'] ?? [];
            $vehicleAdjustment = $vehicleMultipliers[$vehicleType] ?? 1.0;

            return $baseMultiplier * $vehicleAdjustment;

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to get zone pricing multiplier', $e, [
                'zone_id' => $zone->id,
                'vehicle_type' => $vehicleType,
            ]);

            return 1.0; // Default multiplier
        }
    }

    /**
     * Optimize provider selection based on criteria.
     */
    public function optimizeProviderSelection(Collection $providers, array $criteria): Collection
    {
        try {
            $sortBy = $criteria['sort_by'] ?? 'rating';
            $maxProviders = $criteria['max_providers'] ?? 5;

            $optimized = $providers->sortByDesc(function ($provider) use ($sortBy) {
                return match ($sortBy) {
                    'rating' => $provider->performance_rating_avg ?? 0,
                    'price' => -($provider->base_pricing ?? 0), // Negative for ascending price
                    'distance' => -($provider->distance ?? 0), // Negative for ascending distance
                    'delivery_count' => $provider->delivery_count ?? 0,
                    default => $provider->performance_rating_avg ?? 0,
                };
            });

            return $optimized->take($maxProviders);

        } catch (\Exception $e) {
            $this->loggingService->logError('Failed to optimize provider selection', $e, [
                'provider_count' => $providers->count(),
                'criteria' => $criteria,
            ]);

            return $providers->take(5); // Return first 5 as fallback
        }
    }

    /**
     * Select the best zone from multiple matching zones.
     */
    private function selectBestZone(Collection $zones, Address $address): ?DeliveryZone
    {
        if ($zones->isEmpty()) {
            return null;
        }

        // Priority: City > State > Interstate > Polygon
        $priorityOrder = ['city', 'state', 'interstate', 'polygon'];

        foreach ($priorityOrder as $type) {
            $zone = $zones->firstWhere('zone_type', $type);
            if ($zone) {
                return $zone;
            }
        }

        return $zones->first();
    }

    /**
     * Check if provider covers the route.
     */
    private function providerCoversRoute(DeliveryProvider $provider, Address $pickup, Address $delivery, bool $isInterstate): bool
    {
        // Check service scope compatibility
        if (! $provider->service_scope->canServe($pickup->state_id, $delivery->state_id)) {
            return false;
        }

        // Check if provider has service areas covering the route
        foreach ($provider->serviceAreas as $serviceArea) {
            if ($serviceArea->canServeRoute($pickup->state_id, $delivery->state_id)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Calculate distance for simple zones (city/state).
     */
    private function calculateSimpleZoneDistance(DeliveryZone $zone, Address $address): float
    {
        // For simple zones, we can use the address coordinates
        // and calculate distance to zone center (simplified)
        if (! $address->latitude || ! $address->longitude) {
            return 0.0;
        }

        // This is a simplified implementation
        // In reality, you'd calculate distance to zone center
        return 0.0;
    }

    /**
     * Calculate distance for polygon zones (future implementation).
     */
    private function calculatePolygonZoneDistance(DeliveryZone $zone, Address $address): float
    {
        // Future implementation for polygon zones
        // Would use geometric calculations to find distance to polygon boundary
        return 0.0;
    }
}
