<?php

declare(strict_types=1);

namespace App\Services\Marketing;

use App\Models\Marketing\Campaign;
use App\Models\Marketing\CampaignAudience;
use App\Models\Marketing\CampaignMetric;
use App\Models\User\User;
use App\Services\System\LoggingService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class CampaignManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get campaign overview statistics.
     */
    public function getCampaignOverview(): array
    {
        return [
            'total_campaigns' => Campaign::count(),
            'active_campaigns' => Campaign::where('status', 'active')->count(),
            'draft_campaigns' => Campaign::where('status', 'draft')->count(),
            'completed_campaigns' => Campaign::where('status', 'completed')->count(),
            'paused_campaigns' => Campaign::where('status', 'paused')->count(),
            'campaigns_this_month' => Campaign::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
            'total_reach' => CampaignMetric::sum('total_sent'),
            'total_engagement' => CampaignMetric::sum('total_opened'),
            'average_open_rate' => $this->calculateAverageOpenRate(),
            'average_click_rate' => $this->calculateAverageClickRate(),
            'total_conversions' => CampaignMetric::sum('total_conversions'),
            'total_revenue' => CampaignMetric::sum('revenue_generated'),
        ];
    }

    /**
     * Get campaign performance analytics.
     */
    public function getCampaignAnalytics(array $filters = []): array
    {
        $query = Campaign::with(['metrics', 'audiences']);

        // Apply filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['type'])) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $campaigns = $query->get();

        return [
            'campaign_performance' => $this->getCampaignPerformanceBreakdown($campaigns),
            'channel_performance' => $this->getChannelPerformanceBreakdown($campaigns),
            'audience_insights' => $this->getAudienceInsights($campaigns),
            'conversion_funnel' => $this->getConversionFunnel($campaigns),
            'roi_analysis' => $this->getROIAnalysis($campaigns),
            'trend_analysis' => $this->getTrendAnalysis($campaigns),
        ];
    }

    /**
     * Create a new campaign.
     */
    public function createCampaign(array $data): Campaign
    {
        return DB::transaction(function () use ($data) {
            // Create campaign
            $campaign = Campaign::create([
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'type' => $data['type'],
                'status' => 'draft',
                'channels' => $data['channels'],
                'content' => [
                    'subject' => $data['subject'] ?? null,
                    'message' => $data['message'],
                    'template_id' => $data['template_id'] ?? null,
                    'variables' => $data['variables'] ?? [],
                ],
                'settings' => [
                    'send_immediately' => $data['send_immediately'] ?? false,
                    'scheduled_at' => $data['scheduled_at'] ?? null,
                    'timezone' => $data['timezone'] ?? 'Africa/Lagos',
                    'frequency' => $data['frequency'] ?? 'once',
                    'ab_test_enabled' => $data['ab_test_enabled'] ?? false,
                ],
                'budget' => [
                    'total_budget' => $data['total_budget'] ?? null,
                    'daily_budget' => $data['daily_budget'] ?? null,
                    'currency' => $data['currency'] ?? 'NGN',
                ],
                'created_by' => auth()->id(),
            ]);

            // Create audience segments
            if (isset($data['audiences'])) {
                foreach ($data['audiences'] as $audienceData) {
                    CampaignAudience::create([
                        'campaign_id' => $campaign->id,
                        'name' => $audienceData['name'],
                        'criteria' => $audienceData['criteria'],
                        'estimated_size' => $this->calculateAudienceSize($audienceData['criteria']),
                    ]);
                }
            }

            // Initialize metrics
            CampaignMetric::create([
                'campaign_id' => $campaign->id,
                'total_sent' => 0,
                'total_delivered' => 0,
                'total_opened' => 0,
                'total_clicked' => 0,
                'total_conversions' => 0,
                'total_unsubscribed' => 0,
                'revenue_generated' => 0,
                'cost_spent' => 0,
            ]);

            $this->loggingService->logInfo('Campaign created', [
                'campaign_id' => $campaign->id,
                'campaign_name' => $campaign->name,
                'type' => $campaign->type,
                'created_by' => auth()->id(),
            ]);

            return $campaign->load(['audiences', 'metrics']);
        });
    }

    /**
     * Update campaign.
     */
    public function updateCampaign(string $campaignId, array $data): Campaign
    {
        return DB::transaction(function () use ($campaignId, $data) {
            $campaign = Campaign::findOrFail($campaignId);

            // Prevent editing active campaigns
            if ($campaign->status === 'active' && ! isset($data['status'])) {
                throw new \InvalidArgumentException('Cannot edit active campaign content');
            }

            $updateData = array_filter([
                'name' => $data['name'] ?? null,
                'description' => $data['description'] ?? null,
                'type' => $data['type'] ?? null,
                'channels' => $data['channels'] ?? null,
                'status' => $data['status'] ?? null,
            ], fn ($value) => $value !== null);

            // Update content if provided
            if (isset($data['content'])) {
                $content = $campaign->content ?? [];
                $updateData['content'] = array_merge($content, $data['content']);
            }

            // Update settings if provided
            if (isset($data['settings'])) {
                $settings = $campaign->settings ?? [];
                $updateData['settings'] = array_merge($settings, $data['settings']);
            }

            // Update budget if provided
            if (isset($data['budget'])) {
                $budget = $campaign->budget ?? [];
                $updateData['budget'] = array_merge($budget, $data['budget']);
            }

            $campaign->update($updateData);

            $this->loggingService->logInfo('Campaign updated', [
                'campaign_id' => $campaign->id,
                'updated_fields' => array_keys($updateData),
                'updated_by' => auth()->id(),
            ]);

            return $campaign->fresh(['audiences', 'metrics']);
        });
    }

    /**
     * Launch campaign.
     */
    public function launchCampaign(string $campaignId): array
    {
        return DB::transaction(function () use ($campaignId) {
            $campaign = Campaign::with(['audiences', 'metrics'])->findOrFail($campaignId);

            if ($campaign->status !== 'draft') {
                throw new \InvalidArgumentException('Only draft campaigns can be launched');
            }

            // Validate campaign is ready for launch
            $this->validateCampaignForLaunch($campaign);

            // Update campaign status
            $campaign->update([
                'status' => 'active',
                'launched_at' => now(),
                'launched_by' => auth()->id(),
            ]);

            // Calculate total audience size
            $totalAudience = $this->calculateTotalAudienceSize($campaign);

            // Queue campaign for sending (this would integrate with actual sending service)
            $this->queueCampaignForSending($campaign);

            $this->loggingService->logInfo('Campaign launched', [
                'campaign_id' => $campaign->id,
                'campaign_name' => $campaign->name,
                'total_audience' => $totalAudience,
                'launched_by' => auth()->id(),
            ]);

            return [
                'campaign' => $campaign->fresh(['audiences', 'metrics']),
                'total_audience' => $totalAudience,
                'estimated_delivery_time' => $this->estimateDeliveryTime($totalAudience, $campaign->channels),
                'launch_status' => 'success',
            ];
        });
    }

    /**
     * Pause campaign.
     */
    public function pauseCampaign(string $campaignId, ?string $reason = null): Campaign
    {
        $campaign = Campaign::findOrFail($campaignId);

        if ($campaign->status !== 'active') {
            throw new \InvalidArgumentException('Only active campaigns can be paused');
        }

        $campaign->update([
            'status' => 'paused',
            'paused_at' => now(),
            'pause_reason' => $reason,
        ]);

        $this->loggingService->logInfo('Campaign paused', [
            'campaign_id' => $campaign->id,
            'reason' => $reason,
            'paused_by' => auth()->id(),
        ]);

        return $campaign->fresh(['audiences', 'metrics']);
    }

    /**
     * Get campaign recipients.
     */
    public function getCampaignRecipients(string $campaignId, array $filters = []): array
    {
        $campaign = Campaign::with('audiences')->findOrFail($campaignId);

        $recipients = collect();

        foreach ($campaign->audiences as $audience) {
            $audienceRecipients = $this->getAudienceRecipients($audience->criteria, $filters);
            $recipients = $recipients->merge($audienceRecipients);
        }

        // Remove duplicates
        $recipients = $recipients->unique('id');

        return [
            'total_recipients' => $recipients->count(),
            'recipients' => $recipients->take(100), // Limit for performance
            'audience_breakdown' => $campaign->audiences->map(function ($audience) {
                return [
                    'name' => $audience->name,
                    'estimated_size' => $audience->estimated_size,
                    'actual_size' => $this->calculateAudienceSize($audience->criteria),
                ];
            }),
        ];
    }

    /**
     * Update campaign metrics.
     */
    public function updateCampaignMetrics(string $campaignId, array $metrics): CampaignMetric
    {
        $campaign = Campaign::findOrFail($campaignId);
        $campaignMetric = $campaign->metrics;

        if (! $campaignMetric) {
            $campaignMetric = CampaignMetric::create([
                'campaign_id' => $campaign->id,
                'total_sent' => 0,
                'total_delivered' => 0,
                'total_opened' => 0,
                'total_clicked' => 0,
                'total_conversions' => 0,
                'total_unsubscribed' => 0,
                'revenue_generated' => 0,
                'cost_spent' => 0,
            ]);
        }

        $updateData = array_filter([
            'total_sent' => $metrics['total_sent'] ?? null,
            'total_delivered' => $metrics['total_delivered'] ?? null,
            'total_opened' => $metrics['total_opened'] ?? null,
            'total_clicked' => $metrics['total_clicked'] ?? null,
            'total_conversions' => $metrics['total_conversions'] ?? null,
            'total_unsubscribed' => $metrics['total_unsubscribed'] ?? null,
            'revenue_generated' => $metrics['revenue_generated'] ?? null,
            'cost_spent' => $metrics['cost_spent'] ?? null,
        ], fn ($value) => $value !== null);

        $campaignMetric->update($updateData);

        return $campaignMetric->fresh();
    }

    /**
     * Get campaign A/B test results.
     */
    public function getABTestResults(string $campaignId): array
    {
        $campaign = Campaign::with('metrics')->findOrFail($campaignId);

        if (! ($campaign->settings['ab_test_enabled'] ?? false)) {
            throw new \InvalidArgumentException('A/B testing is not enabled for this campaign');
        }

        // Mock A/B test data - in real implementation, this would come from actual test results
        return [
            'test_status' => 'completed',
            'winner' => 'variant_a',
            'confidence_level' => 95.5,
            'variants' => [
                'variant_a' => [
                    'name' => 'Original',
                    'sent' => 5000,
                    'opened' => 1250,
                    'clicked' => 375,
                    'conversions' => 75,
                    'open_rate' => 25.0,
                    'click_rate' => 7.5,
                    'conversion_rate' => 1.5,
                ],
                'variant_b' => [
                    'name' => 'Variation',
                    'sent' => 5000,
                    'opened' => 1100,
                    'clicked' => 330,
                    'conversions' => 66,
                    'open_rate' => 22.0,
                    'click_rate' => 6.6,
                    'conversion_rate' => 1.32,
                ],
            ],
            'statistical_significance' => true,
            'improvement' => [
                'open_rate' => 13.6,
                'click_rate' => 13.6,
                'conversion_rate' => 13.6,
            ],
        ];
    }

    /**
     * Calculate average open rate.
     */
    private function calculateAverageOpenRate(): float
    {
        $metrics = CampaignMetric::where('total_sent', '>', 0)->get();

        if ($metrics->isEmpty()) {
            return 0;
        }

        $totalOpenRate = $metrics->sum(function ($metric) {
            return $metric->total_sent > 0 ? ($metric->total_opened / $metric->total_sent) * 100 : 0;
        });

        return round($totalOpenRate / $metrics->count(), 2);
    }

    /**
     * Calculate average click rate.
     */
    private function calculateAverageClickRate(): float
    {
        $metrics = CampaignMetric::where('total_delivered', '>', 0)->get();

        if ($metrics->isEmpty()) {
            return 0;
        }

        $totalClickRate = $metrics->sum(function ($metric) {
            return $metric->total_delivered > 0 ? ($metric->total_clicked / $metric->total_delivered) * 100 : 0;
        });

        return round($totalClickRate / $metrics->count(), 2);
    }

    /**
     * Get campaign performance breakdown.
     */
    private function getCampaignPerformanceBreakdown(Collection $campaigns): array
    {
        return $campaigns->map(function ($campaign) {
            $metrics = $campaign->metrics;

            return [
                'campaign_id' => $campaign->id,
                'campaign_name' => $campaign->name,
                'status' => $campaign->status,
                'type' => $campaign->type,
                'total_sent' => $metrics->total_sent ?? 0,
                'total_opened' => $metrics->total_opened ?? 0,
                'total_clicked' => $metrics->total_clicked ?? 0,
                'total_conversions' => $metrics->total_conversions ?? 0,
                'open_rate' => $metrics && $metrics->total_sent > 0 ?
                    round(($metrics->total_opened / $metrics->total_sent) * 100, 2) : 0,
                'click_rate' => $metrics && $metrics->total_delivered > 0 ?
                    round(($metrics->total_clicked / $metrics->total_delivered) * 100, 2) : 0,
                'conversion_rate' => $metrics && $metrics->total_sent > 0 ?
                    round(($metrics->total_conversions / $metrics->total_sent) * 100, 2) : 0,
                'revenue_generated' => $metrics->revenue_generated ?? 0,
                'roi' => $this->calculateCampaignROI($campaign),
            ];
        })->toArray();
    }

    /**
     * Get channel performance breakdown.
     */
    private function getChannelPerformanceBreakdown(Collection $campaigns): array
    {
        $channelPerformance = [];

        foreach ($campaigns as $campaign) {
            foreach ($campaign->channels as $channel) {
                if (! isset($channelPerformance[$channel])) {
                    $channelPerformance[$channel] = [
                        'campaigns_count' => 0,
                        'total_sent' => 0,
                        'total_opened' => 0,
                        'total_clicked' => 0,
                        'total_conversions' => 0,
                        'revenue_generated' => 0,
                    ];
                }

                $metrics = $campaign->metrics;
                $channelPerformance[$channel]['campaigns_count']++;
                $channelPerformance[$channel]['total_sent'] += $metrics->total_sent ?? 0;
                $channelPerformance[$channel]['total_opened'] += $metrics->total_opened ?? 0;
                $channelPerformance[$channel]['total_clicked'] += $metrics->total_clicked ?? 0;
                $channelPerformance[$channel]['total_conversions'] += $metrics->total_conversions ?? 0;
                $channelPerformance[$channel]['revenue_generated'] += $metrics->revenue_generated ?? 0;
            }
        }

        // Calculate rates
        foreach ($channelPerformance as $channel => &$performance) {
            $performance['open_rate'] = $performance['total_sent'] > 0 ?
                round(($performance['total_opened'] / $performance['total_sent']) * 100, 2) : 0;
            $performance['click_rate'] = $performance['total_sent'] > 0 ?
                round(($performance['total_clicked'] / $performance['total_sent']) * 100, 2) : 0;
            $performance['conversion_rate'] = $performance['total_sent'] > 0 ?
                round(($performance['total_conversions'] / $performance['total_sent']) * 100, 2) : 0;
        }

        return $channelPerformance;
    }

    /**
     * Get audience insights.
     */
    private function getAudienceInsights(Collection $campaigns): array
    {
        $audienceData = [];

        foreach ($campaigns as $campaign) {
            foreach ($campaign->audiences as $audience) {
                $criteria = $audience->criteria;
                $key = json_encode($criteria);

                if (! isset($audienceData[$key])) {
                    $audienceData[$key] = [
                        'criteria' => $criteria,
                        'campaigns_count' => 0,
                        'total_size' => 0,
                        'performance' => [
                            'total_sent' => 0,
                            'total_opened' => 0,
                            'total_clicked' => 0,
                            'total_conversions' => 0,
                        ],
                    ];
                }

                $audienceData[$key]['campaigns_count']++;
                $audienceData[$key]['total_size'] += $audience->estimated_size;

                $metrics = $campaign->metrics;
                if ($metrics) {
                    $audienceData[$key]['performance']['total_sent'] += $metrics->total_sent;
                    $audienceData[$key]['performance']['total_opened'] += $metrics->total_opened;
                    $audienceData[$key]['performance']['total_clicked'] += $metrics->total_clicked;
                    $audienceData[$key]['performance']['total_conversions'] += $metrics->total_conversions;
                }
            }
        }

        return array_values($audienceData);
    }

    /**
     * Get conversion funnel.
     */
    private function getConversionFunnel(Collection $campaigns): array
    {
        $totalSent = $campaigns->sum(fn ($c) => $c->metrics->total_sent ?? 0);
        $totalDelivered = $campaigns->sum(fn ($c) => $c->metrics->total_delivered ?? 0);
        $totalOpened = $campaigns->sum(fn ($c) => $c->metrics->total_opened ?? 0);
        $totalClicked = $campaigns->sum(fn ($c) => $c->metrics->total_clicked ?? 0);
        $totalConversions = $campaigns->sum(fn ($c) => $c->metrics->total_conversions ?? 0);

        return [
            'sent' => [
                'count' => $totalSent,
                'percentage' => 100,
            ],
            'delivered' => [
                'count' => $totalDelivered,
                'percentage' => $totalSent > 0 ? round(($totalDelivered / $totalSent) * 100, 2) : 0,
            ],
            'opened' => [
                'count' => $totalOpened,
                'percentage' => $totalSent > 0 ? round(($totalOpened / $totalSent) * 100, 2) : 0,
            ],
            'clicked' => [
                'count' => $totalClicked,
                'percentage' => $totalSent > 0 ? round(($totalClicked / $totalSent) * 100, 2) : 0,
            ],
            'converted' => [
                'count' => $totalConversions,
                'percentage' => $totalSent > 0 ? round(($totalConversions / $totalSent) * 100, 2) : 0,
            ],
        ];
    }

    /**
     * Get ROI analysis.
     */
    private function getROIAnalysis(Collection $campaigns): array
    {
        $totalRevenue = $campaigns->sum(fn ($c) => $c->metrics->revenue_generated ?? 0);
        $totalCost = $campaigns->sum(fn ($c) => $c->metrics->cost_spent ?? 0);
        $roi = $totalCost > 0 ? round((($totalRevenue - $totalCost) / $totalCost) * 100, 2) : 0;

        return [
            'total_revenue' => $totalRevenue,
            'total_cost' => $totalCost,
            'net_profit' => $totalRevenue - $totalCost,
            'roi_percentage' => $roi,
            'cost_per_acquisition' => $campaigns->sum(fn ($c) => $c->metrics->total_conversions ?? 0) > 0 ?
                round($totalCost / $campaigns->sum(fn ($c) => $c->metrics->total_conversions ?? 0), 2) : 0,
            'revenue_per_campaign' => $campaigns->count() > 0 ? round($totalRevenue / $campaigns->count(), 2) : 0,
        ];
    }

    /**
     * Get trend analysis.
     */
    private function getTrendAnalysis(Collection $campaigns): array
    {
        $monthlyData = $campaigns->groupBy(function ($campaign) {
            return $campaign->created_at->format('Y-m');
        })->map(function ($monthCampaigns) {
            return [
                'campaigns_count' => $monthCampaigns->count(),
                'total_sent' => $monthCampaigns->sum(fn ($c) => $c->metrics->total_sent ?? 0),
                'total_conversions' => $monthCampaigns->sum(fn ($c) => $c->metrics->total_conversions ?? 0),
                'revenue_generated' => $monthCampaigns->sum(fn ($c) => $c->metrics->revenue_generated ?? 0),
            ];
        });

        return $monthlyData->toArray();
    }

    /**
     * Calculate audience size based on criteria.
     */
    private function calculateAudienceSize(array $criteria): int
    {
        $query = User::query();

        // Apply criteria filters
        foreach ($criteria as $criterion) {
            switch ($criterion['field']) {
                case 'role':
                    $query->whereHas('roles', function ($q) use ($criterion) {
                        $q->where('name', $criterion['value']);
                    });
                    break;
                case 'location':
                    $query->where('state', $criterion['value']);
                    break;
                case 'registration_date':
                    if ($criterion['operator'] === 'after') {
                        $query->where('created_at', '>=', $criterion['value']);
                    } elseif ($criterion['operator'] === 'before') {
                        $query->where('created_at', '<=', $criterion['value']);
                    }
                    break;
                case 'last_login':
                    if ($criterion['operator'] === 'after') {
                        $query->where('last_login_at', '>=', $criterion['value']);
                    } elseif ($criterion['operator'] === 'before') {
                        $query->where('last_login_at', '<=', $criterion['value']);
                    }
                    break;
            }
        }

        return $query->count();
    }

    /**
     * Calculate total audience size for campaign.
     */
    private function calculateTotalAudienceSize(Campaign $campaign): int
    {
        $totalSize = 0;
        $uniqueUsers = collect();

        foreach ($campaign->audiences as $audience) {
            $audienceUsers = $this->getAudienceRecipients($audience->criteria);
            $uniqueUsers = $uniqueUsers->merge($audienceUsers)->unique('id');
        }

        return $uniqueUsers->count();
    }

    /**
     * Get audience recipients based on criteria.
     */
    private function getAudienceRecipients(array $criteria, array $filters = []): Collection
    {
        $query = User::query();

        // Apply criteria filters (same logic as calculateAudienceSize)
        foreach ($criteria as $criterion) {
            switch ($criterion['field']) {
                case 'role':
                    $query->whereHas('roles', function ($q) use ($criterion) {
                        $q->where('name', $criterion['value']);
                    });
                    break;
                case 'location':
                    $query->where('state', $criterion['value']);
                    break;
                case 'registration_date':
                    if ($criterion['operator'] === 'after') {
                        $query->where('created_at', '>=', $criterion['value']);
                    } elseif ($criterion['operator'] === 'before') {
                        $query->where('created_at', '<=', $criterion['value']);
                    }
                    break;
                case 'last_login':
                    if ($criterion['operator'] === 'after') {
                        $query->where('last_login_at', '>=', $criterion['value']);
                    } elseif ($criterion['operator'] === 'before') {
                        $query->where('last_login_at', '<=', $criterion['value']);
                    }
                    break;
            }
        }

        // Apply additional filters
        if (isset($filters['limit'])) {
            $query->limit($filters['limit']);
        }

        return $query->get();
    }

    /**
     * Validate campaign for launch.
     */
    private function validateCampaignForLaunch(Campaign $campaign): void
    {
        if (empty($campaign->name)) {
            throw new \InvalidArgumentException('Campaign name is required');
        }

        if (empty($campaign->content['message'])) {
            throw new \InvalidArgumentException('Campaign message is required');
        }

        if (empty($campaign->channels)) {
            throw new \InvalidArgumentException('At least one channel is required');
        }

        if ($campaign->audiences->isEmpty()) {
            throw new \InvalidArgumentException('At least one audience is required');
        }

        $totalAudience = $this->calculateTotalAudienceSize($campaign);
        if ($totalAudience === 0) {
            throw new \InvalidArgumentException('Campaign has no valid recipients');
        }
    }

    /**
     * Queue campaign for sending.
     */
    private function queueCampaignForSending(Campaign $campaign): void
    {
        // This would integrate with actual queue/job system
        $this->loggingService->logInfo('Campaign queued for sending', [
            'campaign_id' => $campaign->id,
            'channels' => $campaign->channels,
            'estimated_recipients' => $this->calculateTotalAudienceSize($campaign),
        ]);
    }

    /**
     * Estimate delivery time.
     */
    private function estimateDeliveryTime(int $audienceSize, array $channels): string
    {
        // Mock estimation based on audience size and channels
        $baseTime = 5; // minutes
        $timePerRecipient = 0.1; // seconds per recipient

        $estimatedMinutes = $baseTime + ($audienceSize * $timePerRecipient / 60);

        if ($estimatedMinutes < 60) {
            return round($estimatedMinutes).' minutes';
        } else {
            return round($estimatedMinutes / 60, 1).' hours';
        }
    }

    /**
     * Calculate campaign ROI.
     */
    private function calculateCampaignROI(Campaign $campaign): float
    {
        $metrics = $campaign->metrics;

        if (! $metrics || $metrics->cost_spent <= 0) {
            return 0;
        }

        $revenue = $metrics->revenue_generated ?? 0;
        $cost = $metrics->cost_spent;

        return round((($revenue - $cost) / $cost) * 100, 2);
    }
}
