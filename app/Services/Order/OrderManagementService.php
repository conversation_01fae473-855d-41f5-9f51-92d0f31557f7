<?php

declare(strict_types=1);

namespace App\Services\Order;

use App\Enums\Delivery\OrderStatus;
use App\Enums\Financial\PaymentStatus;
use App\Models\Delivery\Order;
use App\Models\Financial\Dispute;
use App\Services\System\LoggingService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class OrderManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get comprehensive order statistics.
     */
    public function getOrderStatistics(): array
    {
        return [
            'total_orders' => Order::count(),
            'orders_by_status' => Order::selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status'),
            'orders_by_payment_status' => Order::selectRaw('payment_status, COUNT(*) as count')
                ->groupBy('payment_status')
                ->pluck('count', 'payment_status'),
            'total_revenue' => Order::where('status', OrderStatus::COMPLETED)
                ->sum('total_amount'),
            'average_order_value' => Order::where('status', OrderStatus::COMPLETED)
                ->avg('total_amount'),
            'orders_today' => Order::whereDate('created_at', today())->count(),
            'orders_this_week' => Order::whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek(),
            ])->count(),
            'orders_this_month' => Order::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
            'top_businesses' => $this->getTopBusinessesByOrders(),
            'pending_orders' => Order::where('status', OrderStatus::PENDING)->count(),
            'disputed_orders' => Order::whereHas('disputes')->count(),
        ];
    }

    /**
     * Get order analytics for dashboard.
     */
    public function getOrderAnalytics(array $filters = []): array
    {
        $query = Order::query();

        // Apply date filters
        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $orders = $query->get();

        return [
            'total_orders' => $orders->count(),
            'total_revenue' => $orders->where('status', OrderStatus::COMPLETED)->sum('total_amount'),
            'average_order_value' => $orders->where('status', OrderStatus::COMPLETED)->avg('total_amount'),
            'completion_rate' => $this->calculateCompletionRate($orders),
            'cancellation_rate' => $this->calculateCancellationRate($orders),
            'daily_breakdown' => $this->getDailyOrderBreakdown($orders),
            'hourly_distribution' => $this->getHourlyOrderDistribution($orders),
            'payment_method_breakdown' => $orders->groupBy('payment_method')
                ->map(fn ($group) => $group->count()),
            'order_source_breakdown' => $orders->groupBy('source')
                ->map(fn ($group) => $group->count()),
        ];
    }

    /**
     * Bulk update order status.
     */
    public function bulkUpdateOrderStatus(array $orderIds, OrderStatus $status, ?string $reason = null): array
    {
        $results = ['updated' => 0, 'failed' => 0, 'details' => []];

        DB::transaction(function () use ($orderIds, $status, $reason, &$results) {
            foreach ($orderIds as $orderId) {
                try {
                    $order = Order::findOrFail($orderId);

                    // Validate status transition
                    if (! $this->isValidStatusTransition($order->status, $status)) {
                        $results['failed']++;
                        $results['details'][$orderId] = 'Invalid status transition';

                        continue;
                    }

                    $oldStatus = $order->status;
                    $order->update([
                        'status' => $status,
                        'previous_status' => $oldStatus,
                    ]);

                    // Add cancellation details if cancelling
                    if ($status === OrderStatus::CANCELLED) {
                        $order->update([
                            'cancelled_at' => now(),
                            'cancellation_reason' => $reason ?? 'Bulk cancellation by admin',
                            'cancelled_by_user_id' => auth()->id(),
                        ]);
                    }

                    $results['updated']++;
                    $results['details'][$orderId] = 'updated';

                    $this->loggingService->logInfo('Order status updated via bulk operation', [
                        'order_id' => $order->id,
                        'old_status' => $oldStatus->value,
                        'new_status' => $status->value,
                        'admin_id' => auth()->id(),
                    ]);

                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['details'][$orderId] = 'error: '.$e->getMessage();
                }
            }
        });

        return $results;
    }

    /**
     * Get orders requiring attention.
     */
    public function getOrdersRequiringAttention(): array
    {
        return [
            'pending_long_time' => Order::where('status', OrderStatus::PENDING)
                ->where('created_at', '<', now()->subHours(2))
                ->with(['customer:id,first_name,last_name', 'business:id,name'])
                ->limit(20)
                ->get(),
            'payment_failed' => Order::where('payment_status', PaymentStatus::FAILED)
                ->where('created_at', '>=', now()->subDays(7))
                ->with(['customer:id,first_name,last_name', 'business:id,name'])
                ->limit(20)
                ->get(),
            'disputed_orders' => Order::whereHas('disputes', function ($query) {
                $query->where('status', 'open');
            })
                ->with(['customer:id,first_name,last_name', 'business:id,name', 'disputes'])
                ->limit(20)
                ->get(),
            'high_value_orders' => Order::where('total_amount', '>', 50000)
                ->where('status', OrderStatus::PENDING)
                ->with(['customer:id,first_name,last_name', 'business:id,name'])
                ->orderByDesc('total_amount')
                ->limit(10)
                ->get(),
        ];
    }

    /**
     * Detect potentially fraudulent orders.
     */
    public function detectFraudulentOrders(): Collection
    {
        return Order::where(function ($query) {
            // Multiple orders from same customer in short time
            $query->whereIn('customer_id', function ($subQuery) {
                $subQuery->select('customer_id')
                    ->from('orders')
                    ->where('created_at', '>=', now()->subHours(1))
                    ->groupBy('customer_id')
                    ->havingRaw('COUNT(*) > 5');
            })
            // Or unusually high order values
                ->orWhere('total_amount', '>', 100000)
            // Or orders with failed payments but multiple attempts
                ->orWhereHas('payments', function ($paymentQuery) {
                    $paymentQuery->where('status', PaymentStatus::FAILED)
                        ->havingRaw('COUNT(*) > 3');
                });
        })
            ->with(['customer:id,first_name,last_name,email', 'business:id,name', 'payments'])
            ->orderByDesc('created_at')
            ->limit(50)
            ->get();
    }

    /**
     * Get order dispute summary.
     */
    public function getOrderDisputeSummary(): array
    {
        $disputes = Dispute::with(['order:id,order_reference,total_amount'])
            ->where('created_at', '>=', now()->subDays(30))
            ->get();

        return [
            'total_disputes' => $disputes->count(),
            'open_disputes' => $disputes->where('status', 'open')->count(),
            'resolved_disputes' => $disputes->where('status', 'resolved')->count(),
            'dispute_rate' => $this->calculateDisputeRate(),
            'average_dispute_amount' => $disputes->avg('amount'),
            'disputes_by_reason' => $disputes->groupBy('reason')
                ->map(fn ($group) => $group->count()),
            'recent_disputes' => $disputes->sortByDesc('created_at')->take(10)->values(),
        ];
    }

    /**
     * Export orders data.
     */
    public function exportOrders(array $filters = [], string $format = 'csv'): string
    {
        $query = Order::with([
            'customer:id,first_name,last_name,email',
            'business:id,name',
            'businessBranch:id,name',
        ]);

        // Apply filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['payment_status'])) {
            $query->where('payment_status', $filters['payment_status']);
        }

        if (isset($filters['business_id'])) {
            $query->where('business_id', $filters['business_id']);
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $orders = $query->orderBy('created_at', 'desc')->get();

        // Generate filename
        $filename = 'orders_export_'.now()->format('Y_m_d_H_i_s').'.'.$format;
        $filepath = storage_path('app/exports/'.$filename);

        // Ensure directory exists
        if (! file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        if ($format === 'csv') {
            $this->exportToCsv($orders, $filepath);
        } elseif ($format === 'json') {
            $this->exportToJson($orders, $filepath);
        }

        return $filename;
    }

    /**
     * Get top businesses by order count.
     */
    private function getTopBusinessesByOrders(): Collection
    {
        return Order::with('business:id,name')
            ->selectRaw('business_id, COUNT(*) as order_count, SUM(total_amount) as total_revenue')
            ->groupBy('business_id')
            ->orderByDesc('order_count')
            ->limit(10)
            ->get();
    }

    /**
     * Calculate order completion rate.
     */
    private function calculateCompletionRate(Collection $orders): float
    {
        $total = $orders->count();
        if ($total === 0) {
            return 0;
        }

        $completed = $orders->where('status', OrderStatus::COMPLETED)->count();

        return round(($completed / $total) * 100, 2);
    }

    /**
     * Calculate order cancellation rate.
     */
    private function calculateCancellationRate(Collection $orders): float
    {
        $total = $orders->count();
        if ($total === 0) {
            return 0;
        }

        $cancelled = $orders->where('status', OrderStatus::CANCELLED)->count();

        return round(($cancelled / $total) * 100, 2);
    }

    /**
     * Get daily order breakdown.
     */
    private function getDailyOrderBreakdown(Collection $orders): array
    {
        return $orders->groupBy(function ($order) {
            return $order->created_at->format('Y-m-d');
        })->map(function ($dayOrders) {
            return [
                'count' => $dayOrders->count(),
                'revenue' => $dayOrders->where('status', OrderStatus::COMPLETED)->sum('total_amount'),
            ];
        })->toArray();
    }

    /**
     * Get hourly order distribution.
     */
    private function getHourlyOrderDistribution(Collection $orders): array
    {
        return $orders->groupBy(function ($order) {
            return $order->created_at->format('H');
        })->map(fn ($hourOrders) => $hourOrders->count())->toArray();
    }

    /**
     * Check if status transition is valid.
     */
    private function isValidStatusTransition(OrderStatus $from, OrderStatus $to): bool
    {
        $validTransitions = [
            OrderStatus::PENDING->value => [
                OrderStatus::CONFIRMED->value,
                OrderStatus::CANCELLED->value,
            ],
            OrderStatus::CONFIRMED->value => [
                OrderStatus::PREPARING->value,
                OrderStatus::CANCELLED->value,
            ],
            OrderStatus::PREPARING->value => [
                OrderStatus::READY_FOR_PICKUP->value,
                OrderStatus::CANCELLED->value,
            ],
            OrderStatus::READY_FOR_PICKUP->value => [
                OrderStatus::OUT_FOR_DELIVERY->value,
                OrderStatus::COMPLETED->value,
                OrderStatus::CANCELLED->value,
            ],
            OrderStatus::OUT_FOR_DELIVERY->value => [
                OrderStatus::COMPLETED->value,
                OrderStatus::CANCELLED->value,
            ],
        ];

        return in_array($to->value, $validTransitions[$from->value] ?? []);
    }

    /**
     * Calculate dispute rate.
     */
    private function calculateDisputeRate(): float
    {
        $totalOrders = Order::where('created_at', '>=', now()->subDays(30))->count();
        $disputedOrders = Order::whereHas('disputes')
            ->where('created_at', '>=', now()->subDays(30))
            ->count();

        return $totalOrders > 0 ? round(($disputedOrders / $totalOrders) * 100, 2) : 0;
    }

    /**
     * Export orders to CSV.
     */
    private function exportToCsv(Collection $orders, string $filepath): void
    {
        $file = fopen($filepath, 'w');

        // Write header
        fputcsv($file, [
            'Order Reference', 'Customer', 'Business', 'Branch', 'Status',
            'Payment Status', 'Sub Total', 'Delivery Fee', 'Total Amount',
            'Currency', 'Order Type', 'Source', 'Created At',
        ]);

        // Write data
        foreach ($orders as $order) {
            fputcsv($file, [
                $order->order_reference,
                $order->customer ? $order->customer->first_name.' '.$order->customer->last_name : 'N/A',
                $order->business?->name ?? 'N/A',
                $order->businessBranch?->name ?? 'N/A',
                $order->status->value,
                $order->payment_status->value,
                $order->sub_total,
                $order->delivery_fee,
                $order->total_amount,
                $order->currency,
                $order->order_type->value,
                $order->source->value,
                $order->created_at->toISOString(),
            ]);
        }

        fclose($file);
    }

    /**
     * Export orders to JSON.
     */
    private function exportToJson(Collection $orders, string $filepath): void
    {
        $data = $orders->map(function ($order) {
            return [
                'order_reference' => $order->order_reference,
                'customer' => $order->customer ? [
                    'name' => $order->customer->first_name.' '.$order->customer->last_name,
                    'email' => $order->customer->email,
                ] : null,
                'business' => $order->business?->name,
                'branch' => $order->businessBranch?->name,
                'status' => $order->status->value,
                'payment_status' => $order->payment_status->value,
                'sub_total' => $order->sub_total,
                'delivery_fee' => $order->delivery_fee,
                'total_amount' => $order->total_amount,
                'currency' => $order->currency,
                'order_type' => $order->order_type->value,
                'source' => $order->source->value,
                'created_at' => $order->created_at->toISOString(),
            ];
        });

        file_put_contents($filepath, json_encode($data, JSON_PRETTY_PRINT));
    }

    /**
     * Get order with detailed information.
     */
    public function getOrderWithDetails(string $orderId): Order
    {
        return Order::with([
            'customer:id,first_name,last_name,email,phone_number',
            'business:id,name,tenant_id',
            'businessBranch:id,name',
            'orderDelivery:id,status',
            'payments:id,status,amount,currency',
            'orderItems.product:id,name,price',
            'orderItems.productVariant:id,name,price',
        ])->findOrFail($orderId);
    }

    /**
     * Update order status.
     */
    public function updateOrderStatus(string $orderId, string $status, ?string $reason = null, ?string $notes = null): Order
    {
        return DB::transaction(function () use ($orderId, $status, $reason, $notes) {
            $order = Order::findOrFail($orderId);
            $orderStatus = OrderStatus::from($status);

            // Validate status transition
            if (! $this->isValidStatusTransition($order->status, $orderStatus)) {
                throw new \InvalidArgumentException("Invalid status transition from {$order->status->value} to {$status}");
            }

            $oldStatus = $order->status;
            $order->update([
                'status' => $orderStatus,
                'previous_status' => $oldStatus,
                'business_notes' => $notes ? ($order->business_notes."\n".$notes) : $order->business_notes,
            ]);

            $this->loggingService->logInfo('Order status updated', [
                'order_id' => $order->id,
                'old_status' => $oldStatus->value,
                'new_status' => $status,
                'reason' => $reason,
                'admin_id' => auth()->id(),
            ]);

            return $order->fresh();
        });
    }

    /**
     * Cancel order.
     */
    public function cancelOrder(string $orderId, string $reason, ?float $refundAmount = null, bool $notifyCustomer = true): array
    {
        return DB::transaction(function () use ($orderId, $reason, $refundAmount, $notifyCustomer) {
            $order = Order::findOrFail($orderId);

            if ($order->status === OrderStatus::CANCELLED) {
                throw new \InvalidArgumentException('Order is already cancelled');
            }

            $oldStatus = $order->status;
            $order->update([
                'status' => OrderStatus::CANCELLED,
                'previous_status' => $oldStatus,
                'cancelled_at' => now(),
                'cancellation_reason' => $reason,
                'cancelled_by_user_id' => auth()->id(),
            ]);

            $result = [
                'order' => $order->fresh(),
                'refund_processed' => false,
                'notification_sent' => false,
            ];

            // Process refund if amount specified
            if ($refundAmount && $refundAmount > 0) {
                // This would integrate with payment service
                $result['refund_processed'] = true;
                $result['refund_amount'] = $refundAmount;
            }

            // Send notification if requested
            if ($notifyCustomer) {
                // This would integrate with notification service
                $result['notification_sent'] = true;
            }

            $this->loggingService->logInfo('Order cancelled', [
                'order_id' => $order->id,
                'reason' => $reason,
                'refund_amount' => $refundAmount,
                'admin_id' => auth()->id(),
            ]);

            return $result;
        });
    }

    /**
     * Process refund for order.
     */
    public function processRefund(string $orderId, float $amount, string $reason, string $refundType): array
    {
        return DB::transaction(function () use ($orderId, $amount, $reason, $refundType) {
            $order = Order::findOrFail($orderId);

            if ($amount > $order->total_amount) {
                throw new \InvalidArgumentException('Refund amount cannot exceed order total');
            }

            // This would integrate with payment service
            $refund = [
                'id' => 'refund_'.uniqid(),
                'order_id' => $order->id,
                'amount' => $amount,
                'reason' => $reason,
                'type' => $refundType,
                'status' => 'processed',
                'processed_at' => now(),
                'processed_by' => auth()->id(),
            ];

            $this->loggingService->logInfo('Refund processed', [
                'order_id' => $order->id,
                'refund_amount' => $amount,
                'reason' => $reason,
                'admin_id' => auth()->id(),
            ]);

            return $refund;
        });
    }

    /**
     * Bulk update orders.
     */
    public function bulkUpdateOrders(array $orderIds, string $action, ?string $reason = null): array
    {
        $results = ['updated' => 0, 'failed' => 0, 'details' => []];

        DB::transaction(function () use ($orderIds, $action, $reason, &$results) {
            foreach ($orderIds as $orderId) {
                try {
                    switch ($action) {
                        case 'cancel':
                            $this->cancelOrder($orderId, $reason ?? 'Bulk cancellation');
                            break;
                        case 'confirm':
                            $this->updateOrderStatus($orderId, OrderStatus::CONFIRMED->value, $reason);
                            break;
                        case 'mark_ready':
                            $this->updateOrderStatus($orderId, OrderStatus::READY_FOR_PICKUP->value, $reason);
                            break;
                        default:
                            throw new \InvalidArgumentException("Invalid action: {$action}");
                    }

                    $results['updated']++;
                    $results['details'][$orderId] = 'success';

                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['details'][$orderId] = 'error: '.$e->getMessage();
                }
            }
        });

        return $results;
    }
}
