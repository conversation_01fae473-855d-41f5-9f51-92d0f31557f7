<?php

declare(strict_types=1);

namespace App\Services\External;

use App\Services\Core\LoggingService;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

/**
 * QoreID Verification Service
 *
 * Unified KYC verification service using QoreID API.
 * Replaces NIBSS, NIMC, and enhances Paystack verification.
 */
class QoreIdService
{
    private string $apiKey;

    private string $baseUrl;

    private string $secretKey;

    private bool $isConfigured;

    public function __construct(
        private LoggingService $loggingService
    ) {
        $this->apiKey = config('services.qoreid.api_key', '');
        $this->baseUrl = config('services.qoreid.base_url', 'https://api.qoreid.com/v1');
        $this->secretKey = config('services.qoreid.secret_key', '');
        $this->isConfigured = ! empty($this->apiKey) && ! empty($this->secretKey);

        if (! $this->isConfigured && ! app()->environment(['testing', 'local'])) {
            Log::warning('QoreID service not configured', [
                'service' => 'QoreIdService',
            ]);
        }
    }

    /**
     * Verify BVN using QoreID API.
     */
    public function verifyBvn(string $bvn, array $customerData = []): array
    {
        if (! $this->isConfigured) {
            return $this->getMockResponse('bvn', $bvn);
        }

        try {
            // Validate BVN format
            if (! $this->isValidBvnFormat($bvn)) {
                return [
                    'success' => false,
                    'data' => null,
                    'message' => 'Invalid BVN format. BVN must be 11 digits.',
                    'reference' => null,
                ];
            }

            // Check cache first
            $cacheKey = 'qoreid_bvn_verification:'.hash('sha256', $bvn);
            $cached = Cache::get($cacheKey);
            if ($cached) {
                Log::info('BVN verification returned from cache', [
                    'service' => 'QoreIdService',
                    'cached' => true,
                ]);

                return $cached;
            }

            $reference = $this->generateReference('BVN');

            $payload = [
                'bvn' => $bvn,
                'reference' => $reference,
            ];

            // Add customer data for matching if provided
            if (! empty($customerData)) {
                $payload['customer_data'] = $customerData;
            }

            $response = Http::withHeaders($this->getHeaders())
                ->timeout(30)
                ->post($this->baseUrl.'/bvn/basic', $payload);

            if ($response->successful()) {
                $data = $response->json();

                if ($data['status'] === 'success') {
                    $result = [
                        'success' => true,
                        'data' => [
                            'first_name' => $data['data']['first_name'] ?? null,
                            'last_name' => $data['data']['last_name'] ?? null,
                            'middle_name' => $data['data']['middle_name'] ?? null,
                            'date_of_birth' => $data['data']['date_of_birth'] ?? null,
                            'phone_number' => $data['data']['phone_number'] ?? null,
                            'email' => $data['data']['email'] ?? null,
                            'gender' => $data['data']['gender'] ?? null,
                            'enrollment_bank' => $data['data']['enrollment_bank'] ?? null,
                            'watch_listed' => $data['data']['watch_listed'] ?? false,
                            'verification_score' => $data['data']['verification_score'] ?? 0,
                        ],
                        'message' => 'BVN verification successful',
                        'reference' => $reference,
                        'qoreid_reference' => $data['reference'] ?? null,
                    ];

                    // Cache successful verification for 24 hours
                    Cache::put($cacheKey, $result, 86400);

                    $this->loggingService->logInfo('BVN verification successful', [
                        'reference' => $reference,
                        'qoreid_reference' => $data['reference'] ?? null,
                        'service' => 'QoreIdService',
                    ]);

                    return $result;
                }
            }

            $this->loggingService->logError('QoreID BVN verification failed', null, [
                'status' => $response->status(),
                'response' => $response->body(),
                'reference' => $reference,
                'service' => 'QoreIdService',
            ]);

            return [
                'success' => false,
                'data' => null,
                'message' => 'BVN verification failed',
                'reference' => $reference,
            ];

        } catch (\Throwable $e) {
            $this->loggingService->logError('BVN verification error', $e, [
                'service' => 'QoreIdService',
            ]);

            return [
                'success' => false,
                'data' => null,
                'message' => 'BVN verification service error',
                'reference' => null,
            ];
        }
    }

    /**
     * Verify NIN using QoreID API.
     */
    public function verifyNin(string $nin, array $customerData = []): array
    {
        if (! $this->isConfigured) {
            return $this->getMockResponse('nin', $nin);
        }

        try {
            // Validate NIN format
            if (! $this->isValidNinFormat($nin)) {
                return [
                    'success' => false,
                    'data' => null,
                    'message' => 'Invalid NIN format. NIN must be 11 digits.',
                    'reference' => null,
                ];
            }

            // Check cache first
            $cacheKey = 'qoreid_nin_verification:'.hash('sha256', $nin);
            $cached = Cache::get($cacheKey);
            if ($cached) {
                Log::info('NIN verification returned from cache', [
                    'service' => 'QoreIdService',
                    'cached' => true,
                ]);

                return $cached;
            }

            $reference = $this->generateReference('NIN');

            $payload = [
                'nin' => $nin,
                'reference' => $reference,
            ];

            // Add customer data for matching if provided
            if (! empty($customerData)) {
                $payload['customer_data'] = $customerData;
            }

            $response = Http::withHeaders($this->getHeaders())
                ->timeout(30)
                ->post($this->baseUrl.'/nin/basic', $payload);

            if ($response->successful()) {
                $data = $response->json();

                if ($data['status'] === 'success') {
                    $result = [
                        'success' => true,
                        'data' => [
                            'firstname' => $data['data']['firstname'] ?? null,
                            'surname' => $data['data']['surname'] ?? null,
                            'middlename' => $data['data']['middlename'] ?? null,
                            'birthdate' => $data['data']['birthdate'] ?? null,
                            'gender' => $data['data']['gender'] ?? null,
                            'phone' => $data['data']['phone'] ?? null,
                            'email' => $data['data']['email'] ?? null,
                            'residence_address' => $data['data']['residence_address'] ?? null,
                            'residence_state' => $data['data']['residence_state'] ?? null,
                            'residence_lga' => $data['data']['residence_lga'] ?? null,
                            'verification_score' => $data['data']['verification_score'] ?? 0,
                        ],
                        'message' => 'NIN verification successful',
                        'reference' => $reference,
                        'qoreid_reference' => $data['reference'] ?? null,
                    ];

                    // Cache successful verification for 24 hours
                    Cache::put($cacheKey, $result, 86400);

                    $this->loggingService->logInfo('NIN verification successful', [
                        'reference' => $reference,
                        'qoreid_reference' => $data['reference'] ?? null,
                        'service' => 'QoreIdService',
                    ]);

                    return $result;
                }
            }

            $this->loggingService->logError('QoreID NIN verification failed', null, [
                'status' => $response->status(),
                'response' => $response->body(),
                'reference' => $reference,
                'service' => 'QoreIdService',
            ]);

            return [
                'success' => false,
                'data' => null,
                'message' => 'NIN verification failed',
                'reference' => $reference,
            ];

        } catch (\Throwable $e) {
            $this->loggingService->logError('NIN verification error', $e, [
                'service' => 'QoreIdService',
            ]);

            return [
                'success' => false,
                'data' => null,
                'message' => 'NIN verification service error',
                'reference' => null,
            ];
        }
    }

    /**
     * Verify bank account using QoreID NUBAN API.
     */
    public function verifyBankAccount(string $accountNumber, string $bankCode): array
    {
        if (! $this->isConfigured) {
            return $this->getMockBankResponse($accountNumber);
        }

        try {
            $reference = $this->generateReference('BANK');

            $payload = [
                'account_number' => $accountNumber,
                'bank_code' => $bankCode,
                'reference' => $reference,
            ];

            $response = Http::withHeaders($this->getHeaders())
                ->timeout(30)
                ->post($this->baseUrl.'/nuban/regular', $payload);

            if ($response->successful()) {
                $data = $response->json();

                if ($data['status'] === 'success') {
                    $result = [
                        'success' => true,
                        'data' => [
                            'account_name' => $data['data']['account_name'] ?? null,
                            'account_number' => $data['data']['account_number'] ?? $accountNumber,
                            'bank_name' => $data['data']['bank_name'] ?? null,
                            'bank_code' => $bankCode,
                        ],
                        'message' => 'Bank account verification successful',
                        'reference' => $reference,
                        'qoreid_reference' => $data['reference'] ?? null,
                    ];

                    $this->loggingService->logInfo('Bank account verification successful', [
                        'account_number' => $accountNumber,
                        'bank_code' => $bankCode,
                        'reference' => $reference,
                        'service' => 'QoreIdService',
                    ]);

                    return $result;
                }
            }

            return [
                'success' => false,
                'data' => null,
                'message' => 'Bank account verification failed',
                'reference' => $reference,
            ];

        } catch (\Throwable $e) {
            $this->loggingService->logError('Bank account verification error', $e, [
                'account_number' => $accountNumber,
                'bank_code' => $bankCode,
                'service' => 'QoreIdService',
            ]);

            return [
                'success' => false,
                'data' => null,
                'message' => 'Bank account verification service error',
                'reference' => null,
            ];
        }
    }

    /**
     * Comprehensive KYC verification using QoreID workflow.
     */
    public function comprehensiveKyc(array $data): array
    {
        if (! $this->isConfigured) {
            return $this->getMockWorkflowResponse($data);
        }

        try {
            $reference = $this->generateReference('KYC');

            $payload = [
                'reference' => $reference,
                'workflow_id' => config('services.qoreid.workflow_id'),
                'data' => $data,
            ];

            $response = Http::withHeaders($this->getHeaders())
                ->timeout(60) // Longer timeout for comprehensive verification
                ->post($this->baseUrl.'/workflow/execute', $payload);

            if ($response->successful()) {
                $responseData = $response->json();

                if ($responseData['status'] === 'success') {
                    $result = [
                        'success' => true,
                        'data' => $responseData['data'],
                        'verification_score' => $responseData['data']['verification_score'] ?? 0,
                        'risk_level' => $responseData['data']['risk_level'] ?? 'unknown',
                        'message' => 'Comprehensive KYC verification successful',
                        'reference' => $reference,
                        'qoreid_reference' => $responseData['reference'] ?? null,
                    ];

                    $this->loggingService->logInfo('Comprehensive KYC verification successful', [
                        'reference' => $reference,
                        'verification_score' => $result['verification_score'],
                        'risk_level' => $result['risk_level'],
                        'service' => 'QoreIdService',
                    ]);

                    return $result;
                }
            }

            return [
                'success' => false,
                'data' => null,
                'message' => 'Comprehensive KYC verification failed',
                'reference' => $reference,
            ];

        } catch (\Throwable $e) {
            $this->loggingService->logError('Comprehensive KYC verification error', $e, [
                'service' => 'QoreIdService',
            ]);

            return [
                'success' => false,
                'data' => null,
                'message' => 'Comprehensive KYC verification service error',
                'reference' => null,
            ];
        }
    }

    /**
     * Get verification status by reference.
     */
    public function getVerificationStatus(string $qoreidReference): array
    {
        if (! $this->isConfigured) {
            return ['status' => 'completed', 'data' => []];
        }

        try {
            $response = Http::withHeaders($this->getHeaders())
                ->timeout(30)
                ->get($this->baseUrl.'/verification/'.$qoreidReference);

            if ($response->successful()) {
                return $response->json();
            }

            return ['status' => 'unknown', 'data' => []];

        } catch (\Throwable $e) {
            $this->loggingService->logError('Get verification status error', $e, [
                'qoreid_reference' => $qoreidReference,
                'service' => 'QoreIdService',
            ]);

            return ['status' => 'error', 'data' => []];
        }
    }

    /**
     * Check if service is properly configured.
     */
    public function isConfigured(): bool
    {
        return $this->isConfigured;
    }

    /**
     * Get service status for health checks.
     */
    public function getServiceStatus(): array
    {
        return [
            'service' => 'QoreIdService',
            'configured' => $this->isConfigured,
            'base_url' => $this->baseUrl,
            'environment' => app()->environment(),
        ];
    }

    /**
     * Validate BVN format (11 digits).
     */
    private function isValidBvnFormat(string $bvn): bool
    {
        return preg_match('/^\d{11}$/', $bvn) === 1;
    }

    /**
     * Validate NIN format (11 digits).
     */
    private function isValidNinFormat(string $nin): bool
    {
        return preg_match('/^\d{11}$/', $nin) === 1;
    }

    /**
     * Generate unique reference for tracking.
     */
    private function generateReference(string $type): string
    {
        return 'QOREID_'.$type.'_'.time().'_'.uniqid();
    }

    /**
     * Get authentication headers.
     */
    private function getHeaders(): array
    {
        return [
            'Authorization' => 'Bearer '.$this->apiKey,
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    /**
     * Get mock response for testing/development.
     */
    private function getMockResponse(string $type, string $identifier): array
    {
        if (app()->environment(['testing', 'local'])) {
            return [
                'success' => true,
                'data' => $this->getMockData($type),
                'message' => ucfirst($type).' verification successful (mock)',
                'reference' => 'MOCK_'.strtoupper($type).'_'.time(),
                'qoreid_reference' => 'QOREID_MOCK_'.time(),
            ];
        }

        return [
            'success' => false,
            'data' => null,
            'message' => 'QoreID service not configured',
            'reference' => null,
        ];
    }

    /**
     * Get mock bank response.
     */
    private function getMockBankResponse(string $accountNumber): array
    {
        if (app()->environment(['testing', 'local'])) {
            return [
                'success' => true,
                'data' => [
                    'account_name' => 'John Doe',
                    'account_number' => $accountNumber,
                    'bank_name' => 'First Bank Nigeria',
                    'bank_code' => '011',
                ],
                'message' => 'Bank account verification successful (mock)',
                'reference' => 'MOCK_BANK_'.time(),
                'qoreid_reference' => 'QOREID_MOCK_BANK_'.time(),
            ];
        }

        return [
            'success' => false,
            'data' => null,
            'message' => 'QoreID service not configured',
            'reference' => null,
        ];
    }

    /**
     * Get mock workflow response.
     */
    private function getMockWorkflowResponse(array $data): array
    {
        if (app()->environment(['testing', 'local'])) {
            return [
                'success' => true,
                'data' => [
                    'bvn_verification' => ['status' => 'verified', 'score' => 95],
                    'nin_verification' => ['status' => 'verified', 'score' => 90],
                    'bank_verification' => ['status' => 'verified', 'score' => 100],
                ],
                'verification_score' => 95,
                'risk_level' => 'low',
                'message' => 'Comprehensive KYC verification successful (mock)',
                'reference' => 'MOCK_KYC_'.time(),
                'qoreid_reference' => 'QOREID_MOCK_KYC_'.time(),
            ];
        }

        return [
            'success' => false,
            'data' => null,
            'message' => 'QoreID service not configured',
            'reference' => null,
        ];
    }

    /**
     * Get mock data based on verification type.
     */
    private function getMockData(string $type): array
    {
        return match ($type) {
            'bvn' => [
                'first_name' => 'John',
                'last_name' => 'Doe',
                'middle_name' => 'Smith',
                'date_of_birth' => '1990-01-01',
                'phone_number' => '***********',
                'email' => null,
                'gender' => 'Male',
                'enrollment_bank' => 'First Bank',
                'watch_listed' => false,
                'verification_score' => 95,
            ],
            'nin' => [
                'firstname' => 'Jane',
                'surname' => 'Doe',
                'middlename' => 'Smith',
                'birthdate' => '1990-01-01',
                'gender' => 'Female',
                'phone' => '***********',
                'email' => null,
                'residence_address' => '123 Test Street',
                'residence_state' => 'Lagos',
                'residence_lga' => 'Ikeja',
                'verification_score' => 90,
            ],
            default => [],
        };
    }
}
