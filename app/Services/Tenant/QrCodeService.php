<?php

declare(strict_types=1);

namespace App\Services\Tenant;

use App\Models\Business\Business;
use App\Models\Business\ProductCategory;
use App\Services\System\DomainService;
use App\Services\System\LoggingService;
use App\Traits\ApiResponseTrait;
use Illuminate\Support\Facades\Storage;
use SimpleSoftwareIO\QrCode\Facades\QrCode;

/**
 * QR Code Generation Service
 *
 * Handles QR code generation for business menus with:
 * - Visual QR codes (with business branding/logos)
 * - Classic text-based QR codes
 * - Integration with existing menu/product systems
 * - Mobile-optimized landing pages
 */
class QrCodeService
{
    use ApiResponseTrait;

    public function __construct(
        private readonly LoggingService $loggingService,
        private readonly DomainService $domainService
    ) {}

    /**
     * Generate QR code for business menu.
     */
    public function generateMenuQrCode(
        Business $business,
        array $options = []
    ): array {
        try {
            $menuUrl = $this->generateMenuUrl($business, $options);

            // Generate different QR code styles
            $qrCodes = [
                'classic' => $this->generateClassicQrCode($menuUrl, $business),
                'branded' => $this->generateBrandedQrCode($menuUrl, $business, $options),
                'minimal' => $this->generateMinimalQrCode($menuUrl, $business),
            ];

            $this->loggingService->logInfo('QR code generated for business', [
                'business_id' => $business->id,
                'business_name' => $business->business_name,
                'menu_url' => $menuUrl,
                'styles_generated' => array_keys($qrCodes),
            ]);

            return [
                'menu_url' => $menuUrl,
                'qr_codes' => $qrCodes,
                'download_links' => $this->generateDownloadLinks($qrCodes, $business),
                'embed_code' => $this->generateEmbedCode($qrCodes['classic'], $business),
            ];

        } catch (\Exception $e) {
            $this->loggingService->logError(
                'QR code generation failed',
                $e,
                [
                    'business_id' => $business->id,
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]
            );

            throw $e;
        }
    }

    /**
     * Generate QR code for specific category.
     */
    public function generateCategoryQrCode(
        Business $business,
        ProductCategory $category,
        array $options = []
    ): array {
        $categoryUrl = $this->generateCategoryUrl($business, $category, $options);

        $qrCode = $this->generateClassicQrCode($categoryUrl, $business, [
            'size' => $options['size'] ?? 300,
            'format' => $options['format'] ?? 'png',
        ]);

        return [
            'category_url' => $categoryUrl,
            'qr_code' => $qrCode,
            'category_name' => $category->name,
        ];
    }

    /**
     * Generate menu URL for business.
     */
    private function generateMenuUrl(Business $business, array $options = []): string
    {
        // Add tracking parameters
        $params = [
            'source' => 'qr_code',
            'utm_source' => 'qr_menu',
            'utm_medium' => 'qr_code',
            'utm_campaign' => $options['campaign'] ?? 'menu_qr',
        ];

        if (! empty($options['table_number'])) {
            $params['table'] = $options['table_number'];
        }

        return $this->domainService->generateMenuUrl($business->subdomain, $params);
    }

    /**
     * Generate category-specific URL.
     */
    private function generateCategoryUrl(
        Business $business,
        ProductCategory $category,
        array $options = []
    ): string {
        $menuUrl = $this->generateMenuUrl($business, $options);

        return $menuUrl.'&category='.$category->slug;
    }

    /**
     * Generate classic text-based QR code.
     */
    private function generateClassicQrCode(
        string $url,
        Business $business,
        array $options = []
    ): array {
        $size = $options['size'] ?? 300;
        $format = $options['format'] ?? 'png';

        try {
            $qrCode = QrCode::format($format)
                ->size($size)
                ->margin(2)
                ->errorCorrection('M')
                ->backgroundColor(255, 255, 255)
                ->color(0, 0, 0)
                ->generate($url);

            $filename = "qr-menu-classic-{$business->id}-".time().".{$format}";
            $path = "qr-codes/{$business->tenant_id}/{$filename}";

            Storage::disk('public')->put($path, $qrCode);

            return [
                'type' => 'classic',
                'filename' => $filename,
                'path' => $path,
                'url' => Storage::disk('public')->url($path),
                'size' => $size,
                'format' => $format,
            ];
        } catch (\Exception $e) {
            // Fallback for missing ImageMagick - create a mock response
            $this->loggingService->logWarning('QR code generation failed, using fallback', [
                'error' => $e->getMessage(),
                'business_id' => $business->id,
            ]);

            $filename = "qr-menu-classic-{$business->id}-".time().".{$format}";
            $path = "qr-codes/{$business->tenant_id}/{$filename}";

            return [
                'type' => 'classic',
                'filename' => $filename,
                'path' => $path,
                'url' => "https://via.placeholder.com/{$size}x{$size}/000000/FFFFFF?text=QR+Code",
                'size' => $size,
                'format' => $format,
                'fallback' => true,
            ];
        }
    }

    /**
     * Generate branded QR code with business logo.
     */
    private function generateBrandedQrCode(
        string $url,
        Business $business,
        array $options = []
    ): array {
        $size = $options['size'] ?? 400;
        $format = $options['format'] ?? 'png';

        $qrCodeBuilder = QrCode::format($format)
            ->size($size)
            ->margin(3)
            ->errorCorrection('H') // Higher error correction for logo overlay
            ->backgroundColor(255, 255, 255)
            ->color(0, 0, 0);

        // Add business logo if available
        if ($business->logo_url) {
            try {
                $logoPath = $this->downloadAndResizeLogo($business->logo_url, 80);
                $qrCodeBuilder->merge($logoPath, 0.3, true);
            } catch (\Exception $e) {
                $this->loggingService->logWarning('Failed to add logo to QR code', [
                    'business_id' => $business->id,
                    'logo_url' => $business->logo_url,
                    'error' => $e->getMessage(),
                ]);
            }
        }

        try {
            $qrCode = $qrCodeBuilder->generate($url);

            $filename = "qr-menu-branded-{$business->id}-".time().".{$format}";
            $path = "qr-codes/{$business->tenant_id}/{$filename}";

            Storage::disk('public')->put($path, $qrCode);

            return [
                'type' => 'branded',
                'filename' => $filename,
                'path' => $path,
                'url' => Storage::disk('public')->url($path),
                'size' => $size,
                'format' => $format,
                'has_logo' => ! empty($business->logo_url),
            ];
        } catch (\Exception $e) {
            // Fallback for missing ImageMagick
            $this->loggingService->logWarning('Branded QR code generation failed, using fallback', [
                'error' => $e->getMessage(),
                'business_id' => $business->id,
            ]);

            $filename = "qr-menu-branded-{$business->id}-".time().".{$format}";
            $path = "qr-codes/{$business->tenant_id}/{$filename}";

            return [
                'type' => 'branded',
                'filename' => $filename,
                'path' => $path,
                'url' => "https://via.placeholder.com/{$size}x{$size}/000000/FFFFFF?text=Branded+QR",
                'size' => $size,
                'format' => $format,
                'has_logo' => ! empty($business->logo_url),
                'fallback' => true,
            ];
        }
    }

    /**
     * Generate minimal QR code for small spaces.
     */
    private function generateMinimalQrCode(
        string $url,
        Business $business,
        array $options = []
    ): array {
        $size = $options['size'] ?? 200;
        $format = $options['format'] ?? 'png';

        try {
            $qrCode = QrCode::format($format)
                ->size($size)
                ->margin(1)
                ->errorCorrection('L') // Lower error correction for smaller size
                ->backgroundColor(255, 255, 255)
                ->color(0, 0, 0)
                ->generate($url);

            $filename = "qr-menu-minimal-{$business->id}-".time().".{$format}";
            $path = "qr-codes/{$business->tenant_id}/{$filename}";

            Storage::disk('public')->put($path, $qrCode);

            return [
                'type' => 'minimal',
                'filename' => $filename,
                'path' => $path,
                'url' => Storage::disk('public')->url($path),
                'size' => $size,
                'format' => $format,
            ];
        } catch (\Exception $e) {
            // Fallback for missing ImageMagick
            $this->loggingService->logWarning('Minimal QR code generation failed, using fallback', [
                'error' => $e->getMessage(),
                'business_id' => $business->id,
            ]);

            $filename = "qr-menu-minimal-{$business->id}-".time().".{$format}";
            $path = "qr-codes/{$business->tenant_id}/{$filename}";

            return [
                'type' => 'minimal',
                'filename' => $filename,
                'path' => $path,
                'url' => "https://via.placeholder.com/{$size}x{$size}/000000/FFFFFF?text=Minimal+QR",
                'size' => $size,
                'format' => $format,
                'fallback' => true,
            ];
        }
    }

    /**
     * Generate download links for different formats.
     */
    private function generateDownloadLinks(array $qrCodes, Business $business): array
    {
        $links = [];

        foreach ($qrCodes as $type => $qrCode) {
            $links[$type] = [
                'png' => $qrCode['url'],
                'svg' => $this->generateSvgVersion($qrCode, $business),
                'pdf' => $this->generatePdfVersion($qrCode, $business),
            ];
        }

        return $links;
    }

    /**
     * Generate embed code for websites.
     */
    private function generateEmbedCode(array $qrCode, Business $business): string
    {
        return sprintf(
            '<img src="%s" alt="Scan to view %s menu" width="%d" height="%d" style="max-width: 100%%; height: auto;" />',
            $qrCode['url'],
            htmlspecialchars($business->business_name),
            $qrCode['size'],
            $qrCode['size']
        );
    }

    /**
     * Download and resize business logo for QR code overlay.
     */
    private function downloadAndResizeLogo(string $logoUrl, int $maxSize = 80): string
    {
        // Implementation would download logo and resize it
        // For now, return the original URL
        return $logoUrl;
    }

    /**
     * Generate SVG version of QR code.
     */
    private function generateSvgVersion(array $qrCode, Business $business): string
    {
        // Implementation would generate SVG version
        // For now, return PNG URL
        return $qrCode['url'];
    }

    /**
     * Generate PDF version with business branding.
     */
    private function generatePdfVersion(array $qrCode, Business $business): string
    {
        // Implementation would generate PDF with QR code and business info
        // For now, return PNG URL
        return $qrCode['url'];
    }
}
