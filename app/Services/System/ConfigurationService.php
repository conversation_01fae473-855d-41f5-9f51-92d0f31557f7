<?php

declare(strict_types=1);

namespace App\Services\System;

use App\Enums\System\ConfigurationType;
use App\Models\System\ConfigurationGroup;
use App\Models\System\ConfigurationHistory;
use App\Models\System\ConfigurationSetting;
use App\Models\System\PlatformSetting;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ConfigurationService
{
    private const CACHE_PREFIX = 'config:';

    private const CACHE_TTL = 3600; // 1 hour

    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all configuration groups with settings.
     */
    public function getAllGroups(): Collection
    {
        return ConfigurationGroup::active()
            ->ordered()
            ->with(['activeSettings' => function ($query) {
                $query->orderBy('name');
            }])
            ->get();
    }

    /**
     * Get configuration group with details.
     */
    public function getGroupWithDetails(string $groupId): array
    {
        $group = ConfigurationGroup::with([
            'settings.updatedBy:id,first_name,last_name',
            'settings.history.changedBy:id,first_name,last_name',
        ])->findOrFail($groupId);

        return [
            'group' => $group,
            'statistics' => $this->getGroupStatistics($group),
            'recent_changes' => $this->getRecentChanges($groupId),
        ];
    }

    /**
     * Create new configuration group.
     */
    public function createGroup(array $data): ConfigurationGroup
    {
        return DB::transaction(function () use ($data) {
            // Generate slug if not provided
            if (empty($data['slug'])) {
                $data['slug'] = $this->generateUniqueSlug($data['name']);
            }

            $group = ConfigurationGroup::create([
                'name' => $data['name'],
                'slug' => $data['slug'],
                'description' => $data['description'] ?? null,
                'icon' => $data['icon'] ?? null,
                'sort_order' => $data['sort_order'] ?? 0,
                'is_active' => $data['is_active'] ?? true,
            ]);

            // Log group creation
            activity()
                ->performedOn($group)
                ->withProperties([
                    'group_data' => $data,
                    'admin_id' => auth()->id(),
                ])
                ->log('configuration_group_created');

            $this->loggingService->logInfo('Configuration group created', [
                'group_id' => $group->id,
                'name' => $group->name,
                'admin_id' => auth()->id(),
            ]);

            return $group;
        });
    }

    /**
     * Update configuration group.
     */
    public function updateGroup(string $groupId, array $data): ConfigurationGroup
    {
        return DB::transaction(function () use ($groupId, $data) {
            $group = ConfigurationGroup::findOrFail($groupId);

            // Update slug if name changed
            if (isset($data['name']) && $data['name'] !== $group->name) {
                $data['slug'] = $this->generateUniqueSlug($data['name'], $group->id);
            }

            $group->update(array_filter([
                'name' => $data['name'] ?? $group->name,
                'slug' => $data['slug'] ?? $group->slug,
                'description' => $data['description'] ?? $group->description,
                'icon' => $data['icon'] ?? $group->icon,
                'sort_order' => $data['sort_order'] ?? $group->sort_order,
                'is_active' => $data['is_active'] ?? $group->is_active,
            ]));

            // Log group update
            activity()
                ->performedOn($group)
                ->withProperties([
                    'updated_data' => $data,
                    'admin_id' => auth()->id(),
                ])
                ->log('configuration_group_updated');

            return $group->fresh();
        });
    }

    /**
     * Create new configuration setting.
     */
    public function createSetting(array $data): ConfigurationSetting
    {
        return DB::transaction(function () use ($data) {
            $setting = ConfigurationSetting::create([
                'group_id' => $data['group_id'] ?? null,
                'key' => $data['key'],
                'name' => $data['name'],
                'description' => $data['description'] ?? null,
                'type' => ConfigurationType::from($data['type']),
                'value' => $data['value'] ?? null,
                'default_value' => $data['default_value'] ?? null,
                'validation_rules' => $data['validation_rules'] ?? null,
                'options' => $data['options'] ?? null,
                'is_required' => $data['is_required'] ?? false,
                'is_active' => $data['is_active'] ?? true,
                'is_sensitive' => $data['is_sensitive'] ?? false,
                'updated_by' => auth()->id(),
            ]);

            // Create initial history record
            ConfigurationHistory::create([
                'setting_id' => $setting->id,
                'previous_value' => null,
                'new_value' => $setting->value,
                'change_reason' => 'Initial creation',
                'changed_by' => auth()->id(),
            ]);

            // Clear cache
            $this->clearSettingCache($setting->key);

            // Log setting creation
            activity()
                ->performedOn($setting)
                ->withProperties([
                    'setting_data' => $data,
                    'admin_id' => auth()->id(),
                ])
                ->log('configuration_setting_created');

            return $setting;
        });
    }

    /**
     * Update configuration setting.
     */
    public function updateSetting(string $settingId, array $data): ConfigurationSetting
    {
        return DB::transaction(function () use ($settingId, $data) {
            $setting = ConfigurationSetting::findOrFail($settingId);
            $previousValue = $setting->value;

            // Validate value if provided
            if (isset($data['value'])) {
                $this->validateSettingValue($setting, $data['value']);
            }

            $setting->update(array_filter([
                'group_id' => $data['group_id'] ?? $setting->group_id,
                'name' => $data['name'] ?? $setting->name,
                'description' => $data['description'] ?? $setting->description,
                'value' => $data['value'] ?? $setting->value,
                'default_value' => $data['default_value'] ?? $setting->default_value,
                'validation_rules' => $data['validation_rules'] ?? $setting->validation_rules,
                'options' => $data['options'] ?? $setting->options,
                'is_required' => $data['is_required'] ?? $setting->is_required,
                'is_active' => $data['is_active'] ?? $setting->is_active,
                'is_sensitive' => $data['is_sensitive'] ?? $setting->is_sensitive,
                'updated_by' => auth()->id(),
            ]));

            // Create history record if value changed
            if (isset($data['value']) && $data['value'] !== $previousValue) {
                ConfigurationHistory::create([
                    'setting_id' => $setting->id,
                    'previous_value' => $previousValue,
                    'new_value' => $data['value'],
                    'change_reason' => $data['change_reason'] ?? 'Updated via admin panel',
                    'changed_by' => auth()->id(),
                ]);
            }

            // Clear cache
            $this->clearSettingCache($setting->key);

            // Log setting update
            activity()
                ->performedOn($setting)
                ->withProperties([
                    'updated_data' => $data,
                    'admin_id' => auth()->id(),
                ])
                ->log('configuration_setting_updated');

            return $setting->fresh();
        });
    }

    /**
     * Get configuration value by key.
     */
    public function getValue(string $key, $default = null)
    {
        $cacheKey = self::CACHE_PREFIX.$key;

        return Cache::remember($cacheKey, self::CACHE_TTL, function () use ($key, $default) {
            // Try new configuration system first
            $setting = ConfigurationSetting::active()->byKey($key)->first();
            if ($setting) {
                return $setting->getTypedValue();
            }

            // Fall back to legacy platform settings
            return PlatformSetting::getValue($key, $default);
        });
    }

    /**
     * Set configuration value by key.
     */
    public function setValue(string $key, $value, ?string $reason = null): void
    {
        DB::transaction(function () use ($key, $value, $reason) {
            $setting = ConfigurationSetting::byKey($key)->first();

            if (! $setting) {
                throw new \InvalidArgumentException("Configuration setting with key '{$key}' not found");
            }

            $this->updateSetting($setting->id, [
                'value' => $value,
                'change_reason' => $reason ?? 'Updated programmatically',
            ]);
        });
    }

    /**
     * Bulk update configuration values.
     */
    public function bulkUpdate(array $settings, ?string $reason = null): array
    {
        return DB::transaction(function () use ($settings, $reason) {
            $results = [];

            foreach ($settings as $key => $value) {
                try {
                    $this->setValue($key, $value, $reason);
                    $results[$key] = ['success' => true];
                } catch (\Exception $e) {
                    $results[$key] = [
                        'success' => false,
                        'error' => $e->getMessage(),
                    ];
                }
            }

            return $results;
        });
    }

    /**
     * Get configuration statistics.
     */
    public function getStatistics(): array
    {
        return [
            'total_groups' => ConfigurationGroup::count(),
            'active_groups' => ConfigurationGroup::active()->count(),
            'total_settings' => ConfigurationSetting::count(),
            'active_settings' => ConfigurationSetting::active()->count(),
            'sensitive_settings' => ConfigurationSetting::where('is_sensitive', true)->count(),
            'required_settings' => ConfigurationSetting::required()->count(),
            'recent_changes' => ConfigurationHistory::latest()->limit(10)->with([
                'setting:id,key,name',
                'changedBy:id,first_name,last_name',
            ])->get(),
            'settings_by_type' => ConfigurationSetting::selectRaw('type, COUNT(*) as count')
                ->groupBy('type')
                ->pluck('count', 'type'),
        ];
    }

    /**
     * Validate setting value.
     */
    private function validateSettingValue(ConfigurationSetting $setting, $value): void
    {
        $rules = $setting->validation_rules ?? $setting->type->getValidationRules();

        if ($rules) {
            $validator = validator(['value' => $value], ['value' => $rules]);
            if ($validator->fails()) {
                throw new \InvalidArgumentException('Invalid value: '.implode(', ', $validator->errors()->all()));
            }
        }

        // Additional validation for select types
        if ($setting->type === ConfigurationType::SELECT && $setting->options) {
            $validOptions = array_column($setting->options, 'value');
            if (! in_array($value, $validOptions)) {
                throw new \InvalidArgumentException('Value must be one of: '.implode(', ', $validOptions));
            }
        }

        if ($setting->type === ConfigurationType::MULTISELECT && $setting->options) {
            $validOptions = array_column($setting->options, 'value');
            $invalidValues = array_diff($value, $validOptions);
            if (! empty($invalidValues)) {
                throw new \InvalidArgumentException('Invalid values: '.implode(', ', $invalidValues));
            }
        }
    }

    /**
     * Generate unique slug.
     */
    private function generateUniqueSlug(string $name, ?string $excludeId = null): string
    {
        $baseSlug = Str::slug($name);
        $slug = $baseSlug;
        $counter = 1;

        while ($this->slugExists($slug, $excludeId)) {
            $slug = $baseSlug.'-'.$counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Check if slug exists.
     */
    private function slugExists(string $slug, ?string $excludeId = null): bool
    {
        $query = ConfigurationGroup::where('slug', $slug);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Clear setting cache.
     */
    private function clearSettingCache(string $key): void
    {
        Cache::forget(self::CACHE_PREFIX.$key);
    }

    /**
     * Get group statistics.
     */
    private function getGroupStatistics(ConfigurationGroup $group): array
    {
        return [
            'total_settings' => $group->settings()->count(),
            'active_settings' => $group->activeSettings()->count(),
            'sensitive_settings' => $group->settings()->where('is_sensitive', true)->count(),
            'required_settings' => $group->settings()->where('is_required', true)->count(),
        ];
    }

    /**
     * Get recent changes for a group.
     */
    private function getRecentChanges(string $groupId): Collection
    {
        return ConfigurationHistory::whereHas('setting', function ($query) use ($groupId) {
            $query->where('group_id', $groupId);
        })
            ->with([
                'setting:id,key,name',
                'changedBy:id,first_name,last_name',
            ])
            ->latest()
            ->limit(10)
            ->get();
    }
}
