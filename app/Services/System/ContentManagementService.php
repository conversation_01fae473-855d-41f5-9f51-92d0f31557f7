<?php

declare(strict_types=1);

namespace App\Services\System;

use App\Enums\System\ContentStatus;
use App\Enums\System\ContentType;
use App\Models\System\Content;
use App\Models\System\ContentVersion;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class ContentManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get content with detailed information.
     */
    public function getContentWithDetails(string $contentId): array
    {
        $content = Content::with([
            'author:id,first_name,last_name,email',
            'publisher:id,first_name,last_name,email',
            'versions.createdBy:id,first_name,last_name',
        ])->findOrFail($contentId);

        return [
            'content' => $content,
            'statistics' => $this->getContentStatistics($content),
            'recent_versions' => $content->versions()->latest()->limit(5)->get(),
            'related_content' => $this->getRelatedContent($content),
        ];
    }

    /**
     * Create new content.
     */
    public function createContent(array $data): Content
    {
        return DB::transaction(function () use ($data) {
            // Generate slug if not provided
            if (empty($data['slug'])) {
                $data['slug'] = $this->generateUniqueSlug($data['title']);
            }

            $content = Content::create([
                'title' => $data['title'],
                'slug' => $data['slug'],
                'excerpt' => $data['excerpt'] ?? null,
                'content' => $data['content'],
                'type' => ContentType::from($data['type']),
                'status' => ContentStatus::from($data['status'] ?? ContentStatus::DRAFT->value),
                'meta_title' => $data['meta_title'] ?? null,
                'meta_description' => $data['meta_description'] ?? null,
                'meta_keywords' => $data['meta_keywords'] ?? null,
                'featured_image' => $data['featured_image'] ?? null,
                'settings' => $data['settings'] ?? [],
                'author_id' => auth()->id(),
                'is_featured' => $data['is_featured'] ?? false,
                'allow_comments' => $data['allow_comments'] ?? false,
                'language' => $data['language'] ?? 'en',
                'sort_order' => $data['sort_order'] ?? 0,
            ]);

            // Create initial version if content type supports versioning
            if ($content->type->isVersionable()) {
                ContentVersion::createFromContent($content, 'Initial version');
            }

            // Log content creation
            activity()
                ->performedOn($content)
                ->withProperties([
                    'content_data' => $data,
                    'admin_id' => auth()->id(),
                ])
                ->log('content_created');

            $this->loggingService->logInfo('Content created', [
                'content_id' => $content->id,
                'title' => $content->title,
                'type' => $content->type->value,
                'author_id' => auth()->id(),
            ]);

            return $content;
        });
    }

    /**
     * Update existing content.
     */
    public function updateContent(string $contentId, array $data): Content
    {
        return DB::transaction(function () use ($contentId, $data) {
            $content = Content::findOrFail($contentId);

            // Store original data for version tracking
            $originalData = [
                'title' => $content->title,
                'excerpt' => $content->excerpt,
                'content' => $content->content,
            ];

            // Update slug if title changed
            if (isset($data['title']) && $data['title'] !== $content->title) {
                $data['slug'] = $this->generateUniqueSlug($data['title'], $content->id);
            }

            $content->update(array_filter([
                'title' => $data['title'] ?? $content->title,
                'slug' => $data['slug'] ?? $content->slug,
                'excerpt' => $data['excerpt'] ?? $content->excerpt,
                'content' => $data['content'] ?? $content->content,
                'type' => isset($data['type']) ? ContentType::from($data['type']) : $content->type,
                'status' => isset($data['status']) ? ContentStatus::from($data['status']) : $content->status,
                'meta_title' => $data['meta_title'] ?? $content->meta_title,
                'meta_description' => $data['meta_description'] ?? $content->meta_description,
                'meta_keywords' => $data['meta_keywords'] ?? $content->meta_keywords,
                'featured_image' => $data['featured_image'] ?? $content->featured_image,
                'settings' => $data['settings'] ?? $content->settings,
                'is_featured' => $data['is_featured'] ?? $content->is_featured,
                'allow_comments' => $data['allow_comments'] ?? $content->allow_comments,
                'language' => $data['language'] ?? $content->language,
                'sort_order' => $data['sort_order'] ?? $content->sort_order,
            ]));

            // Create new version if content changed and type supports versioning
            if ($content->type->isVersionable() && $this->hasContentChanged($originalData, $content)) {
                ContentVersion::createFromContent($content, $data['change_summary'] ?? null);
            }

            // Log content update
            activity()
                ->performedOn($content)
                ->withProperties([
                    'updated_data' => $data,
                    'admin_id' => auth()->id(),
                ])
                ->log('content_updated');

            return $content->fresh();
        });
    }

    /**
     * Publish content.
     */
    public function publishContent(string $contentId, ?\DateTime $publishAt = null): Content
    {
        return DB::transaction(function () use ($contentId, $publishAt) {
            $content = Content::findOrFail($contentId);

            if (! $content->status->isPublishable()) {
                throw new \InvalidArgumentException('Content cannot be published in its current status');
            }

            $content->update([
                'status' => $publishAt ? ContentStatus::SCHEDULED : ContentStatus::PUBLISHED,
                'published_at' => $publishAt ?? now(),
                'published_by' => auth()->id(),
            ]);

            // Log content publication
            activity()
                ->performedOn($content)
                ->withProperties([
                    'published_at' => $content->published_at,
                    'admin_id' => auth()->id(),
                ])
                ->log('content_published');

            return $content;
        });
    }

    /**
     * Archive content.
     */
    public function archiveContent(string $contentId): Content
    {
        $content = Content::findOrFail($contentId);

        $content->update([
            'status' => ContentStatus::ARCHIVED,
        ]);

        // Log content archival
        activity()
            ->performedOn($content)
            ->withProperties(['admin_id' => auth()->id()])
            ->log('content_archived');

        return $content;
    }

    /**
     * Delete content.
     */
    public function deleteContent(string $contentId): bool
    {
        return DB::transaction(function () use ($contentId) {
            $content = Content::findOrFail($contentId);

            // Log content deletion
            activity()
                ->performedOn($content)
                ->withProperties([
                    'content_title' => $content->title,
                    'admin_id' => auth()->id(),
                ])
                ->log('content_deleted');

            return $content->delete();
        });
    }

    /**
     * Get content by type.
     */
    public function getContentByType(ContentType $type, int $limit = 10): Collection
    {
        return Content::ofType($type)
            ->with(['author:id,first_name,last_name'])
            ->latest()
            ->limit($limit)
            ->get();
    }

    /**
     * Get published content for public display.
     */
    public function getPublishedContent(ContentType $type, int $limit = 10): Collection
    {
        return Content::published()
            ->ofType($type)
            ->with(['author:id,first_name,last_name'])
            ->orderBy('sort_order')
            ->orderBy('published_at', 'desc')
            ->limit($limit)
            ->get();
    }

    /**
     * Generate unique slug for content.
     */
    private function generateUniqueSlug(string $title, ?string $excludeId = null): string
    {
        $baseSlug = Str::slug($title);
        $slug = $baseSlug;
        $counter = 1;

        while ($this->slugExists($slug, $excludeId)) {
            $slug = $baseSlug.'-'.$counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Check if slug exists.
     */
    private function slugExists(string $slug, ?string $excludeId = null): bool
    {
        $query = Content::where('slug', $slug);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Check if content has changed.
     */
    private function hasContentChanged(array $original, Content $current): bool
    {
        return $original['title'] !== $current->title
            || $original['excerpt'] !== $current->excerpt
            || $original['content'] !== $current->content;
    }

    /**
     * Get content statistics.
     */
    private function getContentStatistics(Content $content): array
    {
        return [
            'view_count' => $content->view_count,
            'version_count' => $content->versions()->count(),
            'word_count' => str_word_count(strip_tags($content->content)),
            'reading_time' => $content->reading_time,
            'last_updated' => $content->updated_at,
            'days_since_update' => $content->updated_at->diffInDays(now()),
        ];
    }

    /**
     * Get related content.
     */
    private function getRelatedContent(Content $content): Collection
    {
        return Content::published()
            ->where('id', '!=', $content->id)
            ->where('type', $content->type)
            ->where('language', $content->language)
            ->limit(5)
            ->get();
    }
}
