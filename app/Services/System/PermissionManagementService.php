<?php

declare(strict_types=1);

namespace App\Services\System;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Silber\Bouncer\Database\Ability;
use Silber\Bouncer\Database\Role;
use Silber\Bouncer\Facades\Bouncer;

class PermissionManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all roles with their abilities.
     */
    public function getAllRolesWithAbilities(): Collection
    {
        return Role::with(['abilities'])
            ->withCount(['users'])
            ->orderBy('name')
            ->get();
    }

    /**
     * Get all abilities grouped by category.
     */
    public function getAllAbilitiesGrouped(): array
    {
        $abilities = Ability::orderBy('name')->get();

        $grouped = [];
        foreach ($abilities as $ability) {
            $category = $this->categorizeAbility($ability->name);
            $grouped[$category][] = $ability;
        }

        return $grouped;
    }

    /**
     * Create a new role.
     */
    public function createRole(string $name, string $title, array $abilities = []): Role
    {
        return DB::transaction(function () use ($name, $title, $abilities) {
            $role = Bouncer::role()->create([
                'name' => $name,
                'title' => $title,
            ]);

            if (! empty($abilities)) {
                foreach ($abilities as $abilityName) {
                    Bouncer::allow($role)->to($abilityName);
                }
            }

            $this->loggingService->logInfo('Role created', [
                'role_name' => $name,
                'role_title' => $title,
                'abilities_count' => count($abilities),
                'admin_id' => auth()->id(),
            ]);

            return $role;
        });
    }

    /**
     * Update role abilities.
     */
    public function updateRoleAbilities(string $roleName, array $abilities): Role
    {
        return DB::transaction(function () use ($roleName, $abilities) {
            $role = Bouncer::role()->where('name', $roleName)->firstOrFail();

            // Remove all current abilities
            Bouncer::disallow($role)->everything();

            // Add new abilities
            foreach ($abilities as $abilityName) {
                Bouncer::allow($role)->to($abilityName);
            }

            $this->loggingService->logInfo('Role abilities updated', [
                'role_name' => $roleName,
                'abilities_count' => count($abilities),
                'admin_id' => auth()->id(),
            ]);

            return $role->fresh(['abilities']);
        });
    }

    /**
     * Delete a role.
     */
    public function deleteRole(string $roleName): bool
    {
        return DB::transaction(function () use ($roleName) {
            $role = Bouncer::role()->where('name', $roleName)->firstOrFail();

            // Check if role is assigned to any users
            $userCount = $role->users()->count();
            if ($userCount > 0) {
                throw new \Exception("Cannot delete role '{$roleName}' as it is assigned to {$userCount} user(s)");
            }

            $deleted = $role->delete();

            if ($deleted) {
                $this->loggingService->logInfo('Role deleted', [
                    'role_name' => $roleName,
                    'admin_id' => auth()->id(),
                ]);
            }

            return $deleted;
        });
    }

    /**
     * Create a new ability.
     */
    public function createAbility(string $name, ?string $title = null): Ability
    {
        $ability = Bouncer::ability()->create([
            'name' => $name,
            'title' => $title ?? ucwords(str_replace(['-', '_'], ' ', $name)),
        ]);

        $this->loggingService->logInfo('Ability created', [
            'ability_name' => $name,
            'ability_title' => $title,
            'admin_id' => auth()->id(),
        ]);

        return $ability;
    }

    /**
     * Assign role to user.
     */
    public function assignRoleToUser(string $userId, string $roleName): void
    {
        DB::transaction(function () use ($userId, $roleName) {
            $user = User::findOrFail($userId);
            $role = Bouncer::role()->where('name', $roleName)->firstOrFail();

            Bouncer::assign($roleName)->to($user);

            $this->loggingService->logInfo('Role assigned to user', [
                'user_id' => $userId,
                'role_name' => $roleName,
                'admin_id' => auth()->id(),
            ]);
        });
    }

    /**
     * Remove role from user.
     */
    public function removeRoleFromUser(string $userId, string $roleName): void
    {
        DB::transaction(function () use ($userId, $roleName) {
            $user = User::findOrFail($userId);

            Bouncer::retract($roleName)->from($user);

            $this->loggingService->logInfo('Role removed from user', [
                'user_id' => $userId,
                'role_name' => $roleName,
                'admin_id' => auth()->id(),
            ]);
        });
    }

    /**
     * Get user roles and abilities.
     */
    public function getUserPermissions(string $userId): array
    {
        $user = User::with(['roles.abilities', 'abilities'])->findOrFail($userId);

        return [
            'user' => $user->only(['id', 'first_name', 'last_name', 'email']),
            'roles' => $user->roles->map(function ($role) {
                return [
                    'id' => $role->id,
                    'name' => $role->name,
                    'title' => $role->title,
                    'abilities' => $role->abilities->pluck('name'),
                ];
            }),
            'direct_abilities' => $user->abilities->pluck('name'),
            'all_abilities' => $user->getAbilities()->pluck('name'),
        ];
    }

    /**
     * Bulk assign roles to users.
     */
    public function bulkAssignRoles(array $userIds, array $roleNames): array
    {
        $results = ['success' => 0, 'failed' => 0, 'details' => []];

        DB::transaction(function () use ($userIds, $roleNames, &$results) {
            foreach ($userIds as $userId) {
                try {
                    $user = User::findOrFail($userId);

                    foreach ($roleNames as $roleName) {
                        Bouncer::assign($roleName)->to($user);
                    }

                    $results['success']++;
                    $results['details'][$userId] = [
                        'status' => 'success',
                        'roles_assigned' => $roleNames,
                    ];

                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['details'][$userId] = [
                        'status' => 'failed',
                        'error' => $e->getMessage(),
                    ];
                }
            }

            $this->loggingService->logInfo('Bulk role assignment completed', [
                'user_count' => count($userIds),
                'roles' => $roleNames,
                'success_count' => $results['success'],
                'failed_count' => $results['failed'],
                'admin_id' => auth()->id(),
            ]);
        });

        return $results;
    }

    /**
     * Get role statistics.
     */
    public function getRoleStatistics(): array
    {
        return [
            'total_roles' => Role::count(),
            'total_abilities' => Ability::count(),
            'roles_with_users' => Role::has('users')->count(),
            'most_assigned_roles' => Role::withCount('users')
                ->orderByDesc('users_count')
                ->limit(10)
                ->get()
                ->map(function ($role) {
                    return [
                        'name' => $role->name,
                        'title' => $role->title,
                        'users_count' => $role->users_count,
                    ];
                }),
            'ability_categories' => $this->getAbilityCategoryStats(),
            'recent_assignments' => $this->getRecentRoleAssignments(),
        ];
    }

    /**
     * Search users by role.
     */
    public function searchUsersByRole(string $roleName, ?string $query = null): Collection
    {
        $usersQuery = User::whereIs($roleName)
            ->select(['id', 'first_name', 'last_name', 'email', 'created_at']);

        if ($query) {
            $usersQuery->where(function ($q) use ($query) {
                $q->where('first_name', 'ILIKE', "%{$query}%")
                    ->orWhere('last_name', 'ILIKE', "%{$query}%")
                    ->orWhere('email', 'ILIKE', "%{$query}%");
            });
        }

        return $usersQuery->limit(50)->get();
    }

    /**
     * Get role hierarchy and dependencies.
     */
    public function getRoleHierarchy(): array
    {
        $roles = Role::with(['abilities'])->get();

        $hierarchy = [
            'platform_admin' => ['super-admin', 'platform-admin', 'support-agent'],
            'business_roles' => ['business-owner', 'business-manager', 'business-staff', 'business-cashier', 'business-kitchen-staff', 'business-inventory-manager'],
            'delivery_roles' => ['delivery-provider-owner', 'delivery-manager', 'delivery-dispatcher', 'delivery-driver', 'delivery-rider', 'delivery-supervisor', 'fleet-manager', 'delivery-analyst', 'route-optimizer'],
            'customer_roles' => ['customer'],
        ];

        $result = [];
        foreach ($hierarchy as $category => $roleNames) {
            $result[$category] = $roles->whereIn('name', $roleNames)->values();
        }

        return $result;
    }

    /**
     * Validate role permissions.
     */
    public function validateRolePermissions(string $roleName): array
    {
        $role = Bouncer::role()->where('name', $roleName)->with(['abilities'])->firstOrFail();

        $issues = [];
        $recommendations = [];

        // Check for conflicting permissions
        $abilities = $role->abilities->pluck('name')->toArray();

        // Check for admin-level permissions on non-admin roles
        if (! str_contains($roleName, 'admin') && ! str_contains($roleName, 'super')) {
            $adminAbilities = array_filter($abilities, fn ($ability) => str_contains($ability, 'manage-platform') || str_contains($ability, 'manage-users'));
            if (! empty($adminAbilities)) {
                $issues[] = 'Non-admin role has admin-level permissions: '.implode(', ', $adminAbilities);
            }
        }

        // Check for missing essential permissions
        if (str_contains($roleName, 'owner')) {
            $essentialAbilities = ['view-own-business', 'manage-own-business'];
            $missing = array_diff($essentialAbilities, $abilities);
            if (! empty($missing)) {
                $recommendations[] = 'Owner role should have: '.implode(', ', $missing);
            }
        }

        return [
            'role' => $role->name,
            'title' => $role->title,
            'abilities_count' => count($abilities),
            'issues' => $issues,
            'recommendations' => $recommendations,
            'risk_level' => $this->calculateRoleRiskLevel($abilities),
        ];
    }

    /**
     * Categorize ability by name.
     */
    private function categorizeAbility(string $abilityName): string
    {
        if (str_contains($abilityName, 'manage-platform') || str_contains($abilityName, 'manage-users')) {
            return 'Platform Administration';
        }

        if (str_contains($abilityName, 'business') || str_contains($abilityName, 'product')) {
            return 'Business Management';
        }

        if (str_contains($abilityName, 'delivery') || str_contains($abilityName, 'order')) {
            return 'Delivery & Orders';
        }

        if (str_contains($abilityName, 'payment') || str_contains($abilityName, 'financial')) {
            return 'Financial';
        }

        if (str_contains($abilityName, 'support') || str_contains($abilityName, 'chat')) {
            return 'Support & Communication';
        }

        if (str_contains($abilityName, 'analytics') || str_contains($abilityName, 'dashboard')) {
            return 'Analytics & Reporting';
        }

        return 'General';
    }

    /**
     * Get ability category statistics.
     */
    private function getAbilityCategoryStats(): array
    {
        $abilities = Ability::all();
        $stats = [];

        foreach ($abilities as $ability) {
            $category = $this->categorizeAbility($ability->name);
            $stats[$category] = ($stats[$category] ?? 0) + 1;
        }

        return $stats;
    }

    /**
     * Get recent role assignments.
     */
    private function getRecentRoleAssignments(): array
    {
        // This would require tracking assignment history
        // For now, return mock data
        return [
            [
                'user_name' => 'John Doe',
                'role_name' => 'business-owner',
                'assigned_at' => now()->subHours(2),
                'assigned_by' => 'Admin User',
            ],
            [
                'user_name' => 'Jane Smith',
                'role_name' => 'delivery-driver',
                'assigned_at' => now()->subHours(5),
                'assigned_by' => 'Admin User',
            ],
        ];
    }

    /**
     * Calculate role risk level based on abilities.
     */
    private function calculateRoleRiskLevel(array $abilities): string
    {
        $highRiskAbilities = [
            'manage-platform-settings',
            'manage-users',
            'manage-api-keys',
            'view-audit-logs',
            'manage-webhooks',
        ];

        $mediumRiskAbilities = [
            'manage-payouts',
            'view-financial-dashboard',
            'manage-subscription-plans',
        ];

        $highRiskCount = count(array_intersect($abilities, $highRiskAbilities));
        $mediumRiskCount = count(array_intersect($abilities, $mediumRiskAbilities));

        if ($highRiskCount > 0) {
            return 'high';
        }

        if ($mediumRiskCount > 2) {
            return 'medium';
        }

        return 'low';
    }
}
