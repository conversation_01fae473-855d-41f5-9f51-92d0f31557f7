<?php

declare(strict_types=1);

namespace App\Services\System;

use Illuminate\Support\Facades\Cache;

/**
 * Theme Management Service
 *
 * Handles comprehensive theme and branding management including:
 * - Theme creation and customization
 * - Brand asset management
 * - Multi-tenant theme support
 * - Theme preview and testing
 * - Custom CSS/styling management
 */
class ThemeManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all themes with filtering.
     */
    public function getThemes(array $filters = []): array
    {
        $themes = $this->getThemeList();

        // Apply filters
        if (isset($filters['status'])) {
            $themes = array_filter($themes, fn ($theme) => $theme['status'] === $filters['status']);
        }

        if (isset($filters['type'])) {
            $themes = array_filter($themes, fn ($theme) => $theme['type'] === $filters['type']);
        }

        if (isset($filters['tenant_id'])) {
            $themes = array_filter($themes, fn ($theme) => $theme['tenant_id'] === $filters['tenant_id'] || $theme['tenant_id'] === null
            );
        }

        if (isset($filters['category'])) {
            $themes = array_filter($themes, fn ($theme) => $theme['category'] === $filters['category']);
        }

        return array_values($themes);
    }

    /**
     * Create a new theme.
     */
    public function createTheme(array $config): array
    {
        $themeId = uniqid('theme_');

        try {
            $theme = [
                'id' => $themeId,
                'name' => $config['name'],
                'description' => $config['description'] ?? '',
                'type' => $config['type'], // default, custom, tenant_specific
                'category' => $config['category'] ?? 'general', // general, seasonal, industry_specific
                'tenant_id' => $config['tenant_id'] ?? null,
                'status' => 'draft',
                'is_default' => $config['is_default'] ?? false,
                'colors' => $config['colors'] ?? $this->getDefaultColors(),
                'fonts' => $config['fonts'] ?? $this->getDefaultFonts(),
                'layout' => $config['layout'] ?? $this->getDefaultLayout(),
                'components' => $config['components'] ?? $this->getDefaultComponents(),
                'custom_css' => $config['custom_css'] ?? '',
                'assets' => $config['assets'] ?? [],
                'preview_url' => null,
                'created_at' => now()->toISOString(),
                'created_by' => auth()->id(),
                'updated_at' => now()->toISOString(),
                'version' => '1.0.0',
                'metadata' => $config['metadata'] ?? [],
            ];

            // Generate preview
            $this->generateThemePreview($theme);

            $this->loggingService->logInfo('Theme created', [
                'theme_id' => $themeId,
                'name' => $config['name'],
                'type' => $config['type'],
                'tenant_id' => $config['tenant_id'] ?? null,
                'created_by' => auth()->id(),
            ]);

            return $theme;

        } catch (\Exception $e) {
            $this->loggingService->logError('Theme creation failed', $e, [
                'theme_id' => $themeId,
                'config' => $config,
            ]);

            throw $e;
        }
    }

    /**
     * Update theme configuration.
     */
    public function updateTheme(string $themeId, array $updates): array
    {
        try {
            $theme = $this->getThemeById($themeId);

            if (! $theme) {
                throw new \InvalidArgumentException('Theme not found');
            }

            // Update allowed fields
            foreach ($updates as $field => $value) {
                if (in_array($field, ['name', 'description', 'colors', 'fonts', 'layout', 'components', 'custom_css', 'assets', 'status'])) {
                    $theme[$field] = $value;
                }
            }

            $theme['updated_at'] = now()->toISOString();
            $theme['version'] = $this->incrementVersion($theme['version']);

            // Regenerate preview if visual changes
            if (array_intersect(array_keys($updates), ['colors', 'fonts', 'layout', 'components', 'custom_css'])) {
                $this->generateThemePreview($theme);
            }

            $this->loggingService->logInfo('Theme updated', [
                'theme_id' => $themeId,
                'updates' => array_keys($updates),
                'new_version' => $theme['version'],
                'updated_by' => auth()->id(),
            ]);

            return $theme;

        } catch (\Exception $e) {
            $this->loggingService->logError('Theme update failed', $e, [
                'theme_id' => $themeId,
                'updates' => $updates,
            ]);

            throw $e;
        }
    }

    /**
     * Activate theme for tenant or platform.
     */
    public function activateTheme(string $themeId, ?string $tenantId = null): array
    {
        try {
            $theme = $this->getThemeById($themeId);

            if (! $theme) {
                throw new \InvalidArgumentException('Theme not found');
            }

            if ($theme['status'] !== 'published') {
                throw new \InvalidArgumentException('Theme must be published before activation');
            }

            // Deactivate current active theme
            $this->deactivateCurrentTheme($tenantId);

            // Activate new theme
            $theme['status'] = 'active';
            $theme['activated_at'] = now()->toISOString();
            $theme['activated_by'] = auth()->id();

            // Cache theme for performance
            $cacheKey = $tenantId ? "theme_tenant_{$tenantId}" : 'theme_platform';
            Cache::put($cacheKey, $theme, now()->addHours(24));

            $this->loggingService->logInfo('Theme activated', [
                'theme_id' => $themeId,
                'theme_name' => $theme['name'],
                'tenant_id' => $tenantId,
                'activated_by' => auth()->id(),
            ]);

            return $theme;

        } catch (\Exception $e) {
            $this->loggingService->logError('Theme activation failed', $e, [
                'theme_id' => $themeId,
                'tenant_id' => $tenantId,
            ]);

            throw $e;
        }
    }

    /**
     * Preview theme without activating.
     */
    public function previewTheme(string $themeId, array $options = []): array
    {
        try {
            $theme = $this->getThemeById($themeId);

            if (! $theme) {
                throw new \InvalidArgumentException('Theme not found');
            }

            $previewId = uniqid('preview_');

            $preview = [
                'id' => $previewId,
                'theme_id' => $themeId,
                'theme_name' => $theme['name'],
                'preview_url' => $this->generatePreviewUrl($theme, $previewId),
                'expires_at' => now()->addHours(2)->toISOString(),
                'created_at' => now()->toISOString(),
                'created_by' => auth()->id(),
                'options' => $options,
            ];

            $this->loggingService->logInfo('Theme preview generated', [
                'theme_id' => $themeId,
                'preview_id' => $previewId,
                'created_by' => auth()->id(),
            ]);

            return $preview;

        } catch (\Exception $e) {
            $this->loggingService->logError('Theme preview failed', $e, [
                'theme_id' => $themeId,
                'options' => $options,
            ]);

            throw $e;
        }
    }

    /**
     * Get theme analytics and usage statistics.
     */
    public function getThemeAnalytics(array $filters = []): array
    {
        $themes = $this->getThemes($filters);

        return [
            'summary' => [
                'total_themes' => count($themes),
                'active_themes' => count(array_filter($themes, fn ($t) => $t['status'] === 'active')),
                'published_themes' => count(array_filter($themes, fn ($t) => $t['status'] === 'published')),
                'draft_themes' => count(array_filter($themes, fn ($t) => $t['status'] === 'draft')),
                'custom_themes' => count(array_filter($themes, fn ($t) => $t['type'] === 'custom')),
            ],
            'usage_by_tenant' => $this->getThemeUsageByTenant($themes),
            'popular_themes' => $this->getPopularThemes($themes),
            'theme_performance' => $this->getThemePerformanceMetrics($themes),
            'customization_trends' => $this->getCustomizationTrends($themes),
            'recommendations' => $this->getThemeRecommendations($themes),
        ];
    }

    /**
     * Export theme configuration.
     */
    public function exportTheme(string $themeId, string $format = 'json'): array
    {
        try {
            $theme = $this->getThemeById($themeId);

            if (! $theme) {
                throw new \InvalidArgumentException('Theme not found');
            }

            $exportId = uniqid('export_');
            $filename = "theme_{$theme['name']}_{$exportId}.".strtolower($format);

            $exportData = [
                'theme' => $theme,
                'exported_at' => now()->toISOString(),
                'exported_by' => auth()->id(),
                'format_version' => '1.0',
            ];

            // Convert to requested format
            $content = match (strtolower($format)) {
                'json' => json_encode($exportData, JSON_PRETTY_PRINT),
                'css' => $this->generateCSSFromTheme($theme),
                'scss' => $this->generateSCSSFromTheme($theme),
                default => json_encode($exportData, JSON_PRETTY_PRINT),
            };

            $export = [
                'export_id' => $exportId,
                'theme_id' => $themeId,
                'filename' => $filename,
                'format' => $format,
                'size' => strlen($content),
                'download_url' => "/admin/themes/download/{$exportId}",
                'expires_at' => now()->addHours(24)->toISOString(),
                'created_at' => now()->toISOString(),
            ];

            $this->loggingService->logInfo('Theme exported', [
                'theme_id' => $themeId,
                'export_id' => $exportId,
                'format' => $format,
                'exported_by' => auth()->id(),
            ]);

            return $export;

        } catch (\Exception $e) {
            $this->loggingService->logError('Theme export failed', $e, [
                'theme_id' => $themeId,
                'format' => $format,
            ]);

            throw $e;
        }
    }

    /**
     * Import theme from configuration.
     */
    public function importTheme(array $themeData, array $options = []): array
    {
        try {
            // Validate theme data structure
            $this->validateThemeData($themeData);

            // Create new theme from imported data
            $importedTheme = $this->createTheme([
                'name' => $themeData['name'].' (Imported)',
                'description' => $themeData['description'] ?? 'Imported theme',
                'type' => 'custom',
                'colors' => $themeData['colors'] ?? $this->getDefaultColors(),
                'fonts' => $themeData['fonts'] ?? $this->getDefaultFonts(),
                'layout' => $themeData['layout'] ?? $this->getDefaultLayout(),
                'components' => $themeData['components'] ?? $this->getDefaultComponents(),
                'custom_css' => $themeData['custom_css'] ?? '',
                'assets' => $themeData['assets'] ?? [],
                'metadata' => array_merge($themeData['metadata'] ?? [], [
                    'imported_at' => now()->toISOString(),
                    'imported_by' => auth()->id(),
                    'original_theme_id' => $themeData['id'] ?? null,
                ]),
            ]);

            $this->loggingService->logInfo('Theme imported', [
                'new_theme_id' => $importedTheme['id'],
                'original_theme_name' => $themeData['name'] ?? 'Unknown',
                'imported_by' => auth()->id(),
            ]);

            return $importedTheme;

        } catch (\Exception $e) {
            $this->loggingService->logError('Theme import failed', $e, [
                'theme_data' => $themeData,
                'options' => $options,
            ]);

            throw $e;
        }
    }

    /**
     * Delete theme.
     */
    public function deleteTheme(string $themeId, ?string $reason = null): bool
    {
        try {
            $theme = $this->getThemeById($themeId);

            if (! $theme) {
                throw new \InvalidArgumentException('Theme not found');
            }

            if ($theme['status'] === 'active') {
                throw new \InvalidArgumentException('Cannot delete active theme');
            }

            // Clean up theme assets and cache
            $this->cleanupThemeAssets($theme);

            $this->loggingService->logInfo('Theme deleted', [
                'theme_id' => $themeId,
                'theme_name' => $theme['name'],
                'reason' => $reason,
                'deleted_by' => auth()->id(),
            ]);

            return true;

        } catch (\Exception $e) {
            $this->loggingService->logError('Theme deletion failed', $e, [
                'theme_id' => $themeId,
            ]);

            throw $e;
        }
    }

    /**
     * Helper methods for theme operations.
     */
    private function getThemeList(): array
    {
        // In a real implementation, this would query the database
        return [
            [
                'id' => 'theme_001',
                'name' => 'DeliveryNexus Default',
                'description' => 'Default platform theme with modern design',
                'type' => 'default',
                'category' => 'general',
                'tenant_id' => null,
                'status' => 'active',
                'is_default' => true,
                'colors' => $this->getDefaultColors(),
                'fonts' => $this->getDefaultFonts(),
                'layout' => $this->getDefaultLayout(),
                'components' => $this->getDefaultComponents(),
                'custom_css' => '',
                'assets' => ['logo.png', 'favicon.ico'],
                'preview_url' => '/themes/preview/theme_001',
                'created_at' => now()->subMonths(6)->toISOString(),
                'created_by' => 'system',
                'updated_at' => now()->subDays(30)->toISOString(),
                'version' => '2.1.0',
                'metadata' => ['usage_count' => 150],
            ],
            [
                'id' => 'theme_002',
                'name' => 'Dark Mode Professional',
                'description' => 'Professional dark theme for business users',
                'type' => 'custom',
                'category' => 'professional',
                'tenant_id' => null,
                'status' => 'published',
                'is_default' => false,
                'colors' => [
                    'primary' => '#1a1a1a',
                    'secondary' => '#333333',
                    'accent' => '#007bff',
                    'background' => '#121212',
                    'text' => '#ffffff',
                ],
                'fonts' => $this->getDefaultFonts(),
                'layout' => $this->getDefaultLayout(),
                'components' => $this->getDefaultComponents(),
                'custom_css' => '.dark-mode { background: #121212; color: #fff; }',
                'assets' => ['dark-logo.png'],
                'preview_url' => '/themes/preview/theme_002',
                'created_at' => now()->subDays(15)->toISOString(),
                'created_by' => 'admin_001',
                'updated_at' => now()->subDays(5)->toISOString(),
                'version' => '1.2.0',
                'metadata' => ['usage_count' => 45],
            ],
        ];
    }

    private function getThemeById(string $themeId): ?array
    {
        $themes = $this->getThemeList();

        return collect($themes)->firstWhere('id', $themeId);
    }

    private function getDefaultColors(): array
    {
        return [
            'primary' => '#007bff',
            'secondary' => '#6c757d',
            'success' => '#28a745',
            'danger' => '#dc3545',
            'warning' => '#ffc107',
            'info' => '#17a2b8',
            'light' => '#f8f9fa',
            'dark' => '#343a40',
            'background' => '#ffffff',
            'text' => '#212529',
        ];
    }

    private function getDefaultFonts(): array
    {
        return [
            'primary' => 'Inter, sans-serif',
            'secondary' => 'Roboto, sans-serif',
            'heading' => 'Poppins, sans-serif',
            'monospace' => 'Monaco, monospace',
        ];
    }

    private function getDefaultLayout(): array
    {
        return [
            'container_width' => '1200px',
            'sidebar_width' => '250px',
            'header_height' => '60px',
            'footer_height' => '80px',
            'border_radius' => '8px',
            'spacing_unit' => '16px',
        ];
    }

    private function getDefaultComponents(): array
    {
        return [
            'buttons' => [
                'border_radius' => '6px',
                'padding' => '12px 24px',
                'font_weight' => '500',
            ],
            'cards' => [
                'border_radius' => '12px',
                'shadow' => '0 2px 8px rgba(0,0,0,0.1)',
                'padding' => '24px',
            ],
            'forms' => [
                'input_height' => '44px',
                'input_border_radius' => '6px',
                'label_font_weight' => '500',
            ],
        ];
    }

    private function generateThemePreview(array &$theme): void
    {
        // Generate preview URL
        $theme['preview_url'] = "/themes/preview/{$theme['id']}";

        $this->loggingService->logInfo('Theme preview generated', [
            'theme_id' => $theme['id'],
            'preview_url' => $theme['preview_url'],
        ]);
    }

    private function incrementVersion(string $version): string
    {
        $parts = explode('.', $version);
        $parts[2] = (int) $parts[2] + 1;

        return implode('.', $parts);
    }

    private function deactivateCurrentTheme(?string $tenantId): void
    {
        // In real implementation, would deactivate current active theme
        $cacheKey = $tenantId ? "theme_tenant_{$tenantId}" : 'theme_platform';
        Cache::forget($cacheKey);
    }

    private function generatePreviewUrl(array $theme, string $previewId): string
    {
        return "/themes/preview/{$theme['id']}?preview_id={$previewId}";
    }

    private function getThemeUsageByTenant(array $themes): array
    {
        $usage = [];
        foreach ($themes as $theme) {
            $tenantId = $theme['tenant_id'] ?? 'platform';
            if (! isset($usage[$tenantId])) {
                $usage[$tenantId] = 0;
            }
            if ($theme['status'] === 'active') {
                $usage[$tenantId]++;
            }
        }

        return $usage;
    }

    private function getPopularThemes(array $themes): array
    {
        // Sort by usage count from metadata
        usort($themes, function ($a, $b) {
            $usageA = $a['metadata']['usage_count'] ?? 0;
            $usageB = $b['metadata']['usage_count'] ?? 0;

            return $usageB - $usageA;
        });

        return array_slice($themes, 0, 5);
    }

    private function getThemePerformanceMetrics(array $themes): array
    {
        return [
            'average_load_time' => 1.2, // seconds
            'cache_hit_rate' => 95.5, // percentage
            'asset_optimization' => 88.0, // percentage
        ];
    }

    private function getCustomizationTrends(array $themes): array
    {
        $customThemes = array_filter($themes, fn ($t) => $t['type'] === 'custom');

        return [
            'custom_themes_percentage' => count($themes) > 0 ? round((count($customThemes) / count($themes)) * 100, 2) : 0,
            'most_customized_elements' => ['colors', 'fonts', 'layout'],
            'trending_colors' => ['#007bff', '#28a745', '#1a1a1a'],
        ];
    }

    private function getThemeRecommendations(array $themes): array
    {
        $recommendations = [];

        $draftThemes = array_filter($themes, fn ($t) => $t['status'] === 'draft');
        if (count($draftThemes) > 5) {
            $recommendations[] = 'Consider publishing or cleaning up '.count($draftThemes).' draft themes';
        }

        $customThemes = array_filter($themes, fn ($t) => $t['type'] === 'custom');
        if (count($customThemes) === 0) {
            $recommendations[] = 'Create custom themes to better match your brand identity';
        }

        return $recommendations;
    }

    private function generateCSSFromTheme(array $theme): string
    {
        $css = "/* Theme: {$theme['name']} */\n";
        $css .= ":root {\n";

        foreach ($theme['colors'] as $name => $value) {
            $css .= "  --color-{$name}: {$value};\n";
        }

        $css .= "}\n\n";
        $css .= $theme['custom_css'];

        return $css;
    }

    private function generateSCSSFromTheme(array $theme): string
    {
        $scss = "// Theme: {$theme['name']}\n";

        foreach ($theme['colors'] as $name => $value) {
            $scss .= "\${$name}: {$value};\n";
        }

        $scss .= "\n".$theme['custom_css'];

        return $scss;
    }

    private function validateThemeData(array $themeData): void
    {
        $required = ['name', 'colors'];
        foreach ($required as $field) {
            if (! isset($themeData[$field])) {
                throw new \InvalidArgumentException("Missing required field: {$field}");
            }
        }
    }

    private function cleanupThemeAssets(array $theme): void
    {
        // Clean up theme assets and cache
        $cacheKeys = [
            "theme_{$theme['id']}",
            "theme_preview_{$theme['id']}",
        ];

        foreach ($cacheKeys as $key) {
            Cache::forget($key);
        }
    }
}
