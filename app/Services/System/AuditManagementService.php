<?php

declare(strict_types=1);

namespace App\Services\System;

use App\Models\System\AuditLog;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class AuditManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get audit logs with detailed information.
     */
    public function getAuditLogsWithDetails(array $filters = []): array
    {
        $query = AuditLog::with(['user:id,first_name,last_name,email']);

        // Apply filters
        if (isset($filters['user_id'])) {
            $query->byUser($filters['user_id']);
        }

        if (isset($filters['action_type'])) {
            $query->byActionType($filters['action_type']);
        }

        if (isset($filters['auditable_type'])) {
            $query->byAuditableType($filters['auditable_type']);
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $logs = $query->latest()->paginate(50);

        return [
            'logs' => $logs,
            'statistics' => $this->getAuditStatistics($filters),
            'summary' => $this->getAuditSummary($filters),
        ];
    }

    /**
     * Create comprehensive audit log entry.
     */
    public function createAuditLog(
        string $actionType,
        ?Model $auditable = null,
        ?array $oldValues = null,
        ?array $newValues = null,
        ?array $metadata = null,
        ?string $userId = null
    ): AuditLog {
        return AuditLog::create([
            'user_id' => $userId ?? auth()->id(),
            'action_type' => $actionType,
            'auditable_id' => $auditable?->getKey(),
            'auditable_type' => $auditable?->getMorphClass(),
            'old_values' => $this->sanitizeValues($oldValues),
            'new_values' => $this->sanitizeValues($newValues),
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
        ]);
    }

    /**
     * Get audit statistics for a given period.
     */
    public function getAuditStatistics(array $filters = []): array
    {
        $query = AuditLog::query();

        // Apply date filters
        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        return [
            'total_events' => $query->count(),
            'by_action_type' => $query->selectRaw('action_type, COUNT(*) as count')
                ->groupBy('action_type')
                ->orderByDesc('count')
                ->pluck('count', 'action_type'),
            'by_user' => $query->with('user:id,first_name,last_name')
                ->selectRaw('user_id, COUNT(*) as count')
                ->whereNotNull('user_id')
                ->groupBy('user_id')
                ->orderByDesc('count')
                ->limit(10)
                ->get()
                ->map(function ($item) {
                    return [
                        'user' => $item->user,
                        'count' => $item->count,
                    ];
                }),
            'by_model_type' => $query->selectRaw('auditable_type, COUNT(*) as count')
                ->whereNotNull('auditable_type')
                ->groupBy('auditable_type')
                ->orderByDesc('count')
                ->pluck('count', 'auditable_type'),
            'recent_activity' => $query->with('user:id,first_name,last_name')
                ->latest()
                ->limit(10)
                ->get(),
        ];
    }

    /**
     * Get audit summary for dashboard.
     */
    public function getAuditSummary(array $filters = []): array
    {
        $today = now()->startOfDay();
        $yesterday = now()->subDay()->startOfDay();
        $thisWeek = now()->startOfWeek();
        $thisMonth = now()->startOfMonth();

        return [
            'today' => [
                'total' => AuditLog::where('created_at', '>=', $today)->count(),
                'users' => AuditLog::where('created_at', '>=', $today)
                    ->distinct('user_id')
                    ->whereNotNull('user_id')
                    ->count(),
                'actions' => AuditLog::where('created_at', '>=', $today)
                    ->distinct('action_type')
                    ->count(),
            ],
            'yesterday' => [
                'total' => AuditLog::whereBetween('created_at', [$yesterday, $today])->count(),
            ],
            'this_week' => [
                'total' => AuditLog::where('created_at', '>=', $thisWeek)->count(),
                'daily_breakdown' => $this->getDailyBreakdown($thisWeek, now()),
            ],
            'this_month' => [
                'total' => AuditLog::where('created_at', '>=', $thisMonth)->count(),
                'weekly_breakdown' => $this->getWeeklyBreakdown($thisMonth, now()),
            ],
        ];
    }

    /**
     * Get security-related audit events.
     */
    public function getSecurityEvents(int $hours = 24): Collection
    {
        $securityActions = [
            'user.login',
            'user.logout',
            'user.login_failed',
            'user.password_changed',
            'user.role_assigned',
            'user.role_removed',
            'user.suspended',
            'user.activated',
            'admin.permission_changed',
            'admin.role_created',
            'admin.role_deleted',
        ];

        return AuditLog::with(['user:id,first_name,last_name,email'])
            ->whereIn('action_type', $securityActions)
            ->where('created_at', '>=', now()->subHours($hours))
            ->latest()
            ->get();
    }

    /**
     * Get suspicious activity patterns.
     */
    public function getSuspiciousActivity(): array
    {
        $suspiciousPatterns = [];

        // Multiple failed login attempts
        $failedLogins = AuditLog::where('action_type', 'user.login_failed')
            ->where('created_at', '>=', now()->subHours(1))
            ->selectRaw('ip_address, COUNT(*) as attempts')
            ->groupBy('ip_address')
            ->having('attempts', '>', 5)
            ->get();

        if ($failedLogins->isNotEmpty()) {
            $suspiciousPatterns['failed_logins'] = $failedLogins;
        }

        // Unusual activity hours
        $unusualHours = AuditLog::whereRaw('EXTRACT(HOUR FROM created_at) NOT BETWEEN 6 AND 22')
            ->where('created_at', '>=', now()->subDays(7))
            ->with('user:id,first_name,last_name')
            ->selectRaw('user_id, COUNT(*) as count')
            ->whereNotNull('user_id')
            ->groupBy('user_id')
            ->having('count', '>', 10)
            ->get();

        if ($unusualHours->isNotEmpty()) {
            $suspiciousPatterns['unusual_hours'] = $unusualHours;
        }

        // Rapid successive actions
        $rapidActions = AuditLog::where('created_at', '>=', now()->subMinutes(5))
            ->selectRaw('user_id, COUNT(*) as count')
            ->whereNotNull('user_id')
            ->groupBy('user_id')
            ->having('count', '>', 50)
            ->with('user:id,first_name,last_name')
            ->get();

        if ($rapidActions->isNotEmpty()) {
            $suspiciousPatterns['rapid_actions'] = $rapidActions;
        }

        return $suspiciousPatterns;
    }

    /**
     * Export audit logs for compliance.
     */
    public function exportAuditLogs(array $filters = [], string $format = 'csv'): string
    {
        $query = AuditLog::with(['user:id,first_name,last_name,email']);

        // Apply filters
        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        if (isset($filters['user_id'])) {
            $query->byUser($filters['user_id']);
        }

        $logs = $query->orderBy('created_at')->get();

        // Generate filename
        $filename = 'audit_logs_'.now()->format('Y_m_d_H_i_s').'.'.$format;
        $filepath = storage_path('app/exports/'.$filename);

        // Ensure directory exists
        if (! file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        if ($format === 'csv') {
            $this->exportToCsv($logs, $filepath);
        } elseif ($format === 'json') {
            $this->exportToJson($logs, $filepath);
        }

        // Log the export
        $this->createAuditLog(
            'audit.exported',
            null,
            null,
            ['format' => $format, 'count' => $logs->count()],
            ['filename' => $filename]
        );

        return $filename;
    }

    /**
     * Get compliance report.
     */
    public function getComplianceReport(\DateTime $from, \DateTime $to): array
    {
        $logs = AuditLog::with(['user:id,first_name,last_name,email'])
            ->whereBetween('created_at', [$from, $to])
            ->get();

        return [
            'period' => [
                'from' => $from->format('Y-m-d'),
                'to' => $to->format('Y-m-d'),
            ],
            'summary' => [
                'total_events' => $logs->count(),
                'unique_users' => $logs->pluck('user_id')->unique()->count(),
                'data_modifications' => $logs->whereIn('action_type', [
                    'created', 'updated', 'deleted',
                ])->count(),
                'security_events' => $this->getSecurityEvents(
                    $from->diffInHours($to)
                )->count(),
            ],
            'user_activity' => $logs->groupBy('user_id')
                ->map(function ($userLogs) {
                    $user = $userLogs->first()->user;

                    return [
                        'user' => $user ? $user->name : 'Unknown',
                        'total_actions' => $userLogs->count(),
                        'action_types' => $userLogs->pluck('action_type')->unique()->values(),
                        'first_activity' => $userLogs->min('created_at'),
                        'last_activity' => $userLogs->max('created_at'),
                    ];
                })
                ->values(),
            'data_integrity' => [
                'models_affected' => $logs->whereNotNull('auditable_type')
                    ->pluck('auditable_type')
                    ->unique()
                    ->count(),
                'records_modified' => $logs->whereNotNull('auditable_id')
                    ->pluck('auditable_id')
                    ->unique()
                    ->count(),
            ],
        ];
    }

    /**
     * Sanitize values to remove sensitive information.
     */
    private function sanitizeValues(?array $values): ?array
    {
        if (! $values) {
            return null;
        }

        $sensitiveFields = [
            'password',
            'password_confirmation',
            'token',
            'api_key',
            'secret',
            'private_key',
            'credit_card',
            'ssn',
            'social_security',
        ];

        foreach ($sensitiveFields as $field) {
            if (isset($values[$field])) {
                $values[$field] = '[REDACTED]';
            }
        }

        return $values;
    }

    /**
     * Get daily breakdown for a period.
     */
    private function getDailyBreakdown(\DateTime $from, \DateTime $to): array
    {
        return AuditLog::selectRaw('DATE(created_at) as date, COUNT(*) as count')
            ->whereBetween('created_at', [$from, $to])
            ->groupBy('date')
            ->orderBy('date')
            ->pluck('count', 'date')
            ->toArray();
    }

    /**
     * Get weekly breakdown for a period.
     */
    private function getWeeklyBreakdown(\DateTime $from, \DateTime $to): array
    {
        return AuditLog::selectRaw('YEARWEEK(created_at) as week, COUNT(*) as count')
            ->whereBetween('created_at', [$from, $to])
            ->groupBy('week')
            ->orderBy('week')
            ->pluck('count', 'week')
            ->toArray();
    }

    /**
     * Export logs to CSV.
     */
    private function exportToCsv(Collection $logs, string $filepath): void
    {
        $file = fopen($filepath, 'w');

        // Write header
        fputcsv($file, [
            'ID', 'User', 'Action Type', 'Auditable Type', 'Auditable ID',
            'IP Address', 'User Agent', 'Created At',
        ]);

        // Write data
        foreach ($logs as $log) {
            fputcsv($file, [
                $log->id,
                $log->user?->name ?? 'Unknown',
                $log->action_type,
                $log->auditable_type,
                $log->auditable_id,
                $log->ip_address,
                $log->user_agent,
                $log->created_at->toISOString(),
            ]);
        }

        fclose($file);
    }

    /**
     * Export logs to JSON.
     */
    private function exportToJson(Collection $logs, string $filepath): void
    {
        $data = $logs->map(function ($log) {
            return [
                'id' => $log->id,
                'user' => $log->user?->name ?? 'Unknown',
                'action_type' => $log->action_type,
                'auditable_type' => $log->auditable_type,
                'auditable_id' => $log->auditable_id,
                'old_values' => $log->old_values,
                'new_values' => $log->new_values,
                'ip_address' => $log->ip_address,
                'user_agent' => $log->user_agent,
                'created_at' => $log->created_at->toISOString(),
            ];
        });

        file_put_contents($filepath, json_encode($data, JSON_PRETTY_PRINT));
    }
}
