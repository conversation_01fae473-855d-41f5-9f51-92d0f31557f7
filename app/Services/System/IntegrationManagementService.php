<?php

declare(strict_types=1);

namespace App\Services\System;

use App\Enums\System\IntegrationStatus;
use App\Enums\System\IntegrationType;
use App\Models\System\Integration;
use App\Models\System\IntegrationLog;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class IntegrationManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get integration with detailed information.
     */
    public function getIntegrationWithDetails(string $integrationId): array
    {
        $integration = Integration::with([
            'createdBy:id,first_name,last_name',
            'updatedBy:id,first_name,last_name',
            'logs' => function ($query) {
                $query->latest()->limit(50);
            },
        ])->findOrFail($integrationId);

        return [
            'integration' => $integration,
            'health_status' => $integration->getHealthStatus(),
            'recent_logs' => $integration->logs,
            'statistics' => $this->getSingleIntegrationStatistics($integration),
        ];
    }

    /**
     * Create new integration.
     */
    public function createIntegration(array $data): Integration
    {
        return DB::transaction(function () use ($data) {
            // Generate slug if not provided
            if (empty($data['slug'])) {
                $data['slug'] = $this->generateUniqueSlug($data['name']);
            }

            $integration = Integration::create([
                'name' => $data['name'],
                'slug' => $data['slug'],
                'type' => IntegrationType::from($data['type']),
                'status' => IntegrationStatus::from($data['status'] ?? IntegrationStatus::DRAFT->value),
                'description' => $data['description'] ?? null,
                'provider' => $data['provider'] ?? null,
                'version' => $data['version'] ?? null,
                'configuration' => $data['configuration'] ?? [],
                'credentials' => $data['credentials'] ?? [],
                'settings' => $data['settings'] ?? [],
                'endpoints' => $data['endpoints'] ?? [],
                'is_active' => $data['is_active'] ?? false,
                'is_sandbox' => $data['is_sandbox'] ?? true,
                'created_by' => auth()->id(),
                'updated_by' => auth()->id(),
            ]);

            $this->loggingService->logInfo('Integration created', [
                'integration_id' => $integration->id,
                'name' => $integration->name,
                'type' => $integration->type->value,
                'admin_id' => auth()->id(),
            ]);

            return $integration;
        });
    }

    /**
     * Update integration.
     */
    public function updateIntegration(string $integrationId, array $data): Integration
    {
        return DB::transaction(function () use ($integrationId, $data) {
            $integration = Integration::findOrFail($integrationId);

            // Update slug if name changed
            if (isset($data['name']) && $data['name'] !== $integration->name) {
                $data['slug'] = $this->generateUniqueSlug($data['name'], $integration->id);
            }

            $integration->update(array_filter([
                'name' => $data['name'] ?? $integration->name,
                'slug' => $data['slug'] ?? $integration->slug,
                'type' => isset($data['type']) ? IntegrationType::from($data['type']) : $integration->type,
                'status' => isset($data['status']) ? IntegrationStatus::from($data['status']) : $integration->status,
                'description' => $data['description'] ?? $integration->description,
                'provider' => $data['provider'] ?? $integration->provider,
                'version' => $data['version'] ?? $integration->version,
                'configuration' => $data['configuration'] ?? $integration->configuration,
                'credentials' => $data['credentials'] ?? $integration->credentials,
                'settings' => $data['settings'] ?? $integration->settings,
                'endpoints' => $data['endpoints'] ?? $integration->endpoints,
                'is_active' => $data['is_active'] ?? $integration->is_active,
                'is_sandbox' => $data['is_sandbox'] ?? $integration->is_sandbox,
                'updated_by' => auth()->id(),
            ]));

            return $integration->fresh();
        });
    }

    /**
     * Test integration connection.
     */
    public function testIntegration(string $integrationId): array
    {
        $integration = Integration::findOrFail($integrationId);

        $startTime = microtime(true);

        try {
            // Update status to testing
            $integration->updateStatus(IntegrationStatus::TESTING);

            // Perform actual test based on integration type
            $testResult = $this->performIntegrationTest($integration);

            $duration = (microtime(true) - $startTime) * 1000;

            // Log the test
            IntegrationLog::create([
                'integration_id' => $integration->id,
                'method' => 'TEST',
                'endpoint' => 'connection_test',
                'request_data' => ['test' => true],
                'response_data' => $testResult,
                'response_code' => $testResult['success'] ? 200 : 500,
                'status' => $testResult['success'] ? 'success' : 'error',
                'duration_ms' => $duration,
                'error_message' => $testResult['error'] ?? null,
                'user_id' => auth()->id(),
            ]);

            // Update integration status based on test result
            if ($testResult['success']) {
                $integration->updateStatus(IntegrationStatus::CONNECTED);
            } else {
                $integration->updateStatus(IntegrationStatus::ERROR, $testResult['error']);
            }

            return $testResult;

        } catch (\Exception $e) {
            $duration = (microtime(true) - $startTime) * 1000;

            // Log the error
            IntegrationLog::create([
                'integration_id' => $integration->id,
                'method' => 'TEST',
                'endpoint' => 'connection_test',
                'request_data' => ['test' => true],
                'response_data' => null,
                'response_code' => 500,
                'status' => 'error',
                'duration_ms' => $duration,
                'error_message' => $e->getMessage(),
                'user_id' => auth()->id(),
            ]);

            $integration->updateStatus(IntegrationStatus::ERROR, $e->getMessage());

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'duration_ms' => $duration,
            ];
        }
    }

    /**
     * Delete integration.
     */
    public function deleteIntegration(string $integrationId): bool
    {
        return DB::transaction(function () use ($integrationId) {
            $integration = Integration::findOrFail($integrationId);

            $this->loggingService->logInfo('Integration deleted', [
                'integration_id' => $integration->id,
                'name' => $integration->name,
                'admin_id' => auth()->id(),
            ]);

            return $integration->delete();
        });
    }

    /**
     * Get integrations by type.
     */
    public function getIntegrationsByType(IntegrationType $type): Collection
    {
        return Integration::ofType($type)
            ->with(['createdBy:id,first_name,last_name'])
            ->orderBy('name')
            ->get();
    }

    /**
     * Get integration statistics.
     */
    public function getIntegrationStatistics(): array
    {
        return [
            'total_integrations' => Integration::count(),
            'active_integrations' => Integration::active()->count(),
            'connected_integrations' => Integration::withStatus(IntegrationStatus::CONNECTED)->count(),
            'error_integrations' => Integration::withStatus(IntegrationStatus::ERROR)->count(),
            'sandbox_integrations' => Integration::sandbox()->count(),
            'production_integrations' => Integration::production()->count(),
            'by_type' => Integration::selectRaw('type, COUNT(*) as count')
                ->groupBy('type')
                ->pluck('count', 'type'),
            'by_status' => Integration::selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status'),
            'recent_logs' => IntegrationLog::with(['integration:id,name'])
                ->latest()
                ->limit(10)
                ->get(),
        ];
    }

    /**
     * Get integration health overview.
     */
    public function getHealthOverview(): array
    {
        $integrations = Integration::active()->get();
        $healthData = [];

        foreach ($integrations as $integration) {
            $healthData[] = [
                'id' => $integration->id,
                'name' => $integration->name,
                'type' => $integration->type->value,
                'status' => $integration->status->value,
                'health' => $integration->getHealthStatus(),
            ];
        }

        return [
            'integrations' => $healthData,
            'summary' => [
                'total' => count($healthData),
                'healthy' => count(array_filter($healthData, fn ($i) => $i['health']['is_healthy'])),
                'unhealthy' => count(array_filter($healthData, fn ($i) => ! $i['health']['is_healthy'])),
            ],
        ];
    }

    /**
     * Bulk update integration statuses.
     */
    public function bulkUpdateStatus(array $integrationIds, IntegrationStatus $status): array
    {
        return DB::transaction(function () use ($integrationIds, $status) {
            $results = ['success' => 0, 'failed' => 0, 'errors' => []];

            foreach ($integrationIds as $id) {
                try {
                    $integration = Integration::findOrFail($id);
                    $integration->updateStatus($status);
                    $results['success']++;
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = [
                        'id' => $id,
                        'error' => $e->getMessage(),
                    ];
                }
            }

            return $results;
        });
    }

    /**
     * Generate unique slug for integration.
     */
    private function generateUniqueSlug(string $name, ?string $excludeId = null): string
    {
        $baseSlug = Str::slug($name);
        $slug = $baseSlug;
        $counter = 1;

        while ($this->slugExists($slug, $excludeId)) {
            $slug = $baseSlug.'-'.$counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Check if slug exists.
     */
    private function slugExists(string $slug, ?string $excludeId = null): bool
    {
        $query = Integration::where('slug', $slug);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Perform integration test based on type.
     */
    private function performIntegrationTest(Integration $integration): array
    {
        // This would be implemented based on integration type
        // For now, return a mock successful test
        return [
            'success' => true,
            'message' => 'Integration test completed successfully',
            'data' => [
                'type' => $integration->type->value,
                'provider' => $integration->provider,
                'timestamp' => now()->toISOString(),
            ],
        ];
    }

    /**
     * Get integration statistics for a specific integration.
     */
    private function getSingleIntegrationStatistics(Integration $integration): array
    {
        $logs = $integration->logs()->recent(24)->get();

        return [
            'total_requests_24h' => $logs->count(),
            'successful_requests_24h' => $logs->where('status', 'success')->count(),
            'failed_requests_24h' => $logs->where('status', 'error')->count(),
            'average_response_time' => $logs->avg('duration_ms'),
            'last_request' => $logs->first()?->created_at,
            'uptime_percentage' => $logs->count() > 0 ?
                ($logs->where('status', 'success')->count() / $logs->count()) * 100 : 0,
        ];
    }
}
