<?php

declare(strict_types=1);

namespace App\Services\System;

/**
 * Feedback Management Service
 *
 * Handles comprehensive user feedback and suggestion management including:
 * - Feedback collection and categorization
 * - Feature request tracking and prioritization
 * - User satisfaction surveys
 * - Feedback analytics and insights
 * - Feedback response management
 */
class FeedbackManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all feedback with filtering.
     */
    public function getFeedback(array $filters = []): array
    {
        $feedback = $this->getFeedbackList();

        // Apply filters
        if (isset($filters['type'])) {
            $feedback = array_filter($feedback, fn ($item) => $item['type'] === $filters['type']);
        }

        if (isset($filters['status'])) {
            $feedback = array_filter($feedback, fn ($item) => $item['status'] === $filters['status']);
        }

        if (isset($filters['category'])) {
            $feedback = array_filter($feedback, fn ($item) => $item['category'] === $filters['category']);
        }

        if (isset($filters['priority'])) {
            $feedback = array_filter($feedback, fn ($item) => $item['priority'] === $filters['priority']);
        }

        if (isset($filters['user_id'])) {
            $feedback = array_filter($feedback, fn ($item) => $item['user_id'] === $filters['user_id']);
        }

        if (isset($filters['date_from'])) {
            $feedback = array_filter($feedback, fn ($item) => $item['created_at'] >= $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $feedback = array_filter($feedback, fn ($item) => $item['created_at'] <= $filters['date_to']);
        }

        return array_values($feedback);
    }

    /**
     * Create new feedback entry.
     */
    public function createFeedback(array $data): array
    {
        $feedbackId = uniqid('feedback_');

        try {
            $feedback = [
                'id' => $feedbackId,
                'type' => $data['type'], // bug_report, feature_request, general_feedback, complaint
                'category' => $data['category'], // ui_ux, performance, functionality, etc.
                'title' => $data['title'],
                'description' => $data['description'],
                'priority' => $data['priority'] ?? 'medium',
                'status' => 'open',
                'user_id' => $data['user_id'] ?? auth()->id(),
                'user_email' => $data['user_email'] ?? auth()->user()?->email,
                'user_name' => $data['user_name'] ?? auth()->user()?->name,
                'tenant_id' => $data['tenant_id'] ?? null,
                'attachments' => $data['attachments'] ?? [],
                'metadata' => $data['metadata'] ?? [],
                'votes' => 0,
                'created_at' => now()->toISOString(),
                'updated_at' => now()->toISOString(),
                'assigned_to' => null,
                'resolution' => null,
                'resolved_at' => null,
                'tags' => $data['tags'] ?? [],
            ];

            // Auto-categorize and prioritize based on content
            $this->autoCategorizeFeedback($feedback);

            $this->loggingService->logInfo('Feedback created', [
                'feedback_id' => $feedbackId,
                'type' => $data['type'],
                'category' => $feedback['category'],
                'user_id' => $feedback['user_id'],
            ]);

            return $feedback;

        } catch (\Exception $e) {
            $this->loggingService->logError('Feedback creation failed', $e, [
                'feedback_id' => $feedbackId,
                'data' => $data,
            ]);

            throw $e;
        }
    }

    /**
     * Update feedback status and details.
     */
    public function updateFeedback(string $feedbackId, array $updates): array
    {
        try {
            $feedback = $this->getFeedbackById($feedbackId);

            if (! $feedback) {
                throw new \InvalidArgumentException('Feedback not found');
            }

            // Update allowed fields
            foreach ($updates as $field => $value) {
                if (in_array($field, ['status', 'priority', 'category', 'assigned_to', 'resolution', 'tags'])) {
                    $feedback[$field] = $value;
                }
            }

            $feedback['updated_at'] = now()->toISOString();

            // Set resolved timestamp if status changed to resolved
            if (isset($updates['status']) && $updates['status'] === 'resolved' && ! $feedback['resolved_at']) {
                $feedback['resolved_at'] = now()->toISOString();
            }

            $this->loggingService->logInfo('Feedback updated', [
                'feedback_id' => $feedbackId,
                'updates' => $updates,
                'updated_by' => auth()->id(),
            ]);

            return $feedback;

        } catch (\Exception $e) {
            $this->loggingService->logError('Feedback update failed', $e, [
                'feedback_id' => $feedbackId,
                'updates' => $updates,
            ]);

            throw $e;
        }
    }

    /**
     * Add response to feedback.
     */
    public function addFeedbackResponse(string $feedbackId, array $responseData): array
    {
        try {
            $feedback = $this->getFeedbackById($feedbackId);

            if (! $feedback) {
                throw new \InvalidArgumentException('Feedback not found');
            }

            $responseId = uniqid('response_');

            $response = [
                'id' => $responseId,
                'feedback_id' => $feedbackId,
                'message' => $responseData['message'],
                'is_public' => $responseData['is_public'] ?? true,
                'author_id' => auth()->id(),
                'author_name' => auth()->user()?->name ?? 'Admin',
                'created_at' => now()->toISOString(),
                'attachments' => $responseData['attachments'] ?? [],
            ];

            // Update feedback status if specified
            if (isset($responseData['update_status'])) {
                $this->updateFeedback($feedbackId, ['status' => $responseData['update_status']]);
            }

            $this->loggingService->logInfo('Feedback response added', [
                'feedback_id' => $feedbackId,
                'response_id' => $responseId,
                'author_id' => auth()->id(),
            ]);

            return $response;

        } catch (\Exception $e) {
            $this->loggingService->logError('Feedback response failed', $e, [
                'feedback_id' => $feedbackId,
                'response_data' => $responseData,
            ]);

            throw $e;
        }
    }

    /**
     * Vote on feedback (upvote/downvote).
     */
    public function voteFeedback(string $feedbackId, string $voteType): array
    {
        try {
            $feedback = $this->getFeedbackById($feedbackId);

            if (! $feedback) {
                throw new \InvalidArgumentException('Feedback not found');
            }

            $userId = auth()->id();

            // In real implementation, would check if user already voted
            $voteValue = $voteType === 'upvote' ? 1 : -1;
            $feedback['votes'] += $voteValue;

            $this->loggingService->logInfo('Feedback voted', [
                'feedback_id' => $feedbackId,
                'vote_type' => $voteType,
                'user_id' => $userId,
                'new_vote_count' => $feedback['votes'],
            ]);

            return $feedback;

        } catch (\Exception $e) {
            $this->loggingService->logError('Feedback voting failed', $e, [
                'feedback_id' => $feedbackId,
                'vote_type' => $voteType,
            ]);

            throw $e;
        }
    }

    /**
     * Get feedback analytics and insights.
     */
    public function getFeedbackAnalytics(array $filters = []): array
    {
        $feedback = $this->getFeedback($filters);

        return [
            'summary' => [
                'total_feedback' => count($feedback),
                'open_feedback' => count(array_filter($feedback, fn ($f) => $f['status'] === 'open')),
                'in_progress_feedback' => count(array_filter($feedback, fn ($f) => $f['status'] === 'in_progress')),
                'resolved_feedback' => count(array_filter($feedback, fn ($f) => $f['status'] === 'resolved')),
                'closed_feedback' => count(array_filter($feedback, fn ($f) => $f['status'] === 'closed')),
                'average_resolution_time' => $this->calculateAverageResolutionTime($feedback),
            ],
            'feedback_by_type' => $this->getFeedbackByType($feedback),
            'feedback_by_category' => $this->getFeedbackByCategory($feedback),
            'feedback_by_priority' => $this->getFeedbackByPriority($feedback),
            'trending_feedback' => $this->getTrendingFeedback($feedback),
            'user_satisfaction' => $this->getUserSatisfactionMetrics($feedback),
            'response_metrics' => $this->getResponseMetrics($feedback),
            'recommendations' => $this->getFeedbackRecommendations($feedback),
        ];
    }

    /**
     * Create satisfaction survey.
     */
    public function createSatisfactionSurvey(array $config): array
    {
        $surveyId = uniqid('survey_');

        try {
            $survey = [
                'id' => $surveyId,
                'title' => $config['title'],
                'description' => $config['description'] ?? '',
                'questions' => $config['questions'],
                'target_audience' => $config['target_audience'] ?? 'all_users',
                'tenant_id' => $config['tenant_id'] ?? null,
                'is_active' => $config['is_active'] ?? true,
                'start_date' => $config['start_date'] ?? now()->toDateString(),
                'end_date' => $config['end_date'] ?? now()->addDays(30)->toDateString(),
                'created_at' => now()->toISOString(),
                'created_by' => auth()->id(),
                'responses_count' => 0,
                'average_rating' => 0,
            ];

            $this->loggingService->logInfo('Satisfaction survey created', [
                'survey_id' => $surveyId,
                'title' => $config['title'],
                'target_audience' => $config['target_audience'] ?? 'all_users',
                'created_by' => auth()->id(),
            ]);

            return $survey;

        } catch (\Exception $e) {
            $this->loggingService->logError('Survey creation failed', $e, [
                'survey_id' => $surveyId,
                'config' => $config,
            ]);

            throw $e;
        }
    }

    /**
     * Bulk feedback operations.
     */
    public function bulkFeedbackOperations(array $feedbackIds, string $action, array $options = []): array
    {
        $results = ['success' => [], 'failed' => []];

        foreach ($feedbackIds as $feedbackId) {
            try {
                switch ($action) {
                    case 'update_status':
                        $this->updateFeedback($feedbackId, ['status' => $options['status']]);
                        break;
                    case 'update_priority':
                        $this->updateFeedback($feedbackId, ['priority' => $options['priority']]);
                        break;
                    case 'assign':
                        $this->updateFeedback($feedbackId, ['assigned_to' => $options['assigned_to']]);
                        break;
                    case 'add_tags':
                        $feedback = $this->getFeedbackById($feedbackId);
                        $newTags = array_unique(array_merge($feedback['tags'], $options['tags']));
                        $this->updateFeedback($feedbackId, ['tags' => $newTags]);
                        break;
                    default:
                        throw new \InvalidArgumentException("Invalid action: {$action}");
                }

                $results['success'][] = $feedbackId;

            } catch (\Exception $e) {
                $results['failed'][] = [
                    'feedback_id' => $feedbackId,
                    'error' => $e->getMessage(),
                ];

                $this->loggingService->logError('Bulk feedback operation failed', $e, [
                    'feedback_id' => $feedbackId,
                    'action' => $action,
                ]);
            }
        }

        return $results;
    }

    /**
     * Helper methods for feedback operations.
     */
    private function getFeedbackList(): array
    {
        // In a real implementation, this would query the database
        return [
            [
                'id' => 'feedback_001',
                'type' => 'feature_request',
                'category' => 'ui_ux',
                'title' => 'Add dark mode theme',
                'description' => 'Please add a dark mode option for better user experience during night time usage.',
                'priority' => 'medium',
                'status' => 'open',
                'user_id' => 'user_123',
                'user_email' => '<EMAIL>',
                'user_name' => 'John Doe',
                'tenant_id' => null,
                'attachments' => [],
                'metadata' => ['browser' => 'Chrome', 'device' => 'Desktop'],
                'votes' => 15,
                'created_at' => now()->subDays(5)->toISOString(),
                'updated_at' => now()->subDays(3)->toISOString(),
                'assigned_to' => 'dev_team_ui',
                'resolution' => null,
                'resolved_at' => null,
                'tags' => ['ui', 'theme', 'accessibility'],
            ],
            [
                'id' => 'feedback_002',
                'type' => 'bug_report',
                'category' => 'performance',
                'title' => 'Slow loading on mobile devices',
                'description' => 'The app takes too long to load on mobile devices, especially on slower connections.',
                'priority' => 'high',
                'status' => 'in_progress',
                'user_id' => 'user_456',
                'user_email' => '<EMAIL>',
                'user_name' => 'Jane Smith',
                'tenant_id' => 'tenant_123',
                'attachments' => ['screenshot.png'],
                'metadata' => ['device' => 'iPhone 12', 'connection' => '3G'],
                'votes' => 8,
                'created_at' => now()->subDays(3)->toISOString(),
                'updated_at' => now()->subDays(1)->toISOString(),
                'assigned_to' => 'dev_team_mobile',
                'resolution' => null,
                'resolved_at' => null,
                'tags' => ['mobile', 'performance', 'loading'],
            ],
            [
                'id' => 'feedback_003',
                'type' => 'general_feedback',
                'category' => 'functionality',
                'title' => 'Great platform overall',
                'description' => 'Really enjoying the platform. The delivery tracking feature is excellent!',
                'priority' => 'low',
                'status' => 'resolved',
                'user_id' => 'user_789',
                'user_email' => '<EMAIL>',
                'user_name' => 'Mike Johnson',
                'tenant_id' => null,
                'attachments' => [],
                'metadata' => ['rating' => 5],
                'votes' => 3,
                'created_at' => now()->subDays(7)->toISOString(),
                'updated_at' => now()->subDays(6)->toISOString(),
                'assigned_to' => 'customer_success',
                'resolution' => 'Thank you for the positive feedback!',
                'resolved_at' => now()->subDays(6)->toISOString(),
                'tags' => ['positive', 'tracking'],
            ],
        ];
    }

    private function getFeedbackById(string $feedbackId): ?array
    {
        $feedback = $this->getFeedbackList();

        return collect($feedback)->firstWhere('id', $feedbackId);
    }

    private function autoCategorizeFeedback(array &$feedback): void
    {
        $title = strtolower($feedback['title']);
        $description = strtolower($feedback['description']);
        $content = $title.' '.$description;

        // Auto-categorize based on keywords
        if (str_contains($content, 'slow') || str_contains($content, 'performance') || str_contains($content, 'loading')) {
            $feedback['category'] = 'performance';
            $feedback['priority'] = 'high';
        } elseif (str_contains($content, 'ui') || str_contains($content, 'design') || str_contains($content, 'theme')) {
            $feedback['category'] = 'ui_ux';
        } elseif (str_contains($content, 'bug') || str_contains($content, 'error') || str_contains($content, 'broken')) {
            $feedback['type'] = 'bug_report';
            $feedback['priority'] = 'high';
        }

        // Auto-tag based on content
        $autoTags = [];
        if (str_contains($content, 'mobile')) {
            $autoTags[] = 'mobile';
        }
        if (str_contains($content, 'payment')) {
            $autoTags[] = 'payment';
        }
        if (str_contains($content, 'delivery')) {
            $autoTags[] = 'delivery';
        }
        if (str_contains($content, 'notification')) {
            $autoTags[] = 'notification';
        }

        $feedback['tags'] = array_unique(array_merge($feedback['tags'], $autoTags));
    }

    private function calculateAverageResolutionTime(array $feedback): float
    {
        $resolvedFeedback = array_filter($feedback, fn ($f) => $f['status'] === 'resolved' && $f['resolved_at']);

        if (empty($resolvedFeedback)) {
            return 0;
        }

        $totalTime = 0;
        foreach ($resolvedFeedback as $item) {
            $createdTime = strtotime($item['created_at']);
            $resolvedTime = strtotime($item['resolved_at']);
            $totalTime += ($resolvedTime - $createdTime);
        }

        return round($totalTime / count($resolvedFeedback) / 3600, 2); // Average hours
    }

    private function getFeedbackByType(array $feedback): array
    {
        $types = [];
        foreach ($feedback as $item) {
            $type = $item['type'];
            if (! isset($types[$type])) {
                $types[$type] = 0;
            }
            $types[$type]++;
        }

        return $types;
    }

    private function getFeedbackByCategory(array $feedback): array
    {
        $categories = [];
        foreach ($feedback as $item) {
            $category = $item['category'];
            if (! isset($categories[$category])) {
                $categories[$category] = 0;
            }
            $categories[$category]++;
        }

        return $categories;
    }

    private function getFeedbackByPriority(array $feedback): array
    {
        $priorities = ['low' => 0, 'medium' => 0, 'high' => 0, 'critical' => 0];
        foreach ($feedback as $item) {
            $priority = $item['priority'];
            if (isset($priorities[$priority])) {
                $priorities[$priority]++;
            }
        }

        return $priorities;
    }

    private function getTrendingFeedback(array $feedback): array
    {
        // Sort by votes descending
        usort($feedback, fn ($a, $b) => $b['votes'] - $a['votes']);

        return array_slice($feedback, 0, 5); // Top 5 trending
    }

    private function getUserSatisfactionMetrics(array $feedback): array
    {
        $positiveFeedback = array_filter($feedback, function ($item) {
            return $item['type'] === 'general_feedback' &&
                   (str_contains(strtolower($item['description']), 'great') ||
                    str_contains(strtolower($item['description']), 'excellent') ||
                    str_contains(strtolower($item['description']), 'love'));
        });

        $negativeFeedback = array_filter($feedback, function ($item) {
            return $item['type'] === 'complaint' ||
                   str_contains(strtolower($item['description']), 'terrible') ||
                   str_contains(strtolower($item['description']), 'awful');
        });

        $total = count($feedback);

        return [
            'satisfaction_score' => $total > 0 ? round((count($positiveFeedback) / $total) * 100, 2) : 0,
            'positive_feedback_count' => count($positiveFeedback),
            'negative_feedback_count' => count($negativeFeedback),
            'neutral_feedback_count' => $total - count($positiveFeedback) - count($negativeFeedback),
        ];
    }

    private function getResponseMetrics(array $feedback): array
    {
        $respondedFeedback = array_filter($feedback, fn ($f) => $f['status'] !== 'open');
        $total = count($feedback);

        return [
            'response_rate' => $total > 0 ? round((count($respondedFeedback) / $total) * 100, 2) : 0,
            'average_first_response_time' => 4.5, // Mock: hours
            'resolution_rate' => $total > 0 ? round((count(array_filter($feedback, fn ($f) => $f['status'] === 'resolved')) / $total) * 100, 2) : 0,
        ];
    }

    private function getFeedbackRecommendations(array $feedback): array
    {
        $recommendations = [];

        $openFeedback = array_filter($feedback, fn ($f) => $f['status'] === 'open');
        if (count($openFeedback) > 10) {
            $recommendations[] = 'High number of open feedback items - consider increasing response capacity';
        }

        $highPriorityFeedback = array_filter($feedback, fn ($f) => $f['priority'] === 'high' && $f['status'] === 'open');
        if (count($highPriorityFeedback) > 0) {
            $recommendations[] = 'Address '.count($highPriorityFeedback).' high-priority feedback items immediately';
        }

        $bugReports = array_filter($feedback, fn ($f) => $f['type'] === 'bug_report');
        if (count($bugReports) > count($feedback) * 0.3) {
            $recommendations[] = 'High percentage of bug reports - consider improving QA processes';
        }

        return $recommendations;
    }
}
