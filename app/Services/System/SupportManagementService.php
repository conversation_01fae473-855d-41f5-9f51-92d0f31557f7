<?php

declare(strict_types=1);

namespace App\Services\System;

use App\Enums\System\TicketStatus;
use App\Models\System\SupportMessage;
use App\Models\User\SupportTicket;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class SupportManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get support ticket with detailed information.
     */
    public function getTicketWithDetails(string $ticketId): array
    {
        $ticket = SupportTicket::with([
            'creator',
            'assignedTo:id,first_name,last_name,email',
            'messages.sender',
        ])->findOrFail($ticketId);

        return [
            'ticket' => $ticket,
            'statistics' => $this->getTicketStatistics($ticket),
            'timeline' => $this->getTicketTimeline($ticket),
        ];
    }

    /**
     * Assign ticket to support agent.
     */
    public function assignTicket(string $ticketId, string $agentId): SupportTicket
    {
        return DB::transaction(function () use ($ticketId, $agentId) {
            $ticket = SupportTicket::findOrFail($ticketId);
            $agent = User::findOrFail($agentId);

            $previousAgent = $ticket->assignedTo;

            $ticket->update([
                'assigned_to_user_id' => $agentId,
                'status' => TicketStatus::IN_PROGRESS,
            ]);

            // Create system message about assignment
            SupportMessage::create([
                'ticket_id' => $ticket->id,
                'sender_id' => auth()->id(),
                'sender_type' => User::class,
                'message' => $previousAgent
                    ? "Ticket reassigned from {$previousAgent->name} to {$agent->name}"
                    : "Ticket assigned to {$agent->name}",
            ]);

            $this->loggingService->logInfo('Support ticket assigned', [
                'ticket_id' => $ticket->id,
                'agent_id' => $agentId,
                'previous_agent_id' => $previousAgent?->id,
                'admin_id' => auth()->id(),
            ]);

            return $ticket->fresh();
        });
    }

    /**
     * Update ticket status.
     */
    public function updateTicketStatus(string $ticketId, TicketStatus $status, ?string $note = null): SupportTicket
    {
        return DB::transaction(function () use ($ticketId, $status, $note) {
            $ticket = SupportTicket::findOrFail($ticketId);
            $previousStatus = $ticket->status;

            $updateData = ['status' => $status];

            // Set timestamps based on status
            if ($status === TicketStatus::RESOLVED && ! $ticket->resolved_at) {
                $updateData['resolved_at'] = now();
            } elseif ($status === TicketStatus::CLOSED && ! $ticket->closed_at) {
                $updateData['closed_at'] = now();
            }

            $ticket->update($updateData);

            // Create system message about status change
            $message = "Status changed from {$previousStatus->label()} to {$status->label()}";
            if ($note) {
                $message .= ". Note: {$note}";
            }

            SupportMessage::create([
                'ticket_id' => $ticket->id,
                'sender_id' => auth()->id(),
                'sender_type' => User::class,
                'message' => $message,
            ]);

            $this->loggingService->logInfo('Support ticket status updated', [
                'ticket_id' => $ticket->id,
                'previous_status' => $previousStatus->value,
                'new_status' => $status->value,
                'admin_id' => auth()->id(),
            ]);

            return $ticket->fresh();
        });
    }

    /**
     * Update ticket priority.
     */
    public function updateTicketPriority(string $ticketId, string $priority, ?string $reason = null): SupportTicket
    {
        return DB::transaction(function () use ($ticketId, $priority, $reason) {
            $ticket = SupportTicket::findOrFail($ticketId);
            $previousPriority = $ticket->priority;

            $ticket->update(['priority' => $priority]);

            // Create system message about priority change
            $message = "Priority changed from {$previousPriority} to {$priority}";
            if ($reason) {
                $message .= ". Reason: {$reason}";
            }

            SupportMessage::create([
                'ticket_id' => $ticket->id,
                'sender_id' => auth()->id(),
                'sender_type' => User::class,
                'message' => $message,
            ]);

            return $ticket->fresh();
        });
    }

    /**
     * Add message to ticket.
     */
    public function addMessage(string $ticketId, string $message, bool $isInternal = false): SupportMessage
    {
        return DB::transaction(function () use ($ticketId, $message, $isInternal) {
            $ticket = SupportTicket::findOrFail($ticketId);

            // Update ticket status if it's closed and a new message is added
            if ($ticket->status === TicketStatus::CLOSED) {
                $ticket->update(['status' => TicketStatus::OPEN]);
            }

            $supportMessage = SupportMessage::create([
                'ticket_id' => $ticketId,
                'sender_id' => auth()->id(),
                'sender_type' => User::class,
                'message' => $message,
                'is_internal' => $isInternal,
            ]);

            return $supportMessage;
        });
    }

    /**
     * Get support statistics.
     */
    public function getSupportStatistics(): array
    {
        return [
            'total_tickets' => SupportTicket::count(),
            'open_tickets' => SupportTicket::open()->count(),
            'in_progress_tickets' => SupportTicket::byStatus(TicketStatus::IN_PROGRESS->value)->count(),
            'resolved_tickets' => SupportTicket::resolved()->count(),
            'closed_tickets' => SupportTicket::closed()->count(),
            'unassigned_tickets' => SupportTicket::whereNull('assigned_to_user_id')->count(),
            'by_priority' => SupportTicket::selectRaw('priority, COUNT(*) as count')
                ->groupBy('priority')
                ->pluck('count', 'priority'),
            'by_status' => SupportTicket::selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status'),
            'average_resolution_time' => $this->getAverageResolutionTime(),
            'tickets_created_today' => SupportTicket::whereDate('created_at', today())->count(),
            'tickets_resolved_today' => SupportTicket::whereDate('resolved_at', today())->count(),
            'response_time_stats' => $this->getResponseTimeStatistics(),
        ];
    }

    /**
     * Get agent performance statistics.
     */
    public function getAgentPerformance(): array
    {
        $agents = User::whereHas('assignedTickets')->get();
        $performance = [];

        foreach ($agents as $agent) {
            $tickets = $agent->assignedTickets();

            $performance[] = [
                'agent' => [
                    'id' => $agent->id,
                    'name' => $agent->name,
                    'email' => $agent->email,
                ],
                'total_assigned' => $tickets->count(),
                'resolved' => $tickets->resolved()->count(),
                'in_progress' => $tickets->byStatus(TicketStatus::IN_PROGRESS->value)->count(),
                'average_resolution_time' => $this->getAgentAverageResolutionTime($agent->id),
                'tickets_resolved_today' => $tickets->whereDate('resolved_at', today())->count(),
                'response_rate' => $this->getAgentResponseRate($agent->id),
            ];
        }

        return $performance;
    }

    /**
     * Bulk assign tickets.
     */
    public function bulkAssignTickets(array $ticketIds, string $agentId): array
    {
        return DB::transaction(function () use ($ticketIds, $agentId) {
            $results = ['success' => 0, 'failed' => 0, 'errors' => []];

            foreach ($ticketIds as $ticketId) {
                try {
                    $this->assignTicket($ticketId, $agentId);
                    $results['success']++;
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = [
                        'ticket_id' => $ticketId,
                        'error' => $e->getMessage(),
                    ];
                }
            }

            return $results;
        });
    }

    /**
     * Bulk update ticket status.
     */
    public function bulkUpdateStatus(array $ticketIds, TicketStatus $status): array
    {
        return DB::transaction(function () use ($ticketIds, $status) {
            $results = ['success' => 0, 'failed' => 0, 'errors' => []];

            foreach ($ticketIds as $ticketId) {
                try {
                    $this->updateTicketStatus($ticketId, $status);
                    $results['success']++;
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['errors'][] = [
                        'ticket_id' => $ticketId,
                        'error' => $e->getMessage(),
                    ];
                }
            }

            return $results;
        });
    }

    /**
     * Get tickets requiring attention.
     */
    public function getTicketsRequiringAttention(): Collection
    {
        return SupportTicket::with(['creator', 'assignedTo'])
            ->where(function ($query) {
                $query->where('priority', 'high')
                    ->orWhere('status', TicketStatus::OPEN)
                    ->orWhere(function ($q) {
                        $q->where('status', TicketStatus::IN_PROGRESS)
                            ->where('updated_at', '<', now()->subHours(24));
                    });
            })
            ->orderBy('priority', 'desc')
            ->orderBy('created_at', 'asc')
            ->limit(50)
            ->get();
    }

    /**
     * Get ticket statistics for a specific ticket.
     */
    private function getTicketStatistics(SupportTicket $ticket): array
    {
        return [
            'total_messages' => $ticket->messages()->count(),
            'agent_messages' => $ticket->messages()
                ->where('sender_type', User::class)
                ->whereHas('sender', function ($query) {
                    $query->whereHas('roles', function ($q) {
                        $q->where('name', 'support_agent');
                    });
                })
                ->count(),
            'customer_messages' => $ticket->messages()
                ->where('sender_id', $ticket->created_by_id)
                ->where('sender_type', $ticket->created_by_type)
                ->count(),
            'days_open' => $ticket->created_at->diffInDays(now()),
            'last_activity' => $ticket->messages()->latest()->first()?->created_at,
            'time_to_first_response' => $this->getTimeToFirstResponse($ticket),
        ];
    }

    /**
     * Get ticket timeline.
     */
    private function getTicketTimeline(SupportTicket $ticket): array
    {
        $timeline = [];

        // Add ticket creation
        $timeline[] = [
            'type' => 'created',
            'timestamp' => $ticket->created_at,
            'actor' => $ticket->creator,
            'description' => 'Ticket created',
        ];

        // Add messages
        foreach ($ticket->messages as $message) {
            $timeline[] = [
                'type' => 'message',
                'timestamp' => $message->created_at,
                'actor' => $message->sender,
                'description' => 'Message sent',
                'content' => $message->message,
            ];
        }

        // Sort by timestamp
        usort($timeline, fn ($a, $b) => $a['timestamp']->compare($b['timestamp']));

        return $timeline;
    }

    /**
     * Get average resolution time in hours.
     */
    private function getAverageResolutionTime(): float
    {
        $resolvedTickets = SupportTicket::resolved()
            ->whereNotNull('resolved_at')
            ->get();

        if ($resolvedTickets->isEmpty()) {
            return 0;
        }

        $totalHours = $resolvedTickets->sum(function ($ticket) {
            return $ticket->created_at->diffInHours($ticket->resolved_at);
        });

        return round($totalHours / $resolvedTickets->count(), 2);
    }

    /**
     * Get response time statistics.
     */
    private function getResponseTimeStatistics(): array
    {
        // This would calculate first response times, average response times, etc.
        // For now, return placeholder data
        return [
            'average_first_response_hours' => 2.5,
            'average_response_hours' => 1.2,
            'sla_compliance_percentage' => 85.5,
        ];
    }

    /**
     * Get agent average resolution time.
     */
    private function getAgentAverageResolutionTime(string $agentId): float
    {
        $resolvedTickets = SupportTicket::where('assigned_to_user_id', $agentId)
            ->resolved()
            ->whereNotNull('resolved_at')
            ->get();

        if ($resolvedTickets->isEmpty()) {
            return 0;
        }

        $totalHours = $resolvedTickets->sum(function ($ticket) {
            return $ticket->created_at->diffInHours($ticket->resolved_at);
        });

        return round($totalHours / $resolvedTickets->count(), 2);
    }

    /**
     * Get agent response rate.
     */
    private function getAgentResponseRate(string $agentId): float
    {
        $totalTickets = SupportTicket::where('assigned_to_user_id', $agentId)->count();

        if ($totalTickets === 0) {
            return 0;
        }

        $respondedTickets = SupportTicket::where('assigned_to_user_id', $agentId)
            ->whereHas('messages', function ($query) use ($agentId) {
                $query->where('sender_id', $agentId)
                    ->where('sender_type', User::class);
            })
            ->count();

        return round(($respondedTickets / $totalTickets) * 100, 2);
    }

    /**
     * Get time to first response for a ticket.
     */
    private function getTimeToFirstResponse(SupportTicket $ticket): ?float
    {
        $firstAgentMessage = $ticket->messages()
            ->where('sender_type', User::class)
            ->whereHas('sender', function ($query) {
                $query->whereHas('roles', function ($q) {
                    $q->where('name', 'support_agent');
                });
            })
            ->oldest()
            ->first();

        if (! $firstAgentMessage) {
            return null;
        }

        return $ticket->created_at->diffInHours($firstAgentMessage->created_at);
    }
}
