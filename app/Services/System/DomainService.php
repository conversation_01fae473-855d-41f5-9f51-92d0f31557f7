<?php

declare(strict_types=1);

namespace App\Services\System;

/**
 * Domain Service
 * 
 * Provides centralized domain management and URL generation
 * to avoid hardcoding domains throughout the application.
 */
class DomainService
{
    /**
     * Get the base domain for the current environment.
     */
    public function getBaseDomain(): string
    {
        return config('domains.base_domain', 'deliverynexus.test');
    }

    /**
     * Get the central domain (main platform domain).
     */
    public function getCentralDomain(): string
    {
        return config('domains.central_domain', 'deliverynexus.test');
    }

    /**
     * Get the email domain for the current environment.
     */
    public function getEmailDomain(): string
    {
        return config('domains.email.domain', $this->getBaseDomain());
    }

    /**
     * Get support email address.
     */
    public function getSupportEmail(): string
    {
        return config('domains.email.support', 'support@' . $this->getEmailDomain());
    }

    /**
     * Get no-reply email address.
     */
    public function getNoReplyEmail(): string
    {
        return config('domains.email.noreply', 'noreply@' . $this->getEmailDomain());
    }

    /**
     * Get admin email address.
     */
    public function getAdminEmail(): string
    {
        return config('domains.email.admin', 'admin@' . $this->getEmailDomain());
    }

    /**
     * Generate tenant URL for a given subdomain.
     */
    public function generateTenantUrl(string $subdomain, string $path = ''): string
    {
        $protocol = config('domains.tenant.protocol', 'https');
        $suffix = config('domains.tenant.subdomain_suffix', '.deliverynexus.test');
        $baseUrl = "{$protocol}://{$subdomain}{$suffix}";
        
        return $path ? $baseUrl . '/' . ltrim($path, '/') : $baseUrl;
    }

    /**
     * Generate menu URL for a business.
     */
    public function generateMenuUrl(string $subdomain, array $params = []): string
    {
        $url = $this->generateTenantUrl($subdomain, 'menu');
        
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }
        
        return $url;
    }

    /**
     * Get frontend URLs for different user types.
     */
    public function getFrontendUrls(): array
    {
        return config('domains.frontend', [
            'customer_app' => 'https://app.' . $this->getBaseDomain(),
            'admin_portal' => 'https://admin.' . $this->getBaseDomain(),
            'marketing_site' => 'https://' . $this->getBaseDomain(),
        ]);
    }

    /**
     * Get customer app URL.
     */
    public function getCustomerAppUrl(): string
    {
        return config('domains.frontend.customer_app', 'https://app.' . $this->getBaseDomain());
    }

    /**
     * Get tenant frontend URL for a specific subdomain.
     * This is where businesses and providers access their frontend.
     */
    public function getTenantFrontendUrl(string $subdomain): string
    {
        $protocol = $this->getProtocol();
        $suffix = config('domains.tenant.subdomain_suffix', '.' . $this->getBaseDomain());
        return "{$protocol}://{$subdomain}{$suffix}";
    }

    /**
     * Get admin portal URL.
     */
    public function getAdminPortalUrl(): string
    {
        return config('domains.frontend.admin_portal', 'https://admin.' . $this->getBaseDomain());
    }

    /**
     * Get marketing site URL.
     */
    public function getMarketingSiteUrl(): string
    {
        return config('domains.frontend.marketing_site', 'https://' . $this->getBaseDomain());
    }

    /**
     * Get OAuth redirect URLs.
     */
    public function getOAuthRedirectUrls(): array
    {
        $appUrl = config('app.url');
        
        return [
            'google' => config('domains.oauth.google_redirect', $appUrl . '/api/v1/auth/oauth/callback/google'),
            'apple' => config('domains.oauth.apple_redirect', $appUrl . '/api/v1/auth/oauth/callback/apple'),
        ];
    }

    /**
     * Check if the current environment is local.
     */
    public function isLocal(): bool
    {
        return app()->environment('local');
    }

    /**
     * Check if the current environment is production.
     */
    public function isProduction(): bool
    {
        return app()->environment('production');
    }

    /**
     * Get the appropriate protocol for the current environment.
     */
    public function getProtocol(): string
    {
        return $this->isLocal() ? 'http' : 'https';
    }

    /**
     * Get CDN URL.
     */
    public function getCdnUrl(): string
    {
        return config('domains.cdn.url', 'https://cdn.' . $this->getBaseDomain());
    }

    /**
     * Get WebSocket configuration.
     */
    public function getWebSocketConfig(): array
    {
        return config('domains.websocket', [
            'host' => 'localhost',
            'port' => 8080,
            'scheme' => 'http',
        ]);
    }

    /**
     * Generate a full URL with proper protocol and domain.
     */
    public function generateUrl(string $path = '', ?string $domain = null): string
    {
        $domain = $domain ?: $this->getCentralDomain();
        $protocol = $this->getProtocol();
        
        $url = "{$protocol}://{$domain}";
        
        if ($path) {
            $url .= '/' . ltrim($path, '/');
        }
        
        return $url;
    }

    /**
     * Get all domain-related configuration for frontend.
     */
    public function getConfigForFrontend(): array
    {
        return [
            'base_domain' => $this->getBaseDomain(),
            'central_domain' => $this->getCentralDomain(),
            'email_domain' => $this->getEmailDomain(),
            'frontend_urls' => $this->getFrontendUrls(),
            'oauth_redirects' => $this->getOAuthRedirectUrls(),
            'websocket' => $this->getWebSocketConfig(),
            'cdn_url' => $this->getCdnUrl(),
            'protocol' => $this->getProtocol(),
            'is_local' => $this->isLocal(),
            'is_production' => $this->isProduction(),
        ];
    }
}
