<?php

declare(strict_types=1);

namespace App\Services\System;

use App\Models\System\ApiKey;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class ApiManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all API keys with details.
     */
    public function getAllApiKeysWithDetails(): Collection
    {
        return ApiKey::with(['user:id,first_name,last_name,email', 'business:id,name'])
            ->orderBy('created_at', 'desc')
            ->get()
            ->map(function ($apiKey) {
                return [
                    'id' => $apiKey->id,
                    'name' => $apiKey->name,
                    'masked_key' => $apiKey->getMaskedKey(),
                    'user' => $apiKey->user,
                    'business' => $apiKey->business,
                    'abilities' => $apiKey->abilities,
                    'is_active' => $apiKey->is_active,
                    'is_expired' => $apiKey->isExpired(),
                    'last_used_at' => $apiKey->last_used_at,
                    'expires_at' => $apiKey->expires_at,
                    'created_at' => $apiKey->created_at,
                ];
            });
    }

    /**
     * Create a new API key.
     */
    public function createApiKey(array $data): array
    {
        return DB::transaction(function () use ($data) {
            $result = ApiKey::createKey($data);

            $this->loggingService->logInfo('API key created', [
                'api_key_id' => $result['api_key']->id,
                'api_key_name' => $result['api_key']->name,
                'user_id' => $data['user_id'] ?? null,
                'business_id' => $data['business_id'] ?? null,
                'admin_id' => auth()->id(),
            ]);

            return $result;
        });
    }

    /**
     * Update API key.
     */
    public function updateApiKey(string $apiKeyId, array $data): ApiKey
    {
        return DB::transaction(function () use ($apiKeyId, $data) {
            $apiKey = ApiKey::findOrFail($apiKeyId);

            $oldData = $apiKey->toArray();
            $apiKey->update($data);

            $this->loggingService->logInfo('API key updated', [
                'api_key_id' => $apiKey->id,
                'api_key_name' => $apiKey->name,
                'changes' => array_diff_assoc($data, $oldData),
                'admin_id' => auth()->id(),
            ]);

            return $apiKey->fresh();
        });
    }

    /**
     * Revoke API key.
     */
    public function revokeApiKey(string $apiKeyId): bool
    {
        return DB::transaction(function () use ($apiKeyId) {
            $apiKey = ApiKey::findOrFail($apiKeyId);
            $revoked = $apiKey->revoke();

            if ($revoked) {
                $this->loggingService->logInfo('API key revoked', [
                    'api_key_id' => $apiKey->id,
                    'api_key_name' => $apiKey->name,
                    'admin_id' => auth()->id(),
                ]);
            }

            return $revoked;
        });
    }

    /**
     * Regenerate API key.
     */
    public function regenerateApiKey(string $apiKeyId): string
    {
        return DB::transaction(function () use ($apiKeyId) {
            $apiKey = ApiKey::findOrFail($apiKeyId);
            $newKey = $apiKey->regenerate();

            $this->loggingService->logInfo('API key regenerated', [
                'api_key_id' => $apiKey->id,
                'api_key_name' => $apiKey->name,
                'admin_id' => auth()->id(),
            ]);

            return $newKey;
        });
    }

    /**
     * Bulk revoke API keys.
     */
    public function bulkRevokeApiKeys(array $apiKeyIds): array
    {
        $results = ['revoked' => 0, 'failed' => 0, 'details' => []];

        DB::transaction(function () use ($apiKeyIds, &$results) {
            foreach ($apiKeyIds as $apiKeyId) {
                try {
                    $revoked = $this->revokeApiKey($apiKeyId);
                    if ($revoked) {
                        $results['revoked']++;
                        $results['details'][$apiKeyId] = 'revoked';
                    } else {
                        $results['failed']++;
                        $results['details'][$apiKeyId] = 'failed to revoke';
                    }
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['details'][$apiKeyId] = 'error: '.$e->getMessage();
                }
            }
        });

        return $results;
    }

    /**
     * Get API key statistics.
     */
    public function getApiKeyStatistics(): array
    {
        return [
            'total_keys' => ApiKey::count(),
            'active_keys' => ApiKey::active()->count(),
            'expired_keys' => ApiKey::expired()->count(),
            'keys_by_user_type' => [
                'with_user' => ApiKey::whereNotNull('user_id')->count(),
                'with_business' => ApiKey::whereNotNull('business_id')->count(),
                'system_keys' => ApiKey::whereNull('user_id')->whereNull('business_id')->count(),
            ],
            'recent_usage' => $this->getRecentUsageStats(),
            'top_users' => $this->getTopApiKeyUsers(),
            'expiring_soon' => ApiKey::where('expires_at', '>', now())
                ->where('expires_at', '<=', now()->addDays(30))
                ->count(),
        ];
    }

    /**
     * Get API usage analytics.
     */
    public function getApiUsageAnalytics(array $filters = []): array
    {
        // This would integrate with API request logs
        // For now, return mock analytics data
        return [
            'total_requests' => 125000,
            'requests_today' => 2500,
            'requests_this_week' => 18000,
            'requests_this_month' => 75000,
            'top_endpoints' => [
                '/api/v1/orders' => 35000,
                '/api/v1/products' => 28000,
                '/api/v1/businesses' => 22000,
                '/api/v1/deliveries' => 18000,
                '/api/v1/analytics' => 12000,
            ],
            'response_times' => [
                'average' => 245, // milliseconds
                'p95' => 580,
                'p99' => 1200,
            ],
            'error_rates' => [
                '2xx' => 92.5,
                '4xx' => 6.2,
                '5xx' => 1.3,
            ],
            'rate_limit_hits' => 45,
        ];
    }

    /**
     * Get API key usage for specific key.
     */
    public function getApiKeyUsage(string $apiKeyId): array
    {
        $apiKey = ApiKey::findOrFail($apiKeyId);

        // This would integrate with API request logs
        // For now, return mock usage data
        return [
            'api_key' => $apiKey->only(['id', 'name', 'last_used_at']),
            'usage_stats' => [
                'total_requests' => 1250,
                'requests_today' => 45,
                'requests_this_week' => 320,
                'requests_this_month' => 1250,
                'average_requests_per_day' => 42,
            ],
            'endpoint_usage' => [
                '/api/v1/orders' => 450,
                '/api/v1/products' => 380,
                '/api/v1/businesses' => 250,
                '/api/v1/deliveries' => 170,
            ],
            'recent_activity' => [
                [
                    'endpoint' => '/api/v1/orders',
                    'method' => 'GET',
                    'status' => 200,
                    'timestamp' => now()->subMinutes(5),
                ],
                [
                    'endpoint' => '/api/v1/products',
                    'method' => 'POST',
                    'status' => 201,
                    'timestamp' => now()->subMinutes(12),
                ],
            ],
        ];
    }

    /**
     * Search API keys.
     */
    public function searchApiKeys(string $query): Collection
    {
        return ApiKey::with(['user:id,first_name,last_name,email', 'business:id,name'])
            ->where(function ($q) use ($query) {
                $q->where('name', 'ILIKE', "%{$query}%")
                    ->orWhereHas('user', function ($userQuery) use ($query) {
                        $userQuery->where('first_name', 'ILIKE', "%{$query}%")
                            ->orWhere('last_name', 'ILIKE', "%{$query}%")
                            ->orWhere('email', 'ILIKE', "%{$query}%");
                    })
                    ->orWhereHas('business', function ($businessQuery) use ($query) {
                        $businessQuery->where('name', 'ILIKE', "%{$query}%");
                    });
            })
            ->limit(50)
            ->get();
    }

    /**
     * Get API keys by user.
     */
    public function getApiKeysByUser(string $userId): Collection
    {
        return ApiKey::where('user_id', $userId)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Get API keys by business.
     */
    public function getApiKeysByBusiness(string $businessId): Collection
    {
        return ApiKey::where('business_id', $businessId)
            ->with(['user:id,first_name,last_name,email'])
            ->orderBy('created_at', 'desc')
            ->get();
    }

    /**
     * Validate API key permissions.
     */
    public function validateApiKeyPermissions(string $apiKeyId, array $requiredAbilities): array
    {
        $apiKey = ApiKey::findOrFail($apiKeyId);

        $validation = [
            'api_key' => $apiKey->only(['id', 'name']),
            'is_valid' => $apiKey->isValid(),
            'is_expired' => $apiKey->isExpired(),
            'abilities_check' => [],
            'has_all_required' => true,
        ];

        foreach ($requiredAbilities as $ability) {
            $hasAbility = $apiKey->hasAbility($ability);
            $validation['abilities_check'][$ability] = $hasAbility;

            if (! $hasAbility) {
                $validation['has_all_required'] = false;
            }
        }

        return $validation;
    }

    /**
     * Clean up expired API keys.
     */
    public function cleanupExpiredKeys(): array
    {
        $expiredKeys = ApiKey::expired()->get();
        $cleanedCount = 0;

        foreach ($expiredKeys as $key) {
            if ($key->expires_at && $key->expires_at->addDays(30)->isPast()) {
                // Delete keys that expired more than 30 days ago
                $key->delete();
                $cleanedCount++;
            } else {
                // Just deactivate recently expired keys
                $key->update(['is_active' => false]);
            }
        }

        $this->loggingService->logInfo('Expired API keys cleaned up', [
            'total_expired' => $expiredKeys->count(),
            'deleted_count' => $cleanedCount,
            'deactivated_count' => $expiredKeys->count() - $cleanedCount,
        ]);

        return [
            'total_expired' => $expiredKeys->count(),
            'deleted' => $cleanedCount,
            'deactivated' => $expiredKeys->count() - $cleanedCount,
        ];
    }

    /**
     * Get recent usage statistics.
     */
    private function getRecentUsageStats(): array
    {
        return [
            'keys_used_today' => ApiKey::where('last_used_at', '>=', now()->startOfDay())->count(),
            'keys_used_this_week' => ApiKey::where('last_used_at', '>=', now()->startOfWeek())->count(),
            'keys_used_this_month' => ApiKey::where('last_used_at', '>=', now()->startOfMonth())->count(),
            'never_used' => ApiKey::whereNull('last_used_at')->count(),
        ];
    }

    /**
     * Get top API key users.
     */
    private function getTopApiKeyUsers(): array
    {
        return ApiKey::with(['user:id,first_name,last_name,email'])
            ->selectRaw('user_id, COUNT(*) as key_count')
            ->whereNotNull('user_id')
            ->groupBy('user_id')
            ->orderByDesc('key_count')
            ->limit(10)
            ->get()
            ->map(function ($item) {
                return [
                    'user' => $item->user,
                    'key_count' => $item->key_count,
                ];
            })
            ->toArray();
    }
}
