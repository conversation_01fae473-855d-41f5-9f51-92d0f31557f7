<?php

declare(strict_types=1);

namespace App\Services\System;

use App\Models\System\ConfigurationGroup;
use App\Models\System\ConfigurationSetting;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class SettingsManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get all platform settings grouped by category.
     */
    public function getAllSettingsGrouped(): array
    {
        return Cache::remember('platform_settings_grouped', 3600, function () {
            $groups = ConfigurationGroup::with(['settings' => function ($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            }])->where('is_active', true)->orderBy('sort_order')->get();

            $result = [];
            foreach ($groups as $group) {
                $result[$group->slug] = [
                    'group' => $group,
                    'settings' => $group->settings->map(function ($setting) {
                        return [
                            'key' => $setting->key,
                            'value' => $setting->getTypedValue(),
                            'type' => $setting->type,
                            'label' => $setting->label,
                            'description' => $setting->description,
                            'is_public' => $setting->is_public,
                            'validation_rules' => $setting->validation_rules,
                        ];
                    }),
                ];
            }

            return $result;
        });
    }

    /**
     * Get platform-wide settings.
     */
    public function getPlatformSettings(): array
    {
        return [
            'general' => $this->getGeneralSettings(),
            'business' => $this->getBusinessSettings(),
            'delivery' => $this->getDeliverySettings(),
            'payment' => $this->getPaymentSettings(),
            'notification' => $this->getNotificationSettings(),
            'security' => $this->getSecuritySettings(),
            'api' => $this->getApiSettings(),
            'maintenance' => $this->getMaintenanceSettings(),
        ];
    }

    /**
     * Update platform setting.
     */
    public function updateSetting(string $key, mixed $value): ConfigurationSetting
    {
        return DB::transaction(function () use ($key, $value) {
            $setting = ConfigurationSetting::where('key', $key)->firstOrFail();

            // Validate the value
            $this->validateSettingValue($setting, $value);

            $oldValue = $setting->value;
            $setting->update(['value' => $value]);

            // Clear cache
            $this->clearSettingsCache();

            $this->loggingService->logInfo('Platform setting updated', [
                'setting_key' => $key,
                'old_value' => $oldValue,
                'new_value' => $value,
                'admin_id' => auth()->id(),
            ]);

            return $setting->fresh();
        });
    }

    /**
     * Bulk update settings.
     */
    public function bulkUpdateSettings(array $settings): array
    {
        $results = ['updated' => 0, 'failed' => 0, 'details' => []];

        DB::transaction(function () use ($settings, &$results) {
            foreach ($settings as $key => $value) {
                try {
                    $this->updateSetting($key, $value);
                    $results['updated']++;
                    $results['details'][$key] = ['status' => 'success'];
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['details'][$key] = [
                        'status' => 'failed',
                        'error' => $e->getMessage(),
                    ];
                }
            }
        });

        return $results;
    }

    /**
     * Reset settings to default values.
     */
    public function resetToDefaults(array $settingKeys = []): array
    {
        return DB::transaction(function () use ($settingKeys) {
            $query = ConfigurationSetting::query();

            if (! empty($settingKeys)) {
                $query->whereIn('key', $settingKeys);
            }

            $settings = $query->get();
            $resetCount = 0;

            foreach ($settings as $setting) {
                if ($setting->default_value !== null) {
                    $setting->update(['value' => $setting->default_value]);
                    $resetCount++;
                }
            }

            $this->clearSettingsCache();

            $this->loggingService->logInfo('Settings reset to defaults', [
                'settings_count' => $resetCount,
                'setting_keys' => $settingKeys,
                'admin_id' => auth()->id(),
            ]);

            return [
                'reset_count' => $resetCount,
                'total_settings' => $settings->count(),
            ];
        });
    }

    /**
     * Get regional settings.
     */
    public function getRegionalSettings(): array
    {
        return [
            'default_country' => $this->getSetting('platform.default_country', 'NG'),
            'default_currency' => $this->getSetting('platform.default_currency', 'NGN'),
            'default_timezone' => $this->getSetting('platform.default_timezone', 'Africa/Lagos'),
            'default_language' => $this->getSetting('platform.default_language', 'en'),
            'supported_countries' => $this->getSetting('platform.supported_countries', ['NG']),
            'supported_currencies' => $this->getSetting('platform.supported_currencies', ['NGN']),
            'supported_languages' => $this->getSetting('platform.supported_languages', ['en']),
            'date_format' => $this->getSetting('platform.date_format', 'Y-m-d'),
            'time_format' => $this->getSetting('platform.time_format', 'H:i:s'),
            'number_format' => $this->getSetting('platform.number_format', 'en_US'),
        ];
    }

    /**
     * Update regional settings.
     */
    public function updateRegionalSettings(array $settings): array
    {
        $allowedKeys = [
            'default_country', 'default_currency', 'default_timezone',
            'default_language', 'supported_countries', 'supported_currencies',
            'supported_languages', 'date_format', 'time_format', 'number_format',
        ];

        $results = [];
        foreach ($settings as $key => $value) {
            if (in_array($key, $allowedKeys)) {
                $fullKey = "platform.{$key}";
                try {
                    $this->updateSetting($fullKey, $value);
                    $results[$key] = 'updated';
                } catch (\Exception $e) {
                    $results[$key] = 'failed: '.$e->getMessage();
                }
            }
        }

        return $results;
    }

    /**
     * Get business rules configuration.
     */
    public function getBusinessRules(): array
    {
        return [
            'min_order_amount' => $this->getSetting('business.min_order_amount', 500),
            'max_order_amount' => $this->getSetting('business.max_order_amount', 100000),
            'default_commission_rate' => $this->getSetting('business.default_commission_rate', 15.0),
            'auto_approve_businesses' => $this->getSetting('business.auto_approve_businesses', false),
            'require_business_verification' => $this->getSetting('business.require_business_verification', true),
            'max_products_per_business' => $this->getSetting('business.max_products_per_business', 1000),
            'allow_business_self_registration' => $this->getSetting('business.allow_business_self_registration', true),
            'business_approval_timeout_days' => $this->getSetting('business.business_approval_timeout_days', 7),
        ];
    }

    /**
     * Get delivery configuration.
     */
    public function getDeliveryConfiguration(): array
    {
        return [
            'default_delivery_fee' => $this->getSetting('delivery.default_delivery_fee', 500),
            'max_delivery_distance_km' => $this->getSetting('delivery.max_delivery_distance_km', 50),
            'delivery_time_slots' => $this->getSetting('delivery.delivery_time_slots', [
                '09:00-12:00', '12:00-15:00', '15:00-18:00', '18:00-21:00',
            ]),
            'auto_assign_deliveries' => $this->getSetting('delivery.auto_assign_deliveries', false),
            'delivery_tracking_enabled' => $this->getSetting('delivery.delivery_tracking_enabled', true),
            'require_delivery_verification' => $this->getSetting('delivery.require_delivery_verification', true),
            'max_delivery_attempts' => $this->getSetting('delivery.max_delivery_attempts', 3),
            'delivery_timeout_minutes' => $this->getSetting('delivery.delivery_timeout_minutes', 120),
        ];
    }

    /**
     * Export settings for backup.
     */
    public function exportSettings(array $groups = []): array
    {
        $query = ConfigurationSetting::with('group');

        if (! empty($groups)) {
            $query->whereHas('group', function ($q) use ($groups) {
                $q->whereIn('slug', $groups);
            });
        }

        $settings = $query->get();

        $export = [
            'exported_at' => now()->toISOString(),
            'exported_by' => auth()->user()?->email ?? 'system',
            'settings_count' => $settings->count(),
            'settings' => [],
        ];

        foreach ($settings as $setting) {
            $export['settings'][] = [
                'group' => $setting->group->slug,
                'key' => $setting->key,
                'value' => $setting->value,
                'type' => $setting->type,
                'label' => $setting->label,
                'description' => $setting->description,
                'default_value' => $setting->default_value,
                'validation_rules' => $setting->validation_rules,
                'is_public' => $setting->is_public,
                'is_active' => $setting->is_active,
            ];
        }

        return $export;
    }

    /**
     * Import settings from backup.
     */
    public function importSettings(array $settingsData): array
    {
        $results = ['imported' => 0, 'updated' => 0, 'failed' => 0, 'details' => []];

        DB::transaction(function () use ($settingsData, &$results) {
            foreach ($settingsData['settings'] as $settingData) {
                try {
                    $setting = ConfigurationSetting::where('key', $settingData['key'])->first();

                    if ($setting) {
                        $setting->update([
                            'value' => $settingData['value'],
                            'is_active' => $settingData['is_active'] ?? true,
                        ]);
                        $results['updated']++;
                    } else {
                        // Create new setting if it doesn't exist
                        $group = ConfigurationGroup::where('slug', $settingData['group'])->first();
                        if ($group) {
                            ConfigurationSetting::create([
                                'group_id' => $group->id,
                                'key' => $settingData['key'],
                                'value' => $settingData['value'],
                                'type' => $settingData['type'],
                                'label' => $settingData['label'],
                                'description' => $settingData['description'],
                                'default_value' => $settingData['default_value'],
                                'validation_rules' => $settingData['validation_rules'],
                                'is_public' => $settingData['is_public'] ?? false,
                                'is_active' => $settingData['is_active'] ?? true,
                            ]);
                            $results['imported']++;
                        }
                    }

                    $results['details'][$settingData['key']] = 'success';
                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['details'][$settingData['key']] = 'failed: '.$e->getMessage();
                }
            }

            $this->clearSettingsCache();
        });

        $this->loggingService->logInfo('Settings imported', [
            'imported_count' => $results['imported'],
            'updated_count' => $results['updated'],
            'failed_count' => $results['failed'],
            'admin_id' => auth()->id(),
        ]);

        return $results;
    }

    /**
     * Get setting value with default.
     */
    private function getSetting(string $key, mixed $default = null): mixed
    {
        $setting = ConfigurationSetting::where('key', $key)->first();

        return $setting ? $setting->getTypedValue() : $default;
    }

    /**
     * Get general settings.
     */
    private function getGeneralSettings(): array
    {
        return [
            'platform_name' => $this->getSetting('platform.name', 'DeliveryNexus'),
            'platform_description' => $this->getSetting('platform.description', 'Multi-purpose e-commerce platform'),
            'platform_url' => $this->getSetting('platform.url', 'https://deliverynexus.com'),
            'support_email' => $this->getSetting('platform.support_email', '<EMAIL>'),
            'contact_phone' => $this->getSetting('platform.contact_phone', '+234-************'),
            'maintenance_mode' => $this->getSetting('platform.maintenance_mode', false),
            'registration_enabled' => $this->getSetting('platform.registration_enabled', true),
        ];
    }

    /**
     * Get business settings.
     */
    private function getBusinessSettings(): array
    {
        return [
            'auto_approve_businesses' => $this->getSetting('business.auto_approve_businesses', false),
            'require_business_verification' => $this->getSetting('business.require_business_verification', true),
            'default_commission_rate' => $this->getSetting('business.default_commission_rate', 15.0),
            'max_products_per_business' => $this->getSetting('business.max_products_per_business', 1000),
        ];
    }

    /**
     * Get delivery settings.
     */
    private function getDeliverySettings(): array
    {
        return [
            'default_delivery_fee' => $this->getSetting('delivery.default_delivery_fee', 500),
            'max_delivery_distance_km' => $this->getSetting('delivery.max_delivery_distance_km', 50),
            'auto_assign_deliveries' => $this->getSetting('delivery.auto_assign_deliveries', false),
            'delivery_tracking_enabled' => $this->getSetting('delivery.delivery_tracking_enabled', true),
        ];
    }

    /**
     * Get payment settings.
     */
    private function getPaymentSettings(): array
    {
        return [
            'default_payment_gateway' => $this->getSetting('payment.default_gateway', 'paystack'),
            'payment_timeout_minutes' => $this->getSetting('payment.timeout_minutes', 15),
            'auto_refund_enabled' => $this->getSetting('payment.auto_refund_enabled', true),
            'min_payout_amount' => $this->getSetting('payment.min_payout_amount', 1000),
        ];
    }

    /**
     * Get notification settings.
     */
    private function getNotificationSettings(): array
    {
        return [
            'email_notifications_enabled' => $this->getSetting('notifications.email_enabled', true),
            'sms_notifications_enabled' => $this->getSetting('notifications.sms_enabled', true),
            'push_notifications_enabled' => $this->getSetting('notifications.push_enabled', true),
            'whatsapp_notifications_enabled' => $this->getSetting('notifications.whatsapp_enabled', false),
        ];
    }

    /**
     * Get security settings.
     */
    private function getSecuritySettings(): array
    {
        return [
            'password_min_length' => $this->getSetting('security.password_min_length', 8),
            'require_email_verification' => $this->getSetting('security.require_email_verification', true),
            'require_phone_verification' => $this->getSetting('security.require_phone_verification', true),
            'max_login_attempts' => $this->getSetting('security.max_login_attempts', 5),
            'lockout_duration_minutes' => $this->getSetting('security.lockout_duration_minutes', 30),
        ];
    }

    /**
     * Get API settings.
     */
    private function getApiSettings(): array
    {
        return [
            'api_rate_limit_per_minute' => $this->getSetting('api.rate_limit_per_minute', 60),
            'api_documentation_enabled' => $this->getSetting('api.documentation_enabled', true),
            'webhook_timeout_seconds' => $this->getSetting('api.webhook_timeout_seconds', 30),
            'api_versioning_enabled' => $this->getSetting('api.versioning_enabled', true),
        ];
    }

    /**
     * Get maintenance settings.
     */
    private function getMaintenanceSettings(): array
    {
        return [
            'maintenance_mode' => $this->getSetting('platform.maintenance_mode', false),
            'maintenance_message' => $this->getSetting('platform.maintenance_message', 'System under maintenance'),
            'backup_enabled' => $this->getSetting('maintenance.backup_enabled', true),
            'backup_frequency_hours' => $this->getSetting('maintenance.backup_frequency_hours', 24),
        ];
    }

    /**
     * Validate setting value.
     */
    private function validateSettingValue(ConfigurationSetting $setting, mixed $value): void
    {
        if ($setting->validation_rules) {
            $rules = json_decode($setting->validation_rules, true);

            // Basic validation based on type
            switch ($setting->type) {
                case 'boolean':
                    if (! is_bool($value)) {
                        throw new \InvalidArgumentException("Setting '{$setting->key}' must be a boolean value");
                    }
                    break;
                case 'integer':
                    if (! is_int($value)) {
                        throw new \InvalidArgumentException("Setting '{$setting->key}' must be an integer value");
                    }
                    break;
                case 'float':
                    if (! is_numeric($value)) {
                        throw new \InvalidArgumentException("Setting '{$setting->key}' must be a numeric value");
                    }
                    break;
                case 'array':
                    if (! is_array($value)) {
                        throw new \InvalidArgumentException("Setting '{$setting->key}' must be an array value");
                    }
                    break;
            }
        }
    }

    /**
     * Clear settings cache.
     */
    private function clearSettingsCache(): void
    {
        Cache::forget('platform_settings_grouped');
        Cache::tags(['settings'])->flush();
    }
}
