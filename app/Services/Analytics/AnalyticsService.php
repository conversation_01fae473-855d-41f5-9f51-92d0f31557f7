<?php

declare(strict_types=1);

namespace App\Services\Analytics;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AnalyticsService
{
    /**
     * Get platform overview analytics.
     */
    public function getPlatformOverview(string $period = 'month', string $timezone = 'Africa/Lagos'): array
    {
        $now = Carbon::now($timezone);
        $periodDates = $this->getPeriodDates($period, $now);

        return [
            'overview' => $this->getOverviewStats(),
            'period_stats' => $this->getPeriodStats($periodDates),
            'growth_metrics' => $this->getGrowthMetrics($periodDates),
            'top_performing' => $this->getTopPerforming($periodDates),
            'geographic_distribution' => $this->getGeographicDistribution(),
        ];
    }

    /**
     * Get subscription analytics.
     */
    public function getSubscriptionAnalytics(string $period = 'month'): array
    {
        $now = Carbon::now();
        $periodDates = $this->getPeriodDates($period, $now);

        return [
            'subscription_overview' => $this->getSubscriptionOverview(),
            'plan_distribution' => $this->getPlanDistribution(),
            'churn_analysis' => $this->getChurnAnalysis($periodDates),
            'revenue_by_plan' => $this->getRevenueByPlan($periodDates),
            'upgrade_downgrade_trends' => $this->getUpgradeDowngradeTrends($periodDates),
        ];
    }

    /**
     * Get financial analytics.
     */
    public function getFinancialAnalytics(string $period = 'month', string $currency = 'NGN'): array
    {
        $now = Carbon::now();
        $periodDates = $this->getPeriodDates($period, $now);

        return [
            'revenue_overview' => $this->getRevenueOverview($periodDates, $currency),
            'commission_breakdown' => $this->getCommissionBreakdown($periodDates, $currency),
            'payout_analytics' => $this->getPayoutAnalytics($periodDates, $currency),
            'payment_methods' => $this->getPaymentMethodAnalytics($periodDates),
            'financial_trends' => $this->getFinancialTrends($periodDates, $currency),
        ];
    }

    /**
     * Get usage analytics.
     */
    public function getUsageAnalytics(string $period = 'month'): array
    {
        $now = Carbon::now();
        $periodDates = $this->getPeriodDates($period, $now);

        return [
            'api_usage' => $this->getApiUsageAnalytics($periodDates),
            'feature_usage' => $this->getFeatureUsageAnalytics($periodDates),
            'user_activity' => $this->getUserActivityAnalytics($periodDates),
            'system_performance' => $this->getSystemPerformanceMetrics($periodDates),
        ];
    }

    /**
     * Export analytics data.
     */
    public function exportAnalytics(string $type, string $period = 'month', string $format = 'csv'): array
    {
        $exportData = match ($type) {
            'overview' => $this->getPlatformOverview($period),
            'subscriptions' => $this->getSubscriptionAnalytics($period),
            'financial' => $this->getFinancialAnalytics($period),
            'usage' => $this->getUsageAnalytics($period),
            default => throw new \InvalidArgumentException("Invalid export type: {$type}"),
        };

        $filename = $this->generateExportFile($exportData, $type, $format);

        return [
            'download_url' => url("storage/exports/{$filename}"),
            'filename' => $filename,
            'expires_at' => now()->addHours(24)->toISOString(),
        ];
    }

    /**
     * Get period dates based on period type.
     */
    private function getPeriodDates(string $period, Carbon $now): array
    {
        return match ($period) {
            'today' => [
                'start' => $now->copy()->startOfDay(),
                'end' => $now->copy()->endOfDay(),
            ],
            'week' => [
                'start' => $now->copy()->startOfWeek(),
                'end' => $now->copy()->endOfWeek(),
            ],
            'month' => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
            'quarter' => [
                'start' => $now->copy()->startOfQuarter(),
                'end' => $now->copy()->endOfQuarter(),
            ],
            'year' => [
                'start' => $now->copy()->startOfYear(),
                'end' => $now->copy()->endOfYear(),
            ],
            default => [
                'start' => $now->copy()->startOfMonth(),
                'end' => $now->copy()->endOfMonth(),
            ],
        };
    }

    /**
     * Get overview statistics.
     */
    private function getOverviewStats(): array
    {
        return [
            'total_businesses' => DB::table('businesses')->count(),
            'total_providers' => DB::table('delivery_providers')->count(),
            'total_users' => DB::table('users')->count(),
            'total_orders' => DB::table('orders')->count(),
            'total_deliveries' => DB::table('deliveries')->count(),
            'platform_revenue' => DB::table('payments')
                ->where('status', 'paid')
                ->sum('amount'),
            'active_subscriptions' => DB::table('user_subscriptions')
                ->where('status', 'active')
                ->count(),
        ];
    }

    /**
     * Get period statistics.
     */
    private function getPeriodStats(array $periodDates): array
    {
        $startDate = $periodDates['start'];
        $endDate = $periodDates['end'];

        return [
            'new_businesses' => DB::table('businesses')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count(),
            'new_providers' => DB::table('delivery_providers')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count(),
            'new_users' => DB::table('users')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count(),
            'orders_count' => DB::table('orders')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count(),
            'deliveries_count' => DB::table('deliveries')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count(),
            'revenue' => DB::table('payments')
                ->where('status', 'paid')
                ->whereBetween('created_at', [$startDate, $endDate])
                ->sum('amount'),
        ];
    }

    /**
     * Get growth metrics.
     */
    private function getGrowthMetrics(array $periodDates): array
    {
        $currentStart = $periodDates['start'];
        $currentEnd = $periodDates['end'];
        $periodLength = $currentStart->diffInDays($currentEnd);

        $previousStart = $currentStart->copy()->subDays($periodLength + 1);
        $previousEnd = $currentStart->copy()->subDay();

        $currentStats = $this->getPeriodStats($periodDates);
        $previousStats = $this->getPeriodStats([
            'start' => $previousStart,
            'end' => $previousEnd,
        ]);

        return [
            'business_growth' => $this->calculateGrowthRate(
                $previousStats['new_businesses'],
                $currentStats['new_businesses']
            ),
            'provider_growth' => $this->calculateGrowthRate(
                $previousStats['new_providers'],
                $currentStats['new_providers']
            ),
            'user_growth' => $this->calculateGrowthRate(
                $previousStats['new_users'],
                $currentStats['new_users']
            ),
            'revenue_growth' => $this->calculateGrowthRate(
                $previousStats['revenue'],
                $currentStats['revenue']
            ),
        ];
    }

    /**
     * Calculate growth rate percentage.
     */
    private function calculateGrowthRate(float $previous, float $current): float
    {
        if ($previous == 0) {
            return $current > 0 ? 100.0 : 0.0;
        }

        return round((($current - $previous) / $previous) * 100, 2);
    }

    /**
     * Get top performing entities.
     */
    private function getTopPerforming(array $periodDates): array
    {
        $startDate = $periodDates['start'];
        $endDate = $periodDates['end'];

        return [
            'top_businesses' => DB::table('businesses')
                ->join('orders', 'businesses.id', '=', 'orders.business_id')
                ->join('payments', 'orders.id', '=', 'payments.order_id')
                ->where('payments.status', 'paid')
                ->whereBetween('orders.created_at', [$startDate, $endDate])
                ->select('businesses.business_name', DB::raw('SUM(payments.amount) as revenue'))
                ->groupBy('businesses.id', 'businesses.business_name')
                ->orderByDesc('revenue')
                ->limit(10)
                ->get(),
            'top_providers' => DB::table('delivery_providers')
                ->join('deliveries', 'delivery_providers.id', '=', 'deliveries.delivery_provider_id')
                ->whereBetween('deliveries.created_at', [$startDate, $endDate])
                ->where('deliveries.status', 'delivered')
                ->select('delivery_providers.business_name', DB::raw('COUNT(*) as deliveries_count'))
                ->groupBy('delivery_providers.id', 'delivery_providers.business_name')
                ->orderByDesc('deliveries_count')
                ->limit(10)
                ->get(),
        ];
    }

    /**
     * Get geographic distribution.
     */
    private function getGeographicDistribution(): array
    {
        return [
            'businesses_by_state' => DB::table('businesses')
                ->join('states', 'businesses.state_id', '=', 'states.id')
                ->select('states.name as state_name', DB::raw('COUNT(*) as count'))
                ->groupBy('states.id', 'states.name')
                ->orderByDesc('count')
                ->get(),
            'providers_by_state' => DB::table('delivery_providers')
                ->join('states', 'delivery_providers.state_id', '=', 'states.id')
                ->select('states.name as state_name', DB::raw('COUNT(*) as count'))
                ->groupBy('states.id', 'states.name')
                ->orderByDesc('count')
                ->get(),
        ];
    }

    /**
     * Placeholder methods for complex analytics.
     */
    private function getSubscriptionOverview(): array
    {
        return [
            'total_subscriptions' => DB::table('user_subscriptions')->count(),
            'active_subscriptions' => DB::table('user_subscriptions')
                ->where('status', 'active')
                ->count(),
            'monthly_recurring_revenue' => 0,
        ];
    }

    private function getPlanDistribution(): array
    {
        return DB::table('user_subscriptions')
            ->join('subscription_plans', 'user_subscriptions.plan_id', '=', 'subscription_plans.id')
            ->where('user_subscriptions.status', 'active')
            ->select('subscription_plans.name as plan_name', DB::raw('COUNT(*) as count'))
            ->groupBy('subscription_plans.id', 'subscription_plans.name')
            ->orderByDesc('count')
            ->get()
            ->toArray();
    }

    private function getChurnAnalysis(array $periodDates): array
    {
        return ['churn_rate' => 0, 'cancelled_subscriptions' => 0];
    }

    private function getRevenueByPlan(array $periodDates): array
    {
        return [];
    }

    private function getUpgradeDowngradeTrends(array $periodDates): array
    {
        return ['upgrades' => 0, 'downgrades' => 0];
    }

    private function getRevenueOverview(array $periodDates, string $currency): array
    {
        return ['total_revenue' => 0, 'subscription_revenue' => 0];
    }

    private function getCommissionBreakdown(array $periodDates, string $currency): array
    {
        return ['total_commissions' => 0];
    }

    private function getPayoutAnalytics(array $periodDates, string $currency): array
    {
        return ['total_payouts' => 0];
    }

    private function getPaymentMethodAnalytics(array $periodDates): array
    {
        return ['card' => 0, 'bank_transfer' => 0];
    }

    private function getFinancialTrends(array $periodDates, string $currency): array
    {
        return ['daily_revenue' => []];
    }

    private function getApiUsageAnalytics(array $periodDates): array
    {
        return ['total_requests' => 0];
    }

    private function getFeatureUsageAnalytics(array $periodDates): array
    {
        return ['feature_adoption' => []];
    }

    private function getUserActivityAnalytics(array $periodDates): array
    {
        return ['active_users' => 0];
    }

    private function getSystemPerformanceMetrics(array $periodDates): array
    {
        return ['avg_response_time' => 0];
    }

    private function generateExportFile(array $data, string $type, string $format): string
    {
        return "analytics_{$type}_{$format}_".now()->format('Y-m-d_H-i-s').".{$format}";
    }
}
