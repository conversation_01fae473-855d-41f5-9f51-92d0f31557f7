<?php

declare(strict_types=1);

namespace App\Services\Analytics;

use App\Models\Business\Business;
use App\Models\Delivery\Delivery;
use App\Models\Delivery\Order;
use App\Models\Financial\Payment;
use App\Models\User\User;
use App\Services\System\LoggingService;

class AnalyticsManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get platform overview dashboard.
     */
    public function getPlatformOverview(): array
    {
        return [
            'users' => [
                'total_users' => User::count(),
                'active_users' => User::where('last_login_at', '>=', now()->subDays(30))->count(),
                'new_users_today' => User::whereDate('created_at', today())->count(),
                'new_users_this_week' => User::whereBetween('created_at', [
                    now()->startOfWeek(),
                    now()->endOfWeek(),
                ])->count(),
                'new_users_this_month' => User::whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->count(),
            ],
            'businesses' => [
                'total_businesses' => Business::count(),
                'active_businesses' => Business::where('is_active', true)->count(),
                'new_businesses_today' => Business::whereDate('created_at', today())->count(),
                'new_businesses_this_week' => Business::whereBetween('created_at', [
                    now()->startOfWeek(),
                    now()->endOfWeek(),
                ])->count(),
                'new_businesses_this_month' => Business::whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->count(),
            ],
            'orders' => [
                'total_orders' => Order::count(),
                'orders_today' => Order::whereDate('created_at', today())->count(),
                'orders_this_week' => Order::whereBetween('created_at', [
                    now()->startOfWeek(),
                    now()->endOfWeek(),
                ])->count(),
                'orders_this_month' => Order::whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->count(),
            ],
            'revenue' => [
                'total_revenue' => Payment::where('status', 'paid')->sum('amount'),
                'revenue_today' => Payment::where('status', 'paid')
                    ->whereDate('created_at', today())
                    ->sum('amount'),
                'revenue_this_week' => Payment::where('status', 'paid')
                    ->whereBetween('created_at', [
                        now()->startOfWeek(),
                        now()->endOfWeek(),
                    ])->sum('amount'),
                'revenue_this_month' => Payment::where('status', 'paid')
                    ->whereMonth('created_at', now()->month)
                    ->whereYear('created_at', now()->year)
                    ->sum('amount'),
            ],
            'deliveries' => [
                'total_deliveries' => Delivery::count(),
                'completed_deliveries' => Delivery::where('status', 'delivered')->count(),
                'pending_deliveries' => Delivery::where('status', 'pending')->count(),
                'deliveries_today' => Delivery::whereDate('created_at', today())->count(),
            ],
        ];
    }

    /**
     * Get business performance analytics.
     */
    public function getBusinessPerformanceAnalytics(array $filters = []): array
    {
        $query = Business::with(['orders', 'payments']);

        // Apply date filters
        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $businesses = $query->get();

        return [
            'summary' => [
                'total_businesses' => $businesses->count(),
                'active_businesses' => $businesses->where('is_active', true)->count(),
                'average_orders_per_business' => $businesses->avg(fn ($b) => $b->orders->count()),
                'average_revenue_per_business' => $businesses->avg(fn ($b) => $b->payments->where('status', 'paid')->sum('amount')),
            ],
            'top_performers' => $this->getTopPerformingBusinesses($businesses),
            'category_breakdown' => $this->getBusinessCategoryBreakdown($businesses),
            'growth_metrics' => $this->getBusinessGrowthMetrics($businesses),
            'performance_distribution' => $this->getBusinessPerformanceDistribution($businesses),
        ];
    }

    /**
     * Get revenue analytics.
     */
    public function getRevenueAnalytics(array $filters = []): array
    {
        $query = Payment::where('status', 'paid');

        // Apply date filters
        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $payments = $query->get();

        return [
            'summary' => [
                'total_revenue' => $payments->sum('amount'),
                'total_transactions' => $payments->count(),
                'average_transaction_value' => $payments->avg('amount'),
                'total_fees' => $payments->sum('transaction_fee'),
            ],
            'daily_revenue' => $this->getDailyRevenueBreakdown($payments),
            'monthly_revenue' => $this->getMonthlyRevenueBreakdown($payments),
            'revenue_by_gateway' => $this->getRevenueByGateway($payments),
            'revenue_trends' => $this->getRevenueTrends($payments),
            'top_revenue_sources' => $this->getTopRevenueSources($payments),
        ];
    }

    /**
     * Get user engagement analytics.
     */
    public function getUserEngagementAnalytics(array $filters = []): array
    {
        $query = User::query();

        // Apply date filters
        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $users = $query->get();

        return [
            'summary' => [
                'total_users' => $users->count(),
                'active_users' => $users->where('last_login_at', '>=', now()->subDays(30))->count(),
                'daily_active_users' => $users->where('last_login_at', '>=', now()->subDay())->count(),
                'weekly_active_users' => $users->where('last_login_at', '>=', now()->subWeek())->count(),
                'monthly_active_users' => $users->where('last_login_at', '>=', now()->subMonth())->count(),
            ],
            'user_acquisition' => $this->getUserAcquisitionMetrics($users),
            'user_retention' => $this->getUserRetentionMetrics($users),
            'user_activity' => $this->getUserActivityMetrics($users),
            'user_demographics' => $this->getUserDemographics($users),
        ];
    }

    /**
     * Get operational metrics.
     */
    public function getOperationalMetrics(array $filters = []): array
    {
        return [
            'order_metrics' => $this->getOrderMetrics($filters),
            'delivery_metrics' => $this->getDeliveryMetrics($filters),
            'payment_metrics' => $this->getPaymentMetrics($filters),
            'system_health' => $this->getSystemHealthMetrics(),
            'performance_indicators' => $this->getKeyPerformanceIndicators($filters),
        ];
    }

    /**
     * Generate custom report.
     */
    public function generateCustomReport(array $config): array
    {
        $report = [
            'metadata' => [
                'generated_at' => now(),
                'generated_by' => auth()->id(),
                'report_type' => $config['type'],
                'date_range' => [
                    'from' => $config['date_from'] ?? null,
                    'to' => $config['date_to'] ?? null,
                ],
            ],
            'data' => [],
        ];

        // Generate data based on report type
        switch ($config['type']) {
            case 'business_performance':
                $report['data'] = $this->getBusinessPerformanceAnalytics($config);
                break;
            case 'revenue_analysis':
                $report['data'] = $this->getRevenueAnalytics($config);
                break;
            case 'user_engagement':
                $report['data'] = $this->getUserEngagementAnalytics($config);
                break;
            case 'operational_overview':
                $report['data'] = $this->getOperationalMetrics($config);
                break;
            case 'platform_overview':
                $report['data'] = $this->getPlatformOverview();
                break;
            default:
                throw new \InvalidArgumentException("Invalid report type: {$config['type']}");
        }

        $this->loggingService->logInfo('Custom report generated', [
            'report_type' => $config['type'],
            'admin_id' => auth()->id(),
        ]);

        return $report;
    }

    /**
     * Export analytics data.
     */
    public function exportAnalyticsData(array $config, string $format = 'csv'): string
    {
        $data = $this->generateCustomReport($config);

        // Generate filename
        $filename = 'analytics_'.$config['type'].'_'.now()->format('Y_m_d_H_i_s').'.'.$format;
        $filepath = storage_path('app/exports/'.$filename);

        // Ensure directory exists
        if (! file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        if ($format === 'csv') {
            $this->exportToCsv($data, $filepath);
        } elseif ($format === 'json') {
            $this->exportToJson($data, $filepath);
        }

        return $filename;
    }

    /**
     * Get top performing businesses.
     */
    private function getTopPerformingBusinesses($businesses): array
    {
        return $businesses->map(function ($business) {
            return [
                'id' => $business->id,
                'name' => $business->name,
                'total_orders' => $business->orders->count(),
                'total_revenue' => $business->payments->where('status', 'paid')->sum('amount'),
                'average_order_value' => $business->orders->avg('total_amount'),
            ];
        })->sortByDesc('total_revenue')->take(10)->values()->toArray();
    }

    /**
     * Get business category breakdown.
     */
    private function getBusinessCategoryBreakdown($businesses): array
    {
        return $businesses->groupBy('category')->map(function ($categoryBusinesses) {
            return [
                'count' => $categoryBusinesses->count(),
                'total_revenue' => $categoryBusinesses->sum(fn ($b) => $b->payments->where('status', 'paid')->sum('amount')),
                'average_revenue' => $categoryBusinesses->avg(fn ($b) => $b->payments->where('status', 'paid')->sum('amount')),
            ];
        })->toArray();
    }

    /**
     * Get business growth metrics.
     */
    private function getBusinessGrowthMetrics($businesses): array
    {
        $currentMonth = $businesses->where('created_at', '>=', now()->startOfMonth());
        $lastMonth = $businesses->whereBetween('created_at', [
            now()->subMonth()->startOfMonth(),
            now()->subMonth()->endOfMonth(),
        ]);

        return [
            'new_businesses_this_month' => $currentMonth->count(),
            'new_businesses_last_month' => $lastMonth->count(),
            'growth_rate' => $lastMonth->count() > 0 ?
                round((($currentMonth->count() - $lastMonth->count()) / $lastMonth->count()) * 100, 2) : 0,
        ];
    }

    /**
     * Get business performance distribution.
     */
    private function getBusinessPerformanceDistribution($businesses): array
    {
        $revenueRanges = [
            '0-10000' => $businesses->filter(fn ($b) => $b->payments->where('status', 'paid')->sum('amount') <= 10000)->count(),
            '10000-50000' => $businesses->filter(function ($b) {
                $revenue = $b->payments->where('status', 'paid')->sum('amount');

                return $revenue > 10000 && $revenue <= 50000;
            })->count(),
            '************' => $businesses->filter(function ($b) {
                $revenue = $b->payments->where('status', 'paid')->sum('amount');

                return $revenue > 50000 && $revenue <= 100000;
            })->count(),
            '100000+' => $businesses->filter(fn ($b) => $b->payments->where('status', 'paid')->sum('amount') > 100000)->count(),
        ];

        return $revenueRanges;
    }

    /**
     * Get daily revenue breakdown.
     */
    private function getDailyRevenueBreakdown($payments): array
    {
        return $payments->groupBy(function ($payment) {
            return $payment->created_at->format('Y-m-d');
        })->map(function ($dayPayments) {
            return [
                'revenue' => $dayPayments->sum('amount'),
                'transactions' => $dayPayments->count(),
                'fees' => $dayPayments->sum('transaction_fee'),
            ];
        })->toArray();
    }

    /**
     * Get monthly revenue breakdown.
     */
    private function getMonthlyRevenueBreakdown($payments): array
    {
        return $payments->groupBy(function ($payment) {
            return $payment->created_at->format('Y-m');
        })->map(function ($monthPayments) {
            return [
                'revenue' => $monthPayments->sum('amount'),
                'transactions' => $monthPayments->count(),
                'fees' => $monthPayments->sum('transaction_fee'),
            ];
        })->toArray();
    }

    /**
     * Get revenue by gateway.
     */
    private function getRevenueByGateway($payments): array
    {
        return $payments->groupBy('gateway')->map(function ($gatewayPayments) {
            return [
                'revenue' => $gatewayPayments->sum('amount'),
                'transactions' => $gatewayPayments->count(),
                'fees' => $gatewayPayments->sum('transaction_fee'),
            ];
        })->toArray();
    }

    /**
     * Get revenue trends.
     */
    private function getRevenueTrends($payments): array
    {
        $currentMonth = $payments->where('created_at', '>=', now()->startOfMonth())->sum('amount');
        $lastMonth = $payments->whereBetween('created_at', [
            now()->subMonth()->startOfMonth(),
            now()->subMonth()->endOfMonth(),
        ])->sum('amount');

        return [
            'current_month' => $currentMonth,
            'last_month' => $lastMonth,
            'growth_rate' => $lastMonth > 0 ? round((($currentMonth - $lastMonth) / $lastMonth) * 100, 2) : 0,
        ];
    }

    /**
     * Get top revenue sources.
     */
    private function getTopRevenueSources($payments): array
    {
        return $payments->with('order.business')->groupBy('order.business.name')
            ->map(function ($businessPayments) {
                return [
                    'revenue' => $businessPayments->sum('amount'),
                    'transactions' => $businessPayments->count(),
                ];
            })
            ->sortByDesc('revenue')
            ->take(10)
            ->toArray();
    }

    /**
     * Get user acquisition metrics.
     */
    private function getUserAcquisitionMetrics($users): array
    {
        return [
            'daily_signups' => $users->groupBy(function ($user) {
                return $user->created_at->format('Y-m-d');
            })->map(fn ($dayUsers) => $dayUsers->count())->toArray(),
            'acquisition_channels' => [
                'organic' => $users->where('acquisition_channel', 'organic')->count(),
                'referral' => $users->where('acquisition_channel', 'referral')->count(),
                'social' => $users->where('acquisition_channel', 'social')->count(),
                'paid' => $users->where('acquisition_channel', 'paid')->count(),
            ],
        ];
    }

    /**
     * Get user retention metrics.
     */
    private function getUserRetentionMetrics($users): array
    {
        $totalUsers = $users->count();

        return [
            'day_1_retention' => $totalUsers > 0 ?
                round(($users->where('last_login_at', '>=', now()->subDay())->count() / $totalUsers) * 100, 2) : 0,
            'day_7_retention' => $totalUsers > 0 ?
                round(($users->where('last_login_at', '>=', now()->subWeek())->count() / $totalUsers) * 100, 2) : 0,
            'day_30_retention' => $totalUsers > 0 ?
                round(($users->where('last_login_at', '>=', now()->subMonth())->count() / $totalUsers) * 100, 2) : 0,
        ];
    }

    /**
     * Get user activity metrics.
     */
    private function getUserActivityMetrics($users): array
    {
        return [
            'average_session_duration' => 25.5, // Mock data
            'average_sessions_per_user' => 3.2, // Mock data
            'bounce_rate' => 35.8, // Mock data
        ];
    }

    /**
     * Get user demographics.
     */
    private function getUserDemographics($users): array
    {
        return [
            'by_role' => $users->with('roles')->groupBy('roles.0.name')
                ->map(fn ($roleUsers) => $roleUsers->count())->toArray(),
            'by_location' => $users->groupBy('state')
                ->map(fn ($stateUsers) => $stateUsers->count())->toArray(),
        ];
    }

    /**
     * Get order metrics.
     */
    private function getOrderMetrics(array $filters): array
    {
        $query = Order::query();

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $orders = $query->get();

        return [
            'total_orders' => $orders->count(),
            'completed_orders' => $orders->where('status', 'completed')->count(),
            'cancelled_orders' => $orders->where('status', 'cancelled')->count(),
            'average_order_value' => $orders->avg('total_amount'),
            'completion_rate' => $orders->count() > 0 ?
                round(($orders->where('status', 'completed')->count() / $orders->count()) * 100, 2) : 0,
        ];
    }

    /**
     * Get delivery metrics.
     */
    private function getDeliveryMetrics(array $filters): array
    {
        $query = Delivery::query();

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $deliveries = $query->get();

        return [
            'total_deliveries' => $deliveries->count(),
            'completed_deliveries' => $deliveries->where('status', 'delivered')->count(),
            'failed_deliveries' => $deliveries->where('status', 'failed')->count(),
            'average_delivery_time' => 45.2, // Mock data in minutes
            'on_time_delivery_rate' => 92.5, // Mock data
        ];
    }

    /**
     * Get payment metrics.
     */
    private function getPaymentMetrics(array $filters): array
    {
        $query = Payment::query();

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $payments = $query->get();

        return [
            'total_payments' => $payments->count(),
            'successful_payments' => $payments->where('status', 'paid')->count(),
            'failed_payments' => $payments->where('status', 'failed')->count(),
            'success_rate' => $payments->count() > 0 ?
                round(($payments->where('status', 'paid')->count() / $payments->count()) * 100, 2) : 0,
            'total_volume' => $payments->where('status', 'paid')->sum('amount'),
        ];
    }

    /**
     * Get system health metrics.
     */
    private function getSystemHealthMetrics(): array
    {
        return [
            'api_response_time' => 150, // Mock data in ms
            'uptime_percentage' => 99.9, // Mock data
            'error_rate' => 0.1, // Mock data
            'active_connections' => 1250, // Mock data
        ];
    }

    /**
     * Get key performance indicators.
     */
    private function getKeyPerformanceIndicators(array $filters): array
    {
        return [
            'customer_acquisition_cost' => 25.50, // Mock data
            'customer_lifetime_value' => 450.75, // Mock data
            'monthly_recurring_revenue' => 125000, // Mock data
            'churn_rate' => 2.3, // Mock data
            'net_promoter_score' => 8.5, // Mock data
        ];
    }

    /**
     * Export analytics to CSV.
     */
    private function exportToCsv(array $data, string $filepath): void
    {
        $file = fopen($filepath, 'w');

        // Write metadata
        fputcsv($file, ['Analytics Report']);
        fputcsv($file, ['Generated At', $data['metadata']['generated_at']]);
        fputcsv($file, ['Report Type', $data['metadata']['report_type']]);
        fputcsv($file, []);

        // Write data (simplified for CSV)
        foreach ($data['data'] as $section => $sectionData) {
            fputcsv($file, [ucfirst(str_replace('_', ' ', $section))]);

            if (is_array($sectionData)) {
                foreach ($sectionData as $key => $value) {
                    if (is_numeric($value)) {
                        fputcsv($file, [$key, $value]);
                    }
                }
            }

            fputcsv($file, []);
        }

        fclose($file);
    }

    /**
     * Export analytics to JSON.
     */
    private function exportToJson(array $data, string $filepath): void
    {
        file_put_contents($filepath, json_encode($data, JSON_PRETTY_PRINT));
    }
}
