<?php

declare(strict_types=1);

namespace App\Services\Financial;

use App\Enums\Financial\PaymentGateway;
use App\Enums\Financial\PaymentStatus;
use App\Models\Financial\Dispute;
use App\Models\Financial\Payment;
use App\Models\Financial\Payout;
use App\Services\System\LoggingService;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class PaymentManagementService
{
    public function __construct(
        private readonly LoggingService $loggingService
    ) {}

    /**
     * Get comprehensive payment statistics.
     */
    public function getPaymentStatistics(): array
    {
        return [
            'total_payments' => Payment::count(),
            'payments_by_status' => Payment::selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status'),
            'payments_by_gateway' => Payment::selectRaw('gateway, COUNT(*) as count')
                ->groupBy('gateway')
                ->pluck('count', 'gateway'),
            'total_revenue' => Payment::where('status', PaymentStatus::PAID)->sum('amount'),
            'total_fees' => Payment::where('status', PaymentStatus::PAID)->sum('transaction_fee'),
            'average_payment_amount' => Payment::where('status', PaymentStatus::PAID)->avg('amount'),
            'payments_today' => Payment::whereDate('created_at', today())->count(),
            'payments_this_week' => Payment::whereBetween('created_at', [
                now()->startOfWeek(),
                now()->endOfWeek(),
            ])->count(),
            'payments_this_month' => Payment::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
            'failed_payments' => Payment::where('status', PaymentStatus::FAILED)->count(),
            'pending_payments' => Payment::where('status', PaymentStatus::PENDING)->count(),
            'refund_rate' => $this->calculateRefundRate(),
        ];
    }

    /**
     * Get payment analytics.
     */
    public function getPaymentAnalytics(array $filters = []): array
    {
        $query = Payment::query();

        // Apply date filters
        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $payments = $query->get();

        return [
            'total_payments' => $payments->count(),
            'total_revenue' => $payments->where('status', PaymentStatus::PAID)->sum('amount'),
            'total_fees' => $payments->where('status', PaymentStatus::PAID)->sum('transaction_fee'),
            'success_rate' => $this->calculateSuccessRate($payments),
            'failure_rate' => $this->calculateFailureRate($payments),
            'average_payment_amount' => $payments->where('status', PaymentStatus::PAID)->avg('amount'),
            'daily_breakdown' => $this->getDailyPaymentBreakdown($payments),
            'hourly_distribution' => $this->getHourlyPaymentDistribution($payments),
            'gateway_performance' => $this->getGatewayPerformanceBreakdown($payments),
            'payment_method_breakdown' => $this->getPaymentMethodBreakdown($payments),
        ];
    }

    /**
     * Get payments requiring attention.
     */
    public function getPaymentsRequiringAttention(): array
    {
        return [
            'failed_payments' => Payment::where('status', PaymentStatus::FAILED)
                ->where('created_at', '>=', now()->subDays(7))
                ->with(['order:id,order_reference', 'user:id,first_name,last_name,email'])
                ->limit(20)
                ->get(),
            'long_pending' => Payment::where('status', PaymentStatus::PENDING)
                ->where('created_at', '<', now()->subHours(2))
                ->with(['order:id,order_reference', 'user:id,first_name,last_name,email'])
                ->limit(20)
                ->get(),
            'high_value_payments' => Payment::where('amount', '>', 100000)
                ->where('status', PaymentStatus::PENDING)
                ->with(['order:id,order_reference', 'user:id,first_name,last_name,email'])
                ->orderByDesc('amount')
                ->limit(10)
                ->get(),
            'suspicious_payments' => $this->getSuspiciousPayments(),
        ];
    }

    /**
     * Get payment fraud analysis.
     */
    public function getFraudAnalysis(): array
    {
        return [
            'suspicious_patterns' => $this->detectSuspiciousPatterns(),
            'high_risk_payments' => $this->getHighRiskPayments(),
            'fraud_indicators' => $this->getFraudIndicators(),
            'blocked_payments' => Payment::where('status', PaymentStatus::BLOCKED)->count(),
            'chargeback_rate' => $this->calculateChargebackRate(),
        ];
    }

    /**
     * Process payment refund.
     */
    public function processRefund(string $paymentId, float $amount, string $reason): array
    {
        return DB::transaction(function () use ($paymentId, $amount, $reason) {
            $payment = Payment::findOrFail($paymentId);

            if ($payment->status !== PaymentStatus::PAID) {
                throw new \InvalidArgumentException('Can only refund paid payments');
            }

            if ($amount > $payment->amount) {
                throw new \InvalidArgumentException('Refund amount cannot exceed payment amount');
            }

            // Create refund record
            $refund = Payout::create([
                'payoutable_type' => Payment::class,
                'payoutable_id' => $payment->id,
                'amount' => $amount,
                'currency' => $payment->currency,
                'type' => 'refund',
                'status' => 'processed',
                'reason' => $reason,
                'processed_by' => auth()->id(),
                'processed_at' => now(),
            ]);

            // Update payment status if full refund
            if ($amount >= $payment->amount) {
                $payment->update(['status' => PaymentStatus::REFUNDED]);
            }

            $this->loggingService->logInfo('Payment refund processed', [
                'payment_id' => $payment->id,
                'refund_amount' => $amount,
                'reason' => $reason,
                'admin_id' => auth()->id(),
            ]);

            return [
                'refund' => $refund,
                'payment' => $payment->fresh(),
                'refund_processed' => true,
            ];
        });
    }

    /**
     * Bulk update payment status.
     */
    public function bulkUpdatePaymentStatus(array $paymentIds, PaymentStatus $status, ?string $reason = null): array
    {
        $results = ['updated' => 0, 'failed' => 0, 'details' => []];

        DB::transaction(function () use ($paymentIds, $status, $reason, &$results) {
            foreach ($paymentIds as $paymentId) {
                try {
                    $payment = Payment::findOrFail($paymentId);

                    // Validate status transition
                    if (! $this->isValidStatusTransition($payment->status, $status)) {
                        $results['failed']++;
                        $results['details'][$paymentId] = 'Invalid status transition';

                        continue;
                    }

                    $oldStatus = $payment->status;
                    $metadata = $payment->metadata ?? [];

                    if ($reason) {
                        $metadata['admin_update_reason'] = $reason;
                    }

                    $payment->update([
                        'status' => $status,
                        'metadata' => $metadata,
                    ]);

                    $results['updated']++;
                    $results['details'][$paymentId] = 'updated';

                    $this->loggingService->logInfo('Payment status updated via bulk operation', [
                        'payment_id' => $payment->id,
                        'old_status' => $oldStatus->value,
                        'new_status' => $status->value,
                        'admin_id' => auth()->id(),
                    ]);

                } catch (\Exception $e) {
                    $results['failed']++;
                    $results['details'][$paymentId] = 'error: '.$e->getMessage();
                }
            }
        });

        return $results;
    }

    /**
     * Get gateway performance metrics.
     */
    public function getGatewayPerformance(): array
    {
        $gateways = PaymentGateway::cases();
        $performance = [];

        foreach ($gateways as $gateway) {
            $payments = Payment::where('gateway', $gateway)
                ->where('created_at', '>=', now()->subDays(30))
                ->get();

            $performance[] = [
                'gateway' => $gateway->value,
                'total_payments' => $payments->count(),
                'successful_payments' => $payments->where('status', PaymentStatus::PAID)->count(),
                'failed_payments' => $payments->where('status', PaymentStatus::FAILED)->count(),
                'success_rate' => $this->calculateSuccessRate($payments),
                'total_volume' => $payments->where('status', PaymentStatus::PAID)->sum('amount'),
                'average_processing_time' => $this->calculateAverageProcessingTime($payments),
            ];
        }

        return $performance;
    }

    /**
     * Export payments data.
     */
    public function exportPayments(array $filters = [], string $format = 'csv'): string
    {
        $query = Payment::with([
            'order:id,order_reference',
            'user:id,first_name,last_name,email',
            'business:id,name',
        ]);

        // Apply filters
        if (isset($filters['status'])) {
            $query->where('status', $filters['status']);
        }

        if (isset($filters['gateway'])) {
            $query->where('gateway', $filters['gateway']);
        }

        if (isset($filters['date_from'])) {
            $query->where('created_at', '>=', $filters['date_from']);
        }

        if (isset($filters['date_to'])) {
            $query->where('created_at', '<=', $filters['date_to']);
        }

        $payments = $query->orderBy('created_at', 'desc')->get();

        // Generate filename
        $filename = 'payments_export_'.now()->format('Y_m_d_H_i_s').'.'.$format;
        $filepath = storage_path('app/exports/'.$filename);

        // Ensure directory exists
        if (! file_exists(dirname($filepath))) {
            mkdir(dirname($filepath), 0755, true);
        }

        if ($format === 'csv') {
            $this->exportToCsv($payments, $filepath);
        } elseif ($format === 'json') {
            $this->exportToJson($payments, $filepath);
        }

        return $filename;
    }

    /**
     * Calculate refund rate.
     */
    private function calculateRefundRate(): float
    {
        $totalPaid = Payment::where('status', PaymentStatus::PAID)->count();
        if ($totalPaid === 0) {
            return 0;
        }

        $refunded = Payment::where('status', PaymentStatus::REFUNDED)->count();

        return round(($refunded / $totalPaid) * 100, 2);
    }

    /**
     * Calculate success rate for collection.
     */
    private function calculateSuccessRate(Collection $payments): float
    {
        $total = $payments->count();
        if ($total === 0) {
            return 0;
        }

        $successful = $payments->where('status', PaymentStatus::PAID)->count();

        return round(($successful / $total) * 100, 2);
    }

    /**
     * Calculate failure rate for collection.
     */
    private function calculateFailureRate(Collection $payments): float
    {
        $total = $payments->count();
        if ($total === 0) {
            return 0;
        }

        $failed = $payments->where('status', PaymentStatus::FAILED)->count();

        return round(($failed / $total) * 100, 2);
    }

    /**
     * Get daily payment breakdown.
     */
    private function getDailyPaymentBreakdown(Collection $payments): array
    {
        return $payments->groupBy(function ($payment) {
            return $payment->created_at->format('Y-m-d');
        })->map(function ($dayPayments) {
            return [
                'count' => $dayPayments->count(),
                'revenue' => $dayPayments->where('status', PaymentStatus::PAID)->sum('amount'),
                'fees' => $dayPayments->where('status', PaymentStatus::PAID)->sum('transaction_fee'),
                'success_rate' => $this->calculateSuccessRate($dayPayments),
            ];
        })->toArray();
    }

    /**
     * Get hourly payment distribution.
     */
    private function getHourlyPaymentDistribution(Collection $payments): array
    {
        return $payments->groupBy(function ($payment) {
            return $payment->created_at->format('H');
        })->map(fn ($hourPayments) => $hourPayments->count())->toArray();
    }

    /**
     * Get gateway performance breakdown.
     */
    private function getGatewayPerformanceBreakdown(Collection $payments): array
    {
        return $payments->groupBy('gateway')->map(function ($gatewayPayments) {
            return [
                'total_payments' => $gatewayPayments->count(),
                'successful_payments' => $gatewayPayments->where('status', PaymentStatus::PAID)->count(),
                'success_rate' => $this->calculateSuccessRate($gatewayPayments),
                'total_volume' => $gatewayPayments->where('status', PaymentStatus::PAID)->sum('amount'),
            ];
        })->toArray();
    }

    /**
     * Get payment method breakdown.
     */
    private function getPaymentMethodBreakdown(Collection $payments): array
    {
        return $payments->with('paymentMethod')->groupBy('payment_method_id')->map(function ($methodPayments) {
            $method = $methodPayments->first()->paymentMethod;

            return [
                'method_type' => $method?->type->value ?? 'unknown',
                'count' => $methodPayments->count(),
                'volume' => $methodPayments->where('status', PaymentStatus::PAID)->sum('amount'),
            ];
        })->values()->toArray();
    }

    /**
     * Get suspicious payments.
     */
    private function getSuspiciousPayments(): Collection
    {
        return Payment::where(function ($query) {
            // Multiple failed attempts from same user
            $query->whereIn('user_id', function ($subQuery) {
                $subQuery->select('user_id')
                    ->from('payments')
                    ->where('status', PaymentStatus::FAILED)
                    ->where('created_at', '>=', now()->subHours(24))
                    ->groupBy('user_id')
                    ->havingRaw('COUNT(*) > 3');
            })
            // Or unusually high amounts
                ->orWhere('amount', '>', 500000);
        })
            ->with(['user:id,first_name,last_name,email', 'order:id,order_reference'])
            ->orderByDesc('created_at')
            ->limit(20)
            ->get();
    }

    /**
     * Detect suspicious patterns.
     */
    private function detectSuspiciousPatterns(): array
    {
        return [
            'rapid_fire_attempts' => Payment::where('created_at', '>=', now()->subMinutes(5))
                ->groupBy('user_id')
                ->havingRaw('COUNT(*) > 5')
                ->count(),
            'high_failure_rate_users' => Payment::where('created_at', '>=', now()->subDays(7))
                ->selectRaw('user_id, COUNT(*) as total, SUM(CASE WHEN status = ? THEN 1 ELSE 0 END) as failed', [PaymentStatus::FAILED->value])
                ->groupBy('user_id')
                ->havingRaw('failed / total > 0.8')
                ->count(),
        ];
    }

    /**
     * Get high risk payments.
     */
    private function getHighRiskPayments(): Collection
    {
        return Payment::where('amount', '>', 200000)
            ->where('status', PaymentStatus::PENDING)
            ->with(['user:id,first_name,last_name,email', 'order:id,order_reference'])
            ->orderByDesc('amount')
            ->limit(10)
            ->get();
    }

    /**
     * Get fraud indicators.
     */
    private function getFraudIndicators(): array
    {
        return [
            'velocity_checks' => [
                'payments_last_hour' => Payment::where('created_at', '>=', now()->subHour())->count(),
                'payments_last_day' => Payment::where('created_at', '>=', now()->subDay())->count(),
            ],
            'failure_patterns' => [
                'consecutive_failures' => $this->getConsecutiveFailures(),
                'geographic_anomalies' => $this->getGeographicAnomalies(),
            ],
        ];
    }

    /**
     * Calculate chargeback rate.
     */
    private function calculateChargebackRate(): float
    {
        // This would integrate with actual chargeback data
        // For now, return a mock rate
        return 0.5; // 0.5% chargeback rate
    }

    /**
     * Calculate average processing time.
     */
    private function calculateAverageProcessingTime(Collection $payments): float
    {
        $processed = $payments->where('status', PaymentStatus::PAID)->whereNotNull('paid_at');

        if ($processed->isEmpty()) {
            return 0;
        }

        $totalSeconds = $processed->sum(function ($payment) {
            return $payment->created_at->diffInSeconds($payment->paid_at);
        });

        return round($totalSeconds / $processed->count(), 2);
    }

    /**
     * Check if status transition is valid.
     */
    private function isValidStatusTransition(PaymentStatus $from, PaymentStatus $to): bool
    {
        $validTransitions = [
            PaymentStatus::PENDING->value => [
                PaymentStatus::PAID->value,
                PaymentStatus::FAILED->value,
                PaymentStatus::CANCELLED->value,
            ],
            PaymentStatus::PAID->value => [
                PaymentStatus::REFUNDED->value,
                PaymentStatus::DISPUTED->value,
            ],
            PaymentStatus::FAILED->value => [
                PaymentStatus::PENDING->value, // Retry
            ],
        ];

        return in_array($to->value, $validTransitions[$from->value] ?? []);
    }

    /**
     * Get consecutive failures.
     */
    private function getConsecutiveFailures(): int
    {
        // This would implement actual consecutive failure detection
        return Payment::where('status', PaymentStatus::FAILED)
            ->where('created_at', '>=', now()->subHours(1))
            ->count();
    }

    /**
     * Get geographic anomalies.
     */
    private function getGeographicAnomalies(): int
    {
        // This would implement geographic anomaly detection
        // For now, return a mock count
        return 0;
    }

    /**
     * Export payments to CSV.
     */
    private function exportToCsv(Collection $payments, string $filepath): void
    {
        $file = fopen($filepath, 'w');

        // Write header
        fputcsv($file, [
            'Payment ID', 'Order Reference', 'User', 'Business', 'Amount',
            'Currency', 'Gateway', 'Status', 'Transaction Fee', 'Created At', 'Paid At',
        ]);

        // Write data
        foreach ($payments as $payment) {
            fputcsv($file, [
                $payment->id,
                $payment->order?->order_reference ?? 'N/A',
                $payment->user ? $payment->user->first_name.' '.$payment->user->last_name : 'N/A',
                $payment->business?->name ?? 'N/A',
                $payment->amount,
                $payment->currency,
                $payment->gateway->value,
                $payment->status->value,
                $payment->transaction_fee,
                $payment->created_at->toISOString(),
                $payment->paid_at?->toISOString() ?? 'N/A',
            ]);
        }

        fclose($file);
    }

    /**
     * Export payments to JSON.
     */
    private function exportToJson(Collection $payments, string $filepath): void
    {
        $data = $payments->map(function ($payment) {
            return [
                'payment_id' => $payment->id,
                'order_reference' => $payment->order?->order_reference,
                'user' => $payment->user ? [
                    'name' => $payment->user->first_name.' '.$payment->user->last_name,
                    'email' => $payment->user->email,
                ] : null,
                'business' => $payment->business?->name,
                'amount' => $payment->amount,
                'currency' => $payment->currency,
                'gateway' => $payment->gateway->value,
                'status' => $payment->status->value,
                'transaction_fee' => $payment->transaction_fee,
                'created_at' => $payment->created_at->toISOString(),
                'paid_at' => $payment->paid_at?->toISOString(),
            ];
        });

        file_put_contents($filepath, json_encode($data, JSON_PRETTY_PRINT));
    }

    /**
     * Get payment with detailed information.
     */
    public function getPaymentWithDetails(string $paymentId): Payment
    {
        return Payment::with([
            'order:id,order_reference,total_amount,currency',
            'user:id,first_name,last_name,email,phone_number',
            'business:id,name,tenant_id',
            'paymentMethod:id,type,provider',
            'refunds',
        ])->findOrFail($paymentId);
    }

    /**
     * Verify payment with gateway.
     */
    public function verifyPaymentWithGateway(string $paymentId): array
    {
        $payment = Payment::findOrFail($paymentId);

        // This would integrate with actual payment gateway verification
        // For now, return mock verification data
        return [
            'payment' => $payment,
            'gateway_status' => 'verified',
            'gateway_response' => [
                'status' => 'success',
                'reference' => $payment->gateway_reference,
                'amount' => $payment->amount,
                'currency' => $payment->currency,
                'verified_at' => now(),
            ],
            'discrepancies' => [],
        ];
    }

    /**
     * Mark payment as disputed.
     */
    public function markAsDisputed(string $paymentId, string $disputeReason, ?float $disputeAmount = null, ?string $notes = null): Payment
    {
        return DB::transaction(function () use ($paymentId, $disputeReason, $disputeAmount, $notes) {
            $payment = Payment::findOrFail($paymentId);

            if ($payment->status === PaymentStatus::DISPUTED) {
                throw new \InvalidArgumentException('Payment is already disputed');
            }

            $metadata = $payment->metadata ?? [];
            $metadata['dispute'] = [
                'reason' => $disputeReason,
                'amount' => $disputeAmount ?? $payment->amount,
                'notes' => $notes,
                'disputed_at' => now(),
                'disputed_by' => auth()->id(),
            ];

            $payment->update([
                'status' => PaymentStatus::DISPUTED,
                'metadata' => $metadata,
            ]);

            // Create dispute record if Dispute model exists
            if (class_exists(Dispute::class)) {
                Dispute::create([
                    'payment_id' => $payment->id,
                    'reason' => $disputeReason,
                    'amount' => $disputeAmount ?? $payment->amount,
                    'status' => 'open',
                    'notes' => $notes,
                    'created_by' => auth()->id(),
                ]);
            }

            $this->loggingService->logInfo('Payment marked as disputed', [
                'payment_id' => $payment->id,
                'dispute_reason' => $disputeReason,
                'dispute_amount' => $disputeAmount,
                'admin_id' => auth()->id(),
            ]);

            return $payment->fresh();
        });
    }
}
