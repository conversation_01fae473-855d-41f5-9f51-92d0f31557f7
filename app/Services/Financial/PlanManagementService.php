<?php

declare(strict_types=1);

namespace App\Services\Financial;

use App\Models\Financial\PlanFeature;
use App\Models\Financial\PlanPrice;
use App\Models\Financial\SubscriptionPlan;
use App\Models\Financial\UserSubscription;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class PlanManagementService
{
    /**
     * Get plan with all details.
     */
    public function getPlanWithDetails(string $planId): array
    {
        $plan = SubscriptionPlan::with([
            'planPrices' => function ($query) {
                $query->orderBy('billing_interval');
            },
            'planFeatures.feature',
            'userSubscriptions' => function ($query) {
                $query->where('status', 'active')->limit(5);
            },
        ])->findOrFail($planId);

        return [
            'plan' => $plan,
            'statistics' => $this->getPlanStatistics($plan),
            'usage_analytics' => $this->getPlanUsageAnalytics($plan),
        ];
    }

    /**
     * Create new subscription plan.
     */
    public function createPlan(array $data): SubscriptionPlan
    {
        return DB::transaction(function () use ($data) {
            // Create the plan
            $plan = SubscriptionPlan::create([
                'name' => $data['name'],
                'slug' => $data['slug'] ?? Str::slug($data['name']),
                'description' => $data['description'] ?? null,
                'target_type' => $data['target_type'],
                'is_active' => $data['is_active'] ?? true,
            ]);

            // Add pricing if provided
            if (isset($data['prices'])) {
                $this->updatePlanPricing($plan->id, $data['prices']);
            }

            // Add features if provided
            if (isset($data['features'])) {
                $this->updatePlanFeatures($plan->id, $data['features']);
            }

            return $plan->load(['planPrices', 'planFeatures.feature']);
        });
    }

    /**
     * Update subscription plan.
     */
    public function updatePlan(string $planId, array $data): SubscriptionPlan
    {
        return DB::transaction(function () use ($planId, $data) {
            $plan = SubscriptionPlan::findOrFail($planId);

            // Update basic plan details
            $plan->update([
                'name' => $data['name'] ?? $plan->name,
                'slug' => $data['slug'] ?? $plan->slug,
                'description' => $data['description'] ?? $plan->description,
                'target_type' => $data['target_type'] ?? $plan->target_type,
                'is_active' => $data['is_active'] ?? $plan->is_active,
            ]);

            // Update pricing if provided
            if (isset($data['prices'])) {
                $this->updatePlanPricing($planId, $data['prices']);
            }

            // Update features if provided
            if (isset($data['features'])) {
                $this->updatePlanFeatures($planId, $data['features']);
            }

            return $plan->load(['planPrices', 'planFeatures.feature']);
        });
    }

    /**
     * Delete subscription plan.
     */
    public function deletePlan(string $planId): void
    {
        DB::transaction(function () use ($planId) {
            $plan = SubscriptionPlan::findOrFail($planId);

            // Check if plan has active subscriptions
            $activeSubscriptions = UserSubscription::where('plan_id', $planId)
                ->where('status', 'active')
                ->count();

            if ($activeSubscriptions > 0) {
                throw new \InvalidArgumentException(
                    "Cannot delete plan with {$activeSubscriptions} active subscriptions"
                );
            }

            // Delete related data
            PlanPrice::where('plan_id', $planId)->delete();
            PlanFeature::where('plan_id', $planId)->delete();

            // Delete the plan
            $plan->delete();
        });
    }

    /**
     * Toggle plan active status.
     */
    public function togglePlanStatus(string $planId, bool $isActive): SubscriptionPlan
    {
        $plan = SubscriptionPlan::findOrFail($planId);

        // If deactivating, check for active subscriptions
        if (! $isActive) {
            $activeSubscriptions = UserSubscription::where('plan_id', $planId)
                ->where('status', 'active')
                ->count();

            if ($activeSubscriptions > 0) {
                throw new \InvalidArgumentException(
                    "Cannot deactivate plan with {$activeSubscriptions} active subscriptions"
                );
            }
        }

        $plan->update(['is_active' => $isActive]);

        return $plan->load(['planPrices', 'planFeatures.feature']);
    }

    /**
     * Update plan pricing.
     */
    public function updatePlanPricing(string $planId, array $prices): SubscriptionPlan
    {
        return DB::transaction(function () use ($planId, $prices) {
            $plan = SubscriptionPlan::findOrFail($planId);

            // Delete existing prices
            PlanPrice::where('plan_id', $planId)->delete();

            // Create new prices
            foreach ($prices as $priceData) {
                PlanPrice::create([
                    'plan_id' => $planId,
                    'billing_interval' => $priceData['billing_interval'],
                    'price' => $priceData['price'],
                    'currency' => $priceData['currency'],
                    'is_active' => $priceData['is_active'] ?? true,
                ]);
            }

            return $plan->load(['planPrices', 'planFeatures.feature']);
        });
    }

    /**
     * Update plan features.
     */
    public function updatePlanFeatures(string $planId, array $features): SubscriptionPlan
    {
        return DB::transaction(function () use ($planId, $features) {
            $plan = SubscriptionPlan::findOrFail($planId);

            // Delete existing features
            PlanFeature::where('plan_id', $planId)->delete();

            // Create new features
            foreach ($features as $featureData) {
                if ($featureData['is_enabled']) {
                    PlanFeature::create([
                        'plan_id' => $planId,
                        'feature_id' => $featureData['feature_id'],
                        'limit' => $featureData['limit'],
                    ]);
                }
            }

            return $plan->load(['planPrices', 'planFeatures.feature']);
        });
    }

    /**
     * Get plan analytics.
     */
    public function getPlanAnalytics(string $planId): array
    {
        $plan = SubscriptionPlan::findOrFail($planId);

        return [
            'subscription_stats' => $this->getPlanStatistics($plan),
            'revenue_stats' => $this->getPlanRevenueStats($plan),
            'usage_stats' => $this->getPlanUsageAnalytics($plan),
            'conversion_stats' => $this->getPlanConversionStats($plan),
        ];
    }

    /**
     * Clone existing plan.
     */
    public function clonePlan(string $planId, array $newPlanData): SubscriptionPlan
    {
        return DB::transaction(function () use ($planId, $newPlanData) {
            $originalPlan = SubscriptionPlan::with(['planPrices', 'planFeatures'])
                ->findOrFail($planId);

            // Create new plan
            $newPlan = SubscriptionPlan::create([
                'name' => $newPlanData['name'],
                'slug' => $newPlanData['slug'],
                'description' => $originalPlan->description,
                'target_type' => $originalPlan->target_type,
                'is_active' => false, // Start as inactive
            ]);

            // Clone prices
            foreach ($originalPlan->planPrices as $price) {
                PlanPrice::create([
                    'plan_id' => $newPlan->id,
                    'billing_interval' => $price->billing_interval,
                    'price' => $price->price,
                    'currency' => $price->currency,
                    'is_active' => $price->is_active,
                ]);
            }

            // Clone features
            foreach ($originalPlan->planFeatures as $feature) {
                PlanFeature::create([
                    'plan_id' => $newPlan->id,
                    'feature_id' => $feature->feature_id,
                    'limit' => $feature->limit,
                ]);
            }

            return $newPlan->load(['planPrices', 'planFeatures.feature']);
        });
    }

    /**
     * Get plan statistics.
     */
    private function getPlanStatistics(SubscriptionPlan $plan): array
    {
        return [
            'total_subscriptions' => $plan->userSubscriptions()->count(),
            'active_subscriptions' => $plan->userSubscriptions()->where('status', 'active')->count(),
            'cancelled_subscriptions' => $plan->userSubscriptions()->where('status', 'cancelled')->count(),
            'expired_subscriptions' => $plan->userSubscriptions()->where('status', 'expired')->count(),
        ];
    }

    /**
     * Get plan revenue statistics.
     */
    private function getPlanRevenueStats(SubscriptionPlan $plan): array
    {
        // This would integrate with payment data
        return [
            'total_revenue' => 0,
            'monthly_recurring_revenue' => 0,
            'average_revenue_per_user' => 0,
        ];
    }

    /**
     * Get plan usage analytics.
     */
    private function getPlanUsageAnalytics(SubscriptionPlan $plan): array
    {
        // This would integrate with usage tracking
        return [
            'feature_adoption' => [],
            'usage_patterns' => [],
            'limit_utilization' => [],
        ];
    }

    /**
     * Get plan conversion statistics.
     */
    private function getPlanConversionStats(SubscriptionPlan $plan): array
    {
        // This would track conversion from other plans
        return [
            'upgrades_from' => [],
            'downgrades_to' => [],
            'conversion_rate' => 0,
        ];
    }
}
