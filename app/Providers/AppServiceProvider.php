<?php

namespace App\Providers;

use App\Services\Search\UnifiedSearchService;
use App\Services\System\LoggingService;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\ServiceProvider;
use Illuminate\Support\Facades\View;
use App\View\Composers\DomainComposer;
use Laravel\Socialite\Facades\Socialite;
use SocialiteProviders\Apple\Provider as AppleProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->singleton(UnifiedSearchService::class, function ($app) {
            return new UnifiedSearchService($app->make(LoggingService::class));
        });
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // Configure morph map for model reorganization
        $this->configureMorphMap();

        // Register Apple OAuth provider
        $this->bootSocialiteProviders();

        // Register domain composer for all email views
        View::composer('emails.*', DomainComposer::class);

        // Also register for any other views that might need domain info
        View::composer(['layouts.*', 'components.*'], DomainComposer::class);
    }

    /**
     * Configure morph map for model reorganization.
     */
    private function configureMorphMap(): void
    {
        Relation::morphMap([
            'App\Models\Order' => 'App\Models\Delivery\Order',
        ]);
    }

    /**
     * Configure Socialite providers.
     */
    private function bootSocialiteProviders(): void
    {
        // Apple provider configuration
        Socialite::extend('apple', function ($app) {
            $config = $app['config']['services.apple'];

            return Socialite::buildProvider(AppleProvider::class, $config);
        });
    }
}
