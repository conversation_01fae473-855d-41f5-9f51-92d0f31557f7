<?php

declare(strict_types=1);

namespace App\View\Composers;

use App\Services\System\DomainService;
use Illuminate\View\View;

/**
 * Domain View Composer
 * 
 * Makes domain-related information available to all views,
 * particularly email templates, to avoid hardcoding domains.
 */
class DomainComposer
{
    public function __construct(
        private readonly DomainService $domainService
    ) {}

    /**
     * Bind data to the view.
     */
    public function compose(View $view): void
    {
        $view->with([
            'baseDomain' => $this->domainService->getBaseDomain(),
            'centralDomain' => $this->domainService->getCentralDomain(),
            'emailDomain' => $this->domainService->getEmailDomain(),
            'supportEmail' => $this->domainService->getSupportEmail(),
            'noReplyEmail' => $this->domainService->getNoReplyEmail(),
            'adminEmail' => $this->domainService->getAdminEmail(),
            'customerAppUrl' => $this->domainService->getCustomerAppUrl(),
            'businessPortalUrl' => $this->domainService->getBusinessPortalUrl(),
            'providerPortalUrl' => $this->domainService->getProviderPortalUrl(),
            'adminPortalUrl' => $this->domainService->getAdminPortalUrl(),
            'marketingSiteUrl' => $this->domainService->getMarketingSiteUrl(),
            'cdnUrl' => $this->domainService->getCdnUrl(),
            'isLocal' => $this->domainService->isLocal(),
            'isProduction' => $this->domainService->isProduction(),
        ]);
    }
}
