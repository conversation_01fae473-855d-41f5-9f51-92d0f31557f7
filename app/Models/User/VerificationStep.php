<?php

declare(strict_types=1);

namespace App\Models\User;

use App\Models\Core\Country;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string|null $country_id
 * @property string $name e.g., verify_cac, verify_bank_details, verify_identity, verify_background_check
 * @property string|null $description
 * @property string $entity_type users, businesses, delivery_providers - Which entity type this step applies to
 * @property bool $required
 * @property int $display_order
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\User\EntityVerification> $entityVerifications
 * @property-read int|null $entity_verifications_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VerificationStep forCountry(?string $countryId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VerificationStep forEntityType(string $entityType)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VerificationStep global()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VerificationStep newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VerificationStep newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VerificationStep ordered()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VerificationStep query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VerificationStep required()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VerificationStep whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VerificationStep whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VerificationStep whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VerificationStep whereDisplayOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VerificationStep whereEntityType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VerificationStep whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VerificationStep whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VerificationStep whereRequired($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|VerificationStep whereUpdatedAt($value)
 *
 * @property-read Country|null $country
 *
 * @mixin \Eloquent
 */
class VerificationStep extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'country_id',
        'name',
        'description',
        'entity_type',
        'required',
        'display_order',
    ];

    protected $casts = [
        'required' => 'boolean',
        'display_order' => 'integer',
    ];

    /**
     * Get the country for this verification step.
     *
     * @return BelongsTo<Country,VerificationStep>
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    /**
     * Get the entity verifications for this step.
     *
     * @return HasMany<EntityVerification,VerificationStep>
     */
    public function entityVerifications(): HasMany
    {
        return $this->hasMany(EntityVerification::class);
    }

    /**
     * Scope to get steps for a specific entity type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForEntityType($query, string $entityType)
    {
        return $query->where('entity_type', $entityType);
    }

    /**
     * Scope to get only required steps.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRequired($query)
    {
        return $query->where('required', true);
    }

    /**
     * Scope to get steps ordered by display order.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order', 'asc');
    }

    /**
     * Scope to get steps for a specific country.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForCountry($query, ?string $countryId)
    {
        return $query->where('country_id', $countryId);
    }

    /**
     * Scope to get global steps (no country restriction).
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeGlobal($query)
    {
        return $query->whereNull('country_id');
    }
}
