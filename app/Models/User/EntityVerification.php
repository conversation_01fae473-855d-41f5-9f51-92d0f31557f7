<?php

declare(strict_types=1);

namespace App\Models\User;

use App\Enums\User\VerificationStatus;
use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property string $id
 * @property string|null $tenant_id FK to tenants.id, nullable - if entity is a user or platform-level verification
 * @property string $entity_id Polymorphic FK to user, business, or provider
 * @property string $entity_type users, businesses, delivery_providers
 * @property string $verification_step_id
 * @property mixed $status
 * @property \Illuminate\Support\Carbon|null $submitted_at
 * @property string|null $reviewed_by_user_id
 * @property \Illuminate\Support\Carbon|null $reviewed_at
 * @property string|null $notes Reviewer notes or rejection reason
 * @property array<array-key, mixed>|null $verification_data Any data submitted for this step (e.g., file URLs, submitted text)
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Model|\Eloquent $entity
 * @property-read \App\Models\User\User|null $reviewedBy
 * @property-read \App\Models\System\Tenant|null $tenant
 * @property-read \App\Models\User\VerificationStep $verificationStep
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification approved()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification byStatus(\App\Enums\VerificationStatus $status)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification forEntity(string $entityId, string $entityType)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification rejected()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification reviewed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification submitted()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification whereEntityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification whereEntityType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification whereReviewedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification whereReviewedByUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification whereSubmittedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification whereVerificationData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|EntityVerification whereVerificationStepId($value)
 *
 * @mixin \Eloquent
 */
class EntityVerification extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'entity_id',
        'entity_type',
        'verification_step_id',
        'status',
        'submitted_at',
        'reviewed_by_user_id',
        'reviewed_at',
        'notes',
        'verification_data',
    ];

    protected $casts = [
        'status' => VerificationStatus::class,
        'submitted_at' => 'datetime',
        'reviewed_at' => 'datetime',
        'verification_data' => 'array',
    ];

    /**
     * Get the tenant that owns the entity verification.
     *
     * @return BelongsTo<Tenant,EntityVerification>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the polymorphic entity (User, Business, or DeliveryProvider).
     */
    public function entity(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the verification step for this verification.
     *
     * @return BelongsTo<VerificationStep,EntityVerification>
     */
    public function verificationStep(): BelongsTo
    {
        return $this->belongsTo(VerificationStep::class, 'verification_step_id');
    }

    /**
     * Get the user who reviewed this verification.
     *
     * @return BelongsTo<User,EntityVerification>
     */
    public function reviewedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'reviewed_by_user_id');
    }

    /**
     * Scope to get verifications by status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByStatus($query, VerificationStatus $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending verifications.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', VerificationStatus::PENDING);
    }

    /**
     * Scope to get approved verifications.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeApproved($query)
    {
        return $query->where('status', VerificationStatus::APPROVED);
    }

    /**
     * Scope to get rejected verifications.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRejected($query)
    {
        return $query->where('status', VerificationStatus::REJECTED);
    }

    /**
     * Scope to get verifications for a specific entity.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForEntity($query, string $entityId, string $entityType)
    {
        return $query->where('entity_id', $entityId)->where('entity_type', $entityType);
    }

    /**
     * Scope to get submitted verifications.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSubmitted($query)
    {
        return $query->whereNotNull('submitted_at');
    }

    /**
     * Scope to get reviewed verifications.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeReviewed($query)
    {
        return $query->whereNotNull('reviewed_at');
    }
}
