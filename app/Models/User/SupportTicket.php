<?php

declare(strict_types=1);

namespace App\Models\User;

use App\Enums\System\TicketStatus;
use App\Models\System\SupportMessage;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property string $id
 * @property string $subject
 * @property string|null $description
 * @property mixed $status
 * @property string|null $priority e.g., low, medium, high
 * @property string $created_by_id
 * @property string $created_by_type
 * @property string|null $assigned_to_user_id
 * @property \Illuminate\Support\Carbon|null $resolved_at
 * @property \Illuminate\Support\Carbon|null $closed_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User\User|null $assignedTo
 * @property-read Model|\Eloquent $creator
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket byPriority(string $priority)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket byStatus(string $status)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket closed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket open()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket resolved()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket whereAssignedToUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket whereClosedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket whereCreatedById($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket whereCreatedByType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket wherePriority($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket whereResolvedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket whereSubject($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportTicket whereUpdatedAt($value)
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, SupportMessage> $messages
 * @property-read int|null $messages_count
 *
 * @mixin \Eloquent
 */
class SupportTicket extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'subject',
        'description',
        'status',
        'priority',
        'created_by_id',
        'created_by_type',
        'assigned_to_user_id',
        'resolved_at',
        'closed_at',
    ];

    protected $casts = [
        'status' => TicketStatus::class,
        'resolved_at' => 'datetime',
        'closed_at' => 'datetime',
    ];

    /**
     * Get the creator of the support ticket (polymorphic).
     */
    public function creator(): MorphTo
    {
        return $this->morphTo(__FUNCTION__, 'created_by_type', 'created_by_id');
    }

    /**
     * Get the user assigned to this ticket.
     *
     * @return BelongsTo<User,SupportTicket>
     */
    public function assignedTo(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_to_user_id');
    }

    /**
     * Get the messages for this support ticket.
     *
     * @return HasMany<SupportMessage,SupportTicket>
     */
    public function messages(): HasMany
    {
        return $this->hasMany(SupportMessage::class, 'ticket_id');
    }

    /**
     * Scope to get tickets by status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get tickets by priority.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByPriority($query, string $priority)
    {
        return $query->where('priority', $priority);
    }

    /**
     * Scope to get open tickets.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOpen($query)
    {
        return $query->where('status', TicketStatus::OPEN);
    }

    /**
     * Scope to get resolved tickets.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeResolved($query)
    {
        return $query->where('status', TicketStatus::RESOLVED);
    }

    /**
     * Scope to get closed tickets.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeClosed($query)
    {
        return $query->where('status', TicketStatus::CLOSED);
    }

    /**
     * Check if the ticket is open.
     */
    public function isOpen(): bool
    {
        return $this->status === TicketStatus::OPEN;
    }

    /**
     * Check if the ticket is resolved.
     */
    public function isResolved(): bool
    {
        return $this->status === TicketStatus::RESOLVED;
    }

    /**
     * Check if the ticket is closed.
     */
    public function isClosed(): bool
    {
        return $this->status === TicketStatus::CLOSED;
    }

    /**
     * Mark the ticket as resolved.
     */
    public function markAsResolved(): bool
    {
        return (bool) $this->update([
            'status' => TicketStatus::RESOLVED,
            'resolved_at' => now(),
        ]);
    }

    /**
     * Mark the ticket as closed.
     */
    public function markAsClosed(): bool
    {
        return (bool) $this->update([
            'status' => TicketStatus::CLOSED,
            'closed_at' => now(),
        ]);
    }

    /**
     * Get ticket summary for API responses.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'subject' => $this->subject,
            'status' => $this->status,
            'priority' => $this->priority,
            'created_by' => $this->creator?->name ?? null,
            'assigned_to' => $this->assignedTo?->name ?? null,
            'resolved_at' => $this->resolved_at,
            'closed_at' => $this->closed_at,
            'created_at' => $this->created_at,
        ];
    }
}
