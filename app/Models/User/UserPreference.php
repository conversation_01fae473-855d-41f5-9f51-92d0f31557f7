<?php

declare(strict_types=1);

namespace App\Models\User;

use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $user_id
 * @property string|null $tenant_id FK to tenants.id, nullable for global preferences
 * @property string $preference_key e.g., app.theme, privacy.location_sharing
 * @property string $preference_value JSON or string value
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\System\Tenant|null $tenant
 * @property-read \App\Models\User\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserPreference forKey(string $key)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserPreference forTenant(string $tenantId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserPreference global()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserPreference newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserPreference newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserPreference query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserPreference whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserPreference whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserPreference wherePreferenceKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserPreference wherePreferenceValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserPreference whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserPreference whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserPreference whereUserId($value)
 *
 * @mixin \Eloquent
 */
class UserPreference extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'user_id',
        'tenant_id',
        'preference_key',
        'preference_value',
    ];

    /**
     * Get the user that owns this preference.
     *
     * @return BelongsTo<User,UserPreference>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the tenant for this preference.
     *
     * @return BelongsTo<Tenant,UserPreference>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Scope to get preferences for a specific key.
     */
    public function scopeForKey($query, string $key)
    {
        return $query->where('preference_key', $key);
    }

    /**
     * Scope to get global preferences (no tenant).
     */
    public function scopeGlobal($query)
    {
        return $query->whereNull('tenant_id');
    }

    /**
     * Scope to get tenant-specific preferences.
     */
    public function scopeForTenant($query, string $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }
}
