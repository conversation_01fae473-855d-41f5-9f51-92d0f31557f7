<?php

namespace App\Models\User;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property-read \App\Models\User\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationPreference newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationPreference newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationPreference query()
 *
 * @mixin \Eloquent
 */
class NotificationPreference extends Model
{
    use HasUuids;

    protected $fillable = [
        'user_id',
        'notification_type',
        'channel_preferences',
        'is_enabled',
        'quiet_hours_start',
        'quiet_hours_end',
    ];

    protected $casts = [
        'is_enabled' => 'boolean',
        'channel_preferences' => 'array',
        'quiet_hours_start' => 'datetime',
        'quiet_hours_end' => 'datetime',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function isInQuietHours(): bool
    {
        if (! $this->quiet_hours_start || ! $this->quiet_hours_end) {
            return false;
        }

        $now = now();

        return $now->between($this->quiet_hours_start, $this->quiet_hours_end);
    }
}
