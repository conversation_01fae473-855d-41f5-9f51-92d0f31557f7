<?php

declare(strict_types=1);

namespace App\Models\User;

use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;

/**
 * Team Invitation Model
 *
 * Represents invitations sent to users to join a business or provider team.
 * Used for tracking invitation status and enabling registration from invitation tokens.
 *
 * @property string $id
 * @property string $tenant_id
 * @property string $email
 * @property string $role
 * @property string $token
 * @property string $status
 * @property \Illuminate\Support\Carbon $expires_at
 * @property \Illuminate\Support\Carbon|null $accepted_at
 * @property string|null $user_id
 * @property string|null $invited_by_id
 * @property array|null $metadata
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Tenant $tenant
 * @property-read User|null $user
 * @property-read User|null $invitedBy
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamInvitation accepted()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamInvitation expired()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamInvitation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamInvitation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamInvitation pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamInvitation query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamInvitation whereAcceptedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamInvitation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamInvitation whereEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamInvitation whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamInvitation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamInvitation whereInvitedById($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamInvitation whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamInvitation whereRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamInvitation whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamInvitation whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamInvitation whereToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamInvitation whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TeamInvitation whereUserId($value)
 *
 * @mixin \Eloquent
 */
class TeamInvitation extends Model
{
    use BelongsToTenant, HasFactory, HasUuids;

    protected $fillable = [
        'id',
        'tenant_id',
        'email',
        'role',
        'token',
        'status',
        'expires_at',
        'accepted_at',
        'user_id',
        'invited_by_id',
        'metadata',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'accepted_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Invitation statuses
     */
    public const STATUS_PENDING = 'pending';

    public const STATUS_ACCEPTED = 'accepted';

    public const STATUS_EXPIRED = 'expired';

    public const STATUS_CANCELLED = 'cancelled';

    /**
     * Get the tenant that owns the invitation.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    /**
     * Get the user who accepted the invitation.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the user who sent the invitation.
     */
    public function invitedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'invited_by_id');
    }

    /**
     * Check if invitation is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at->isPast();
    }

    /**
     * Check if invitation is pending.
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING && ! $this->isExpired();
    }

    /**
     * Check if invitation is accepted.
     */
    public function isAccepted(): bool
    {
        return $this->status === self::STATUS_ACCEPTED;
    }

    /**
     * Mark invitation as accepted.
     */
    public function markAsAccepted(User $user): void
    {
        $this->update([
            'status' => self::STATUS_ACCEPTED,
            'accepted_at' => now(),
            'user_id' => $user->id,
        ]);
    }

    /**
     * Mark invitation as expired.
     */
    public function markAsExpired(): void
    {
        $this->update([
            'status' => self::STATUS_EXPIRED,
        ]);
    }

    /**
     * Mark invitation as cancelled.
     */
    public function markAsCancelled(): void
    {
        $this->update([
            'status' => self::STATUS_CANCELLED,
        ]);
    }

    /**
     * Generate a unique invitation token.
     */
    public static function generateToken(): string
    {
        return bin2hex(random_bytes(32));
    }

    /**
     * Create a new invitation.
     */
    public static function createInvitation(
        string $tenantId,
        string $email,
        string $role,
        User $invitedBy,
        int $expiresInHours = 72,
        array $metadata = []
    ): self {
        return self::create([
            'tenant_id' => $tenantId,
            'email' => $email,
            'role' => $role,
            'token' => self::generateToken(),
            'status' => self::STATUS_PENDING,
            'expires_at' => now()->addHours($expiresInHours),
            'invited_by_id' => $invitedBy->id,
            'metadata' => $metadata,
        ]);
    }

    /**
     * Find invitation by token.
     */
    public static function findByToken(string $token): ?self
    {
        return self::where('token', $token)
            ->where('status', self::STATUS_PENDING)
            ->where('expires_at', '>', now())
            ->first();
    }

    /**
     * Scope for pending invitations.
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING)
            ->where('expires_at', '>', now());
    }

    /**
     * Scope for expired invitations.
     */
    public function scopeExpired($query)
    {
        return $query->where(function ($q) {
            $q->where('status', self::STATUS_EXPIRED)
                ->orWhere('expires_at', '<=', now());
        });
    }

    /**
     * Scope for accepted invitations.
     */
    public function scopeAccepted($query)
    {
        return $query->where('status', self::STATUS_ACCEPTED);
    }
}
