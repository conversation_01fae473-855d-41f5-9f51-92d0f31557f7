<?php

declare(strict_types=1);

namespace App\Models\User;

use App\Enums\System\NotificationChannel as NotificationChannelEnum;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $name
 * @property string|null $description
 * @property mixed $channel_type
 * @property bool $is_active
 * @property array<array-key, mixed>|null $provider_settings Channel-specific configuration settings
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\User\Notification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\User\UserNotificationPreference> $userPreferences
 * @property-read int|null $user_preferences_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationChannel active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationChannel newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationChannel newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationChannel ofType(\App\Enums\NotificationChannel $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationChannel query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationChannel whereChannelType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationChannel whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationChannel whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationChannel whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationChannel whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationChannel whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationChannel whereProviderSettings($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationChannel whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class NotificationChannel extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'channel_type',
        'is_active',
        'provider_settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'provider_settings' => 'array',
        'channel_type' => NotificationChannelEnum::class,
    ];

    /**
     * Get the notifications sent through this channel.
     *
     * @return HasMany<Notification,NotificationChannel>
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class, 'channel', 'channel_type');
    }

    /**
     * Get the notification preferences for this channel.
     *
     * @return HasMany<UserNotificationPreference,NotificationChannel>
     */
    public function userPreferences(): HasMany
    {
        return $this->hasMany(UserNotificationPreference::class, 'channel', 'channel_type');
    }

    /**
     * Scope query to only include active channels.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope query to get channels of a specific type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfType($query, NotificationChannelEnum $type)
    {
        return $query->where('channel_type', $type);
    }

    /**
     * Check if the channel is properly configured.
     */
    public function isConfigured(): bool
    {
        // Basic validation
        if (! $this->is_active || empty($this->provider_settings)) {
            return false;
        }

        // Check required settings based on channel type
        return match ($this->channel_type) {
            NotificationChannelEnum::EMAIL => $this->hasValidEmailConfig(),
            NotificationChannelEnum::SMS => $this->hasValidSmsConfig(),
            NotificationChannelEnum::PUSH => $this->hasValidPushConfig(),
            NotificationChannelEnum::IN_APP => true, // In-app notifications don't need external config
            default => false,
        };
    }

    /**
     * Validate email provider configuration.
     */
    protected function hasValidEmailConfig(): bool
    {
        $required = ['smtp_host', 'smtp_port', 'smtp_username', 'smtp_password', 'from_address'];

        return $this->hasRequiredSettings($required);
    }

    /**
     * Validate SMS provider configuration.
     */
    protected function hasValidSmsConfig(): bool
    {
        $required = ['api_key', 'sender_id'];

        return $this->hasRequiredSettings($required);
    }

    /**
     * Validate push notification provider configuration.
     */
    protected function hasValidPushConfig(): bool
    {
        $required = ['app_id', 'api_key'];

        return $this->hasRequiredSettings($required);
    }

    /**
     * Check if provider settings has all required fields.
     *
     * @param  array<string>  $required
     */
    protected function hasRequiredSettings(array $required): bool
    {
        $settings = $this->provider_settings;

        return ! empty($settings) && count(array_intersect_key(array_flip($required), $settings)) === count($required);
    }
}
