<?php

declare(strict_types=1);

namespace App\Models\User;

use App\Enums\NotificationStatus as NotificationStatusEnum;
use App\Enums\System\NotificationChannel;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $notification_id
 * @property string $recipient_id
 * @property NotificationChannel $channel
 * @property mixed $status
 * @property string|null $error_message
 * @property \Illuminate\Support\Carbon|null $sent_at
 * @property \Illuminate\Support\Carbon|null $delivered_at
 * @property \Illuminate\Support\Carbon|null $read_at
 * @property int $failed_attempts
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User\Notification $notification
 * @property-read \App\Models\User\User $recipient
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationStatus delivered()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationStatus failed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationStatus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationStatus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationStatus pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationStatus query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationStatus sent()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationStatus whereChannel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationStatus whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationStatus whereDeliveredAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationStatus whereErrorMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationStatus whereFailedAttempts($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationStatus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationStatus whereNotificationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationStatus whereReadAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationStatus whereRecipientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationStatus whereSentAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationStatus whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationStatus whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class NotificationStatus extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'notification_id',
        'recipient_id',
        'channel',
        'status',
        'error_message',
        'sent_at',
        'delivered_at',
        'read_at',
        'failed_attempts',
    ];

    protected $casts = [
        'channel' => NotificationChannel::class,
        'status' => NotificationStatusEnum::class,
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime',
        'read_at' => 'datetime',
        'failed_attempts' => 'integer',
    ];

    public function notification(): BelongsTo
    {
        return $this->belongsTo(Notification::class);
    }

    public function recipient(): BelongsTo
    {
        return $this->belongsTo(User::class, 'recipient_id');
    }

    public function scopeFailed($query)
    {
        return $query->where('status', NotificationStatusEnum::FAILED);
    }

    public function scopePending($query)
    {
        return $query->where('status', NotificationStatusEnum::PENDING);
    }

    public function scopeSent($query)
    {
        return $query->where('status', NotificationStatusEnum::SENT);
    }

    public function scopeDelivered($query)
    {
        return $query->where('status', NotificationStatusEnum::DELIVERED);
    }
}
