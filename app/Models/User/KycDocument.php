<?php

declare(strict_types=1);

namespace App\Models\User;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property string $id
 * @property string $verifiable_type
 * @property string $verifiable_id
 * @property string $document_type
 * @property string|null $document_number
 * @property string $document_url
 * @property string $verification_status
 * @property \Illuminate\Support\Carbon|null $verified_at
 * @property string|null $verified_by
 * @property string|null $rejection_reason
 * @property \Illuminate\Support\Carbon|null $expires_at
 * @property array<array-key, mixed>|null $metadata
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Model|\Eloquent $verifiable
 * @property-read User|null $verifiedBy
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument rejected()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument expired()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument whereDocumentNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument whereDocumentType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument whereDocumentUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument whereRejectionReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument whereVerifiableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument whereVerifiableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument whereVerificationStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument whereVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|KycDocument whereVerifiedBy($value)
 *
 * @mixin \Eloquent
 */
class KycDocument extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'verifiable_type',
        'verifiable_id',
        'document_type',
        'document_number',
        'document_url',
        'verification_status',
        'verified_at',
        'verified_by',
        'rejection_reason',
        'expires_at',
        'metadata',
    ];

    protected $casts = [
        'verified_at' => 'datetime',
        'expires_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the verifiable entity (User or DeliveryProvider).
     */
    public function verifiable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the admin user who verified this document.
     *
     * @return BelongsTo<User,KycDocument>
     */
    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Scope to get only pending documents.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('verification_status', 'pending');
    }

    /**
     * Scope to get only verified documents.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeVerified($query)
    {
        return $query->where('verification_status', 'verified');
    }

    /**
     * Scope to get only rejected documents.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRejected($query)
    {
        return $query->where('verification_status', 'rejected');
    }

    /**
     * Scope to get only expired documents.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeExpired($query)
    {
        return $query->where('verification_status', 'expired')
            ->orWhere(function ($query) {
                $query->whereNotNull('expires_at')
                    ->where('expires_at', '<', now());
            });
    }

    /**
     * Check if the document is verified.
     */
    public function isVerified(): bool
    {
        return $this->verification_status === 'verified';
    }

    /**
     * Check if the document is pending verification.
     */
    public function isPending(): bool
    {
        return $this->verification_status === 'pending';
    }

    /**
     * Check if the document is rejected.
     */
    public function isRejected(): bool
    {
        return $this->verification_status === 'rejected';
    }

    /**
     * Check if the document is expired.
     */
    public function isExpired(): bool
    {
        if ($this->verification_status === 'expired') {
            return true;
        }

        if ($this->expires_at && $this->expires_at->isPast()) {
            return true;
        }

        return false;
    }

    /**
     * Mark document as verified.
     */
    public function markAsVerified(string $verifiedBy): void
    {
        $this->update([
            'verification_status' => 'verified',
            'verified_at' => now(),
            'verified_by' => $verifiedBy,
            'rejection_reason' => null,
        ]);
    }

    /**
     * Mark document as rejected.
     */
    public function markAsRejected(string $reason, string $rejectedBy): void
    {
        $this->update([
            'verification_status' => 'rejected',
            'rejection_reason' => $reason,
            'verified_by' => $rejectedBy,
            'verified_at' => now(),
        ]);
    }

    /**
     * Mark document as expired.
     */
    public function markAsExpired(): void
    {
        $this->update([
            'verification_status' => 'expired',
        ]);
    }

    /**
     * Get human-readable document type.
     */
    public function getDocumentTypeLabel(): string
    {
        return match ($this->document_type) {
            'identity_card' => 'National Identity Card',
            'passport' => 'International Passport',
            'drivers_license' => 'Driver\'s License',
            'business_registration' => 'Business Registration Certificate',
            'tax_identification' => 'Tax Identification Number',
            'bank_statement' => 'Bank Statement',
            'utility_bill' => 'Utility Bill',
            'address_verification' => 'Address Verification Document',
            default => ucwords(str_replace('_', ' ', $this->document_type)),
        };
    }

    /**
     * Get verification status label.
     */
    public function getVerificationStatusLabel(): string
    {
        return match ($this->verification_status) {
            'pending' => 'Pending Review',
            'under_review' => 'Under Review',
            'verified' => 'Verified',
            'rejected' => 'Rejected',
            'expired' => 'Expired',
            default => ucwords($this->verification_status),
        };
    }

    /**
     * Get verification status color for UI.
     */
    public function getVerificationStatusColor(): string
    {
        return match ($this->verification_status) {
            'pending' => 'yellow',
            'under_review' => 'blue',
            'verified' => 'green',
            'rejected' => 'red',
            'expired' => 'gray',
            default => 'gray',
        };
    }

    /**
     * Check if document requires renewal.
     */
    public function requiresRenewal(): bool
    {
        if (! $this->expires_at) {
            return false;
        }

        // Require renewal 30 days before expiration
        return $this->expires_at->subDays(30)->isPast();
    }

    /**
     * Get days until expiration.
     */
    public function getDaysUntilExpiration(): ?int
    {
        if (! $this->expires_at) {
            return null;
        }

        return max(0, now()->diffInDays($this->expires_at, false));
    }
}
