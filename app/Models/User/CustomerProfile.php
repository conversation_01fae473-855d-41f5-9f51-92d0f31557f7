<?php

declare(strict_types=1);

namespace App\Models\User;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $user_id
 * @property string|null $default_address_id
 * @property string|null $avatar_url
 * @property string|null $bio
 * @property \Illuminate\Support\Carbon|null $date_of_birth
 * @property string|null $gender
 * @property string|null $business_role For tenant users
 * @property string|null $department For tenant users
 * @property array<array-key, mixed>|null $preferences Additional flexible preferences
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User\Address|null $defaultAddress
 * @property-read \App\Models\User\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerProfile newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerProfile newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerProfile query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerProfile whereAvatarUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerProfile whereBio($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerProfile whereBusinessRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerProfile whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerProfile whereDateOfBirth($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerProfile whereDefaultAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerProfile whereDepartment($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerProfile whereGender($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerProfile whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerProfile wherePreferences($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerProfile whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerProfile whereUserId($value)
 *
 * @mixin \Eloquent
 */
class CustomerProfile extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'user_id',
        'default_address_id',
        'avatar_url',
        'bio',
        'date_of_birth',
        'gender',
        'business_role',
        'department',
        'preferences',
    ];

    protected $casts = [
        'date_of_birth' => 'date',
        'preferences' => 'array',
    ];

    /**
     * Get the user that owns the customer profile.
     *
     * @return BelongsTo<User,CustomerProfile>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the default address for the customer.
     *
     * @return BelongsTo<Address,CustomerProfile>
     */
    public function defaultAddress(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'default_address_id');
    }
}
