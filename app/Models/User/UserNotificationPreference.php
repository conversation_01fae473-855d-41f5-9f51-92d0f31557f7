<?php

declare(strict_types=1);

namespace App\Models\User;

use App\Enums\System\NotificationChannel;
use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $user_id
 * @property string|null $tenant_id FK to tenants.id, nullable if preference is global or for a customer
 * @property string $notification_type e.g., order_status_update, delivery_assigned, promotion_alert
 * @property NotificationChannel $channel e.g., email, push, sms
 * @property bool $is_enabled
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\System\Tenant|null $tenant
 * @property-read \App\Models\User\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserNotificationPreference enabled()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserNotificationPreference forChannel(\App\Enums\System\NotificationChannel $channel)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserNotificationPreference forType(string $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserNotificationPreference newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserNotificationPreference newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserNotificationPreference query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserNotificationPreference whereChannel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserNotificationPreference whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserNotificationPreference whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserNotificationPreference whereIsEnabled($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserNotificationPreference whereNotificationType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserNotificationPreference whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserNotificationPreference whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserNotificationPreference whereUserId($value)
 *
 * @mixin \Eloquent
 */
class UserNotificationPreference extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'user_notification_preferences';

    protected $fillable = [
        'user_id',
        'tenant_id',
        'notification_type',
        'channel',
        'is_enabled',
    ];

    protected $casts = [
        'channel' => NotificationChannel::class,
        'is_enabled' => 'boolean',
    ];

    /**
     * Get the user that owns this notification preference.
     *
     * @return BelongsTo<User,UserNotificationPreference>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the tenant for this notification preference.
     *
     * @return BelongsTo<Tenant,UserNotificationPreference>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Scope to get enabled preferences.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeEnabled($query)
    {
        return $query->where('is_enabled', true);
    }

    /**
     * Scope to get preferences for a specific notification type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForType($query, string $type)
    {
        return $query->where('notification_type', $type);
    }

    /**
     * Scope to get preferences for a specific channel.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForChannel($query, NotificationChannel $channel)
    {
        return $query->where('channel', $channel);
    }
}
