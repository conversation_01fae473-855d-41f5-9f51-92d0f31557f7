<?php

declare(strict_types=1);

namespace App\Models\User;

use App\Models\Business\Business;
use App\Models\Delivery\DeliveryProvider;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;

/**
 * Staff Activity Model
 *
 * Tracks all staff activities and actions for audit and monitoring purposes.
 *
 * @property string $id
 * @property string $tenant_id
 * @property string $user_id
 * @property string|null $business_id
 * @property string|null $delivery_provider_id
 * @property string $activity_type
 * @property string $activity_category
 * @property string $description
 * @property string|null $subject_id
 * @property string|null $subject_type
 * @property array<array-key, mixed>|null $old_values
 * @property array<array-key, mixed>|null $new_values
 * @property array<array-key, mixed>|null $metadata
 * @property string|null $ip_address
 * @property string|null $user_agent
 * @property string|null $session_id
 * @property string $severity
 * @property bool $is_sensitive
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Business|null $business
 * @property-read DeliveryProvider|null $deliveryProvider
 * @property-read Model|\Eloquent|null $subject
 * @property-read \App\Models\System\Tenant $tenant
 * @property-read \App\Models\User\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity byBusiness(string $businessId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity byProvider(string $providerId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity byUser(string $userId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity dateRange($startDate, $endDate)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity ofCategory(string $category)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity ofSeverity(string $severity)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity ofType(string $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity sensitive()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereActivityCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereActivityType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereDeliveryProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereIpAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereIsSensitive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereNewValues($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereOldValues($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereSessionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereSeverity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereSubjectId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereSubjectType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereUserAgent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|StaffActivity whereUserId($value)
 *
 * @mixin \Eloquent
 */
class StaffActivity extends Model
{
    use BelongsToTenant, HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'user_id',
        'business_id',
        'delivery_provider_id',
        'activity_type',
        'activity_category',
        'description',
        'subject_id',
        'subject_type',
        'old_values',
        'new_values',
        'metadata',
        'ip_address',
        'user_agent',
        'session_id',
        'severity',
        'is_sensitive',
    ];

    protected $casts = [
        'old_values' => 'array',
        'new_values' => 'array',
        'metadata' => 'array',
        'is_sensitive' => 'boolean',
    ];

    /**
     * Get the user who performed the activity.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the business context (if applicable).
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }

    /**
     * Get the delivery provider context (if applicable).
     */
    public function deliveryProvider(): BelongsTo
    {
        return $this->belongsTo(DeliveryProvider::class, 'delivery_provider_id');
    }

    /**
     * Get the subject of the activity (polymorphic).
     */
    public function subject(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope to filter by activity type.
     */
    public function scopeOfType($query, string $type)
    {
        return $query->where('activity_type', $type);
    }

    /**
     * Scope to filter by activity category.
     */
    public function scopeOfCategory($query, string $category)
    {
        return $query->where('activity_category', $category);
    }

    /**
     * Scope to filter by severity.
     */
    public function scopeOfSeverity($query, string $severity)
    {
        return $query->where('severity', $severity);
    }

    /**
     * Scope to get sensitive activities.
     */
    public function scopeSensitive($query)
    {
        return $query->where('is_sensitive', true);
    }

    /**
     * Scope to filter by user.
     */
    public function scopeByUser($query, string $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter by business.
     */
    public function scopeByBusiness($query, string $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    /**
     * Scope to filter by delivery provider.
     */
    public function scopeByProvider($query, string $providerId)
    {
        return $query->where('delivery_provider_id', $providerId);
    }

    /**
     * Scope to filter by date range.
     */
    public function scopeDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Get formatted activity description with context.
     */
    public function getFormattedDescription(): string
    {
        $userName = $this->user ? $this->user->first_name.' '.$this->user->last_name : 'Unknown User';
        $timestamp = $this->created_at->format('Y-m-d H:i:s');

        return "[{$timestamp}] {$userName}: {$this->description}";
    }

    /**
     * Check if activity has changes.
     */
    public function hasActivityChanges(): bool
    {
        return ! empty($this->old_values) || ! empty($this->new_values);
    }

    /**
     * Get the changes made in this activity.
     */
    public function getChanges(): array
    {
        if (! $this->hasActivityChanges()) {
            return [];
        }

        $changes = [];
        $oldValues = $this->old_values ?? [];
        $newValues = $this->new_values ?? [];

        // Get all fields that changed
        $allFields = array_unique(array_merge(array_keys($oldValues), array_keys($newValues)));

        foreach ($allFields as $field) {
            $oldValue = $oldValues[$field] ?? null;
            $newValue = $newValues[$field] ?? null;

            if ($oldValue !== $newValue) {
                $changes[$field] = [
                    'old' => $oldValue,
                    'new' => $newValue,
                ];
            }
        }

        return $changes;
    }

    /**
     * Get activity severity color for UI.
     */
    public function getSeverityColor(): string
    {
        return match ($this->severity) {
            'low' => 'green',
            'medium' => 'yellow',
            'high' => 'orange',
            'critical' => 'red',
            default => 'gray',
        };
    }

    /**
     * Get activity category icon for UI.
     */
    public function getCategoryIcon(): string
    {
        return match ($this->activity_category) {
            'authentication' => 'login',
            'order_management' => 'shopping-cart',
            'inventory' => 'package',
            'user_management' => 'users',
            'payment' => 'credit-card',
            'delivery' => 'truck',
            'settings' => 'settings',
            'security' => 'shield',
            default => 'activity',
        };
    }
}
