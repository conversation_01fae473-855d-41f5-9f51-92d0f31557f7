<?php

declare(strict_types=1);

namespace App\Models\User;

use App\Enums\User\VerificationStatus;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property string $id
 * @property string $verifiable_type Polymorphic type (User, Business, etc.)
 * @property string $verifiable_id Polymorphic ID
 * @property string $verification_type Type of verification (email, phone, identity, etc.)
 * @property mixed $status
 * @property string|null $verification_code
 * @property \Illuminate\Support\Carbon|null $code_expires_at
 * @property array<array-key, mixed>|null $verification_data Additional verification data
 * @property \Illuminate\Support\Carbon|null $verified_at
 * @property string|null $verified_by
 * @property string|null $rejection_reason
 * @property int $attempts
 * @property int $max_attempts
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Model|\Eloquent $verifiable
 * @property-read \App\Models\User\User|null $verifiedBy
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification approved()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification byType(string $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification validCode()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification whereAttempts($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification whereCodeExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification whereMaxAttempts($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification whereRejectionReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification whereVerifiableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification whereVerifiableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification whereVerificationCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification whereVerificationData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification whereVerificationType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification whereVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Verification whereVerifiedBy($value)
 *
 * @mixin \Eloquent
 */
class Verification extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'verifiable_type',
        'verifiable_id',
        'verification_type',
        'status',
        'verification_code',
        'code_expires_at',
        'verification_data',
        'verified_at',
        'verified_by',
        'rejection_reason',
        'attempts',
        'max_attempts',
    ];

    protected $casts = [
        'status' => VerificationStatus::class,
        'verification_data' => 'array',
        'verified_at' => 'datetime',
        'code_expires_at' => 'datetime',
        'attempts' => 'integer',
        'max_attempts' => 'integer',
    ];

    /**
     * Get the verifiable entity (User, Business, etc.).
     */
    public function verifiable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the user who verified this record.
     *
     * @return BelongsTo<User,Verification>
     */
    public function verifiedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'verified_by');
    }

    /**
     * Scope to get pending verifications.
     */
    public function scopePending($query)
    {
        return $query->where('status', VerificationStatus::PENDING);
    }

    /**
     * Scope to get approved verifications.
     */
    public function scopeApproved($query)
    {
        return $query->where('status', VerificationStatus::APPROVED);
    }

    /**
     * Scope to get verifications by type.
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('verification_type', $type);
    }

    /**
     * Scope to get non-expired verification codes.
     */
    public function scopeValidCode($query)
    {
        return $query->whereNotNull('verification_code')
            ->where('code_expires_at', '>', now());
    }

    /**
     * Check if verification is approved.
     */
    public function isApproved(): bool
    {
        return $this->status === VerificationStatus::APPROVED;
    }

    /**
     * Check if verification is pending.
     */
    public function isPending(): bool
    {
        return $this->status === VerificationStatus::PENDING;
    }

    /**
     * Check if verification code is valid.
     */
    public function hasValidCode(): bool
    {
        return ! is_null($this->verification_code)
            && ! is_null($this->code_expires_at)
            && $this->code_expires_at->isFuture();
    }

    /**
     * Check if max attempts reached.
     */
    public function hasReachedMaxAttempts(): bool
    {
        return $this->attempts >= $this->max_attempts;
    }

    /**
     * Increment verification attempts.
     */
    public function incrementAttempts(): bool
    {
        $this->increment('attempts');

        return true;
    }
}
