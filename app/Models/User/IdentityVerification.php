<?php

declare(strict_types=1);

namespace App\Models\User;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $user_id
 * @property string $verification_type
 * @property string $identity_number_encrypted
 * @property string $verification_status
 * @property string|null $external_reference
 * @property array<array-key, mixed>|null $verification_data
 * @property int|null $verification_score
 * @property \Illuminate\Support\Carbon|null $verified_at
 * @property string|null $failure_reason
 * @property \Illuminate\Support\Carbon|null $expires_at
 * @property array<array-key, mixed>|null $data_matches
 * @property float|null $match_percentage
 * @property array<array-key, mixed>|null $verification_attempts
 * @property string|null $ip_address
 * @property string|null $user_agent
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification failed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification bvn()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification nin()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereDataMatches($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereExternalReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereFailureReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereIdentityNumberEncrypted($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereIpAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereMatchPercentage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereUserAgent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereVerificationAttempts($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereVerificationData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereVerificationScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereVerificationStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereVerificationType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereVerifiedAt($value)
 *
 * @property string|null $qoreid_reference
 * @property string|null $risk_level
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereQoreidReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|IdentityVerification whereRiskLevel($value)
 *
 * @mixin \Eloquent
 */
class IdentityVerification extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'user_id',
        'verification_type',
        'identity_number_encrypted',
        'verification_status',
        'external_reference',
        'verification_data',
        'verification_score',
        'verified_at',
        'failure_reason',
        'expires_at',
        'data_matches',
        'match_percentage',
        'verification_attempts',
        'ip_address',
        'user_agent',
    ];

    protected $casts = [
        'verification_data' => 'array',
        'verification_score' => 'integer',
        'verified_at' => 'datetime',
        'expires_at' => 'datetime',
        'data_matches' => 'array',
        'match_percentage' => 'decimal:2',
        'verification_attempts' => 'array',
    ];

    protected $hidden = [
        'identity_number_encrypted',
        'verification_data',
    ];

    /**
     * Get the user that owns this identity verification.
     *
     * @return BelongsTo<User,IdentityVerification>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get only pending verifications.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('verification_status', 'pending');
    }

    /**
     * Scope to get only verified identities.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeVerified($query)
    {
        return $query->where('verification_status', 'verified');
    }

    /**
     * Scope to get only failed verifications.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFailed($query)
    {
        return $query->where('verification_status', 'failed');
    }

    /**
     * Scope to get only BVN verifications.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeBvn($query)
    {
        return $query->where('verification_type', 'bvn');
    }

    /**
     * Scope to get only NIN verifications.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNin($query)
    {
        return $query->where('verification_type', 'nin');
    }

    /**
     * Check if the identity is verified.
     */
    public function isVerified(): bool
    {
        return $this->verification_status === 'verified';
    }

    /**
     * Check if the verification is pending.
     */
    public function isPending(): bool
    {
        return $this->verification_status === 'pending';
    }

    /**
     * Check if the verification failed.
     */
    public function isFailed(): bool
    {
        return $this->verification_status === 'failed';
    }

    /**
     * Check if the verification is in progress.
     */
    public function isInProgress(): bool
    {
        return $this->verification_status === 'in_progress';
    }

    /**
     * Check if the verification has expired.
     */
    public function isExpired(): bool
    {
        if ($this->verification_status === 'expired') {
            return true;
        }

        if ($this->expires_at && $this->expires_at->isPast()) {
            return true;
        }

        return false;
    }

    /**
     * Check if this is a BVN verification.
     */
    public function isBvn(): bool
    {
        return $this->verification_type === 'bvn';
    }

    /**
     * Check if this is a NIN verification.
     */
    public function isNin(): bool
    {
        return $this->verification_type === 'nin';
    }

    /**
     * Mark verification as successful.
     */
    public function markAsVerified(array $verificationData, array $dataMatches, float $matchPercentage, int $score = 100): void
    {
        $this->update([
            'verification_status' => 'verified',
            'verification_data' => $verificationData,
            'data_matches' => $dataMatches,
            'match_percentage' => $matchPercentage,
            'verification_score' => $score,
            'verified_at' => now(),
            'failure_reason' => null,
            'expires_at' => now()->addYear(), // Identity verification valid for 1 year
        ]);
    }

    /**
     * Mark verification as failed.
     */
    public function markAsFailed(string $reason): void
    {
        $this->update([
            'verification_status' => 'failed',
            'failure_reason' => $reason,
        ]);
    }

    /**
     * Mark verification as in progress.
     */
    public function markAsInProgress(string $externalReference): void
    {
        $this->update([
            'verification_status' => 'in_progress',
            'external_reference' => $externalReference,
        ]);
    }

    /**
     * Add verification attempt to log.
     */
    public function logVerificationAttempt(array $attemptData): void
    {
        $attempts = $this->verification_attempts ?? [];
        $attempts[] = array_merge($attemptData, [
            'timestamp' => now()->toISOString(),
        ]);

        $this->update([
            'verification_attempts' => $attempts,
        ]);
    }

    /**
     * Get the number of verification attempts.
     */
    public function getVerificationAttemptCount(): int
    {
        return count($this->verification_attempts ?? []);
    }

    /**
     * Get verification type label.
     */
    public function getVerificationTypeLabel(): string
    {
        return match ($this->verification_type) {
            'bvn' => 'Bank Verification Number (BVN)',
            'nin' => 'National Identification Number (NIN)',
            default => ucwords($this->verification_type),
        };
    }

    /**
     * Get verification status label.
     */
    public function getVerificationStatusLabel(): string
    {
        return match ($this->verification_status) {
            'pending' => 'Pending Verification',
            'in_progress' => 'Verification in Progress',
            'verified' => 'Verified',
            'failed' => 'Verification Failed',
            'expired' => 'Verification Expired',
            default => ucwords($this->verification_status),
        };
    }

    /**
     * Get verification status color for UI.
     */
    public function getVerificationStatusColor(): string
    {
        return match ($this->verification_status) {
            'pending' => 'yellow',
            'in_progress' => 'blue',
            'verified' => 'green',
            'failed' => 'red',
            'expired' => 'gray',
            default => 'gray',
        };
    }

    /**
     * Get data match quality label.
     */
    public function getMatchQualityLabel(): string
    {
        if (! $this->match_percentage) {
            return 'Not Available';
        }

        return match (true) {
            $this->match_percentage >= 95 => 'Excellent Match',
            $this->match_percentage >= 85 => 'Good Match',
            $this->match_percentage >= 70 => 'Fair Match',
            default => 'Poor Match',
        };
    }

    /**
     * Check if verification requires renewal.
     */
    public function requiresRenewal(): bool
    {
        if (! $this->expires_at || ! $this->isVerified()) {
            return false;
        }

        // Require renewal 30 days before expiration
        return $this->expires_at->subDays(30)->isPast();
    }

    /**
     * Get days until expiration.
     */
    public function getDaysUntilExpiration(): ?int
    {
        if (! $this->expires_at) {
            return null;
        }

        return max(0, now()->diffInDays($this->expires_at, false));
    }
}
