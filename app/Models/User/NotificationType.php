<?php

namespace App\Models\User;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;

/**
 * @property string $id
 * @property string $name
 * @property string|null $description
 * @property array<array-key, mixed> $available_channels Array of available notification channels
 * @property array<array-key, mixed>|null $template Notification template configuration
 * @property bool $is_active
 * @property int $priority 1=low, 2=medium, 3=high, 4=urgent
 * @property string|null $category Notification category for grouping
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read array<NotificationChannel> $channels
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\User\Notification> $notifications
 * @property-read int|null $notifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\User\UserNotificationPreference> $userPreferences
 * @property-read int|null $user_preferences_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationType query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationType whereAvailableChannels($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationType whereCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationType whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationType whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationType wherePriority($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationType whereTemplate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|NotificationType whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class NotificationType extends Model
{
    use HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'description',
        'available_channels',
        'template',
        'is_active',
        'priority',
        'category',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'available_channels' => 'array',
        'template' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the notifications of this type.
     */
    public function notifications()
    {
        return $this->hasMany(Notification::class, 'type', 'name');
    }

    /**
     * Get the notification preferences for this type.
     */
    public function userPreferences()
    {
        return $this->hasMany(UserNotificationPreference::class, 'notification_type', 'name');
    }

    /**
     * Get the available notification channels.
     *
     * @return array<NotificationChannel>
     */
    public function getChannelsAttribute(): array
    {
        return array_map(
            fn (string $channel) => NotificationChannel::from($channel),
            $this->available_channels ?? []
        );
    }
}
