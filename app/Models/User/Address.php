<?php

declare(strict_types=1);

namespace App\Models\User;

use App\Models\Core\City;
use App\Models\Core\Country;
use App\Models\Core\State;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property string $id
 * @property string|null $user_id
 * @property string $addressable_id
 * @property string $addressable_type Polymorphic (e.g., business, order, business_branch, provider_branch, user, user_delivery)
 * @property string|null $label
 * @property string $street_address
 * @property string|null $city_id
 * @property string $city_name_string Store name as string for flexibility/display if city_id null
 * @property string|null $state_id
 * @property string|null $state_province_string Store state/province as string
 * @property string $country_id
 * @property string|null $postal_code
 * @property numeric|null $latitude
 * @property numeric|null $longitude
 * @property bool $is_default
 * @property string|null $delivery_instructions
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Model|\Eloquent $addressable
 * @property-read City|null $city
 * @property-read Country $country
 * @property-read string $city_display_name
 * @property-read string $full_address
 * @property-read string|null $state_display_name
 * @property-read State|null $state
 * @property-read \App\Models\User\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address default()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address forAddressable(string $addressableId, string $addressableType)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereAddressableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereAddressableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereCityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereCityNameString($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereDeliveryInstructions($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereIsDefault($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereLabel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address wherePostalCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereStateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereStateProvinceString($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereStreetAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Address whereUserId($value)
 * @method static \Database\Factories\AddressFactory factory($count = null, $state = [])
 *
 * @mixin \Eloquent
 */
class Address extends Model
{
    use HasFactory, HasUuids;

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): \Database\Factories\AddressFactory
    {
        return \Database\Factories\AddressFactory::new();
    }

    protected $fillable = [
        'id',
        'user_id',
        'addressable_id',
        'addressable_type',
        'label',
        'street_address',
        'city_id',
        'city_name_string',
        'state_id',
        'state_province_string',
        'country_id',
        'postal_code',
        'latitude',
        'longitude',
        'is_default',
        'delivery_instructions',
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'is_default' => 'boolean',
    ];

    /**
     * Get the user that owns the address.
     *
     * @return BelongsTo<User,Address>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the city for the address.
     *
     * @return BelongsTo<City,Address>
     */
    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class, 'city_id');
    }

    /**
     * Get the state for the address.
     *
     * @return BelongsTo<State,Address>
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    /**
     * Get the country for the address.
     *
     * @return BelongsTo<Country,Address>
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    /**
     * Get the polymorphic addressable entity.
     */
    public function addressable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope to get default addresses.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope to get addresses for a specific addressable entity.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForAddressable($query, string $addressableId, string $addressableType)
    {
        return $query->where('addressable_id', $addressableId)
            ->where('addressable_type', $addressableType);
    }

    /**
     * Get the full formatted address.
     */
    public function getFullAddressAttribute(): string
    {
        $parts = array_filter([
            $this->street_address,
            $this->city_name_string,
            $this->state_province_string,
            $this->country?->name,
            $this->postal_code,
        ]);

        return implode(', ', $parts);
    }

    /**
     * Get the display name for the city.
     */
    public function getCityDisplayNameAttribute(): string
    {
        return $this->city?->name ?? $this->city_name_string;
    }

    /**
     * Get the display name for the state.
     */
    public function getStateDisplayNameAttribute(): ?string
    {
        return $this->state?->name ?? $this->state_province_string;
    }

    /**
     * Check if the address has coordinates.
     */
    public function hasCoordinates(): bool
    {
        return ! is_null($this->latitude) && ! is_null($this->longitude);
    }
}
