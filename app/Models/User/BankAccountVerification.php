<?php

declare(strict_types=1);

namespace App\Models\User;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $user_id
 * @property string $account_number
 * @property string $bank_code
 * @property string $account_name
 * @property string|null $bvn_encrypted
 * @property string $verification_status
 * @property string|null $paystack_reference
 * @property array<array-key, mixed>|null $verification_data
 * @property int|null $verification_score
 * @property \Illuminate\Support\Carbon|null $verified_at
 * @property string|null $failure_reason
 * @property \Illuminate\Support\Carbon|null $expires_at
 * @property array<array-key, mixed>|null $verification_attempts
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification failed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification whereAccountName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification whereAccountNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification whereBankCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification whereBvnEncrypted($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification whereFailureReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification wherePaystackReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification whereVerificationAttempts($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification whereVerificationData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification whereVerificationScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification whereVerificationStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification whereVerifiedAt($value)
 *
 * @property string|null $qoreid_reference
 * @property string|null $risk_level
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification whereQoreidReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BankAccountVerification whereRiskLevel($value)
 *
 * @mixin \Eloquent
 */
class BankAccountVerification extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'user_id',
        'account_number',
        'bank_code',
        'account_name',
        'bvn_encrypted',
        'verification_status',
        'paystack_reference',
        'verification_data',
        'verification_score',
        'verified_at',
        'failure_reason',
        'expires_at',
        'verification_attempts',
    ];

    protected $casts = [
        'verification_data' => 'array',
        'verification_score' => 'integer',
        'verified_at' => 'datetime',
        'expires_at' => 'datetime',
        'verification_attempts' => 'array',
    ];

    protected $hidden = [
        'bvn_encrypted',
        'verification_data',
    ];

    /**
     * Get the user that owns this bank account verification.
     *
     * @return BelongsTo<User,BankAccountVerification>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Scope to get only pending verifications.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('verification_status', 'pending');
    }

    /**
     * Scope to get only verified accounts.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeVerified($query)
    {
        return $query->where('verification_status', 'verified');
    }

    /**
     * Scope to get only failed verifications.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFailed($query)
    {
        return $query->where('verification_status', 'failed');
    }

    /**
     * Check if the bank account is verified.
     */
    public function isVerified(): bool
    {
        return $this->verification_status === 'verified';
    }

    /**
     * Check if the verification is pending.
     */
    public function isPending(): bool
    {
        return $this->verification_status === 'pending';
    }

    /**
     * Check if the verification failed.
     */
    public function isFailed(): bool
    {
        return $this->verification_status === 'failed';
    }

    /**
     * Check if the verification is in progress.
     */
    public function isInProgress(): bool
    {
        return $this->verification_status === 'in_progress';
    }

    /**
     * Check if the verification has expired.
     */
    public function isExpired(): bool
    {
        if ($this->verification_status === 'expired') {
            return true;
        }

        if ($this->expires_at && $this->expires_at->isPast()) {
            return true;
        }

        return false;
    }

    /**
     * Mark verification as successful.
     */
    public function markAsVerified(array $verificationData, int $score = 100): void
    {
        $this->update([
            'verification_status' => 'verified',
            'verification_data' => $verificationData,
            'verification_score' => $score,
            'verified_at' => now(),
            'failure_reason' => null,
            'expires_at' => now()->addYear(), // Bank verification valid for 1 year
        ]);
    }

    /**
     * Mark verification as failed.
     */
    public function markAsFailed(string $reason): void
    {
        $this->update([
            'verification_status' => 'failed',
            'failure_reason' => $reason,
        ]);
    }

    /**
     * Mark verification as in progress.
     */
    public function markAsInProgress(string $paystackReference): void
    {
        $this->update([
            'verification_status' => 'in_progress',
            'paystack_reference' => $paystackReference,
        ]);
    }

    /**
     * Add verification attempt to log.
     */
    public function logVerificationAttempt(array $attemptData): void
    {
        $attempts = $this->verification_attempts ?? [];
        $attempts[] = array_merge($attemptData, [
            'timestamp' => now()->toISOString(),
        ]);

        $this->update([
            'verification_attempts' => $attempts,
        ]);
    }

    /**
     * Get the number of verification attempts.
     */
    public function getVerificationAttemptCount(): int
    {
        return count($this->verification_attempts ?? []);
    }

    /**
     * Get verification status label.
     */
    public function getVerificationStatusLabel(): string
    {
        return match ($this->verification_status) {
            'pending' => 'Pending Verification',
            'in_progress' => 'Verification in Progress',
            'verified' => 'Verified',
            'failed' => 'Verification Failed',
            'expired' => 'Verification Expired',
            default => ucwords($this->verification_status),
        };
    }

    /**
     * Get verification status color for UI.
     */
    public function getVerificationStatusColor(): string
    {
        return match ($this->verification_status) {
            'pending' => 'yellow',
            'in_progress' => 'blue',
            'verified' => 'green',
            'failed' => 'red',
            'expired' => 'gray',
            default => 'gray',
        };
    }

    /**
     * Get masked account number for display.
     */
    public function getMaskedAccountNumber(): string
    {
        $accountNumber = $this->account_number;
        $length = strlen($accountNumber);

        if ($length <= 4) {
            return $accountNumber;
        }

        return str_repeat('*', $length - 4).substr($accountNumber, -4);
    }

    /**
     * Get bank name from bank code.
     */
    public function getBankName(): string
    {
        // This would typically come from a banks lookup table or service
        $bankCodes = [
            '044' => 'Access Bank',
            '014' => 'Afribank Nigeria Plc',
            '023' => 'Citibank Nigeria Limited',
            '050' => 'Ecobank Nigeria Plc',
            '011' => 'First Bank of Nigeria Limited',
            '214' => 'First City Monument Bank Limited',
            '058' => 'Guaranty Trust Bank Plc',
            '030' => 'Heritage Banking Company Ltd',
            '082' => 'Keystone Bank Limited',
            '076' => 'Skye Bank Plc',
            '221' => 'Stanbic IBTC Bank Plc',
            '068' => 'Standard Chartered Bank Nigeria Limited',
            '232' => 'Sterling Bank Plc',
            '032' => 'Union Bank of Nigeria Plc',
            '033' => 'United Bank for Africa Plc',
            '215' => 'Unity Bank Plc',
            '035' => 'Wema Bank Plc',
            '057' => 'Zenith Bank Plc',
        ];

        return $bankCodes[$this->bank_code] ?? 'Unknown Bank';
    }

    /**
     * Check if verification requires renewal.
     */
    public function requiresRenewal(): bool
    {
        if (! $this->expires_at || ! $this->isVerified()) {
            return false;
        }

        // Require renewal 30 days before expiration
        return $this->expires_at->subDays(30)->isPast();
    }

    /**
     * Get days until expiration.
     */
    public function getDaysUntilExpiration(): ?int
    {
        if (! $this->expires_at) {
            return null;
        }

        return max(0, now()->diffInDays($this->expires_at, false));
    }
}
