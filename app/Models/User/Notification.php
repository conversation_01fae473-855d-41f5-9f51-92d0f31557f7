<?php

declare(strict_types=1);

namespace App\Models\User;

use App\Enums\System\NotificationChannel;
use App\Models\Business\Business;
use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $type
 * @property string $notifiable_type
 * @property int $notifiable_id
 * @property array<array-key, mixed> $data
 * @property \Illuminate\Support\Carbon|null $read_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $tenant_id
 * @property string|null $business_id
 * @property string|null $title
 * @property string|null $message
 * @property NotificationChannel|null $channel e.g., push, email, sms, in_app
 * @property \Illuminate\Support\Carbon|null $sent_at
 * @property-read \App\Models\System\Tenant|null $tenant
 * @property-read \App\Models\User\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification byChannel(\App\Enums\System\NotificationChannel $channel)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification byType(string $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification forUser(string $userId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification read()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification recent(int $days = 7)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification sent()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification unread()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification whereChannel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification whereData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification whereMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification whereNotifiableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification whereNotifiableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification whereReadAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification whereSentAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Notification whereUpdatedAt($value)
 *
 * @property-read Business|null $business
 * @property-read Model|\Eloquent $notifiable
 *
 * @mixin \Eloquent
 */
class Notification extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'notifiable_type',
        'notifiable_id',
        'tenant_id',
        'business_id',
        'type',
        'title',
        'message',
        'channel',
        'data',
        'read_at',
        'sent_at',
    ];

    protected $casts = [
        'channel' => NotificationChannel::class,
        'data' => 'array',
        'read_at' => 'datetime',
        'sent_at' => 'datetime',
    ];

    /**
     * Get the notifiable entity (polymorphic relationship).
     *
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function notifiable()
    {
        return $this->morphTo();
    }

    /**
     * Get the user that owns the notification (for backward compatibility).
     *
     * @return BelongsTo<User,Notification>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'notifiable_id')
            ->where('notifiable_type', 'App\\Models\\User\\User');
    }

    /**
     * Get the tenant associated with the notification.
     *
     * @return BelongsTo<Tenant,Notification>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the business associated with the notification.
     *
     * @return BelongsTo<Business,Notification>
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }

    /**
     * Scope to get unread notifications.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUnread($query)
    {
        return $query->whereNull('read_at');
    }

    /**
     * Scope to get read notifications.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRead($query)
    {
        return $query->whereNotNull('read_at');
    }

    /**
     * Scope to get sent notifications.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSent($query)
    {
        return $query->whereNotNull('sent_at');
    }

    /**
     * Scope to get pending notifications.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->whereNull('sent_at');
    }

    /**
     * Scope to get notifications by channel.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByChannel($query, NotificationChannel $channel)
    {
        return $query->where('channel', $channel);
    }

    /**
     * Scope to get notifications by type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get notifications for a specific user.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForUser($query, string $userId)
    {
        return $query->where('notifiable_type', 'App\\Models\\User\\User')
            ->where('notifiable_id', $userId);
    }

    /**
     * Scope to get recent notifications.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Check if the notification is read.
     */
    public function isRead(): bool
    {
        return ! is_null($this->read_at);
    }

    /**
     * Check if the notification is unread.
     */
    public function isUnread(): bool
    {
        return is_null($this->read_at);
    }

    /**
     * Check if the notification has been sent.
     */
    public function isSent(): bool
    {
        return ! is_null($this->sent_at);
    }

    /**
     * Check if the notification is pending.
     */
    public function isPending(): bool
    {
        return is_null($this->sent_at);
    }

    /**
     * Mark the notification as read.
     */
    public function markAsRead(): bool
    {
        if ($this->isRead()) {
            return true;
        }

        return $this->update(['read_at' => now()]);
    }

    /**
     * Mark the notification as unread.
     */
    public function markAsUnread(): bool
    {
        return $this->update(['read_at' => null]);
    }

    /**
     * Mark the notification as sent.
     */
    public function markAsSent(): bool
    {
        if ($this->isSent()) {
            return true;
        }

        return $this->update(['sent_at' => now()]);
    }

    /**
     * Get the notification's action URL from data.
     */
    public function getActionUrl(): ?string
    {
        return $this->data['action_url'] ?? $this->data['url'] ?? null;
    }

    /**
     * Get the notification's route from data.
     */
    public function getRoute(): ?string
    {
        return $this->data['route'] ?? null;
    }

    /**
     * Get the notification icon.
     */
    public function getIcon(): string
    {
        return match ($this->type) {
            'order_update' => 'fas fa-shopping-bag',
            'delivery_assigned' => 'fas fa-truck',
            'payment_processed' => 'fas fa-credit-card',
            'payout_processed' => 'fas fa-money-bill-wave',
            'subscription_renewal' => 'fas fa-sync-alt',
            'verification_update' => 'fas fa-shield-alt',
            'new_message' => 'fas fa-envelope',
            'rating_received' => 'fas fa-star',
            'promotion_available' => 'fas fa-tags',
            'system_announcement' => 'fas fa-bullhorn',
            default => 'fas fa-bell',
        };
    }

    /**
     * Get the notification color class.
     */
    public function getColorClass(): string
    {
        return match ($this->type) {
            'order_update' => 'text-blue-600',
            'delivery_assigned' => 'text-green-600',
            'payment_processed' => 'text-purple-600',
            'payout_processed' => 'text-emerald-600',
            'subscription_renewal' => 'text-orange-600',
            'verification_update' => 'text-indigo-600',
            'new_message' => 'text-cyan-600',
            'rating_received' => 'text-yellow-600',
            'promotion_available' => 'text-pink-600',
            'system_announcement' => 'text-red-600',
            default => 'text-gray-600',
        };
    }

    /**
     * Get the time ago formatted string.
     */
    public function getTimeAgo(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Get the priority level of the notification.
     */
    public function getPriority(): string
    {
        return $this->data['priority'] ?? 'normal';
    }

    /**
     * Check if the notification is high priority.
     */
    public function isHighPriority(): bool
    {
        return $this->getPriority() === 'high';
    }

    /**
     * Get related entity ID from data.
     */
    public function getRelatedEntityId(string $entityType): ?string
    {
        return $this->data["{$entityType}_id"] ?? null;
    }

    /**
     * Create a notification for multiple users.
     */
    public static function createForUsers(array $userIds, array $notificationData): int
    {
        $notifications = [];
        $now = now();

        foreach ($userIds as $userId) {
            $notifications[] = array_merge($notificationData, [
                'id' => \Illuminate\Support\Str::uuid(),
                'notifiable_type' => 'App\\Models\\User\\User',
                'notifiable_id' => $userId,
                'created_at' => $now,
                'updated_at' => $now,
            ]);
        }

        static::insert($notifications);

        return count($notifications);
    }

    /**
     * Get the display title with fallback.
     */
    public function getDisplayTitle(): string
    {
        if ($this->title) {
            return $this->title;
        }

        return match ($this->type) {
            'order_update' => 'Order Update',
            'delivery_assigned' => 'Delivery Assigned',
            'payment_processed' => 'Payment Processed',
            'payout_processed' => 'Payout Processed',
            'subscription_renewal' => 'Subscription Renewal',
            'verification_update' => 'Verification Update',
            'new_message' => 'New Message',
            'rating_received' => 'New Rating',
            'promotion_available' => 'New Promotion',
            'system_announcement' => 'System Announcement',
            default => 'Notification',
        };
    }

    /**
     * Get notification summary for API responses.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'title' => $this->getDisplayTitle(),
            'message' => $this->message,
            'type' => $this->type,
            'is_read' => $this->isRead(),
            'time_ago' => $this->getTimeAgo(),
            'action_url' => $this->getActionUrl(),
            'icon' => $this->getIcon(),
            'color_class' => $this->getColorClass(),
            'priority' => $this->getPriority(),
        ];
    }
}
