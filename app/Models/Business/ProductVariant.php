<?php

declare(strict_types=1);

namespace App\Models\Business;

use App\Models\Delivery\OrderItem;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $product_id
 * @property string $name
 * @property string $price_adjustment Adjustment in the product's currency
 * @property string|null $sku
 * @property int $quantity -1 means unlimited (Current stock level for this variant)
 * @property bool $is_default
 * @property array<array-key, mixed> $attributes e.g., {"color": "Red", "size": "Large"} - Use this for inherent variant properties, NOT selectable options
 * @property string|null $image_url
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $display_name
 * @property-read \App\Models\Business\Product $product
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductVariant available()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductVariant inStock()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductVariant newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductVariant newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductVariant query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductVariant whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductVariant whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductVariant whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductVariant whereImageUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductVariant whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductVariant whereIsDefault($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductVariant whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductVariant wherePriceAdjustment($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductVariant whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductVariant whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductVariant whereSku($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductVariant whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductVariant whereUpdatedAt($value)
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, OrderItem> $orderItems
 * @property-read int|null $order_items_count
 *
 * @mixin \Eloquent
 */
class ProductVariant extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'product_id',
        'name',
        'sku',
        'barcode',
        'price',
        'cost_price',
        'quantity',
        'attributes',
        'is_available',
        'main_image_url',
        'additional_images',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'quantity' => 'integer',
        'attributes' => 'array',
        'is_available' => 'boolean',
        'additional_images' => 'array',
    ];

    /**
     * Get the product that owns the variant.
     *
     * @return BelongsTo<Product,ProductVariant>
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    /**
     * Get the order items for this variant.
     *
     * @return HasMany<OrderItem,ProductVariant>
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class, 'variant_id');
    }

    /**
     * Scope to get available variants.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope to get variants in stock.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInStock($query)
    {
        return $query->where(function ($query) {
            $query->where('quantity', '>', 0)
                ->orWhere('quantity', -1);
        });
    }

    /**
     * Check if the variant is in stock.
     */
    public function isInStock(): bool
    {
        return $this->quantity > 0 || $this->quantity === -1;
    }

    /**
     * Check if the variant has unlimited stock.
     */
    public function hasUnlimitedStock(): bool
    {
        return $this->quantity === -1;
    }

    /**
     * Get the effective price (with product fallback).
     */
    public function getEffectivePrice(): float
    {
        return $this->price ?? $this->product->price;
    }

    /**
     * Get the profit margin.
     */
    public function getProfitMargin(): ?float
    {
        $effectivePrice = $this->getEffectivePrice();
        $costPrice = $this->cost_price ?? $this->product->cost_price;

        if (is_null($costPrice) || $effectivePrice == 0) {
            return null;
        }

        return (($effectivePrice - $costPrice) / $effectivePrice) * 100;
    }

    /**
     * Reduce the variant quantity.
     */
    public function reduceQuantity(int $quantity): bool
    {
        if ($this->quantity === -1) {
            return true;
        }

        if ($this->quantity < $quantity) {
            return false;
        }

        $this->quantity -= $quantity;

        return $this->save();
    }

    /**
     * Increase the variant quantity.
     */
    public function increaseQuantity(int $quantity): bool
    {
        if ($this->quantity === -1) {
            return true;
        }

        $this->quantity += $quantity;

        return $this->save();
    }

    /**
     * Get the display name with product name.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->product->name.' - '.$this->name;
    }

    /**
     * Get all images (main + additional).
     */
    public function getAllImages(): array
    {
        $images = [];

        if ($this->main_image_url) {
            $images[] = $this->main_image_url;
        } elseif ($this->product->main_image_url) {
            $images[] = $this->product->main_image_url;
        }

        if ($this->additional_images) {
            $images = array_merge($images, $this->additional_images);
        } elseif ($this->product->additional_images) {
            $images = array_merge($images, $this->product->additional_images);
        }

        return $images;
    }
}
