<?php

declare(strict_types=1);

namespace App\Models\Business;

use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $purchase_order_id
 * @property string $product_id
 * @property string|null $variant_id
 * @property int $quantity
 * @property numeric $unit_cost Cost per unit at the time of PO
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $display_name
 * @property-read string $formatted_total_cost
 * @property-read string $formatted_unit_cost
 * @property-read \App\Models\Business\Product $product
 * @property-read \App\Models\Business\PurchaseOrder $purchaseOrder
 * @property-read \App\Models\System\Tenant $tenant
 * @property-read \App\Models\Business\ProductVariant|null $variant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PurchaseOrderItem forProduct(string $productId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PurchaseOrderItem forPurchaseOrder(string $purchaseOrderId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PurchaseOrderItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PurchaseOrderItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PurchaseOrderItem query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PurchaseOrderItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PurchaseOrderItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PurchaseOrderItem whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PurchaseOrderItem wherePurchaseOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PurchaseOrderItem whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PurchaseOrderItem whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PurchaseOrderItem whereUnitCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PurchaseOrderItem whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PurchaseOrderItem whereVariantId($value)
 *
 * @mixin \Eloquent
 */
class PurchaseOrderItem extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'purchase_order_id',
        'product_id',
        'variant_id',
        'quantity',
        'unit_cost',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'unit_cost' => 'decimal:2',
    ];

    /**
     * Get the tenant that owns the purchase order item.
     *
     * @return BelongsTo<Tenant,PurchaseOrderItem>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the purchase order that owns this item.
     *
     * @return BelongsTo<PurchaseOrder,PurchaseOrderItem>
     */
    public function purchaseOrder(): BelongsTo
    {
        return $this->belongsTo(PurchaseOrder::class, 'purchase_order_id');
    }

    /**
     * Get the product for this purchase order item.
     *
     * @return BelongsTo<Product,PurchaseOrderItem>
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    /**
     * Get the product variant for this purchase order item.
     *
     * @return BelongsTo<ProductVariant,PurchaseOrderItem>
     */
    public function variant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class, 'variant_id');
    }

    /**
     * Scope to get items by purchase order.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForPurchaseOrder($query, string $purchaseOrderId)
    {
        return $query->where('purchase_order_id', $purchaseOrderId);
    }

    /**
     * Scope to get items by product.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForProduct($query, string $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * Check if this purchase order item uses a variant.
     */
    public function hasVariant(): bool
    {
        return ! is_null($this->variant_id);
    }

    /**
     * Get the total cost for this purchase order item.
     */
    public function getTotalCost(): float
    {
        return $this->unit_cost * $this->quantity;
    }

    /**
     * Get the display name for this purchase order item.
     */
    public function getDisplayNameAttribute(): string
    {
        $name = $this->product->name;

        if ($this->hasVariant()) {
            $name .= ' - '.$this->variant->name;
        }

        return $name;
    }

    /**
     * Get formatted unit cost with currency.
     */
    public function getFormattedUnitCostAttribute(): string
    {
        return $this->purchaseOrder->business->currency.' '.number_format($this->unit_cost, 2);
    }

    /**
     * Get formatted total cost with currency.
     */
    public function getFormattedTotalCostAttribute(): string
    {
        // TODO:: fIGURE WHAT THIS IS
        return $this->purchaseOrder->business->currency.' '.number_format($this->getTotalCost(), 2);
    }

    /**
     * Update product/variant cost price based on this purchase order item.
     */
    public function updateProductCostPrice(): void
    {
        if ($this->hasVariant()) {
            $this->variant->update(['cost_price' => $this->unit_cost]);
        } else {
            $this->product->update(['cost_price' => $this->unit_cost]);
        }
    }

    /**
     * Get purchase order item summary.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'purchase_order_id' => $this->purchase_order_id,
            'product_id' => $this->product_id,
            'product_name' => $this->product->name,
            'variant_id' => $this->variant_id,
            'variant_name' => $this->variant?->name,
            'display_name' => $this->display_name,
            'quantity' => $this->quantity,
            'unit_cost' => $this->unit_cost,
            'total_cost' => $this->getTotalCost(),
            'formatted_unit_cost' => $this->formatted_unit_cost,
            'formatted_total_cost' => $this->formatted_total_cost,
            'has_variant' => $this->hasVariant(),
        ];
    }
}
