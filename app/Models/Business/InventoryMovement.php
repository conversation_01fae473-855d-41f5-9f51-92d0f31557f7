<?php

declare(strict_types=1);

namespace App\Models\Business;

use App\Enums\Business\InventoryMovementType;
use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $business_id
 * @property string|null $business_branch_id
 * @property string $product_id
 * @property string|null $variant_id
 * @property mixed $type e.g., stock_in, stock_out, adjustment_increase, manufactured
 * @property int $quantity_change The quantity added (positive) or removed (negative)
 * @property int|null $current_stock Stock level after this movement (for auditing)
 * @property string|null $source_id Polymorphic FK to the source of the movement (e.g., order_item, purchase_order_item, adjustment record, manufacturing record)
 * @property string|null $source_type Polymorphic type (e.g., order_items, purchase_order_items, inventory_adjustments, production_runs)
 * @property string|null $notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Business\Business $business
 * @property-read \App\Models\Business\BusinessBranch|null $businessBranch
 * @property-read \App\Models\Business\Product $product
 * @property-read Model|\Eloquent|null $source
 * @property-read \App\Models\System\Tenant $tenant
 * @property-read \App\Models\Business\ProductVariant|null $variant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement byType(\App\Enums\InventoryMovementType $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement forBranch(string $branchId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement forProduct(string $productId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement forVariant(string $variantId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement recent(int $days = 7)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement stockIn()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement stockOut()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement whereBusinessBranchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement whereCurrentStock($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement whereQuantityChange($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement whereSourceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement whereSourceType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement whereVariantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InventoryMovement withinDateRange(string $startDate, string $endDate)
 *
 * @mixin \Eloquent
 */
class InventoryMovement extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'business_id',
        'business_branch_id',
        'product_id',
        'variant_id',
        'type',
        'quantity_change',
        'current_stock',
        'source_id',
        'source_type',
        'notes',
    ];

    protected $casts = [
        'type' => InventoryMovementType::class,
        'quantity_change' => 'integer',
        'current_stock' => 'integer',
    ];

    /**
     * Get the tenant that owns the inventory movement.
     *
     * @return BelongsTo<Tenant,InventoryMovement>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the business that owns the inventory movement.
     *
     * @return BelongsTo<Business,InventoryMovement>
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }

    /**
     * Get the business branch where the movement occurred.
     *
     * @return BelongsTo<BusinessBranch,InventoryMovement>
     */
    public function businessBranch(): BelongsTo
    {
        return $this->belongsTo(BusinessBranch::class, 'business_branch_id');
    }

    /**
     * Get the product for this inventory movement.
     *
     * @return BelongsTo<Product,InventoryMovement>
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    /**
     * Get the product variant for this inventory movement.
     *
     * @return BelongsTo<ProductVariant,InventoryMovement>
     */
    public function variant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class, 'variant_id');
    }

    /**
     * Get the polymorphic source of the movement.
     */
    public function source(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope to get movements by type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByType($query, InventoryMovementType $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get stock-in movements.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeStockIn($query)
    {
        return $query->where('quantity_change', '>', 0);
    }

    /**
     * Scope to get stock-out movements.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeStockOut($query)
    {
        return $query->where('quantity_change', '<', 0);
    }

    /**
     * Scope to get movements for a specific product.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForProduct($query, string $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * Scope to get movements for a specific variant.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForVariant($query, string $variantId)
    {
        return $query->where('variant_id', $variantId);
    }

    /**
     * Scope to get movements for a specific branch.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForBranch($query, string $branchId)
    {
        return $query->where('business_branch_id', $branchId);
    }

    /**
     * Scope to get movements within a date range.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithinDateRange($query, string $startDate, string $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Scope to get recent movements.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecent($query, int $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Check if this is a stock increase movement.
     */
    public function isStockIncrease(): bool
    {
        return $this->quantity_change > 0;
    }

    /**
     * Check if this is a stock decrease movement.
     */
    public function isStockDecrease(): bool
    {
        return $this->quantity_change < 0;
    }

    /**
     * Check if this movement involves a variant.
     */
    public function hasVariant(): bool
    {
        return ! is_null($this->variant_id);
    }

    /**
     * Get the absolute quantity change.
     */
    public function getAbsoluteQuantityChange(): int
    {
        return abs($this->quantity_change);
    }

    /**
     * Get the movement direction as a string.
     */
    public function getDirection(): string
    {
        return $this->isStockIncrease() ? 'in' : 'out';
    }

    /**
     * Get the movement type description.
     */
    public function getTypeDescription(): string
    {
        return match ($this->type) {
            InventoryMovementType::STOCK_IN => 'Stock In',
            InventoryMovementType::STOCK_OUT => 'Stock Out',
            InventoryMovementType::ADJUSTMENT_INCREASE => 'Adjustment (Increase)',
            InventoryMovementType::ADJUSTMENT_DECREASE => 'Adjustment (Decrease)',
            InventoryMovementType::MANUFACTURED => 'Manufactured',
            // InventoryMovementType::DAMAGED => 'Damaged',
            // InventoryMovementType::EXPIRED => 'Expired',
            // InventoryMovementType::RETURNED => 'Returned',
            default => 'Unknown',
        };
    }

    /**
     * Get the source description.
     */
    public function getSourceDescription(): string
    {
        if (! $this->source_type || ! $this->source_id) {
            return 'Manual Entry';
        }

        return match ($this->source_type) {
            'order_items' => 'Order Sale',
            'purchase_order_items' => 'Purchase Order',
            'inventory_adjustments' => 'Inventory Adjustment',
            'production_runs' => 'Production',
            'returns' => 'Return',
            default => $this->source_type,
        };
    }

    /**
     * Get the display name for the product/variant.
     */
    public function getProductDisplayName(): string
    {
        $name = $this->product->name;

        if ($this->hasVariant()) {
            $name .= ' - '.$this->variant->name;
        }

        return $name;
    }

    /**
     * Create a stock-in movement.
     */
    public static function createStockIn(
        string $tenantId,
        string $businessId,
        string $productId,
        int $quantity,
        array $additionalData = []
    ): static {
        return static::create(array_merge([
            'tenant_id' => $tenantId,
            'business_id' => $businessId,
            'product_id' => $productId,
            'type' => InventoryMovementType::STOCK_IN,
            'quantity_change' => $quantity,
        ], $additionalData));
    }

    /**
     * Create a stock-out movement.
     */
    public static function createStockOut(
        string $tenantId,
        string $businessId,
        string $productId,
        int $quantity,
        array $additionalData = []
    ): static {
        return static::create(array_merge([
            'tenant_id' => $tenantId,
            'business_id' => $businessId,
            'product_id' => $productId,
            'type' => InventoryMovementType::STOCK_OUT,
            'quantity_change' => -$quantity,
        ], $additionalData));
    }

    /**
     * Create an adjustment movement.
     */
    public static function createAdjustment(
        string $tenantId,
        string $businessId,
        string $productId,
        int $quantityChange,
        string $reason = '',
        array $additionalData = []
    ): static {
        $type = $quantityChange > 0
            ? InventoryMovementType::ADJUSTMENT_INCREASE
            : InventoryMovementType::ADJUSTMENT_DECREASE;

        return static::create(array_merge([
            'tenant_id' => $tenantId,
            'business_id' => $businessId,
            'product_id' => $productId,
            'type' => $type,
            'quantity_change' => $quantityChange,
            'notes' => $reason,
        ], $additionalData));
    }

    /**
     * Get movement summary for API responses.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type->value,
            'type_description' => $this->getTypeDescription(),
            'product_name' => $this->getProductDisplayName(),
            'quantity_change' => $this->quantity_change,
            'absolute_quantity' => $this->getAbsoluteQuantityChange(),
            'direction' => $this->getDirection(),
            'current_stock' => $this->current_stock,
            'source_description' => $this->getSourceDescription(),
            'branch_name' => $this->businessBranch?->name,
            'notes' => $this->notes,
            'created_at' => $this->created_at,
        ];
    }

    /**
     * Get inventory statistics for a product.
     */
    public static function getProductStats(string $productId, ?string $variantId = null, int $days = 30): array
    {
        $query = static::forProduct($productId)
            ->where('created_at', '>=', now()->subDays($days));

        if ($variantId) {
            $query->forVariant($variantId);
        }

        $movements = $query->get();

        $totalIn = $movements->where('quantity_change', '>', 0)->sum('quantity_change');
        $totalOut = abs($movements->where('quantity_change', '<', 0)->sum('quantity_change'));
        $netChange = $totalIn - $totalOut;

        return [
            'total_movements' => $movements->count(),
            'total_in' => $totalIn,
            'total_out' => $totalOut,
            'net_change' => $netChange,
            'average_daily_in' => $days > 0 ? round($totalIn / $days, 2) : 0,
            'average_daily_out' => $days > 0 ? round($totalOut / $days, 2) : 0,
        ];
    }
}
