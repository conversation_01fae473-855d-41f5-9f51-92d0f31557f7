<?php

declare(strict_types=1);

namespace App\Models\Business;

use App\Models\Core\Attachment;
use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Laravel\Scout\Searchable;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $business_id
 * @property string|null $product_collection_id
 * @property string $name
 * @property string $slug
 * @property string|null $description
 * @property string|null $image_url
 * @property string|null $parent_id
 * @property int $display_order
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Business\Business $business
 * @property-read \Illuminate\Database\Eloquent\Collection<int, ProductCategory> $children
 * @property-read int|null $children_count
 * @property-read ProductCategory|null $parent
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Business\Product> $products
 * @property-read int|null $products_count
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory ordered()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory root()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory search(string $searchTerm)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory whereDisplayOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory whereImageUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory whereParentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory whereProductCollectionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCategory withProducts()
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Attachment> $attachments
 * @property-read int|null $attachments_count
 *
 * @mixin \Eloquent
 */
class ProductCategory extends Model
{
    use BelongsToTenant, HasFactory, HasUuids, Searchable;

    protected $fillable = [
        'tenant_id',
        'business_id',
        'product_collection_id',
        'parent_id',
        'name',
        'slug',
        'description',
        'image_url',
        'is_active',
        'display_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'display_order' => 'integer',
    ];

    /**
     * Get the tenant that owns the product category.
     *
     * @return BelongsTo<Tenant,ProductCategory>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the business that owns the product category.
     *
     * @return BelongsTo<Business,ProductCategory>
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }

    /**
     * Get the parent category.
     *
     * @return BelongsTo<ProductCategory,ProductCategory>
     */
    public function parent(): BelongsTo
    {
        return $this->belongsTo(ProductCategory::class, 'parent_id');
    }

    /**
     * Get the child categories.
     *
     * @return HasMany<ProductCategory,ProductCategory>
     */
    public function children(): HasMany
    {
        return $this->hasMany(ProductCategory::class, 'parent_id')->orderBy('display_order');
    }

    /**
     * Get all products in this category.
     *
     * @return HasMany<Product,ProductCategory>
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class, 'category_id');
    }

    /**
     * Get the attachments for the category.
     *
     * @return MorphMany<Attachment,ProductCategory>
     */
    public function attachments(): MorphMany
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }

    /**
     * Scope to get only active categories.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get only root categories (no parent).
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRoot($query)
    {
        return $query->whereNull('parent_id');
    }

    /**
     * Scope to get categories ordered by sort order.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order', 'asc')->orderBy('name', 'asc');
    }

    /**
     * Scope to get categories with products.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithProducts($query)
    {
        return $query->has('products');
    }

    /**
     * Scope to search categories by name.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearch($query, string $searchTerm)
    {
        return $query->where(function ($query) use ($searchTerm) {
            $query->where('name', 'like', "%{$searchTerm}%")
                ->orWhere('description', 'like', "%{$searchTerm}%");
        });
    }

    /**
     * Check if this is a root category.
     */
    public function isRoot(): bool
    {
        return is_null($this->parent_id);
    }

    /**
     * Check if this category has children.
     */
    public function hasChildren(): bool
    {
        return $this->children()->exists();
    }

    /**
     * Check if this category has products.
     */
    public function hasProducts(): bool
    {
        return $this->products()->exists();
    }

    /**
     * Get the category depth level.
     */
    public function getDepth(): int
    {
        $depth = 0;
        $parent = $this->parent;

        while ($parent) {
            $depth++;
            $parent = $parent->parent;
        }

        return $depth;
    }

    /**
     * Get all ancestor categories.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getAncestors()
    {
        $ancestors = collect();
        $parent = $this->parent;

        while ($parent) {
            $ancestors->prepend($parent);
            $parent = $parent->parent;
        }

        return $ancestors;
    }

    /**
     * Get all descendant categories.
     *
     * @return \Illuminate\Support\Collection
     */
    public function getDescendants()
    {
        $descendants = collect();

        foreach ($this->children as $child) {
            $descendants->push($child);
            $descendants = $descendants->merge($child->getDescendants());
        }

        return $descendants;
    }

    /**
     * Get the breadcrumb path for this category.
     */
    public function getBreadcrumb(): array
    {
        $breadcrumb = [];
        $ancestors = $this->getAncestors();

        foreach ($ancestors as $ancestor) {
            $breadcrumb[] = [
                'id' => $ancestor->id,
                'name' => $ancestor->name,
                'slug' => $ancestor->slug,
            ];
        }

        $breadcrumb[] = [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
        ];

        return $breadcrumb;
    }

    /**
     * Get the full category path.
     */
    public function getFullPath(): string
    {
        $ancestors = $this->getAncestors();
        $path = $ancestors->pluck('name')->toArray();
        $path[] = $this->name;

        return implode(' > ', $path);
    }

    /**
     * Get products count including subcategories.
     */
    public function getTotalProductsCount(): int
    {
        $count = $this->products()->count();

        foreach ($this->children as $child) {
            $count += $child->getTotalProductsCount();
        }

        return $count;
    }

    /**
     * Move category to a new parent.
     */
    public function moveTo(?string $newParentId): bool
    {
        // Prevent moving to self or descendant
        if ($newParentId === $this->id) {
            return false;
        }

        if ($newParentId) {
            $newParent = static::find($newParentId);
            if (! $newParent || $this->getDescendants()->contains('id', $newParentId)) {
                return false;
            }
        }

        return $this->update(['parent_id' => $newParentId]);
    }

    /**
     * Update the sort order for this category.
     *
     * @param  int  $sortOrder
     */
    public function updateDisplayOrder(int $displayOrder): bool
    {
        return $this->update(['display_order' => $displayOrder]);
    }

    /**
     * Get the category tree structure.
     */
    public function getTreeStructure(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'image_url' => $this->image_url,
            'is_active' => $this->is_active,
            'display_order' => $this->display_order,
            'products_count' => $this->products()->count(),
            'total_products_count' => $this->getTotalProductsCount(),
            'depth' => $this->getDepth(),
            'children' => $this->children->map(function ($child) {
                return $child->getTreeStructure();
            }),
        ];
    }

    /**
     * Get category summary for API responses.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'slug' => $this->slug,
            'description' => $this->description,
            'image_url' => $this->image_url,
            'is_active' => $this->is_active,
            'is_root' => $this->isRoot(),
            'has_children' => $this->hasChildren(),
            'has_products' => $this->hasProducts(),
            'products_count' => $this->products()->count(),
            'children_count' => $this->children()->count(),
            'depth' => $this->getDepth(),
            'full_path' => $this->getFullPath(),
        ];
    }

    /**
     * Create a hierarchical tree from flat collection.
     *
     * @param  \Illuminate\Support\Collection  $categories
     * @return \Illuminate\Support\Collection
     */
    public static function buildTree($categories, ?string $parentId = null)
    {
        return $categories
            ->where('parent_id', $parentId)
            ->map(function ($category) use ($categories) {
                $category->children = static::buildTree($categories, $category->id);

                return $category;
            });
    }

    /**
     * Generate a unique slug for the category.
     */
    public static function generateUniqueSlug(string $name, ?string $businessId = null, ?string $excludeId = null): string
    {
        $slug = \Illuminate\Support\Str::slug($name);
        $originalSlug = $slug;
        $counter = 1;

        while (static::slugExists($slug, $businessId, $excludeId)) {
            $slug = $originalSlug.'-'.$counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Check if a slug exists.
     */
    protected static function slugExists(string $slug, ?string $businessId = null, ?string $excludeId = null): bool
    {
        $query = static::where('slug', $slug);

        if ($businessId) {
            $query->where('business_id', $businessId);
        }

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array<string, mixed>
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'tenant_id' => $this->tenant_id,
            'business_id' => $this->business_id,
            'parent_id' => $this->parent_id,
            'name' => $this->name,
            'description' => $this->description,
            'is_active' => $this->is_active,
            'display_order' => $this->display_order,
            'created_at' => $this->created_at?->timestamp,
        ];
    }

    /**
     * Get the Scout search index name.
     */
    public function searchableAs(): string
    {
        return 'product_categories';
    }

    /**
     * Determine if the model should be searchable.
     */
    public function shouldBeSearchable(): bool
    {
        return $this->is_active;
    }

    /**
     * Get the Scout search key.
     */
    public function getScoutKey(): mixed
    {
        return $this->id;
    }

    /**
     * Get the Scout search key name.
     */
    public function getScoutKeyName(): string
    {
        return 'id';
    }
}
