<?php

declare(strict_types=1);

namespace App\Models\Business;

use App\Enums\Business\ExpenseCategory;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property string $id
 * @property string $expensable_id Polymorphic FK to business or delivery_provider
 * @property string $expensable_type business or delivery_provider
 * @property mixed $category
 * @property string|null $description
 * @property numeric $amount
 * @property string $currency Currency of the expense (matches tenant's primary currency?)
 * @property \Illuminate\Support\Carbon $expense_date The date the expense occurred
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Model|\Eloquent $expensable
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Expense byCategory(\App\Enums\ExpenseCategory $category)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Expense byCurrency(string $currency)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Expense inDateRange(string $startDate, string $endDate)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Expense newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Expense newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Expense query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Expense whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Expense whereCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Expense whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Expense whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Expense whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Expense whereExpensableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Expense whereExpensableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Expense whereExpenseDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Expense whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Expense whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class Expense extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'expensable_id',
        'expensable_type',
        'category',
        'description',
        'amount',
        'currency',
        'expense_date',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'expense_date' => 'date',
        'category' => ExpenseCategory::class,
    ];

    /**
     * Get the entity that the expense belongs to (polymorphic).
     */
    public function expensable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope query to get expenses for specific category.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByCategory($query, ExpenseCategory $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope query to get expenses for a date range.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInDateRange($query, string $startDate, string $endDate)
    {
        return $query->whereBetween('expense_date', [$startDate, $endDate]);
    }

    /**
     * Scope query to get expenses by currency.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }
}
