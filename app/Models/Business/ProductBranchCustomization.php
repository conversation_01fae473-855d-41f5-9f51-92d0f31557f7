<?php

declare(strict_types=1);

namespace App\Models\Business;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $business_id
 * @property string $business_branch_id
 * @property string $product_collection_id
 * @property bool $is_active Override collection active status for this branch
 * @property \Illuminate\Support\Carbon|null $active_start_time Branch-specific start time override
 * @property \Illuminate\Support\Carbon|null $active_end_time Branch-specific end time override
 * @property int|null $display_order Branch-specific display order override
 * @property array<array-key, mixed>|null $custom_settings Branch-specific settings like pricing adjustments, availability overrides
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Business\Business $business
 * @property-read \App\Models\Business\BusinessBranch $businessBranch
 * @property-read \App\Models\Business\ProductCollection $productCollection
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchCustomization active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchCustomization forBranch(string $branchId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchCustomization forProductCollection(string $productCollectionId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchCustomization newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchCustomization newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchCustomization query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchCustomization whereActiveEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchCustomization whereActiveStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchCustomization whereBusinessBranchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchCustomization whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchCustomization whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchCustomization whereCustomSettings($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchCustomization whereDisplayOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchCustomization whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchCustomization whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchCustomization whereProductCollectionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchCustomization whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchCustomization whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class ProductBranchCustomization extends Model
{
    use BelongsToTenant, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'business_id',
        'business_branch_id',
        'product_collection_id',
        'is_active',
        'active_start_time',
        'active_end_time',
        'display_order',
        'custom_settings',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'active_start_time' => 'datetime',
        'active_end_time' => 'datetime',
        'display_order' => 'integer',
        'custom_settings' => 'array',
    ];

    /**
     * Get the business that owns this customization.
     *
     * @return BelongsTo<Business,ProductBranchCustomization>
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the business branch that owns this customization.
     *
     * @return BelongsTo<BusinessBranch,ProductBranchCustomization>
     */
    public function businessBranch(): BelongsTo
    {
        return $this->belongsTo(BusinessBranch::class);
    }

    /**
     * Get the product collection this customization applies to.
     *
     * @return BelongsTo<ProductCollection,ProductBranchCustomization>
     */
    public function productCollection(): BelongsTo
    {
        return $this->belongsTo(ProductCollection::class);
    }

    /**
     * Check if the customization is currently active.
     */
    public function isCurrentlyActive(): bool
    {
        if (! $this->is_active) {
            return false;
        }

        $now = now();

        if ($this->active_start_time && $this->active_start_time->isFuture()) {
            return false;
        }

        if ($this->active_end_time && $this->active_end_time->isPast()) {
            return false;
        }

        return true;
    }

    /**
     * Get custom setting value by key.
     */
    public function getCustomSetting(string $key, mixed $default = null): mixed
    {
        return data_get($this->custom_settings, $key, $default);
    }

    /**
     * Set custom setting value.
     */
    public function setCustomSetting(string $key, mixed $value): void
    {
        $settings = $this->custom_settings ?? [];
        data_set($settings, $key, $value);
        $this->custom_settings = $settings;
    }

    /**
     * Scope a query to only include active customizations.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('active_start_time')
                    ->orWhere('active_start_time', '<=', now());
            })
            ->where(function ($query) {
                $query->whereNull('active_end_time')
                    ->orWhere('active_end_time', '>', now());
            });
    }

    /**
     * Scope a query to filter by business branch.
     */
    public function scopeForBranch($query, string $branchId)
    {
        return $query->where('business_branch_id', $branchId);
    }

    /**
     * Scope a query to filter by product collection.
     */
    public function scopeForProductCollection($query, string $productCollectionId)
    {
        return $query->where('product_collection_id', $productCollectionId);
    }
}
