<?php

declare(strict_types=1);

namespace App\Models\Business;

use App\Models\Delivery\Order;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;

/**
 * Product Review Model
 *
 * Represents customer reviews and ratings for products.
 *
 * @property string $id
 * @property string $tenant_id
 * @property string $product_id
 * @property string $customer_id
 * @property string|null $order_id
 * @property string $business_id
 * @property int $rating Rating from 1 to 5
 * @property string|null $review
 * @property array<array-key, mixed>|null $images Array of review image URLs
 * @property bool $is_verified_purchase
 * @property bool $is_approved
 * @property \Illuminate\Support\Carbon|null $approved_at
 * @property string|null $approved_by
 * @property array<array-key, mixed>|null $metadata Additional review metadata
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User\User|null $approvedBy
 * @property-read \App\Models\Business\Business $business
 * @property-read \App\Models\User\User $customer
 * @property-read \App\Models\Business\Product $product
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview approved()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview verifiedPurchase()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview whereApprovedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview whereApprovedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview whereCustomerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview whereImages($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview whereIsApproved($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview whereIsVerifiedPurchase($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview whereRating($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview whereReview($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview withMinRating(int $minRating)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductReview withRating(int $rating)
 *
 * @property-read Order|null $order
 *
 * @mixin \Eloquent
 */
class ProductReview extends Model
{
    use BelongsToTenant, HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'product_id',
        'customer_id',
        'order_id',
        'business_id',
        'rating',
        'review',
        'images',
        'is_verified_purchase',
        'is_approved',
        'approved_at',
        'approved_by',
        'metadata',
    ];

    protected $casts = [
        'rating' => 'integer',
        'images' => 'array',
        'is_verified_purchase' => 'boolean',
        'is_approved' => 'boolean',
        'approved_at' => 'datetime',
        'metadata' => 'array',
    ];

    /**
     * Get the product that this review belongs to.
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    /**
     * Get the customer who wrote this review.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'customer_id');
    }

    /**
     * Get the order associated with this review.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    /**
     * Get the business that owns the product.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }

    /**
     * Get the user who approved this review.
     */
    public function approvedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'approved_by');
    }

    /**
     * Scope to get only approved reviews.
     */
    public function scopeApproved($query)
    {
        return $query->where('is_approved', true);
    }

    /**
     * Scope to get only verified purchase reviews.
     */
    public function scopeVerifiedPurchase($query)
    {
        return $query->where('is_verified_purchase', true);
    }

    /**
     * Scope to filter by rating.
     */
    public function scopeWithRating($query, int $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * Scope to filter by minimum rating.
     */
    public function scopeWithMinRating($query, int $minRating)
    {
        return $query->where('rating', '>=', $minRating);
    }

    /**
     * Check if this review has images.
     */
    public function hasImages(): bool
    {
        return ! empty($this->images);
    }

    /**
     * Get the review summary for display.
     */
    public function getSummary(int $length = 100): string
    {
        if (! $this->review) {
            return '';
        }

        return strlen($this->review) > $length
            ? substr($this->review, 0, $length).'...'
            : $this->review;
    }

    /**
     * Mark review as approved.
     */
    public function approve(?string $approvedBy = null): bool
    {
        return $this->update([
            'is_approved' => true,
            'approved_at' => now(),
            'approved_by' => $approvedBy,
        ]);
    }

    /**
     * Mark review as unapproved.
     */
    public function unapprove(): bool
    {
        return $this->update([
            'is_approved' => false,
            'approved_at' => null,
            'approved_by' => null,
        ]);
    }
}
