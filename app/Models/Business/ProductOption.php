<?php

declare(strict_types=1);

namespace App\Models\Business;

use App\Enums\Business\ProductOptionSelectionType;
use App\Models\Delivery\OrderItemOption;
use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $option_group_id
 * @property string $name e.g., "Pepperoni", "Mushrooms", "Large" (if size is an option)
 * @property string|null $description
 * @property numeric $price_adjustment Adjustment in the product's currency
 * @property string|null $image_url
 * @property bool $is_default
 * @property int $display_order
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Business\ProductOptionGroup $group
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOption available()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOption inGroup(string $groupId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOption newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOption newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOption ordered()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOption query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOption whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOption whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOption whereDisplayOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOption whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOption whereImageUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOption whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOption whereIsDefault($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOption whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOption whereOptionGroupId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOption wherePriceAdjustment($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOption whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOption whereUpdatedAt($value)
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, OrderItemOption> $orderItemOptions
 * @property-read int|null $order_item_options_count
 *
 * @mixin \Eloquent
 */
class ProductOption extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'option_group_id',
        'name',
        'description',
        'price_adjustment',
        'image_url',
        'is_default',
        'display_order',
        'is_active',
    ];

    protected $casts = [
        'price_adjustment' => 'decimal:2',
        'is_default' => 'boolean',
        'display_order' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get the tenant that owns the product option.
     *
     * @return BelongsTo<Tenant,ProductOption>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the option group that owns this option.
     *
     * @return BelongsTo<ProductOptionGroup,ProductOption>
     */
    public function group(): BelongsTo
    {
        return $this->belongsTo(ProductOptionGroup::class, 'option_group_id');
    }

    /**
     * Get the order item options that use this option.
     *
     * @return HasMany<OrderItemOption,ProductOption>
     */
    public function orderItemOptions(): HasMany
    {
        return $this->hasMany(OrderItemOption::class, 'product_option_id');
    }

    /**
     * Scope to get available options.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get options ordered by sort order.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order', 'asc')->orderBy('name', 'asc');
    }

    /**
     * Scope to get options by group.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInGroup($query, string $groupId)
    {
        return $query->where('option_group_id', $groupId);
    }

    /**
     * Check if the option is available.
     */
    public function isAvailable(): bool
    {
        return $this->is_active && $this->group->is_active;
    }

    /**
     * Check if the option increases the price.
     */
    public function increasesPrice(): bool
    {
        return $this->price_adjustment > 0;
    }

    /**
     * Check if the option decreases the price.
     */
    public function decreasesPrice(): bool
    {
        return $this->price_adjustment < 0;
    }

    /**
     * Check if the option has no price impact.
     */
    public function hasNoPriceImpact(): bool
    {
        return $this->price_adjustment == 0;
    }

    /**
     * Get the formatted price adjustment.
     */
    public function getFormattedPriceAdjustment(string $currency = 'NGN'): string
    {
        if ($this->price_adjustment == 0) {
            return 'No additional cost';
        }

        $sign = $this->price_adjustment > 0 ? '+' : '';

        return $sign.$currency.' '.number_format($this->price_adjustment, 2);
    }

    /**
     * Get the display name with price adjustment.
     */
    public function getDisplayNameWithPrice(string $currency = 'NGN'): string
    {
        $name = $this->name;

        if ($this->price_adjustment != 0) {
            $name .= ' ('.$this->getFormattedPriceAdjustment($currency).')';
        }

        return $name;
    }

    /**
     * Get usage count (how many times this option was ordered).
     */
    public function getUsageCount(): int
    {
        return $this->orderItemOptions()->count();
    }

    /**
     * Get usage statistics for this option.
     */
    public function getUsageStats(int $days = 30): array
    {
        $totalUsage = $this->getUsageCount();
        $recentUsage = $this->orderItemOptions()
            ->where('created_at', '>=', now()->subDays($days))
            ->count();

        $recentRevenue = $this->orderItemOptions()
            ->where('created_at', '>=', now()->subDays($days))
            ->sum('price');

        return [
            'total_usage' => $totalUsage,
            'recent_usage' => $recentUsage,
            'recent_revenue' => $recentRevenue,
            'average_daily_usage' => $days > 0 ? round($recentUsage / $days, 2) : 0,
        ];
    }

    /**
     * Update the sort order for this option.
     *
     * @param  int  $sortOrder
     */
    public function updateDisplayOrder(int $displayOrder): bool
    {
        return $this->update(['display_order' => $displayOrder]);
    }

    /**
     * Toggle availability of the option.
     */
    public function toggleAvailability(): bool
    {
        return $this->update(['is_active' => ! $this->is_active]);
    }

    /**
     * Get option summary for API responses.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'price_adjustment' => $this->price_adjustment,
            'formatted_price_adjustment' => $this->getFormattedPriceAdjustment(),
            'display_name_with_price' => $this->getDisplayNameWithPrice(),
            'is_active' => $this->is_active,
            'image_url' => $this->image_url,
            'display_order' => $this->display_order,
            'group_name' => $this->group->name,
            'group_type' => $this->group->selection_type,
            'usage_count' => $this->getUsageCount(),
        ];
    }

    /**
     * Check if this option can be selected with another option.
     */
    public function canBeSelectedWith(ProductOption $otherOption): bool
    {
        // If they're in the same group, check the selection type
        if ($this->option_group_id === $otherOption->option_group_id) {
            return $this->group->selection_type === ProductOptionSelectionType::MULTIPLE;
        }

        // Different groups can be selected together
        return true;
    }

    /**
     * Get related options (from same group).
     *
     * @return \Illuminate\Database\Eloquent\Collection
     */
    public function getRelatedOptions()
    {
        return static::where('option_group_id', $this->option_group_id)
            ->where('id', '!=', $this->id)
            ->available()
            ->ordered()
            ->get();
    }

    /**
     * Check if this is a default option in its group.
     */
    public function isDefault(): bool
    {
        return $this->group->default_option_id === $this->id;
    }

    /**
     * Set this option as the default for its group.
     */
    public function setAsDefault(): bool
    {
        return $this->group->update(['default_option_id' => $this->id]);
    }
}
