<?php

declare(strict_types=1);

namespace App\Models\Business;

use App\Models\Delivery\Order;
use App\Models\Financial\Payment;
use App\Models\System\Tenant;
use App\Models\User\Address;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $business_id
 * @property string $name Name of the branch (e.g., "Airport Road Branch")
 * @property string $address_id
 * @property string|null $contact_email
 * @property string|null $contact_phone
 * @property array<array-key, mixed>|null $operating_hours Specific hours for this branch
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User\Address $address
 * @property-read \App\Models\Business\Business $business
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Business\Expense> $expenses
 * @property-read int|null $expenses_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Business\InventoryMovement> $inventoryMovements
 * @property-read int|null $inventory_movements_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Business\BusinessTeamMember> $teamMembers
 * @property-read int|null $team_members_count
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessBranch active()
 * @method static \Database\Factories\BusinessBranchFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessBranch newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessBranch newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessBranch query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessBranch search(string $searchTerm)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessBranch whereAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessBranch whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessBranch whereContactEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessBranch whereContactPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessBranch whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessBranch whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessBranch whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessBranch whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessBranch whereOperatingHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessBranch whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessBranch whereUpdatedAt($value)
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Business\ProductCollection> $menuCollections
 * @property-read int|null $menu_collections_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Business\ProductBranchCustomization> $menuCustomizations
 * @property-read int|null $menu_customizations_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Order> $orders
 * @property-read int|null $orders_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Payment> $payments
 * @property-read int|null $payments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Business\ProductBranchOverride> $productOverrides
 * @property-read int|null $product_overrides_count
 *
 * @mixin \Eloquent
 */
class BusinessBranch extends Model
{
    use BelongsToTenant, HasFactory, HasUuids;

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Database\Factories\BusinessBranchFactory::new();
    }

    protected $fillable = [
        'tenant_id',
        'business_id',
        'name',
        'address_id',
        'contact_email',
        'contact_phone',
        'operating_hours',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'operating_hours' => 'array',
    ];

    /**
     * Get the tenant that owns the business branch.
     *
     * @return BelongsTo<Tenant,BusinessBranch>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the business that owns the branch.
     *
     * @return BelongsTo<Business,BusinessBranch>
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }

    /**
     * Get the address for the branch.
     *
     * @return BelongsTo<Address,BusinessBranch>
     */
    public function address(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\Address::class, 'address_id');
    }

    /**
     * Get the team members assigned to this branch.
     *
     * @return HasMany<BusinessTeamMember,BusinessBranch>
     */
    public function teamMembers(): HasMany
    {
        return $this->hasMany(BusinessTeamMember::class, 'branch_id');
    }

    /**
     * Get the orders processed at this branch.
     *
     * @return HasMany<Order,BusinessBranch>
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'business_branch_id');
    }

    /**
     * Get the payments made at this branch.
     *
     * @return HasMany<Payment,BusinessBranch>
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'business_branch_id');
    }

    /**
     * Get the inventory movements for this branch.
     *
     * @return HasMany<InventoryMovement,BusinessBranch>
     */
    public function inventoryMovements(): HasMany
    {
        return $this->hasMany(InventoryMovement::class, 'business_branch_id');
    }

    /**
     * Get the expenses for this branch.
     *
     * @return MorphMany<Expense,BusinessBranch>
     */
    public function expenses(): MorphMany
    {
        return $this->morphMany(Expense::class, 'expensable');
    }

    /**
     * Get the menu collections specific to this branch.
     *
     * @return HasMany<ProductCollection,BusinessBranch>
     */
    public function menuCollections(): HasMany
    {
        return $this->hasMany(ProductCollection::class, 'business_branch_id');
    }

    /**
     * Get the menu customizations for this branch.
     *
     * @return HasMany<ProductBranchCustomization,BusinessBranch>
     */
    public function menuCustomizations(): HasMany
    {
        return $this->hasMany(ProductBranchCustomization::class);
    }

    /**
     * Get the product overrides for this branch.
     *
     * @return HasMany<ProductBranchOverride,BusinessBranch>
     */
    public function productOverrides(): HasMany
    {
        return $this->hasMany(ProductBranchOverride::class);
    }

    /**
     * Scope to get only active branches.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to search branches by name.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearch($query, string $searchTerm)
    {
        return $query->where('name', 'like', "%{$searchTerm}%");
    }

    /**
     * Check if the branch is active.
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Check if the branch is currently open.
     */
    public function isOpen(): bool
    {
        if (! $this->operating_hours) {
            return true; // Always open if no hours specified
        }

        $now = now();
        $dayOfWeek = strtolower($now->format('l'));

        if (! isset($this->operating_hours[$dayOfWeek])) {
            return false; // Closed if no hours for today
        }

        $hours = $this->operating_hours[$dayOfWeek];

        if ($hours['closed'] ?? false) {
            return false;
        }

        $currentTime = $now->format('H:i');
        $openTime = $hours['open'] ?? '00:00';
        $closeTime = $hours['close'] ?? '23:59';

        return $currentTime >= $openTime && $currentTime <= $closeTime;
    }

    /**
     * Get today's operating hours.
     */
    public function getTodaysHours(): ?array
    {
        if (! $this->operating_hours) {
            return null;
        }

        $dayOfWeek = strtolower(now()->format('l'));

        return $this->operating_hours[$dayOfWeek] ?? null;
    }

    /**
     * Get the formatted operating hours for today.
     */
    public function getTodaysHoursFormatted(): string
    {
        $hours = $this->getTodaysHours();

        if (! $hours) {
            return 'Hours not set';
        }

        if ($hours['closed'] ?? false) {
            return 'Closed';
        }

        $open = $hours['open'] ?? '00:00';
        $close = $hours['close'] ?? '23:59';

        return "{$open} - {$close}";
    }

    /**
     * Get the full address string.
     */
    public function getFullAddress(): string
    {
        return $this->address ? $this->address->full_address : 'Address not set';
    }

    /**
     * Get total orders count for this branch.
     */
    public function getTotalOrdersCount(): int
    {
        return $this->orders()->count();
    }

    /**
     * Get recent orders count for this branch.
     */
    public function getRecentOrdersCount(int $days = 30): int
    {
        return $this->orders()
            ->where('created_at', '>=', now()->subDays($days))
            ->count();
    }

    /**
     * Get total revenue for this branch.
     */
    public function getTotalRevenue(): float
    {
        return $this->orders()
            ->where('status', 'delivered')
            ->sum('total_amount');
    }

    /**
     * Get recent revenue for this branch.
     */
    public function getRecentRevenue(int $days = 30): float
    {
        return $this->orders()
            ->where('status', 'delivered')
            ->where('created_at', '>=', now()->subDays($days))
            ->sum('total_amount');
    }

    /**
     * Get the team members count for this branch.
     */
    public function getTeamMembersCount(): int
    {
        return $this->teamMembers()->count();
    }

    /**
     * Update operating hours for the branch.
     */
    public function updateOperatingHours(array $hours): bool
    {
        return $this->update(['operating_hours' => $hours]);
    }

    /**
     * Toggle branch active status.
     */
    public function toggleStatus(): bool
    {
        return $this->update(['is_active' => ! $this->is_active]);
    }

    /**
     * Get branch summary for API responses.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'contact_email' => $this->contact_email,
            'contact_phone' => $this->contact_phone,
            'is_active' => $this->is_active,
            'is_open' => $this->isOpen(),
            'address' => $this->getFullAddress(),
            'todays_hours' => $this->getTodaysHoursFormatted(),
            'team_members_count' => $this->getTeamMembersCount(),
            'total_orders' => $this->getTotalOrdersCount(),
            'recent_orders' => $this->getRecentOrdersCount(),
            'total_revenue' => $this->getTotalRevenue(),
            'recent_revenue' => $this->getRecentRevenue(),
        ];
    }

    /**
     * Get detailed branch information.
     */
    public function getDetailedInfo(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'contact_email' => $this->contact_email,
            'contact_phone' => $this->contact_phone,
            'is_active' => $this->is_active,
            'operating_hours' => $this->operating_hours,
            'address' => $this->address?->getSummary(),
            'business' => [
                'id' => $this->business->id,
                'name' => $this->business->business_name,
            ],
            'statistics' => [
                'team_members_count' => $this->getTeamMembersCount(),
                'total_orders' => $this->getTotalOrdersCount(),
                'recent_orders_30d' => $this->getRecentOrdersCount(30),
                'total_revenue' => $this->getTotalRevenue(),
                'recent_revenue_30d' => $this->getRecentRevenue(30),
            ],
        ];
    }
}
