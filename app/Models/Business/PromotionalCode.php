<?php

declare(strict_types=1);

namespace App\Models\Business;

use App\Enums\Business\PromotionType;
use App\Models\Core\Country;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string|null $tenant_id FK to tenants.id, nullable - if platform-wide or country-scoped
 * @property string|null $business_id
 * @property string|null $country_id
 * @property string $name
 * @property string|null $description
 * @property string|null $code Promo code if applicable
 * @property mixed $type
 * @property numeric|null $discount_value Percentage or fixed amount
 * @property numeric|null $min_order_value Minimum order value to apply (in promotion currency?)
 * @property numeric|null $max_discount_amount Cap on discount value (in promotion currency?)
 * @property string|null $currency Currency if discount is fixed amount
 * @property int|null $usage_limit_per_user
 * @property int|null $usage_limit_total
 * @property \Illuminate\Support\Carbon $starts_at
 * @property \Illuminate\Support\Carbon|null $expires_at
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Business\Business|null $business
 * @property-read \App\Models\Core\Country|null $country
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode search(string $searchTerm)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode whereCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode whereDiscountValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode whereMaxDiscountAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode whereMinOrderValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode whereStartsAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode whereUsageLimitPerUser($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalCode whereUsageLimitTotal($value)
 * @method static \Database\Factories\PromotionalCodeFactory factory($count = null, $state = [])
 *
 * @mixin \Eloquent
 */
class PromotionalCode extends Model
{
    use HasFactory, HasUuids;

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): \Database\Factories\PromotionalCodeFactory
    {
        return \Database\Factories\PromotionalCodeFactory::new();
    }

    protected $table = 'promotions';

    protected $fillable = [
        'tenant_id',
        'business_id',
        'country_id',
        'name',
        'description',
        'code',
        'type',
        'discount_value',
        'min_order_value',
        'max_discount_amount',
        'currency',
        'usage_limit_per_user',
        'usage_limit_total',
        'starts_at',
        'expires_at',
        'is_active',
    ];

    protected $casts = [
        'type' => PromotionType::class,
        'discount_value' => 'decimal:2',
        'min_order_value' => 'decimal:2',
        'max_discount_amount' => 'decimal:2',
        'usage_limit_per_user' => 'integer',
        'usage_limit_total' => 'integer',
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
        'is_active' => 'boolean',
    ];

    /**
     * Get the business associated with the promotional code.
     *
     * @return BelongsTo<Business,PromotionalCode>
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }

    /**
     * Get the country associated with the promotional code.
     *
     * @return BelongsTo<Country,PromotionalCode>
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    /**
     * Scope to get only active promotional codes.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->where(function ($q) {
                $q->whereNull('starts_at')->orWhere('starts_at', '<=', now());
            })
            ->where(function ($q) {
                $q->whereNull('expires_at')->orWhere('expires_at', '>=', now());
            });
    }

    /**
     * Scope to search promotional codes by code or name.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearch($query, string $searchTerm)
    {
        return $query->where(function ($q) use ($searchTerm) {
            $q->where('code', 'like', "%{$searchTerm}%")
                ->orWhere('name', 'like', "%{$searchTerm}%");
        });
    }

    /**
     * Check if the promotional code is currently valid.
     */
    public function isCurrentlyValid(): bool
    {
        $now = now();
        if (! $this->is_active) {
            return false;
        }
        if ($this->starts_at && $now->lt($this->starts_at)) {
            return false;
        }
        if ($this->expires_at && $now->gt($this->expires_at)) {
            return false;
        }

        return true;
    }

    /**
     * Get the formatted discount value.
     */
    public function getFormattedDiscount(): string
    {
        if ($this->type === PromotionType::PERCENTAGE_DISCOUNT) {
            return $this->discount_value.'%';
        }

        return ($this->currency ?? '').' '.number_format($this->discount_value, 2);
    }

    /**
     * Get the formatted minimum order value.
     */
    public function getFormattedMinOrderValue(): ?string
    {
        if (is_null($this->min_order_value)) {
            return null;
        }

        return ($this->currency ?? '').' '.number_format($this->min_order_value, 2);
    }

    /**
     * Get the formatted maximum discount amount.
     */
    public function getFormattedMaxDiscountAmount(): ?string
    {
        if (is_null($this->max_discount_amount)) {
            return null;
        }

        return ($this->currency ?? '').' '.number_format($this->max_discount_amount, 2);
    }

    /**
     * Get a summary for API responses.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'code' => $this->code,
            'name' => $this->name,
            'description' => $this->description,
            'type' => $this->type->value,
            'discount_value' => $this->discount_value,
            'formatted_discount' => $this->getFormattedDiscount(),
            'min_order_value' => $this->min_order_value,
            'formatted_min_order_value' => $this->getFormattedMinOrderValue(),
            'max_discount_amount' => $this->max_discount_amount,
            'formatted_max_discount_amount' => $this->getFormattedMaxDiscountAmount(),
            'currency' => $this->currency,
            'usage_limit_per_user' => $this->usage_limit_per_user,
            'usage_limit_total' => $this->usage_limit_total,
            'starts_at' => $this->starts_at,
            'expires_at' => $this->expires_at,
            'is_active' => $this->is_active,
        ];
    }
}
