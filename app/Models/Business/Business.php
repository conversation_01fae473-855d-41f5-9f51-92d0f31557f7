<?php

declare(strict_types=1);

namespace App\Models\Business;

use App\Enums\Business\BusinessStatus;
use App\Enums\Business\BusinessType;
use App\Models\Core\Country;
use App\Models\Core\State;
use App\Models\Delivery\DeliveryProvider;
use App\Models\Delivery\Order;
use App\Models\Delivery\Rating;
use App\Models\Financial\PlatformAccount;
use App\Models\Financial\UserSubscription;
use App\Models\System\Tenant;
use App\Models\System\TenantWhiteLabelSetting;
use App\Models\User\Address;
use App\Models\User\EntityVerification;
use App\Models\User\User;
use App\Models\User\Verification;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Laravel\Scout\Searchable;
use Silber\Bouncer\Database\HasRolesAndAbilities;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;

/**
 * Business Model
 *
 * @property string $id
 * @property string $tenant_id
 * @property string $user_id
 * @property string $business_name
 * @property BusinessType $business_type
 * @property string|null $subdomain For accessing business portal (e.g., yourbusiness.deliverynexus.com)
 * @property string|null $slug For public marketplace URL (e.g., deliverynexus.com/yourbusiness)
 * @property string|null $cac_registration_number Nigerian specific? May need to be generic or part of verification
 * @property string|null $description
 * @property string|null $logo_url
 * @property string|null $primary_address_id
 * @property string|null $contact_email
 * @property string|null $contact_phone
 * @property string $country_id
 * @property string|null $state_id
 * @property BusinessStatus $status
 * @property array<array-key, mixed>|null $verification_documents
 * @property array<array-key, mixed>|null $operating_hours
 * @property bool $global_auto_accept_orders
 * @property array<array-key, mixed>|null $auto_acceptance_criteria Criteria for auto-accepting orders (e.g., min value, specific products)
 * @property bool $accepts_cash_on_delivery
 * @property bool $allows_pickup
 * @property string|null $tax_identification_number May need to be generic or part of verification
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $current_subscription_id
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Silber\Bouncer\Database\Ability> $abilities
 * @property-read int|null $abilities_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, BusinessBranch> $branches
 * @property-read int|null $branches_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, ProductCategory> $categories
 * @property-read int|null $categories_count
 * @property-read Country $country
 * @property-read UserSubscription|null $currentSubscription
 * @property-read \Illuminate\Database\Eloquent\Collection<int, EntityVerification> $entityVerifications
 * @property-read int|null $entity_verifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Expense> $expenses
 * @property-read int|null $expenses_count
 * @property-read DeliveryProvider|null $internalDeliveryProvider
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Order> $orders
 * @property-read int|null $orders_count
 * @property-read User $owner
 * @property-read PlatformAccount|null $platformAccount
 * @property-read Address|null $primaryAddress
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Product> $products
 * @property-read int|null $products_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Rating> $ratings
 * @property-read int|null $ratings_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Silber\Bouncer\Database\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, BusinessSetting> $settings
 * @property-read int|null $settings_count
 * @property-read State|null $state
 * @property-read \Illuminate\Database\Eloquent\Collection<int, UserSubscription> $subscriptions
 * @property-read int|null $subscriptions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Supplier> $suppliers
 * @property-read int|null $suppliers_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, BusinessTeamMember> $teamMembers
 * @property-read int|null $team_members_count
 * @property-read Tenant $tenant
 * @property-read \Illuminate\Database\Eloquent\Collection<int, User> $users
 * @property-read int|null $users_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Verification> $verifications
 * @property-read int|null $verifications_count
 * @property-read TenantWhiteLabelSetting|null $whiteLabelSettings
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business acceptsCashOnDelivery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business allowsPickup()
 * @method static \Database\Factories\BusinessFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereAcceptsCashOnDelivery($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereAllowsPickup($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereAutoAcceptanceCriteria($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereBusinessName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereBusinessType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereCacRegistrationNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereContactEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereContactPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereCurrentSubscriptionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereDeletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereGlobalAutoAcceptOrders($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereIs($role)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereIsAll($role)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereIsNot($role)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereLogoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereOperatingHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business wherePrimaryAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereStateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereSubdomain($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereTaxIdentificationNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business whereVerificationDocuments($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business withTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Business withoutTrashed()
 *
 * @mixin \Eloquent
 */
class Business extends Model
{
    use BelongsToTenant, HasFactory, HasRolesAndAbilities, HasUuids, Searchable, SoftDeletes;

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Database\Factories\BusinessFactory::new();
    }

    protected $fillable = [
        'tenant_id',
        'user_id',
        'business_name',
        'business_type',
        'subdomain',
        'slug',
        'cac_registration_number',
        'description',
        'logo_url',
        'primary_address_id',
        'contact_email',
        'contact_phone',
        'country_id',
        'state_id',
        'status',
        'verification_documents',
        'operating_hours',
        'global_auto_accept_orders',
        'auto_acceptance_criteria',
        'accepts_cash_on_delivery',
        'allows_pickup',
        'current_subscription_id',
        'tax_identification_number',
    ];

    protected $casts = [
        'business_type' => BusinessType::class,
        'status' => BusinessStatus::class,
        'verification_documents' => 'array',
        'operating_hours' => 'array',
        'global_auto_accept_orders' => 'boolean',
        'auto_acceptance_criteria' => 'array',
        'accepts_cash_on_delivery' => 'boolean',
        'allows_pickup' => 'boolean',
    ];

    /**
     * Get the tenant that owns the business.
     *
     * @return BelongsTo<Tenant,Business>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    /**
     * Get the user that owns the business.
     *
     * @return BelongsTo<User,Business>
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get all users associated with this business (via Bouncer roles).
     * This includes owners, admins, managers, and staff.
     *
     * @return BelongsToMany<User>
     */
    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'assigned_roles', 'entity_id', 'user_id')
            ->where('assigned_roles.entity_type', 'businesses')
            ->withPivot('role_id', 'restricted_to_id', 'restricted_to_type')
            ->withTimestamps();
    }

    /**
     * Get the primary address of the business.
     *
     * @return BelongsTo<Address,Business>
     */
    public function primaryAddress(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'primary_address_id');
    }

    /**
     * Get the country where the business is located.
     *
     * @return BelongsTo<Country,Business>
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * Get the state where the business is located.
     *
     * @return BelongsTo<State,Business>
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class);
    }

    /**
     * Get the current subscription of the business.
     *
     * @return BelongsTo<UserSubscription,Business>
     */
    public function currentSubscription(): BelongsTo
    {
        return $this->belongsTo(UserSubscription::class, 'current_subscription_id');
    }

    /**
     * Get the branches of the business.
     *
     * @return HasMany<BusinessBranch,Business>
     */
    public function branches(): HasMany
    {
        return $this->hasMany(BusinessBranch::class);
    }

    /**
     * Get the team members of the business.
     *
     * @return HasMany<BusinessTeamMember,Business>
     */
    public function teamMembers(): HasMany
    {
        return $this->hasMany(BusinessTeamMember::class);
    }

    /**
     * Get the products of the business.
     *
     * @return HasMany<Product,Business>
     */
    public function products(): HasMany
    {
        return $this->hasMany(Product::class);
    }

    /**
     * Get the product categories of the business.
     *
     * @return HasMany<ProductCategory,Business>
     */
    public function categories(): HasMany
    {
        return $this->hasMany(ProductCategory::class);
    }

    /**
     * Get the orders of the business.
     *
     * @return HasMany<Order,Business>
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    /**
     * Get the suppliers of the business.
     *
     * @return HasMany<Supplier,Business>
     */
    public function suppliers(): HasMany
    {
        return $this->hasMany(Supplier::class);
    }

    /**
     * Get the settings of the business.
     *
     * @return HasMany<BusinessSetting,Business>
     */
    public function settings(): HasMany
    {
        return $this->hasMany(BusinessSetting::class);
    }

    /**
     * Get the white label settings of the business.
     *
     * @return HasOne<TenantWhiteLabelSetting,Business>
     */
    public function whiteLabelSettings(): HasOne
    {
        return $this->hasOne(TenantWhiteLabelSetting::class, 'tenant_id', 'tenant_id');
    }

    /**
     * Get all subscriptions of the business.
     *
     * @return HasMany<UserSubscription,Business>
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class, 'subscriber_id')->where('subscriber_type', 'business');
    }

    /**
     * Get the platform account of the business.
     *
     * @return MorphOne<PlatformAccount,Business>
     */
    public function platformAccount(): MorphOne
    {
        return $this->morphOne(PlatformAccount::class, 'accountable');
    }

    /**
     * Get the ratings of the business.
     *
     * @return MorphMany<Rating,Business>
     */
    public function ratings(): MorphMany
    {
        return $this->morphMany(Rating::class, 'rateable');
    }

    /**
     * Get the expenses of the business.
     *
     * @return MorphMany<Expense,Business>
     */
    public function expenses(): MorphMany
    {
        return $this->morphMany(Expense::class, 'expensable');
    }

    /**
     * Get the internal delivery provider of the business.
     *
     * @return HasOne<DeliveryProvider,Business>
     */
    public function internalDeliveryProvider(): HasOne
    {
        return $this->hasOne(DeliveryProvider::class)->where('is_internal_provider', true);
    }

    /**
     * Get the business's entity verification records (complex verification steps).
     *
     * @return HasMany<EntityVerification,Business>
     */
    public function entityVerifications(): HasMany
    {
        return $this->hasMany(EntityVerification::class, 'entity_id')
            ->where('entity_type', 'businesses');
    }

    /**
     * Get the business's verification records (simple verification codes).
     *
     * @return MorphMany<Verification,Business>
     */
    public function verifications(): MorphMany
    {
        return $this->morphMany(Verification::class, 'verifiable');
    }

    /**
     * Scope to get only active businesses.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', BusinessStatus::ACTIVE);
    }

    /**
     * Scope to get only verified businesses.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeVerified($query)
    {
        return $query->where('status', BusinessStatus::VERIFIED);
    }

    /**
     * Scope to get businesses that allow pickup.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAllowsPickup($query)
    {
        return $query->where('allows_pickup', true);
    }

    /**
     * Scope to get businesses that accept cash on delivery.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAcceptsCashOnDelivery($query)
    {
        return $query->where('accepts_cash_on_delivery', true);
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array<string, mixed>
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'tenant_id' => $this->tenant_id,
            'business_name' => $this->business_name,
            'business_type' => $this->business_type?->value,
            'description' => $this->description,
            'contact_email' => $this->contact_email,
            'contact_phone' => $this->contact_phone,
            'country_id' => $this->country_id,
            'state_id' => $this->state_id,
            'status' => $this->status?->value,
            'allows_pickup' => $this->allows_pickup,
            'accepts_cash_on_delivery' => $this->accepts_cash_on_delivery,
            'created_at' => $this->created_at?->timestamp,
        ];
    }

    /**
     * Get the Scout search index name.
     */
    public function searchableAs(): string
    {
        return 'businesses';
    }

    /**
     * Determine if the model should be searchable.
     */
    public function shouldBeSearchable(): bool
    {
        return $this->status === BusinessStatus::ACTIVE || $this->status === BusinessStatus::VERIFIED;
    }

    /**
     * Get the Scout search key.
     */
    public function getScoutKey(): mixed
    {
        return $this->id;
    }

    /**
     * Get the Scout search key name.
     */
    public function getScoutKeyName(): string
    {
        return 'id';
    }
}
