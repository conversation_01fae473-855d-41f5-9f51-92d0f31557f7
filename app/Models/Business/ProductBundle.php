<?php

declare(strict_types=1);

namespace App\Models\Business;

use App\Models\Core\Attachment;
use App\Models\Delivery\OrderItem;
use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $business_id
 * @property string $name
 * @property string|null $description
 * @property numeric $price Price of the bundle (in the business's currency)
 * @property numeric $discount_amount Discount applied compared to sum of individual items
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Business\BundleProduct> $bundleProducts
 * @property-read int|null $bundle_products_count
 * @property-read \App\Models\Business\Business $business
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBundle active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBundle newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBundle newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBundle query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBundle search(string $searchTerm)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBundle whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBundle whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBundle whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBundle whereDiscountAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBundle whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBundle whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBundle whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBundle wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBundle whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBundle whereUpdatedAt($value)
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Attachment> $attachments
 * @property-read int|null $attachments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, OrderItem> $orderItems
 * @property-read int|null $order_items_count
 *
 * @mixin \Eloquent
 */
class ProductBundle extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'business_id',
        'name',
        'description',
        'slug',
        'price',
        'discount_amount',
        'is_active',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the tenant that owns the product bundle.
     *
     * @return BelongsTo<Tenant,ProductBundle>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the business that owns the product bundle.
     *
     * @return BelongsTo<Business,ProductBundle>
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }

    /**
     * Get the bundle products for this bundle.
     *
     * @return HasMany<BundleProduct,ProductBundle>
     */
    public function bundleProducts(): HasMany
    {
        return $this->hasMany(BundleProduct::class, 'bundle_id');
    }

    /**
     * Get the order items for this bundle.
     *
     * @return HasMany<OrderItem,ProductBundle>
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class, 'product_bundle_id');
    }

    /**
     * Get the attachments for the bundle.
     *
     * @return MorphMany<Attachment,ProductBundle>
     */
    public function attachments(): MorphMany
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }

    /**
     * Scope to get only active bundles.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to search bundles by name or description.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearch($query, string $searchTerm)
    {
        return $query->where(function ($query) use ($searchTerm) {
            $query->where('name', 'like', "%{$searchTerm}%")
                ->orWhere('description', 'like', "%{$searchTerm}%");
        });
    }

    /**
     * Check if the bundle is active.
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Check if the bundle has products.
     */
    public function hasProducts(): bool
    {
        return $this->bundleProducts()->exists();
    }

    /**
     * Get the total individual price of all products in the bundle.
     */
    public function getIndividualTotalPrice(): float
    {
        $total = 0;

        foreach ($this->bundleProducts as $bundleProduct) {
            $product = $bundleProduct->product;
            $variant = $bundleProduct->variant;

            $price = $variant ? $variant->getEffectivePrice() : $product->getEffectivePrice();
            $total += $price * $bundleProduct->quantity;
        }

        return $total;
    }

    /**
     * Get the savings amount compared to individual purchases.
     */
    public function getSavingsAmount(): float
    {
        return $this->getIndividualTotalPrice() - $this->price;
    }

    /**
     * Get the savings percentage.
     */
    public function getSavingsPercentage(): float
    {
        $individualTotal = $this->getIndividualTotalPrice();

        if ($individualTotal <= 0) {
            return 0;
        }

        return (($individualTotal - $this->price) / $individualTotal) * 100;
    }

    /**
     * Get the formatted bundle price with currency.
     */
    public function getFormattedPrice(string $currency = 'NGN'): string
    {
        return $currency.' '.number_format($this->price, 2);
    }

    /**
     * Get the formatted savings amount with currency.
     */
    public function getFormattedSavings(string $currency = 'NGN'): string
    {
        $savings = $this->getSavingsAmount();

        return $currency.' '.number_format($savings, 2);
    }

    /**
     * Get the total quantity of all products in the bundle.
     */
    public function getTotalQuantity(): int
    {
        return $this->bundleProducts()->sum('quantity');
    }

    /**
     * Get the count of unique products in the bundle.
     */
    public function getProductCount(): int
    {
        return $this->bundleProducts()->count();
    }

    /**
     * Check if all products in the bundle are available.
     */
    public function areAllProductsAvailable(): bool
    {
        foreach ($this->bundleProducts as $bundleProduct) {
            $product = $bundleProduct->product;
            $variant = $bundleProduct->variant;

            if (! $product->is_available) {
                return false;
            }

            if ($variant && ! $variant->is_active) {
                return false;
            }

            // Check stock if variant exists, otherwise check product stock
            if ($variant) {
                if (! $variant->isInStock()) {
                    return false;
                }
            } elseif (! $product->isInStock()) {
                return false;
            }
        }

        return true;
    }

    /**
     * Check if the bundle can fulfill the given quantity.
     */
    public function canFulfillQuantity(int $requestedQuantity): bool
    {
        foreach ($this->bundleProducts as $bundleProduct) {
            $product = $bundleProduct->product;
            $variant = $bundleProduct->variant;
            $requiredQuantity = $bundleProduct->quantity * $requestedQuantity;

            // Check stock availability
            if ($variant) {
                if ($variant->quantity !== -1 && $variant->quantity < $requiredQuantity) {
                    return false;
                }
            } else {
                if ($product->quantity !== -1 && $product->quantity < $requiredQuantity) {
                    return false;
                }
            }
        }

        return true;
    }

    /**
     * Add a product to the bundle.
     */
    public function addProduct(string $productId, int $quantity = 1, ?string $variantId = null): BundleProduct
    {
        return BundleProduct::create([
            'tenant_id' => $this->tenant_id,
            'bundle_id' => $this->id,
            'product_id' => $productId,
            'variant_id' => $variantId,
            'quantity' => $quantity,
        ]);
    }

    /**
     * Remove a product from the bundle.
     */
    public function removeProduct(string $productId, ?string $variantId = null): bool
    {
        $query = $this->bundleProducts()
            ->where('product_id', $productId);

        if ($variantId) {
            $query->where('variant_id', $variantId);
        } else {
            $query->whereNull('variant_id');
        }

        return $query->delete() > 0;
    }

    /**
     * Update the quantity of a product in the bundle.
     */
    public function updateProductQuantity(string $productId, int $quantity, ?string $variantId = null): bool
    {
        $query = $this->bundleProducts()
            ->where('product_id', $productId);

        if ($variantId) {
            $query->where('variant_id', $variantId);
        } else {
            $query->whereNull('variant_id');
        }

        return $query->update(['quantity' => $quantity]) > 0;
    }

    /**
     * Recalculate and update the bundle price based on current product prices.
     */
    public function recalculatePrice(?float $discountPercentage = null): bool
    {
        $individualTotal = $this->getIndividualTotalPrice();

        if ($discountPercentage) {
            $newPrice = $individualTotal * (1 - $discountPercentage / 100);
            $discountAmount = $individualTotal - $newPrice;
        } else {
            $newPrice = $individualTotal - $this->discount_amount;
            $discountAmount = $this->discount_amount;
        }

        return $this->update([
            'price' => max(0, $newPrice),
            'discount_amount' => $discountAmount,
        ]);
    }

    /**
     * Clone this bundle to create a new one.
     */
    public function clone(string $newName, ?string $newBusinessId = null): ProductBundle
    {
        $newBundle = $this->replicate();
        $newBundle->name = $newName;
        $newBundle->business_id = $newBusinessId ?? $this->business_id;
        $newBundle->save();

        // Clone all bundle products
        foreach ($this->bundleProducts as $bundleProduct) {
            $newBundleProduct = $bundleProduct->replicate();
            $newBundleProduct->bundle_id = $newBundle->id;
            $newBundleProduct->save();
        }

        return $newBundle;
    }

    /**
     * Get bundle summary for API responses.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'price' => $this->price,
            'formatted_price' => $this->getFormattedPrice(),
            'discount_amount' => $this->discount_amount,
            'individual_total' => $this->getIndividualTotalPrice(),
            'savings_amount' => $this->getSavingsAmount(),
            'savings_percentage' => round($this->getSavingsPercentage(), 2),
            'formatted_savings' => $this->getFormattedSavings(),
            'is_active' => $this->is_active,
            'product_count' => $this->getProductCount(),
            'total_quantity' => $this->getTotalQuantity(),
            'all_products_available' => $this->areAllProductsAvailable(),
        ];
    }

    /**
     * Get detailed bundle structure with products.
     */
    public function getDetailedStructure(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'price' => $this->price,
            'discount_amount' => $this->discount_amount,
            'is_active' => $this->is_active,
            'products' => $this->bundleProducts->map(function ($bundleProduct) {
                return [
                    'product_id' => $bundleProduct->product_id,
                    'product_name' => $bundleProduct->product->name,
                    'variant_id' => $bundleProduct->variant_id,
                    'variant_name' => $bundleProduct->variant?->name,
                    'quantity' => $bundleProduct->quantity,
                    'unit_price' => $bundleProduct->variant
                        ? $bundleProduct->variant->getEffectivePrice()
                        : $bundleProduct->product->getEffectivePrice(),
                    'total_price' => ($bundleProduct->variant
                        ? $bundleProduct->variant->getEffectivePrice()
                        : $bundleProduct->product->getEffectivePrice()) * $bundleProduct->quantity,
                ];
            }),
            'summary' => $this->getSummary(),
        ];
    }

    /**
     * Generate a unique name for the bundle.
     */
    public static function generateUniqueName(string $baseName, string $businessId, ?string $excludeId = null): string
    {
        $name = $baseName;
        $counter = 1;

        while (static::nameExists($name, $businessId, $excludeId)) {
            $name = $baseName.' ('.$counter.')';
            $counter++;
        }

        return $name;
    }

    /**
     * Check if a bundle name exists.
     */
    protected static function nameExists(string $name, string $businessId, ?string $excludeId = null): bool
    {
        $query = static::where('name', $name)->where('business_id', $businessId);

        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        return $query->exists();
    }
}
