<?php

namespace App\Models\Business;

use App\Enums\Business\ProductCollectionType;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $business_id
 * @property string $name e.g., "Lunch Menu", "Seasonal Specials", "Eid Collection"
 * @property string|null $description
 * @property string $slug Unique identifier for the collection within the business, useful for direct links
 * @property mixed $type e.g., catalog, collection, seasonal, special, custom
 * @property bool $is_active Manually toggle visibility
 * @property \Illuminate\Support\Carbon|null $active_start_time For scheduled activation
 * @property \Illuminate\Support\Carbon|null $active_end_time For scheduled deactivation
 * @property int|null $display_order To order collections if multiple are active
 * @property string|null $image_url A banner or image for the collection
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $business_branch_id
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Business\ProductBranchCustomization> $branchCustomizations
 * @property-read int|null $branch_customizations_count
 * @property-read \App\Models\Business\Business $business
 * @property-read \App\Models\Business\BusinessBranch|null $businessBranch
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Business\ProductCategory> $categories
 * @property-read int|null $categories_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Business\Product> $products
 * @property-read int|null $products_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection ofType(\App\Enums\ProductCollectionType $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection ordered()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection whereActiveEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection whereActiveStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection whereBusinessBranchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection whereDisplayOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection whereImageUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductCollection whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class ProductCollection extends Model
{
    use HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'business_id',
        'business_branch_id',
        'name',
        'description',
        'slug',
        'type',
        'is_active',
        'active_start_time',
        'active_end_time',
        'display_order',
        'image_url',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_active' => 'boolean',
        'type' => ProductCollectionType::class,
        'active_start_time' => 'datetime',
        'active_end_time' => 'datetime',
        'display_order' => 'integer',
    ];

    /**
     * Get the business that owns the product collection.
     *
     * @return BelongsTo<Business,ProductCollection>
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the business branch that owns the product collection (if branch-specific).
     *
     * @return BelongsTo<BusinessBranch,ProductCollection>
     */
    public function businessBranch(): BelongsTo
    {
        return $this->belongsTo(BusinessBranch::class);
    }

    /**
     * Get the categories in this product collection.
     *
     * @return HasMany<ProductCategory,ProductCollection>
     */
    public function categories(): HasMany
    {
        return $this->hasMany(ProductCategory::class);
    }

    /**
     * Get the products in this product collection.
     *
     * @return BelongsToMany<Product,ProductCollection>
     */
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, 'product_collection_products')
            ->withPivot('display_order')
            ->withTimestamps();
    }

    /**
     * Scope a query to only include active collections.
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->where(function ($query) {
                $query->whereNull('active_start_time')
                    ->orWhere('active_start_time', '<=', now());
            })
            ->where(function ($query) {
                $query->whereNull('active_end_time')
                    ->orWhere('active_end_time', '>', now());
            });
    }

    /**
     * Scope a query to only include collections of a specific type.
     */
    public function scopeOfType($query, ProductCollectionType $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope a query to order collections by their display order.
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order');
    }

    /**
     * Get the branch customizations for this product collection.
     *
     * @return HasMany<ProductBranchCustomization,ProductCollection>
     */
    public function branchCustomizations(): HasMany
    {
        return $this->hasMany(ProductBranchCustomization::class);
    }

    /**
     * Check if the collection is currently active.
     */
    public function isActive(): bool
    {
        if (! $this->is_active) {
            return false;
        }

        $now = now();

        if ($this->active_start_time && $this->active_start_time->isFuture()) {
            return false;
        }

        if ($this->active_end_time && $this->active_end_time->isPast()) {
            return false;
        }

        return true;
    }

    /**
     * Check if the collection is active for a specific branch.
     */
    public function isActiveForBranch(?string $branchId = null): bool
    {
        // If no branch specified, use default active status
        if (! $branchId) {
            return $this->isActive();
        }

        // Check for branch-specific customization
        $customization = $this->branchCustomizations()
            ->where('business_branch_id', $branchId)
            ->first();

        if ($customization) {
            return $customization->isCurrentlyActive();
        }

        // Fall back to default active status
        return $this->isActive();
    }

    /**
     * Check if this collection is branch-specific.
     */
    public function isBranchSpecific(): bool
    {
        return ! is_null($this->business_branch_id);
    }

    /**
     * Check if this collection is available to all branches.
     */
    public function isGlobal(): bool
    {
        return is_null($this->business_branch_id);
    }
}
