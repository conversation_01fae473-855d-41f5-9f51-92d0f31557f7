<?php

declare(strict_types=1);

namespace App\Models\Business;

use App\Enums\Business\ProductOptionSelectionType;
use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $product_id
 * @property string $name
 * @property string|null $description
 * @property mixed $selection_type single, multiple
 * @property int $min_selections
 * @property int|null $max_selections null means unlimited
 * @property bool $is_required
 * @property int $display_order
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Business\ProductOption> $activeOptions
 * @property-read int|null $active_options_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Business\ProductOption> $options
 * @property-read int|null $options_count
 * @property-read \App\Models\Business\Product $product
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup multipleSelection()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup optional()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup ordered()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup required()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup singleSelection()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup whereDisplayOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup whereIsRequired($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup whereMaxSelections($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup whereMinSelections($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup whereSelectionType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductOptionGroup whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class ProductOptionGroup extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'product_id',
        'name',
        'description',
        'selection_type',
        'min_selections',
        'max_selections',
        'is_required',
        'display_order',
    ];

    protected $casts = [
        'selection_type' => ProductOptionSelectionType::class,
        'min_selections' => 'integer',
        'max_selections' => 'integer',
        'is_required' => 'boolean',
        'display_order' => 'integer',
    ];

    /**
     * Get the tenant that owns the product option group.
     *
     * @return BelongsTo<Tenant,ProductOptionGroup>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the product that owns the option group.
     *
     * @return BelongsTo<Product,ProductOptionGroup>
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    /**
     * Get the options in this group.
     *
     * @return HasMany<ProductOption,ProductOptionGroup>
     */
    public function options(): HasMany
    {
        return $this->hasMany(ProductOption::class, 'option_group_id')->orderBy('display_order');
    }

    /**
     * Get the active options in this group.
     *
     * @return HasMany<ProductOption,ProductOptionGroup>
     */
    public function activeOptions(): HasMany
    {
        return $this->options()->where('is_active', true);
    }

    /**
     * Scope to get required option groups.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRequired($query)
    {
        return $query->where('is_required', true);
    }

    /**
     * Scope to get optional option groups.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOptional($query)
    {
        return $query->where('is_required', false);
    }

    /**
     * Scope to get groups ordered by display order.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order', 'asc')->orderBy('name', 'asc');
    }

    /**
     * Scope to get single selection groups.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSingleSelection($query)
    {
        return $query->where('selection_type', ProductOptionSelectionType::SINGLE);
    }

    /**
     * Scope to get multiple selection groups.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeMultipleSelection($query)
    {
        return $query->where('selection_type', ProductOptionSelectionType::MULTIPLE);
    }

    /**
     * Check if this is a single selection group.
     */
    public function isSingleSelection(): bool
    {
        return $this->selection_type === ProductOptionSelectionType::SINGLE;
    }

    /**
     * Check if this is a multiple selection group.
     */
    public function isMultipleSelection(): bool
    {
        return $this->selection_type === ProductOptionSelectionType::MULTIPLE;
    }

    /**
     * Check if this group is required.
     */
    public function isRequired(): bool
    {
        return $this->is_required;
    }

    /**
     * Check if this group has options.
     */
    public function hasOptions(): bool
    {
        return $this->options()->exists();
    }

    /**
     * Check if this group has active options.
     */
    public function hasActiveOptions(): bool
    {
        return $this->activeOptions()->exists();
    }

    /**
     * Get the count of options in this group.
     */
    public function getOptionsCount(): int
    {
        return $this->options()->count();
    }

    /**
     * Get the count of active options in this group.
     */
    public function getActiveOptionsCount(): int
    {
        return $this->activeOptions()->count();
    }

    /**
     * Validate if the given selections are valid for this group.
     */
    public function validateSelections(array $selectedOptionIds): array
    {
        $errors = [];
        $selectionCount = count($selectedOptionIds);

        // Check if required group has selections
        if ($this->is_required && $selectionCount === 0) {
            $errors[] = "Selection is required for {$this->name}";
        }

        // Check minimum selections
        if ($selectionCount > 0 && $selectionCount < $this->min_selections) {
            $errors[] = "Minimum {$this->min_selections} selection(s) required for {$this->name}";
        }

        // Check maximum selections
        if ($this->max_selections && $selectionCount > $this->max_selections) {
            $errors[] = "Maximum {$this->max_selections} selection(s) allowed for {$this->name}";
        }

        // Check if single selection has only one option
        if ($this->isSingleSelection() && $selectionCount > 1) {
            $errors[] = "Only one selection allowed for {$this->name}";
        }

        // Check if all selected options belong to this group and are active
        if (! empty($selectedOptionIds)) {
            $validOptionIds = $this->activeOptions()->pluck('id')->toArray();
            $invalidOptions = array_diff($selectedOptionIds, $validOptionIds);

            if (! empty($invalidOptions)) {
                $errors[] = "Invalid option selection for {$this->name}";
            }
        }

        return $errors;
    }

    /**
     * Get the default option for this group.
     */
    public function getDefaultOption(): ?ProductOption
    {
        return $this->activeOptions()->where('is_default', true)->first();
    }

    /**
     * Get the cheapest option in this group.
     */
    public function getCheapestOption(): ?ProductOption
    {
        return $this->activeOptions()->orderBy('price_adjustment', 'asc')->first();
    }

    /**
     * Get the most expensive option in this group.
     */
    public function getMostExpensiveOption(): ?ProductOption
    {
        return $this->activeOptions()->orderBy('price_adjustment', 'desc')->first();
    }

    /**
     * Get the price range for options in this group.
     */
    public function getPriceRange(): array
    {
        $options = $this->activeOptions()->get();

        if ($options->isEmpty()) {
            return ['min' => 0, 'max' => 0];
        }

        return [
            'min' => $options->min('price_adjustment'),
            'max' => $options->max('price_adjustment'),
        ];
    }

    /**
     * Update the display order for this group.
     */
    public function updateDisplayOrder(int $displayOrder): bool
    {
        return $this->update(['display_order' => $displayOrder]);
    }

    /**
     * Add a new option to this group.
     */
    public function addOption(array $optionData): ProductOption
    {
        $optionData['tenant_id'] = $this->tenant_id;
        $optionData['option_group_id'] = $this->id;

        if (! isset($optionData['display_order'])) {
            $optionData['display_order'] = $this->getOptionsCount() + 1;
        }

        return ProductOption::create($optionData);
    }

    /**
     * Clone this option group to another product.
     */
    public function cloneToProduct(string $productId): ProductOptionGroup
    {
        $newGroup = $this->replicate();
        $newGroup->product_id = $productId;
        $newGroup->save();

        // Clone all options
        foreach ($this->options as $option) {
            $newOption = $option->replicate();
            $newOption->option_group_id = $newGroup->id;
            $newOption->save();
        }

        return $newGroup;
    }

    /**
     * Get option group summary for API responses.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'selection_type' => $this->selection_type->value,
            'min_selections' => $this->min_selections,
            'max_selections' => $this->max_selections,
            'is_required' => $this->is_required,
            'display_order' => $this->display_order,
            'options_count' => $this->getOptionsCount(),
            'active_options_count' => $this->getActiveOptionsCount(),
            'has_options' => $this->hasOptions(),
            'price_range' => $this->getPriceRange(),
        ];
    }

    /**
     * Get detailed structure with options for API responses.
     */
    public function getDetailedStructure(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'selection_type' => $this->selection_type->value,
            'min_selections' => $this->min_selections,
            'max_selections' => $this->max_selections,
            'is_required' => $this->is_required,
            'display_order' => $this->display_order,
            'options' => $this->activeOptions->map(function ($option) {
                return $option->getSummary();
            }),
        ];
    }

    /**
     * Get selection rules as a human-readable string.
     */
    public function getSelectionRulesText(): string
    {
        $rules = [];

        if ($this->is_required) {
            $rules[] = 'Required';
        }

        if ($this->isSingleSelection()) {
            $rules[] = 'Select one';
        } else {
            if ($this->min_selections > 0 && $this->max_selections) {
                $rules[] = "Select {$this->min_selections}-{$this->max_selections}";
            } elseif ($this->min_selections > 0) {
                $rules[] = "Select at least {$this->min_selections}";
            } elseif ($this->max_selections) {
                $rules[] = "Select up to {$this->max_selections}";
            } else {
                $rules[] = 'Select any';
            }
        }

        return implode(', ', $rules);
    }
}
