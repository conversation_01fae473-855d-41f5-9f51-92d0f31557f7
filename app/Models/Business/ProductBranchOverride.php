<?php

declare(strict_types=1);

namespace App\Models\Business;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $business_id
 * @property string $business_branch_id
 * @property string $product_id
 * @property bool|null $is_available Override product availability for this branch
 * @property numeric|null $price_override Branch-specific price override
 * @property int|null $stock_override Branch-specific stock override
 * @property string|null $description_override Branch-specific description override
 * @property array<array-key, mixed>|null $custom_attributes Branch-specific custom attributes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Business\Business $business
 * @property-read \App\Models\Business\BusinessBranch $businessBranch
 * @property mixed|null $custom
 * @property-read \App\Models\Business\Product $product
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride available()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride forBranch(string $branchId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride forProduct(string $productId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride whereBusinessBranchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride whereCustomAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride whereDescriptionOverride($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride whereIsAvailable($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride wherePriceOverride($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride whereStockOverride($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride withPriceOverride()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProductBranchOverride withStockOverride()
 *
 * @mixin \Eloquent
 */
class ProductBranchOverride extends Model
{
    use BelongsToTenant, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'tenant_id',
        'business_id',
        'business_branch_id',
        'product_id',
        'is_available',
        'price_override',
        'stock_override',
        'description_override',
        'custom_attributes',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_available' => 'boolean',
        'price_override' => 'decimal:2',
        'stock_override' => 'integer',
        'custom_attributes' => 'array',
    ];

    /**
     * Get the business that owns this override.
     *
     * @return BelongsTo<Business,ProductBranchOverride>
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the business branch that owns this override.
     *
     * @return BelongsTo<BusinessBranch,ProductBranchOverride>
     */
    public function businessBranch(): BelongsTo
    {
        return $this->belongsTo(BusinessBranch::class);
    }

    /**
     * Get the product this override applies to.
     *
     * @return BelongsTo<Product,ProductBranchOverride>
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class);
    }

    /**
     * Get custom attribute value by key.
     */
    public function getCustomAttribute(string $key, mixed $default = null): mixed
    {
        return data_get($this->custom_attributes, $key, $default);
    }

    /**
     * Set custom attribute value.
     */
    public function setCustomAttribute(string $key, mixed $value): void
    {
        $attributes = $this->custom_attributes ?? [];
        data_set($attributes, $key, $value);
        $this->custom_attributes = $attributes;
    }

    /**
     * Check if this override makes the product available.
     */
    public function isProductAvailable(): bool
    {
        return $this->is_available ?? true;
    }

    /**
     * Get the effective price for this product at this branch.
     */
    public function getEffectivePrice(): ?float
    {
        return $this->price_override ? (float) $this->price_override : null;
    }

    /**
     * Get the effective stock for this product at this branch.
     */
    public function getEffectiveStock(): ?int
    {
        return $this->stock_override;
    }

    /**
     * Get the effective description for this product at this branch.
     */
    public function getEffectiveDescription(): ?string
    {
        return $this->description_override;
    }

    /**
     * Scope a query to filter by business branch.
     */
    public function scopeForBranch($query, string $branchId)
    {
        return $query->where('business_branch_id', $branchId);
    }

    /**
     * Scope a query to filter by product.
     */
    public function scopeForProduct($query, string $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * Scope a query to only include available products.
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope a query to only include products with price overrides.
     */
    public function scopeWithPriceOverride($query)
    {
        return $query->whereNotNull('price_override');
    }

    /**
     * Scope a query to only include products with stock overrides.
     */
    public function scopeWithStockOverride($query)
    {
        return $query->whereNotNull('stock_override');
    }
}
