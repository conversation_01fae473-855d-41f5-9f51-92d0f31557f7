<?php

declare(strict_types=1);

namespace App\Models\Business;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $tenant_id FK to tenants.id
 * @property string $business_id
 * @property \Illuminate\Support\Carbon $metric_date
 * @property string $period_type
 * @property int $total_orders
 * @property int $completed_orders
 * @property int $cancelled_orders
 * @property numeric $total_revenue
 * @property numeric $total_commission_paid
 * @property numeric $average_order_value
 * @property numeric|null $average_delivery_time_minutes
 * @property numeric|null $customer_satisfaction_score Average rating 1-5
 * @property int $new_customers
 * @property int $returning_customers
 * @property array<array-key, mixed>|null $top_products Best selling products for the period
 * @property array<array-key, mixed>|null $peak_hours Busiest hours of the day
 * @property string $currency
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Business\Business $business
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric whereAverageDeliveryTimeMinutes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric whereAverageOrderValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric whereCancelledOrders($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric whereCompletedOrders($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric whereCustomerSatisfactionScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric whereMetricDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric whereNewCustomers($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric wherePeakHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric wherePeriodType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric whereReturningCustomers($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric whereTopProducts($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric whereTotalCommissionPaid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric whereTotalOrders($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric whereTotalRevenue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessPerformanceMetric whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class BusinessPerformanceMetric extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'business_id',
        'metric_date',
        'period_type',
        'total_orders',
        'completed_orders',
        'cancelled_orders',
        'total_revenue',
        'total_commission_paid',
        'average_order_value',
        'average_delivery_time_minutes',
        'customer_satisfaction_score',
        'new_customers',
        'returning_customers',
        'top_products',
        'peak_hours',
        'currency',
    ];

    protected $casts = [
        'metric_date' => 'date',
        'total_orders' => 'integer',
        'completed_orders' => 'integer',
        'cancelled_orders' => 'integer',
        'total_revenue' => 'decimal:2',
        'total_commission_paid' => 'decimal:2',
        'average_order_value' => 'decimal:2',
        'average_delivery_time_minutes' => 'decimal:2',
        'customer_satisfaction_score' => 'decimal:2',
        'new_customers' => 'integer',
        'returning_customers' => 'integer',
        'top_products' => 'array',
        'peak_hours' => 'array',
    ];

    /**
     * Get the tenant that owns the business performance metric.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the business associated with the performance metric.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }
}
