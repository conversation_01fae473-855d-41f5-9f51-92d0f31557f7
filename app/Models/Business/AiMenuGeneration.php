<?php

declare(strict_types=1);

namespace App\Models\Business;

use App\Models\System\Tenant;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $tenant_id FK to tenants.id
 * @property string $business_id
 * @property string $user_id
 * @property string $input_text Natural language input from user
 * @property array<array-key, mixed> $generated_menu_data AI-generated menu structure
 * @property string $status
 * @property int|null $processing_time_ms Time taken to generate
 * @property numeric|null $confidence_score 0-1
 * @property \Illuminate\Support\Carbon|null $applied_at When the generated menu was applied to the business
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Business\Business $business
 * @property-read Tenant $tenant
 * @property-read User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiMenuGeneration newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiMenuGeneration newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiMenuGeneration query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiMenuGeneration whereAppliedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiMenuGeneration whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiMenuGeneration whereConfidenceScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiMenuGeneration whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiMenuGeneration whereGeneratedMenuData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiMenuGeneration whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiMenuGeneration whereInputText($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiMenuGeneration whereProcessingTimeMs($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiMenuGeneration whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiMenuGeneration whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiMenuGeneration whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiMenuGeneration whereUserId($value)
 *
 * @mixin \Eloquent
 */
class AiMenuGeneration extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'business_id',
        'user_id',
        'input_text',
        'generated_menu_data',
        'status',
        'processing_time_ms',
        'confidence_score',
        'applied_at',
    ];

    protected $casts = [
        'generated_menu_data' => 'array',
        'processing_time_ms' => 'integer',
        'confidence_score' => 'decimal:2',
        'applied_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the AI menu generation.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    /**
     * Get the business that owns the AI menu generation.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the user who requested the generation.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
