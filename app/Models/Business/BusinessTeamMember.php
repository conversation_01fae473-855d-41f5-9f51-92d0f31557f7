<?php

declare(strict_types=1);

namespace App\Models\Business;

use App\Enums\Business\TeamMemberRelationshipType;
use App\Models\System\Tenant;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $business_id
 * @property string $user_id
 * @property string|null $primary_branch_id
 * @property TeamMemberRelationshipType $role Defines the type of relationship to the business (e.g. admin, manager, staff)
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Business $business
 * @property-read BusinessBranch|null $primaryBranch
 * @property-read Tenant $tenant
 * @property-read User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessTeamMember newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessTeamMember newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessTeamMember query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessTeamMember whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessTeamMember whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessTeamMember whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessTeamMember wherePrimaryBranchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessTeamMember whereRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessTeamMember whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessTeamMember whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessTeamMember whereUserId($value)
 *
 * @mixin \Eloquent
 */
class BusinessTeamMember extends Model
{
    use BelongsToTenant, HasFactory, HasUuids;

    protected $fillable = [
        'id',
        'tenant_id',
        'business_id',
        'user_id',
        'primary_branch_id',
        'role',
    ];

    protected $casts = [
        'role' => TeamMemberRelationshipType::class,
    ];

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function primaryBranch(): BelongsTo
    {
        return $this->belongsTo(BusinessBranch::class, 'primary_branch_id');
    }
}
