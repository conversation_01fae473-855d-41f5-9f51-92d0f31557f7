<?php

declare(strict_types=1);

namespace App\Models\Business;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $business_id
 * @property string $setting_key
 * @property array<array-key, mixed> $setting_value
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Business\Business $business
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessSetting newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessSetting newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessSetting query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessSetting whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessSetting whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessSetting whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessSetting whereSettingKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessSetting whereSettingValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessSetting whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BusinessSetting whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class BusinessSetting extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'business_id',
        'setting_key',
        'setting_value',
    ];

    protected $casts = [
        'setting_value' => 'array',
    ];

    /**
     * Get the tenant that owns the business setting.
     *
     * @return BelongsTo<\App\Models\System\Tenant,BusinessSetting>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the business that owns the setting.
     *
     * @return BelongsTo<Business,BusinessSetting>
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }
}
