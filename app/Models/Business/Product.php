<?php

declare(strict_types=1);

namespace App\Models\Business;

use App\Models\Core\Attachment;
use App\Models\Delivery\OrderItem;
use App\Models\Delivery\Rating;
use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Laravel\Scout\Searchable;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $business_id
 * @property string $name
 * @property string $slug
 * @property string|null $description
 * @property numeric $price Price in the business's primary currency
 * @property numeric|null $cost_price Cost of acquiring or manufacturing the product (for profitability tracking)
 * @property numeric|null $sale_price
 * @property string|null $sku
 * @property string|null $barcode
 * @property int $quantity -1 means unlimited (Current stock level)
 * @property numeric|null $weight in kg
 * @property array<array-key, mixed>|null $dimensions length, width, height
 * @property string|null $main_image_url
 * @property array<array-key, mixed>|null $additional_images Array of image URLs
 * @property bool $is_available
 * @property bool $allows_pickup
 * @property bool|null $auto_accept_orders Overrides business setting
 * @property numeric|null $min_order_value_for_auto_accept
 * @property int|null $preparation_time_minutes Minutes
 * @property string|null $category_id
 * @property array<array-key, mixed>|null $tags Array of tag strings
 * @property array<array-key, mixed>|null $attributes Custom product attributes (can still be useful for non-option attributes)
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, ProductReview> $approvedReviews
 * @property-read int|null $approved_reviews_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Attachment> $attachments
 * @property-read int|null $attachments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, ProductBranchOverride> $branchOverrides
 * @property-read int|null $branch_overrides_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, ProductBundle> $bundles
 * @property-read int|null $bundles_count
 * @property-read Business $business
 * @property-read ProductCategory|null $category
 * @property-read \Illuminate\Database\Eloquent\Collection<int, InventoryMovement> $inventoryMovements
 * @property-read int|null $inventory_movements_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, ProductOptionGroup> $optionGroups
 * @property-read int|null $option_groups_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, OrderItem> $orderItems
 * @property-read int|null $order_items_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, ProductCollection> $productCollections
 * @property-read int|null $product_collections_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Rating> $ratings
 * @property-read int|null $ratings_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, ProductReview> $reviews
 * @property-read int|null $reviews_count
 * @property-read Tenant $tenant
 * @property-read \Illuminate\Database\Eloquent\Collection<int, ProductVariant> $variants
 * @property-read int|null $variants_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product allowsPickup()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product available()
 * @method static \Database\Factories\ProductFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product inCategory(string $categoryId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product inStock()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product priceRange(float $minPrice, float $maxPrice)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product search(string $searchTerm)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereAdditionalImages($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereAllowsPickup($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereAttributes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereAutoAcceptOrders($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereBarcode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereCategoryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereCostPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereDimensions($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereIsAvailable($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereMainImageUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereMinOrderValueForAutoAccept($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product wherePreparationTimeMinutes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereSalePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereSku($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereTags($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Product whereWeight($value)
 *
 * @mixin \Eloquent
 */
class Product extends Model
{
    use BelongsToTenant, HasFactory, HasUuids, Searchable;

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Database\Factories\ProductFactory::new();
    }

    protected $fillable = [
        'tenant_id',
        'business_id',
        'name',
        'slug',
        'description',
        'price',
        'cost_price',
        'sale_price',
        'sku',
        'barcode',
        'quantity',
        'weight',
        'dimensions',
        'main_image_url',
        'additional_images',
        'is_available',
        'allows_pickup',
        'auto_accept_orders',
        'min_order_value_for_auto_accept',
        'preparation_time_minutes',
        'category_id',
        'tags',
        'attributes',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'cost_price' => 'decimal:2',
        'sale_price' => 'decimal:2',
        'quantity' => 'integer',
        'weight' => 'decimal:3',
        'dimensions' => 'array',
        'additional_images' => 'array',
        'is_available' => 'boolean',
        'allows_pickup' => 'boolean',
        'auto_accept_orders' => 'boolean',
        'min_order_value_for_auto_accept' => 'decimal:2',
        'preparation_time_minutes' => 'integer',
        'tags' => 'array',
        'attributes' => 'array',
    ];

    /**
     * Get the tenant that owns the product.
     *
     * @return BelongsTo<Tenant,Product>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    /**
     * Get the business that owns the product.
     *
     * @return BelongsTo<Business,Product>
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }

    /**
     * Get the category that owns the product.
     *
     * @return BelongsTo<ProductCategory,Product>
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(ProductCategory::class, 'category_id');
    }

    /**
     * Get the product variants for the product.
     *
     * @return HasMany<ProductVariant,Product>
     */
    public function variants(): HasMany
    {
        return $this->hasMany(ProductVariant::class);
    }

    /**
     * Get the product option groups for the product.
     *
     * @return HasMany<ProductOptionGroup,Product>
     */
    public function optionGroups(): HasMany
    {
        return $this->hasMany(ProductOptionGroup::class);
    }

    /**
     * Get the order items for the product.
     *
     * @return HasMany<OrderItem,Product>
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the inventory movements for the product.
     *
     * @return HasMany<InventoryMovement,Product>
     */
    public function inventoryMovements(): HasMany
    {
        return $this->hasMany(InventoryMovement::class);
    }

    /**
     * Get the product bundles that include this product.
     *
     * @return BelongsToMany<ProductBundle,Product>
     */
    public function bundles(): BelongsToMany
    {
        return $this->belongsToMany(ProductBundle::class, 'product_bundle_items', 'product_id', 'bundle_id')
            ->withPivot('quantity', 'discount_percentage')
            ->withTimestamps();
    }

    /**
     * Get the product collections that include this product.
     *
     * @return BelongsToMany<ProductCollection,Product>
     */
    public function productCollections(): BelongsToMany
    {
        return $this->belongsToMany(ProductCollection::class, 'product_collection_products')
            ->withPivot('display_order')
            ->withTimestamps();
    }

    /**
     * Get the ratings for the product.
     *
     * @return MorphMany<Rating,Product>
     */
    public function ratings(): MorphMany
    {
        return $this->morphMany(Rating::class, 'rateable');
    }

    /**
     * Get the attachments for the product.
     *
     * @return MorphMany<Attachment,Product>
     */
    public function attachments(): MorphMany
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }

    /**
     * Get the reviews for this product.
     *
     * @return HasMany<ProductReview,Product>
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(ProductReview::class, 'product_id');
    }

    /**
     * Get only approved reviews for this product.
     *
     * @return HasMany<ProductReview,Product>
     */
    public function approvedReviews(): HasMany
    {
        return $this->hasMany(ProductReview::class, 'product_id')->where('status', 'approved');
    }

    /**
     * Get the branch overrides for this product.
     *
     * @return HasMany<ProductBranchOverride,Product>
     */
    public function branchOverrides(): HasMany
    {
        return $this->hasMany(ProductBranchOverride::class);
    }

    /**
     * Scope to get available products.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAvailable($query)
    {
        return $query->where('is_available', true);
    }

    /**
     * Scope to get products that allow pickup.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAllowsPickup($query)
    {
        return $query->where('allows_pickup', true);
    }

    /**
     * Scope to get products in stock.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInStock($query)
    {
        return $query->where(function ($query) {
            $query->where('quantity', '>', 0)
                ->orWhere('quantity', -1); // -1 means unlimited
        });
    }

    /**
     * Scope to get products by category.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInCategory($query, string $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope to search products by name or description.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeSearch($query, string $searchTerm)
    {
        return $query->where(function ($query) use ($searchTerm) {
            $query->where('name', 'like', "%{$searchTerm}%")
                ->orWhere('description', 'like', "%{$searchTerm}%")
                ->orWhere('sku', 'like', "%{$searchTerm}%");
        });
    }

    /**
     * Scope to get products within a price range.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePriceRange($query, float $minPrice, float $maxPrice)
    {
        return $query->whereBetween('price', [$minPrice, $maxPrice]);
    }

    /**
     * Check if the product is in stock.
     */
    public function isInStock(): bool
    {
        return $this->quantity > 0 || $this->quantity === -1;
    }

    /**
     * Check if the product has unlimited stock.
     */
    public function hasUnlimitedStock(): bool
    {
        return $this->quantity === -1;
    }

    /**
     * Get the effective price (sale price if available, otherwise regular price).
     */
    public function getEffectivePrice(): float
    {
        return (float) ($this->sale_price ?? $this->price);
    }

    /**
     * Get the profit margin.
     */
    public function getProfitMargin(): ?float
    {
        if (is_null($this->cost_price) || $this->cost_price == 0) {
            return null;
        }

        $effectivePrice = $this->getEffectivePrice();

        return (($effectivePrice - $this->cost_price) / $effectivePrice) * 100;
    }

    /**
     * Get the formatted price with currency.
     */
    public function getFormattedPrice(string $currency = 'NGN'): string
    {
        return $currency.' '.number_format($this->getEffectivePrice(), 2);
    }

    /**
     * Check if the product is on sale.
     */
    public function isOnSale(): bool
    {
        return ! is_null($this->sale_price) && $this->sale_price < $this->price;
    }

    /**
     * Get the discount percentage if on sale.
     */
    public function getDiscountPercentage(): ?float
    {
        if (! $this->isOnSale()) {
            return null;
        }

        return (($this->price - $this->sale_price) / $this->price) * 100;
    }

    /**
     * Reduce the product quantity.
     */
    public function reduceQuantity(int $quantity): bool
    {
        if ($this->quantity === -1) {
            return true; // Unlimited stock
        }

        if ($this->quantity < $quantity) {
            return false; // Insufficient stock
        }

        $this->quantity -= $quantity;

        return $this->save();
    }

    /**
     * Increase the product quantity.
     */
    public function increaseQuantity(int $quantity): bool
    {
        if ($this->quantity === -1) {
            return true; // Unlimited stock
        }

        $this->quantity += $quantity;

        return $this->save();
    }

    /**
     * Get the average rating for the product.
     */
    public function getAverageRating(): float
    {
        return $this->ratings()->avg('rating') ?? 0;
    }

    /**
     * Get the total number of ratings.
     */
    public function getTotalRatings(): int
    {
        return $this->ratings()->count();
    }

    /**
     * Get all images (main + additional).
     */
    public function getAllImages(): array
    {
        $images = [];

        if ($this->main_image_url) {
            $images[] = $this->main_image_url;
        }

        if ($this->additional_images) {
            $images = array_merge($images, $this->additional_images);
        }

        return $images;
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array<string, mixed>
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'tenant_id' => $this->tenant_id,
            'business_id' => $this->business_id,
            'name' => $this->name,
            'description' => $this->description,
            'sku' => $this->sku,
            'price' => (float) $this->price,
            'sale_price' => $this->sale_price ? (float) $this->sale_price : null,
            'quantity' => $this->quantity,
            'is_available' => $this->is_available,
            'allows_pickup' => $this->allows_pickup,
            'category_id' => $this->category_id,
            'category_name' => $this->category?->name,
            'business_name' => $this->business?->business_name,
            'business_type' => $this->business?->business_type?->value,
            'tags' => $this->tags,
            'rating' => $this->getAverageRating(),
            'created_at' => $this->created_at?->timestamp,
        ];
    }

    /**
     * Get the Scout search index name.
     *
     * Uses a single global index for central customers to search
     * across all products from all tenants/businesses.
     */
    public function searchableAs(): string
    {
        return 'products';
    }

    /**
     * Determine if the model should be searchable.
     */
    public function shouldBeSearchable(): bool
    {
        return $this->is_available;
    }

    /**
     * Get the Scout search key.
     */
    public function getScoutKey(): mixed
    {
        return $this->id;
    }

    /**
     * Get the Scout search key name.
     */
    public function getScoutKeyName(): string
    {
        return 'id';
    }
}
