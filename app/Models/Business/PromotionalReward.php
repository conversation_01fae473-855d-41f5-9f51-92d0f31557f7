<?php

namespace App\Models\Business;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $promotion_id
 * @property string $user_id
 * @property string $reward_type
 * @property numeric $reward_value
 * @property string $currency
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $granted_at
 * @property \Illuminate\Support\Carbon|null $redeemed_at
 * @property \Illuminate\Support\Carbon|null $expires_at
 * @property int $usage_count
 * @property int|null $max_usage Null means unlimited usage
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Business\Promotion $promotion
 * @property-read \App\Models\User\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward expired()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward redeemed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward whereGrantedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward whereMaxUsage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward wherePromotionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward whereRedeemedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward whereRewardType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward whereRewardValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward whereUsageCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalReward whereUserId($value)
 *
 * @mixin \Eloquent
 */
class PromotionalReward extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'id',
        'promotion_id',
        'user_id',
        'reward_type',
        'reward_value',
        'currency',
        'status',
        'granted_at',
        'redeemed_at',
        'expires_at',
        'usage_count',
        'max_usage',
    ];

    protected $casts = [
        'reward_value' => 'decimal:2',
        'granted_at' => 'datetime',
        'redeemed_at' => 'datetime',
        'expires_at' => 'datetime',
        'usage_count' => 'integer',
        'max_usage' => 'integer',
    ];

    /**
     * Get the promotion that owns this reward.
     *
     * @return BelongsTo<Promotion,PromotionalReward>
     */
    public function promotion(): BelongsTo
    {
        return $this->belongsTo(Promotion::class, 'promotion_id');
    }

    /**
     * Get the user that received this reward.
     *
     * @return BelongsTo<User,PromotionalReward>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'user_id');
    }

    /**
     * Scope to get active promotional rewards.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get redeemed promotional rewards.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRedeemed($query)
    {
        return $query->where('status', 'redeemed');
    }

    /**
     * Scope to get expired promotional rewards.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeExpired($query)
    {
        return $query->where('status', 'expired')
            ->orWhere(function ($query) {
                $query->where('expires_at', '<', now())
                    ->where('status', '!=', 'redeemed');
            });
    }

    /**
     * Check if the reward is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast() && $this->status !== 'redeemed';
    }

    /**
     * Check if the reward is redeemable.
     */
    public function isRedeemable(): bool
    {
        return $this->status === 'active' && ! $this->isExpired();
    }
}
