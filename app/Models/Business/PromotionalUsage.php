<?php

declare(strict_types=1);

namespace App\Models\Business;

use App\Models\Delivery\Order;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $promotion_id
 * @property string $user_id
 * @property string|null $order_id
 * @property string|null $promotional_code
 * @property numeric $discount_amount
 * @property string $currency
 * @property \Illuminate\Support\Carbon $used_at
 * @property string|null $usage_context Additional context about the usage
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Business\Promotion $promotion
 * @property-read \App\Models\User\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalUsage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalUsage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalUsage query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalUsage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalUsage whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalUsage whereDiscountAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalUsage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalUsage whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalUsage wherePromotionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalUsage wherePromotionalCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalUsage whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalUsage whereUsageContext($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalUsage whereUsedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PromotionalUsage whereUserId($value)
 *
 * @property-read Order|null $order
 *
 * @mixin \Eloquent
 */
class PromotionalUsage extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'id',
        'user_id',
        'promotion_id',
        'order_id',
        'used_at',
        'discount_amount',
        'currency',
    ];

    protected $casts = [
        'used_at' => 'datetime',
        'discount_amount' => 'decimal:2',
    ];

    /**
     * Get the user who used the promotion.
     *
     * @return BelongsTo<User,PromotionalUsage>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'user_id');
    }

    /**
     * Get the promotion that was used.
     *
     * @return BelongsTo<Promotion,PromotionalUsage>
     */
    public function promotion(): BelongsTo
    {
        return $this->belongsTo(Promotion::class, 'promotion_id');
    }

    /**
     * Get the order where the promotion was applied.
     *
     * @return BelongsTo<Order,PromotionalUsage>
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id');
    }
}
