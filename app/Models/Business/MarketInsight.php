<?php

declare(strict_types=1);

namespace App\Models\Business;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $country_id
 * @property string|null $state_id
 * @property string|null $city_id
 * @property string $insight_type Type of insight
 * @property string $time_period Time period for the insight
 * @property \Illuminate\Support\Carbon $period_start
 * @property \Illuminate\Support\Carbon $period_end
 * @property array<array-key, mixed> $insight_data The actual insights and metrics
 * @property numeric|null $confidence_score 0-1
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Core\City|null $city
 * @property-read \App\Models\Core\Country $country
 * @property-read \App\Models\Core\State|null $state
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MarketInsight newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MarketInsight newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MarketInsight query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MarketInsight whereCityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MarketInsight whereConfidenceScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MarketInsight whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MarketInsight whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MarketInsight whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MarketInsight whereInsightData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MarketInsight whereInsightType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MarketInsight wherePeriodEnd($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MarketInsight wherePeriodStart($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MarketInsight whereStateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MarketInsight whereTimePeriod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|MarketInsight whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class MarketInsight extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'country_id',
        'state_id',
        'city_id',
        'insight_type',
        'time_period',
        'period_start',
        'period_end',
        'insight_data',
        'confidence_score',
    ];

    protected $casts = [
        'insight_data' => 'array',
        'confidence_score' => 'decimal:2',
        'period_start' => 'date',
        'period_end' => 'date',
    ];

    /**
     * Get the country for the market insight.
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Core\Country::class);
    }

    /**
     * Get the state for the market insight.
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Core\State::class);
    }

    /**
     * Get the city for the market insight.
     */
    public function city(): BelongsTo
    {
        return $this->belongsTo(\App\Models\Core\City::class);
    }
}
