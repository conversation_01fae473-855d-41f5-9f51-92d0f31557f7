<?php

declare(strict_types=1);

namespace App\Models\Business;

use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $bundle_id
 * @property string $product_id
 * @property string|null $variant_id
 * @property int $quantity
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Business\ProductBundle $bundle
 * @property-read string $display_name
 * @property-read \App\Models\Business\Product $product
 * @property-read Tenant $tenant
 * @property-read \App\Models\Business\ProductVariant|null $variant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BundleProduct forBundle(string $bundleId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BundleProduct forProduct(string $productId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BundleProduct newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BundleProduct newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BundleProduct query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BundleProduct whereBundleId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BundleProduct whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BundleProduct whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BundleProduct whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BundleProduct whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BundleProduct whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BundleProduct whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|BundleProduct whereVariantId($value)
 *
 * @mixin \Eloquent
 */
class BundleProduct extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'bundle_products';

    protected $fillable = [
        'tenant_id',
        'bundle_id',
        'product_id',
        'variant_id',
        'quantity',
    ];

    protected $casts = [
        'quantity' => 'integer',
    ];

    /**
     * Get the tenant that owns the bundle product.
     *
     * @return BelongsTo<Tenant,BundleProduct>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    /**
     * Get the product bundle that owns this bundle product.
     *
     * @return BelongsTo<ProductBundle,BundleProduct>
     */
    public function bundle(): BelongsTo
    {
        return $this->belongsTo(ProductBundle::class, 'bundle_id');
    }

    /**
     * Get the product for this bundle product.
     *
     * @return BelongsTo<Product,BundleProduct>
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    /**
     * Get the product variant for this bundle product.
     *
     * @return BelongsTo<ProductVariant,BundleProduct>
     */
    public function variant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class, 'variant_id');
    }

    /**
     * Scope to get bundle products by bundle.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForBundle($query, string $bundleId)
    {
        return $query->where('bundle_id', $bundleId);
    }

    /**
     * Scope to get bundle products by product.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForProduct($query, string $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * Check if this bundle product uses a variant.
     */
    public function hasVariant(): bool
    {
        return ! is_null($this->variant_id);
    }

    /**
     * Get the effective price for this bundle product.
     */
    public function getEffectivePrice(): float
    {
        if ($this->hasVariant()) {
            return $this->variant->getEffectivePrice();
        }

        return $this->product->getEffectivePrice();
    }

    /**
     * Get the total price for this bundle product (price * quantity).
     */
    public function getTotalPrice(): float
    {
        return $this->getEffectivePrice() * $this->quantity;
    }

    /**
     * Get the display name for this bundle product.
     */
    public function getDisplayNameAttribute(): string
    {
        $name = $this->product->name;

        if ($this->hasVariant()) {
            $name .= ' - '.$this->variant->name;
        }

        if ($this->quantity > 1) {
            $name .= ' (x'.$this->quantity.')';
        }

        return $name;
    }

    /**
     * Check if the bundle product is available.
     */
    public function isAvailable(): bool
    {
        if (! $this->product->is_available) {
            return false;
        }

        if ($this->hasVariant() && ! $this->variant->is_active) {
            return false;
        }

        return true;
    }

    /**
     * Check if the bundle product is in stock for the given quantity.
     */
    public function isInStock(int $requestedQuantity = 1): bool
    {
        $requiredQuantity = $this->quantity * $requestedQuantity;

        if ($this->hasVariant()) {
            return $this->variant->quantity === -1 || $this->variant->quantity >= $requiredQuantity;
        }

        return $this->product->quantity === -1 || $this->product->quantity >= $requiredQuantity;
    }

    /**
     * Get the available stock for this bundle product.
     */
    public function getAvailableStock(): int
    {
        if ($this->hasVariant()) {
            $variantStock = $this->variant->quantity;

            return $variantStock === -1 ? -1 : intval($variantStock / $this->quantity);
        }

        $productStock = $this->product->quantity;

        return $productStock === -1 ? -1 : intval($productStock / $this->quantity);
    }

    /**
     * Get bundle product summary.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'product_id' => $this->product_id,
            'product_name' => $this->product->name,
            'variant_id' => $this->variant_id,
            'variant_name' => $this->variant?->name,
            'display_name' => $this->display_name,
            'quantity' => $this->quantity,
            'unit_price' => $this->getEffectivePrice(),
            'total_price' => $this->getTotalPrice(),
            'is_available' => $this->isAvailable(),
            'is_in_stock' => $this->isInStock(),
            'available_stock' => $this->getAvailableStock(),
        ];
    }
}
