<?php

declare(strict_types=1);

namespace App\Models\System;

use App\Enums\System\LogType;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $name
 * @property string|null $description
 * @property mixed $category
 * @property int $severity_level 1=info, 2=warning, 3=error, 4=critical
 * @property bool $is_active
 * @property bool $requires_user_id Whether this log type requires a user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\System\AuditLog> $auditLogs
 * @property-read int|null $audit_logs_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuditLogType active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuditLogType byCategory(\App\Enums\LogType $category)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuditLogType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuditLogType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuditLogType query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuditLogType whereCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuditLogType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuditLogType whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuditLogType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuditLogType whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuditLogType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuditLogType whereRequiresUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuditLogType whereSeverityLevel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AuditLogType whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class AuditLogType extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'description',
        'category',
        'severity_level',
        'is_active',
        'requires_user_id',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'requires_user_id' => 'boolean',
        'category' => LogType::class,
    ];

    /**
     * Get the audit logs for this type.
     */
    public function auditLogs(): HasMany
    {
        return $this->hasMany(AuditLog::class, 'action_type', 'name');
    }

    /**
     * Scope to get only active audit log types.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Get audit log types by category.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByCategory($query, LogType $category)
    {
        return $query->where('category', $category);
    }
}
