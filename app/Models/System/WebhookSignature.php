<?php

declare(strict_types=1);

namespace App\Models\System;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string|null $tenant_id FK to tenants.id
 * @property string $webhook_log_id
 * @property string $algorithm e.g., sha256, sha1, md5
 * @property string $signature The computed signature
 * @property string $header_name e.g., X-Signature, X-Hub-Signature
 * @property bool|null $is_valid Whether signature verification passed
 * @property \Illuminate\Support\Carbon|null $verified_at When signature was verified
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $algorithm_display_name
 * @property-read \App\Models\System\Tenant|null $tenant
 * @property-read \App\Models\System\WebhookLog $webhookLog
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookSignature byAlgorithm(string $algorithm)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookSignature invalid()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookSignature newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookSignature newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookSignature query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookSignature valid()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookSignature whereAlgorithm($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookSignature whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookSignature whereHeaderName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookSignature whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookSignature whereIsValid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookSignature whereSignature($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookSignature whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookSignature whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookSignature whereVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookSignature whereWebhookLogId($value)
 *
 * @mixin \Eloquent
 */
class WebhookSignature extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'webhook_log_id',
        'algorithm',
        'signature',
        'header_name',
        'is_valid',
        'verified_at',
    ];

    protected $casts = [
        'is_valid' => 'boolean',
        'verified_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the webhook signature.
     *
     * @return BelongsTo<Tenant,WebhookSignature>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    /**
     * Get the webhook log that owns this signature.
     *
     * @return BelongsTo<WebhookLog,WebhookSignature>
     */
    public function webhookLog(): BelongsTo
    {
        return $this->belongsTo(WebhookLog::class, 'webhook_log_id');
    }

    /**
     * Scope to get only valid signatures.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeValid($query)
    {
        return $query->where('is_valid', true);
    }

    /**
     * Scope to get only invalid signatures.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInvalid($query)
    {
        return $query->where('is_valid', false);
    }

    /**
     * Scope to get signatures by algorithm.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByAlgorithm($query, string $algorithm)
    {
        return $query->where('algorithm', $algorithm);
    }

    /**
     * Mark the signature as verified.
     */
    public function markAsVerified(bool $isValid): void
    {
        $this->update([
            'is_valid' => $isValid,
            'verified_at' => now(),
        ]);
    }

    /**
     * Check if the signature has been verified.
     */
    public function isVerified(): bool
    {
        return ! is_null($this->verified_at);
    }

    /**
     * Get the signature algorithm display name.
     */
    public function getAlgorithmDisplayNameAttribute(): string
    {
        return match ($this->algorithm) {
            'sha256' => 'SHA-256',
            'sha1' => 'SHA-1',
            'md5' => 'MD5',
            default => strtoupper($this->algorithm),
        };
    }
}
