<?php

declare(strict_types=1);

namespace App\Models\System;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property string $id
 * @property \Illuminate\Support\Carbon $metric_date
 * @property string $period_type
 * @property int $total_businesses
 * @property int $active_businesses
 * @property int $total_providers
 * @property int $active_providers
 * @property int $total_customers
 * @property int $active_customers
 * @property int $total_orders
 * @property int $completed_orders
 * @property numeric $total_gmv Gross Merchandise Value
 * @property numeric $total_commission_earned
 * @property numeric $total_payouts_made
 * @property numeric $platform_revenue
 * @property array<array-key, mixed>|null $top_cities Most active cities
 * @property array<array-key, mixed>|null $top_categories Most popular product categories
 * @property string $currency
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric whereActiveBusinesses($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric whereActiveCustomers($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric whereActiveProviders($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric whereCompletedOrders($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric whereMetricDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric wherePeriodType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric wherePlatformRevenue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric whereTopCategories($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric whereTopCities($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric whereTotalBusinesses($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric whereTotalCommissionEarned($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric whereTotalCustomers($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric whereTotalGmv($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric whereTotalOrders($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric whereTotalPayoutsMade($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric whereTotalProviders($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformMetric whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class PlatformMetric extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'metric_date',
        'period_type',
        'total_businesses',
        'active_businesses',
        'total_providers',
        'active_providers',
        'total_customers',
        'active_customers',
        'total_orders',
        'completed_orders',
        'total_gmv',
        'total_commission_earned',
        'total_payouts_made',
        'platform_revenue',
        'top_cities',
        'top_categories',
        'currency',
    ];

    protected $casts = [
        'metric_date' => 'date',
        'total_businesses' => 'integer',
        'active_businesses' => 'integer',
        'total_providers' => 'integer',
        'active_providers' => 'integer',
        'total_customers' => 'integer',
        'active_customers' => 'integer',
        'total_orders' => 'integer',
        'completed_orders' => 'integer',
        'total_gmv' => 'decimal:2',
        'total_commission_earned' => 'decimal:2',
        'total_payouts_made' => 'decimal:2',
        'platform_revenue' => 'decimal:2',
        'top_cities' => 'array',
        'top_categories' => 'array',
    ];
}
