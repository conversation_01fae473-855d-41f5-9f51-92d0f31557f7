<?php

namespace App\Models\System;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;

/**
 * @property string $id
 * @property string $name
 * @property string|null $description
 * @property string $frequency Cron expression or frequency description
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $last_run_at
 * @property \Illuminate\Support\Carbon|null $next_run_at
 * @property array<array-key, mixed>|null $configuration Task-specific configuration parameters
 * @property int $max_execution_time Maximum execution time in seconds
 * @property int $retry_attempts Number of retry attempts on failure
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTaskType active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTaskType due()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTaskType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTaskType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTaskType query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTaskType whereConfiguration($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTaskType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTaskType whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTaskType whereFrequency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTaskType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTaskType whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTaskType whereLastRunAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTaskType whereMaxExecutionTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTaskType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTaskType whereNextRunAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTaskType whereRetryAttempts($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTaskType whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class ScheduledTaskType extends Model
{
    use HasUuids;

    protected $fillable = [
        'name',
        'description',
        'frequency',
        'is_active',
        'last_run_at',
        'next_run_at',
        'configuration',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'last_run_at' => 'datetime',
        'next_run_at' => 'datetime',
        'configuration' => 'array',
    ];

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeDue($query)
    {
        return $query->where('next_run_at', '<=', now());
    }
}
