<?php

declare(strict_types=1);

namespace App\Models\System;

use App\Models\User\SupportTicket;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property string $id
 * @property string $ticket_id
 * @property string $sender_id
 * @property string $sender_type
 * @property string $message
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Model|\Eloquent $sender
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportMessage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportMessage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportMessage query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportMessage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportMessage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportMessage whereMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportMessage whereSenderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportMessage whereSenderType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportMessage whereTicketId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SupportMessage whereUpdatedAt($value)
 *
 * @property-read SupportTicket $ticket
 *
 * @mixin \Eloquent
 */
class SupportMessage extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'ticket_id',
        'sender_id',
        'sender_type',
        'message',
    ];

    /**
     * Get the support ticket this message belongs to.
     *
     * @return BelongsTo<SupportTicket,SupportMessage>
     */
    public function ticket(): BelongsTo
    {
        return $this->belongsTo(SupportTicket::class, 'ticket_id');
    }

    /**
     * Get the sender of the message (polymorphic).
     */
    public function sender(): MorphTo
    {
        return $this->morphTo(__FUNCTION__, 'sender_type', 'sender_id');
    }
}
