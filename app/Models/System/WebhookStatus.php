<?php

declare(strict_types=1);

namespace App\Models\System;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $name e.g., pending, success, failed, retrying
 * @property string|null $description
 * @property string|null $color Hex color code for UI display
 * @property bool $is_active
 * @property int $sort_order
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $color_class
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\System\WebhookLog> $webhookLogs
 * @property-read int|null $webhook_logs_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookStatus active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookStatus newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookStatus newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookStatus ordered()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookStatus query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookStatus whereColor($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookStatus whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookStatus whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookStatus whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookStatus whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookStatus whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookStatus whereSortOrder($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookStatus whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class WebhookStatus extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'description',
        'color',
        'is_active',
        'sort_order',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'sort_order' => 'integer',
    ];

    /**
     * Get the webhook logs with this status.
     *
     * @return HasMany<WebhookLog,WebhookStatus>
     */
    public function webhookLogs(): HasMany
    {
        return $this->hasMany(WebhookLog::class, 'status', 'name');
    }

    /**
     * Scope to get only active statuses.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to order by sort order.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order');
    }

    /**
     * Check if this is a success status.
     */
    public function isSuccess(): bool
    {
        return in_array(strtolower($this->name), ['success', 'completed', 'delivered']);
    }

    /**
     * Check if this is a failure status.
     */
    public function isFailure(): bool
    {
        return in_array(strtolower($this->name), ['failed', 'error', 'rejected']);
    }

    /**
     * Check if this is a pending status.
     */
    public function isPending(): bool
    {
        return in_array(strtolower($this->name), ['pending', 'processing', 'queued']);
    }

    /**
     * Get the status color class for UI.
     */
    public function getColorClassAttribute(): string
    {
        return match (strtolower($this->name)) {
            'success', 'completed', 'delivered' => 'text-green-600',
            'failed', 'error', 'rejected' => 'text-red-600',
            'pending', 'processing', 'queued' => 'text-yellow-600',
            default => 'text-gray-600',
        };
    }
}
