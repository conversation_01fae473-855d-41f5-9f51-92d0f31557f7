<?php

declare(strict_types=1);

namespace App\Models\System;

use App\Models\Business\Business;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string|null $api_key_id
 * @property string|null $user_id
 * @property string|null $business_id
 * @property string $endpoint e.g., /api/v1/orders
 * @property string $method e.g., GET, POST
 * @property int|null $status_code
 * @property string|null $ip_address
 * @property string|null $user_agent
 * @property array<array-key, mixed>|null $request_data Sanitized request payload
 * @property array<array-key, mixed>|null $response_data Sanitized response payload
 * @property int|null $duration_ms Request duration
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\System\ApiKey|null $apiKey
 * @property-read \App\Models\User\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog byStatusCode(int $statusCode)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog forBusiness(string $businessId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog forEndpoint(string $endpoint)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog forMethod(string $method)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog forUser(string $userId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog whereApiKeyId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog whereDurationMs($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog whereEndpoint($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog whereIpAddress($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog whereMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog whereRequestData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog whereResponseData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog whereStatusCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog whereUserAgent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ApiRequestLog withinDateRange(string $startDate, string $endDate)
 *
 * @property-read Business|null $business
 *
 * @mixin \Eloquent
 */
class ApiRequestLog extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'api_key_id',
        'user_id',
        'business_id',
        'endpoint',
        'method',
        'status_code',
        'ip_address',
        'user_agent',
        'request_data',
        'response_data',
        'duration_ms',
    ];

    protected $casts = [
        'request_data' => 'array',
        'response_data' => 'array',
        'status_code' => 'integer',
        'duration_ms' => 'integer',
    ];

    /**
     * Get the API key associated with this request log.
     *
     * @return BelongsTo<ApiKey,ApiRequestLog>
     */
    public function apiKey(): BelongsTo
    {
        return $this->belongsTo(ApiKey::class, 'api_key_id');
    }

    /**
     * Get the user associated with this request log.
     *
     * @return BelongsTo<User,ApiRequestLog>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'user_id');
    }

    /**
     * Get the business associated with this request log.
     *
     * @return BelongsTo<Business,ApiRequestLog>
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }

    /**
     * Scope to filter logs by endpoint.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForEndpoint($query, string $endpoint)
    {
        return $query->where('endpoint', $endpoint);
    }

    /**
     * Scope to filter logs by method.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForMethod($query, string $method)
    {
        return $query->where('method', strtoupper($method));
    }

    /**
     * Scope to filter logs by status code.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByStatusCode($query, int $statusCode)
    {
        return $query->where('status_code', $statusCode);
    }

    /**
     * Scope to filter logs by user.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForUser($query, string $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter logs by business.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForBusiness($query, string $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    /**
     * Scope to filter logs within a date range.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithinDateRange($query, string $startDate, string $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Get a summary of the API request log.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'endpoint' => $this->endpoint,
            'method' => $this->method,
            'status_code' => $this->status_code,
            'user_id' => $this->user_id,
            'business_id' => $this->business_id,
            'duration_ms' => $this->duration_ms,
            'created_at' => $this->created_at,
        ];
    }
}
