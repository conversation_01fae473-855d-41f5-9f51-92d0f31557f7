<?php

namespace App\Models\System;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string|null $tenant_id
 * @property string $task_name Name of the scheduled task
 * @property string $task_type Type of task (e.g., report, sync, cleanup)
 * @property string $status
 * @property \Illuminate\Support\Carbon $started_at
 * @property \Illuminate\Support\Carbon|null $completed_at
 * @property int|null $duration_seconds
 * @property array<array-key, mixed>|null $task_data Input parameters for the task
 * @property array<array-key, mixed>|null $result_data Task output or error details
 * @property string|null $error_message
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string|null $duration_text
 * @property-read \App\Models\System\Tenant|null $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTasksLog byStatus(string $status)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTasksLog forTask(string $taskName)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTasksLog forTenant(string $tenantId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTasksLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTasksLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTasksLog query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTasksLog whereCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTasksLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTasksLog whereDurationSeconds($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTasksLog whereErrorMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTasksLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTasksLog whereResultData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTasksLog whereStartedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTasksLog whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTasksLog whereTaskData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTasksLog whereTaskName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTasksLog whereTaskType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTasksLog whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTasksLog whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class ScheduledTasksLog extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'scheduled_tasks_log';

    protected $fillable = [
        'tenant_id',
        'task_name',
        'task_type',
        'status',
        'started_at',
        'completed_at',
        'duration_seconds',
        'task_data',
        'result_data',
        'error_message',
    ];

    protected $casts = [
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'duration_seconds' => 'integer',
        'task_data' => 'array',
        'result_data' => 'array',
    ];

    /**
     * Get the tenant that owns the scheduled task log.
     *
     * @return BelongsTo<Tenant,ScheduledTasksLog>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    /**
     * Scope to get logs by status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get logs for a specific task name.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForTask($query, string $taskName)
    {
        return $query->where('task_name', $taskName);
    }

    /**
     * Scope to get logs for a specific tenant.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForTenant($query, string $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }

    /**
     * Get the duration as a human-readable string.
     */
    public function getDurationTextAttribute(): ?string
    {
        if (is_null($this->duration_seconds)) {
            return null;
        }
        $minutes = intdiv($this->duration_seconds, 60);
        $seconds = $this->duration_seconds % 60;

        return ($minutes > 0 ? "{$minutes}m " : '')."{$seconds}s";
    }

    /**
     * Get a summary of the scheduled task log.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'tenant_id' => $this->tenant_id,
            'task_name' => $this->task_name,
            'task_type' => $this->task_type,
            'status' => $this->status,
            'started_at' => $this->started_at,
            'completed_at' => $this->completed_at,
            'duration_seconds' => $this->duration_seconds,
            'duration_text' => $this->duration_text,
            'error_message' => $this->error_message,
        ];
    }
}
