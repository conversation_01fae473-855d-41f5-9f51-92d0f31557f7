<?php

declare(strict_types=1);

namespace App\Models\System;

use App\Enums\System\DomainVerificationStatus;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Stancl\Tenancy\Database\Models\Domain as BaseDomain;

/**
 * @property string $id
 * @property string $domain The custom domain or subdomain
 * @property string $tenant_id FK to tenants.id
 * @property bool $is_primary
 * @property bool $ssl_enabled
 * @property DomainVerificationStatus $verification_status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\System\Tenant $tenant
 * @property-read \App\Models\System\TenantWhiteLabelSetting|null $whiteLabel
 *
 * @method static \Database\Factories\DomainFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Domain newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Domain newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Domain query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Domain whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Domain whereDomain($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Domain whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Domain whereIsPrimary($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Domain whereSslEnabled($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Domain whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Domain whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Domain whereVerificationStatus($value)
 *
 * @mixin \Eloquent
 */
class Domain extends BaseDomain
{
    use HasFactory, HasUuids;

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): \Database\Factories\DomainFactory
    {
        return \Database\Factories\DomainFactory::new();
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'domain',
        'tenant_id',
        'is_primary',
        'ssl_enabled',
        'verification_status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'is_primary' => 'boolean',
        'ssl_enabled' => 'boolean',
        'verification_status' => DomainVerificationStatus::class,
    ];

    /**
     * Get the custom columns that should be included in domain data.
     * This makes Stancl aware of our custom fields.
     *
     * @return array<string>
     */
    public static function getCustomColumns(): array
    {
        return [
            'is_primary',
            'ssl_enabled',
            'verification_status',
        ];
    }

    /**
     * Get the tenant that owns the domain.
     *
     * @return BelongsTo<Tenant,Domain>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class);
    }

    /**
     * Get the white label settings associated with this domain.
     *
     * @return HasOne<TenantWhiteLabelSetting,Domain>
     */
    public function whiteLabel(): HasOne
    {
        return $this->hasOne(TenantWhiteLabelSetting::class, 'custom_domain_id');
    }
}
