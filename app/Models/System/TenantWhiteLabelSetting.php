<?php

declare(strict_types=1);

namespace App\Models\System;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string|null $subdomain If different from the main tenant subdomain
 * @property string|null $custom_domain_id
 * @property string|null $logo_url
 * @property array<array-key, mixed>|null $color_palette
 * @property string|null $contact_email
 * @property string|null $contact_phone
 * @property string|null $css_overrides Store custom CSS
 * @property array<array-key, mixed>|null $settings Flexible settings
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\System\Domain|null $customDomain
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhiteLabelSetting newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhiteLabelSetting newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhiteLabelSetting query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhiteLabelSetting whereColorPalette($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhiteLabelSetting whereContactEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhiteLabelSetting whereContactPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhiteLabelSetting whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhiteLabelSetting whereCssOverrides($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhiteLabelSetting whereCustomDomainId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhiteLabelSetting whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhiteLabelSetting whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhiteLabelSetting whereLogoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhiteLabelSetting whereSettings($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhiteLabelSetting whereSubdomain($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhiteLabelSetting whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhiteLabelSetting whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class TenantWhiteLabelSetting extends Model
{
    use BelongsToTenant, HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'subdomain',
        'custom_domain_id',
        'logo_url',
        'color_palette',
        'contact_email',
        'contact_phone',
        'css_overrides',
        'settings',
        'is_active',
    ];

    protected $casts = [
        'color_palette' => 'json',
        'settings' => 'json',
        'is_active' => 'boolean',
    ];

    /**
     * Get the tenant that owns these white label settings.
     *
     * @return BelongsTo<Tenant,TenantWhiteLabelSetting>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    /**
     * Get the custom domain for these white label settings.
     *
     * @return BelongsTo<Domain,TenantWhiteLabelSetting>
     */
    public function customDomain(): BelongsTo
    {
        return $this->belongsTo(Domain::class, 'custom_domain_id');
    }

    /**
     * Check if the settings have a custom domain.
     */
    public function hasCustomDomain(): bool
    {
        return ! is_null($this->custom_domain_id);
    }

    /**
     * Get a specific setting value from the settings JSON.
     */
    public function getSetting(string $key, mixed $default = null): mixed
    {
        return $this->settings[$key] ?? $default;
    }

    /**
     * Set a specific setting value in the settings JSON.
     */
    public function setSetting(string $key, mixed $value): bool
    {
        $settings = $this->settings ?? [];
        $settings[$key] = $value;
        $this->settings = $settings;

        return $this->save();
    }

    /**
     * Get a specific color from the color palette.
     */
    public function getColor(string $key, ?string $default = null): ?string
    {
        return $this->color_palette[$key] ?? $default;
    }
}
