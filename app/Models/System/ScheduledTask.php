<?php

declare(strict_types=1);

namespace App\Models\System;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string|null $tenant_id FK to tenants.id, nullable for platform-wide tasks
 * @property string $task_name e.g., daily_report_generation, inventory_sync
 * @property string $task_type e.g., report, sync, cleanup
 * @property string $schedule_cron Cron expression for scheduling
 * @property \Illuminate\Support\Carbon|null $last_run_at
 * @property \Illuminate\Support\Carbon|null $next_run_at
 * @property bool $is_active
 * @property array<array-key, mixed>|null $task_data Any parameters for the task (e.g., report recipients, sync source)
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\System\Tenant|null $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTask newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTask newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTask query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTask whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTask whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTask whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTask whereLastRunAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTask whereNextRunAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTask whereScheduleCron($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTask whereTaskData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTask whereTaskName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTask whereTaskType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTask whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ScheduledTask whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class ScheduledTask extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'task_name',
        'task_type',
        'schedule_cron',
        'last_run_at',
        'next_run_at',
        'is_active',
        'task_data',
    ];

    protected $casts = [
        'last_run_at' => 'datetime',
        'next_run_at' => 'datetime',
        'is_active' => 'boolean',
        'task_data' => 'array',
    ];

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }
}
