<?php

declare(strict_types=1);

namespace App\Models\System;

use App\Models\Core\WhatsAppConversation;
use App\Models\Core\WhatsAppTemplate;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $phone_number
 * @property string|null $display_phone_number
 * @property string $business_account_id
 * @property string|null $access_token
 * @property string $webhook_verify_token
 * @property bool $is_verified
 * @property string $verification_status
 * @property string $api_version
 * @property array<array-key, mixed>|null $business_profile
 * @property array<array-key, mixed>|null $settings
 * @property \Illuminate\Support\Carbon|null $verified_at
 * @property \Illuminate\Support\Carbon|null $last_webhook_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhatsAppConfig newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhatsAppConfig newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhatsAppConfig query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhatsAppConfig whereAccessToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhatsAppConfig whereApiVersion($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhatsAppConfig whereBusinessAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhatsAppConfig whereBusinessProfile($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhatsAppConfig whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhatsAppConfig whereDisplayPhoneNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhatsAppConfig whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhatsAppConfig whereIsVerified($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhatsAppConfig whereLastWebhookAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhatsAppConfig wherePhoneNumber($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhatsAppConfig whereSettings($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhatsAppConfig whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhatsAppConfig whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhatsAppConfig whereVerificationStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhatsAppConfig whereVerifiedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|TenantWhatsAppConfig whereWebhookVerifyToken($value)
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, WhatsAppConversation> $conversations
 * @property-read int|null $conversations_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, WhatsAppTemplate> $templates
 * @property-read int|null $templates_count
 *
 * @mixin \Eloquent
 */
class TenantWhatsAppConfig extends Model
{
    use BelongsToTenant, HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'phone_number',
        'display_phone_number',
        'business_account_id',
        'access_token',
        'webhook_verify_token',
        'is_verified',
        'verification_status',
        'api_version',
        'business_profile',
        'settings',
        'verified_at',
        'last_webhook_at',
    ];

    protected $casts = [
        'business_profile' => 'array',
        'settings' => 'array',
        'is_verified' => 'boolean',
        'verified_at' => 'datetime',
        'last_webhook_at' => 'datetime',
    ];

    protected $hidden = [
        'access_token',
        'webhook_verify_token',
    ];

    /**
     * Get the tenant that owns this WhatsApp configuration.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    /**
     * Get the WhatsApp conversations for this configuration.
     */
    public function conversations(): HasMany
    {
        return $this->hasMany(WhatsAppConversation::class, 'tenant_id', 'tenant_id');
    }

    /**
     * Get the WhatsApp templates for this configuration.
     */
    public function templates(): HasMany
    {
        return $this->hasMany(WhatsAppTemplate::class, 'tenant_id', 'tenant_id');
    }

    /**
     * Get the default settings for WhatsApp configuration.
     */
    public static function getDefaultSettings(): array
    {
        return [
            'auto_reply' => [
                'enabled' => true,
                'message' => 'Thank you for contacting us! We will respond shortly.',
                'business_hours_only' => true,
            ],
            'order_notifications' => [
                'enabled' => true,
                'statuses' => ['confirmed', 'preparing', 'ready', 'out_for_delivery', 'delivered'],
            ],
            'catalog_sharing' => [
                'enabled' => true,
                'include_prices' => true,
                'include_images' => true,
            ],
            'customer_support' => [
                'enabled' => true,
                'escalation_keywords' => ['urgent', 'complaint', 'refund'],
                'business_hours' => [
                    'monday' => ['09:00', '18:00'],
                    'tuesday' => ['09:00', '18:00'],
                    'wednesday' => ['09:00', '18:00'],
                    'thursday' => ['09:00', '18:00'],
                    'friday' => ['09:00', '18:00'],
                    'saturday' => ['10:00', '16:00'],
                    'sunday' => null, // Closed
                ],
            ],
            'message_templates' => [
                'welcome' => 'Welcome to {business_name}! How can we help you today?',
                'order_confirmed' => 'Your order #{order_number} has been confirmed and is being prepared.',
                'order_ready' => 'Your order #{order_number} is ready for pickup/delivery!',
                'order_delivered' => 'Your order #{order_number} has been delivered. Thank you for choosing us!',
            ],
        ];
    }

    /**
     * Check if WhatsApp is properly configured and verified.
     */
    public function isConfigured(): bool
    {
        return $this->is_verified &&
               ! empty($this->access_token) &&
               ! empty($this->business_account_id) &&
               $this->verification_status === 'verified';
    }

    /**
     * Get the encrypted access token.
     */
    public function getAccessTokenAttribute($value): ?string
    {
        return $value ? decrypt($value) : null;
    }

    /**
     * Set the access token (encrypted).
     */
    public function setAccessTokenAttribute($value): void
    {
        $this->attributes['access_token'] = $value ? encrypt($value) : null;
    }

    /**
     * Check if auto-reply is enabled and within business hours.
     */
    public function shouldAutoReply(): bool
    {
        $settings = $this->settings ?? [];

        if (! ($settings['auto_reply']['enabled'] ?? false)) {
            return false;
        }

        if (! ($settings['auto_reply']['business_hours_only'] ?? false)) {
            return true;
        }

        return $this->isWithinBusinessHours();
    }

    /**
     * Check if current time is within business hours.
     */
    public function isWithinBusinessHours(): bool
    {
        $settings = $this->settings ?? [];
        $businessHours = $settings['customer_support']['business_hours'] ?? [];

        $currentDay = strtolower(now()->format('l'));
        $currentTime = now()->format('H:i');

        $todayHours = $businessHours[$currentDay] ?? null;

        if (! $todayHours) {
            return false; // Closed today
        }

        [$openTime, $closeTime] = $todayHours;

        return $currentTime >= $openTime && $currentTime <= $closeTime;
    }
}
