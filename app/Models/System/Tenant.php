<?php

declare(strict_types=1);

namespace App\Models\System;

use App\Enums\Financial\SubscriptionTargetType;
use App\Enums\System\TenantStatus;
use App\Models\Business\Business;
use App\Models\Delivery\DeliveryProvider;
use App\Models\Financial\SubscriptionPlan;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Stancl\Tenancy\Database\Concerns\HasDomains;
use Stancl\Tenancy\Database\Models\Tenant as BaseTenant;

/**
 * @property string $id UUID for tenant ID
 * @property string $name Display name for the tenant (Business name or Delivery Provider name)
 * @property SubscriptionTargetType $tenant_type Type of tenant: business or provider
 * @property array<array-key, mixed>|null $data Additional tenant configuration data expected by Stancl Tenancy. White-labeling settings are in tenant_white_label_settings.
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property TenantStatus $status Tenant status for lifecycle management
 * @property string|null $subscription_plan_id Reference to subscription plan
 * @property array<array-key, mixed>|null $settings Tenant-specific settings and configuration
 * @property string|null $created_by User who created this tenant
 * @property \Illuminate\Support\Carbon|null $archived_at When tenant was archived (soft delete)
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Business> $businesses
 * @property-read int|null $businesses_count
 * @property-read User|null $creator
 * @property-read \Illuminate\Database\Eloquent\Collection<int, DeliveryProvider> $deliveryProviders
 * @property-read int|null $delivery_providers_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\System\Domain> $domains
 * @property-read int|null $domains_count
 * @property-read SubscriptionPlan|null $subscriptionPlan
 * @property-read \App\Models\System\TenantWhiteLabelSetting|null $whiteLabelSettings
 *
 * @method static \Stancl\Tenancy\Database\TenantCollection<int, static> all($columns = ['*'])
 * @method static \Database\Factories\TenantFactory factory($count = null, $state = [])
 * @method static \Stancl\Tenancy\Database\TenantCollection<int, static> get($columns = ['*'])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tenant newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tenant newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tenant query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tenant whereArchivedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tenant whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tenant whereCreatedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tenant whereData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tenant whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tenant whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tenant whereSettings($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tenant whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tenant whereSubscriptionPlanId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tenant whereTenantType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Tenant whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class Tenant extends BaseTenant
{
    use HasDomains, HasFactory, HasUuids;

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Database\Factories\TenantFactory::new();
    }

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'id',
        'name',
        'tenant_type',
        'status',
        'subscription_plan_id',
        'settings',
        'created_by',
        'archived_at',
        'data', // Keep for Stancl compatibility
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'tenant_type' => SubscriptionTargetType::class,
        'status' => TenantStatus::class,
        'settings' => 'array',
        'archived_at' => 'datetime',
        // Let Stancl handle 'data' cast
    ];

    /**
     * Get the custom columns that should be included in tenant data.
     * This makes Stancl aware of our custom fields.
     *
     * @return array<string>
     */
    public static function getCustomColumns(): array
    {
        return [
            'id',
            'name',
            'tenant_type',
            'status',
            'subscription_plan_id',
            'settings',
            'created_by',
            'archived_at',
        ];
    }

    /**
     * Get the subscription plan for the tenant.
     */
    public function subscriptionPlan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class, 'subscription_plan_id');
    }

    /**
     * Get the user who created the tenant.
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the domains for the tenant.
     * Note: This relationship is provided by HasDomains trait
     */
    public function domains(): HasMany
    {
        return $this->hasMany(Domain::class);
    }

    /**
     * Get the white label settings for the tenant.
     */
    public function whiteLabelSettings(): HasOne
    {
        return $this->hasOne(TenantWhiteLabelSetting::class, 'tenant_id');
    }

    /**
     * Get the businesses for this tenant (if tenant_type is business).
     */
    public function businesses(): HasMany
    {
        return $this->hasMany(Business::class, 'tenant_id');
    }

    /**
     * Get the delivery providers for this tenant (if tenant_type is provider).
     */
    public function deliveryProviders(): HasMany
    {
        return $this->hasMany(DeliveryProvider::class, 'tenant_id');
    }

    /**
     * Check if this tenant is a business tenant.
     */
    public function isBusiness(): bool
    {
        return $this->tenant_type === SubscriptionTargetType::BUSINESS;
    }

    /**
     * Check if this tenant is a delivery provider tenant.
     */
    public function isProvider(): bool
    {
        return $this->tenant_type === SubscriptionTargetType::PROVIDER;
    }

    /**
     * Check if this tenant is active.
     */
    public function isActive(): bool
    {
        return $this->status === TenantStatus::ACTIVE || $this->status === TenantStatus::TRIAL;
    }

    /**
     * Check if this tenant is suspended.
     */
    public function isSuspended(): bool
    {
        return $this->status === TenantStatus::SUSPENDED;
    }

    /**
     * Check if this tenant is archived.
     */
    public function isArchived(): bool
    {
        return ! is_null($this->archived_at);
    }

    /**
     * Archive this tenant.
     */
    public function archive(): bool
    {
        return $this->update([
            'status' => TenantStatus::INACTIVE,
            'archived_at' => now(),
        ]);
    }

    /**
     * Restore this tenant from archive.
     */
    public function restore(): bool
    {
        return $this->update([
            'status' => TenantStatus::ACTIVE,
            'archived_at' => null,
        ]);
    }

    /**
     * Suspend this tenant.
     */
    public function suspend(): bool
    {
        return $this->update(['status' => TenantStatus::SUSPENDED]);
    }

    /**
     * Activate this tenant.
     */
    public function activate(): bool
    {
        return $this->update(['status' => TenantStatus::ACTIVE]);
    }

    /**
     * Get a specific setting value with optional default.
     */
    public function getSetting(string $key, mixed $default = null): mixed
    {
        return data_get($this->settings, $key, $default);
    }

    /**
     * Set a specific setting value.
     */
    public function setSetting(string $key, mixed $value): bool
    {
        $settings = $this->settings ?? [];
        data_set($settings, $key, $value);

        return $this->update(['settings' => $settings]);
    }

    /**
     * Get default tenant settings structure for infrastructure/platform configuration.
     */
    public static function getDefaultSettings(): array
    {
        return [
            'platform' => [
                'timezone' => 'Africa/Lagos',
                'currency' => 'NGN',
                'language' => 'en',
                'date_format' => 'Y-m-d',
                'time_format' => 'H:i',
            ],
            'branding' => [
                'primary_color' => '#007bff',
                'secondary_color' => '#6c757d',
                'logo_url' => null,
                'favicon_url' => null,
                'custom_domain' => null,
                'subdomain_prefix' => null,
            ],
            'integrations' => [
                'payment_gateway' => 'paystack',
                'sms_provider' => 'twilio',
                'email_provider' => 'sendgrid',
                'maps_provider' => 'google',
                'analytics_provider' => null,
            ],
            'features' => [
                'multi_business_enabled' => false,
                'white_label_enabled' => false,
                'api_access_enabled' => true,
                'webhook_enabled' => true,
                'real_time_tracking' => true,
            ],
            'limits' => [
                'max_businesses' => 1,
                'max_providers' => 1,
                'max_orders_per_month' => 1000,
                'max_api_calls_per_day' => 10000,
                'max_storage_mb' => 1024,
            ],
            'security' => [
                'two_factor_required' => false,
                'session_timeout_minutes' => 120,
                'password_expiry_days' => 90,
                'ip_whitelist' => [],
                'allowed_domains' => [],
            ],
        ];
    }

    /**
     * Get the primary domain for this tenant.
     */
    public function primaryDomain(): ?Domain
    {
        return $this->domains()->where('is_primary', true)->first();
    }

    /**
     * Get a specific data attribute from the JSON data column.
     * Stancl automatically extracts JSON data as individual model attributes.
     */
    public function getData(string $key, mixed $default = null): mixed
    {
        return $this->getAttribute($key) ?? $default;
    }

    /**
     * Set a specific data attribute in the JSON data column.
     * Stancl automatically stores individual attributes in the JSON data column.
     */
    public function setData(string $key, mixed $value): void
    {
        $this->setAttribute($key, $value);
    }
}
