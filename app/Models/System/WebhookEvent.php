<?php

declare(strict_types=1);

namespace App\Models\System;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property string $id
 * @property string|null $tenant_id FK to tenants.id, nullable for platform-wide events
 * @property string $event_type e.g., order.created, payment.completed, delivery.started
 * @property string $event_name Human-readable event name
 * @property string $eventable_id Polymorphic ID of the entity that triggered the event
 * @property string $eventable_type Polymorphic type (order, payment, delivery, etc.)
 * @property array<array-key, mixed> $payload Event data to be sent via webhook
 * @property \Illuminate\Support\Carbon $triggered_at When the event was triggered
 * @property \Illuminate\Support\Carbon|null $processed_at When the event was processed
 * @property string $status
 * @property int $retry_count
 * @property int $max_retries
 * @property \Illuminate\Support\Carbon|null $next_retry_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Model|\Eloquent $eventable
 * @property-read \App\Models\System\Tenant|null $tenant
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\System\WebhookLog> $webhookLogs
 * @property-read int|null $webhook_logs_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent byEventType(string $eventType)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent byStatus(string $status)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent needsRetry()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent whereEventName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent whereEventType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent whereEventableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent whereEventableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent whereMaxRetries($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent whereNextRetryAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent wherePayload($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent whereProcessedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent whereRetryCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent whereTriggeredAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookEvent whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class WebhookEvent extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'event_type',
        'event_name',
        'eventable_id',
        'eventable_type',
        'payload',
        'triggered_at',
        'processed_at',
        'status',
        'retry_count',
        'max_retries',
        'next_retry_at',
    ];

    protected $casts = [
        'payload' => 'array',
        'triggered_at' => 'datetime',
        'processed_at' => 'datetime',
        'next_retry_at' => 'datetime',
        'retry_count' => 'integer',
        'max_retries' => 'integer',
    ];

    /**
     * Get the tenant that owns the webhook event.
     *
     * @return BelongsTo<Tenant,WebhookEvent>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    /**
     * Get the eventable model (polymorphic relationship).
     */
    public function eventable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the webhook logs for this event.
     *
     * @return HasMany<WebhookLog,WebhookEvent>
     */
    public function webhookLogs(): HasMany
    {
        return $this->hasMany(WebhookLog::class, 'event_id');
    }

    /**
     * Scope to get events by status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get events by type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByEventType($query, string $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    /**
     * Scope to get events that need retry.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNeedsRetry($query)
    {
        return $query->where('status', 'failed')
            ->where('retry_count', '<', 'max_retries')
            ->whereNotNull('next_retry_at')
            ->where('next_retry_at', '<=', now());
    }

    /**
     * Check if the event is pending.
     */
    public function isPending(): bool
    {
        return $this->status === 'pending';
    }

    /**
     * Check if the event is processed.
     */
    public function isProcessed(): bool
    {
        return $this->status === 'processed';
    }

    /**
     * Check if the event failed.
     */
    public function isFailed(): bool
    {
        return $this->status === 'failed';
    }

    /**
     * Check if the event can be retried.
     */
    public function canRetry(): bool
    {
        return $this->isFailed() && $this->retry_count < $this->max_retries;
    }

    /**
     * Increment retry count and set next retry time.
     */
    public function incrementRetry(int $delayMinutes = 5): void
    {
        $this->increment('retry_count');
        $this->next_retry_at = now()->addMinutes($delayMinutes * $this->retry_count);
        $this->save();
    }
}
