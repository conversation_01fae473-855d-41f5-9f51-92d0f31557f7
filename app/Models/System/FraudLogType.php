<?php

declare(strict_types=1);

namespace App\Models\System;

use App\Enums\System\SeverityLevel;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $name e.g., suspicious_order, failed_login, risk_prediction
 * @property string|null $description
 * @property mixed $severity_level
 * @property bool $is_active
 * @property int|null $auto_flag_threshold Number of occurrences before auto-flagging
 * @property bool $requires_manual_review
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\System\FraudLog> $fraudLogs
 * @property-read int|null $fraud_logs_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLogType active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLogType bySeverity(string $severity)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLogType newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLogType newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLogType query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLogType requiresManualReview()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLogType whereAutoFlagThreshold($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLogType whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLogType whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLogType whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLogType whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLogType whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLogType whereRequiresManualReview($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLogType whereSeverityLevel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLogType whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class FraudLogType extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'description',
        'severity_level',
        'is_active',
        'auto_flag_threshold',
        'requires_manual_review',
    ];

    protected $casts = [
        'severity_level' => SeverityLevel::class,
        'is_active' => 'boolean',
        'auto_flag_threshold' => 'integer',
        'requires_manual_review' => 'boolean',
    ];

    /**
     * Get the fraud logs for this type.
     *
     * @return HasMany<FraudLog,FraudLogType>
     */
    public function fraudLogs(): HasMany
    {
        return $this->hasMany(FraudLog::class, 'type', 'name');
    }

    /**
     * Scope to get only active fraud log types.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get fraud log types by severity level.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeBySeverity($query, string $severity)
    {
        return $query->where('severity_level', $severity);
    }

    /**
     * Scope to get fraud log types that require manual review.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRequiresManualReview($query)
    {
        return $query->where('requires_manual_review', true);
    }

    /**
     * Check if this fraud type is high severity.
     */
    public function isHighSeverity(): bool
    {
        return $this->severity_level === SeverityLevel::HIGH;
    }

    /**
     * Check if this fraud type should auto-flag.
     */
    public function shouldAutoFlag(int $count): bool
    {
        return $this->auto_flag_threshold && $count >= $this->auto_flag_threshold;
    }
}
