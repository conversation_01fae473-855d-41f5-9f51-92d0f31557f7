<?php

declare(strict_types=1);

namespace App\Models\System;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Configuration History Model
 *
 * Tracks changes to configuration settings for audit purposes.
 *
 * @property string $id
 * @property string $setting_id
 * @property mixed $previous_value
 * @property mixed $new_value
 * @property string|null $change_reason
 * @property string $changed_by
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read ConfigurationSetting $setting
 * @property-read User $changedBy
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationHistory whereChangeReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationHistory whereChangedBy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationHistory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationHistory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationHistory whereNewValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationHistory wherePreviousValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationHistory whereSettingId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ConfigurationHistory whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class ConfigurationHistory extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'setting_id',
        'previous_value',
        'new_value',
        'change_reason',
        'changed_by',
    ];

    protected $casts = [
        'previous_value' => 'json',
        'new_value' => 'json',
    ];

    /**
     * Get the setting this history belongs to.
     */
    public function setting(): BelongsTo
    {
        return $this->belongsTo(ConfigurationSetting::class, 'setting_id');
    }

    /**
     * Get the user who made this change.
     */
    public function changedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'changed_by');
    }

    /**
     * Get a summary of the change.
     */
    public function getChangeSummary(): string
    {
        $setting = $this->setting;

        if (! $setting) {
            return 'Setting updated';
        }

        $previousDisplay = $this->getDisplayValue($this->previous_value, $setting);
        $newDisplay = $this->getDisplayValue($this->new_value, $setting);

        return "Changed '{$setting->name}' from '{$previousDisplay}' to '{$newDisplay}'";
    }

    /**
     * Get display value for history.
     */
    private function getDisplayValue($value, ConfigurationSetting $setting): string
    {
        if ($setting->is_sensitive && $value !== null) {
            return '••••••••';
        }

        if ($value === null) {
            return 'null';
        }

        return match ($setting->type) {
            \App\Enums\System\ConfigurationType::BOOLEAN => $value ? 'Yes' : 'No',
            \App\Enums\System\ConfigurationType::ARRAY, \App\Enums\System\ConfigurationType::JSON => json_encode($value),
            \App\Enums\System\ConfigurationType::PASSWORD => '••••••••',
            default => (string) $value,
        };
    }
}
