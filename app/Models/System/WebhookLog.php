<?php

declare(strict_types=1);

namespace App\Models\System;

use App\Enums\System\WebhookStatus;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string|null $tenant_id
 * @property string $event_type Type of webhook event
 * @property string $endpoint_url Target webhook URL
 * @property array<array-key, mixed> $payload Webhook payload sent
 * @property mixed $status
 * @property int|null $http_status_code
 * @property string|null $response_body
 * @property int $attempts
 * @property \Illuminate\Support\Carbon|null $next_retry_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\System\Tenant|null $tenant
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\System\WebhookSignature> $webhookSignatures
 * @property-read int|null $webhook_signatures_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookLog byStatus(\App\Enums\WebhookStatus $status)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookLog forEventType(string $eventType)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookLog forTenant(string $tenantId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookLog needsRetry()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookLog query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookLog whereAttempts($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookLog whereEndpointUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookLog whereEventType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookLog whereHttpStatusCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookLog whereNextRetryAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookLog wherePayload($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookLog whereResponseBody($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookLog whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookLog whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WebhookLog whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class WebhookLog extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'event_type',
        'endpoint_url',
        'payload',
        'status',
        'http_status_code',
        'response_body',
        'attempts',
        'next_retry_at',
    ];

    protected $casts = [
        'payload' => 'array',
        'status' => WebhookStatus::class,
        'attempts' => 'integer',
        'next_retry_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the webhook log.
     *
     * @return BelongsTo<Tenant,WebhookLog>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    /**
     * Get the webhook signatures for this log.
     *
     * @return HasMany<WebhookSignature,WebhookLog>
     */
    public function webhookSignatures(): HasMany
    {
        return $this->hasMany(WebhookSignature::class, 'webhook_log_id');
    }

    /**
     * Scope to get logs by status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByStatus($query, WebhookStatus $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get logs for a specific event type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForEventType($query, string $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    /**
     * Scope to get logs for a specific tenant.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForTenant($query, string $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }

    /**
     * Scope to get logs that need retry.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNeedsRetry($query)
    {
        return $query->where('status', WebhookStatus::FAILED)
            ->whereNotNull('next_retry_at')
            ->where('next_retry_at', '<=', now());
    }

    /**
     * Check if the webhook log is successful.
     */
    public function isSuccessful(): bool
    {
        return $this->status === WebhookStatus::COMPLETED;
    }

    /**
     * Check if the webhook log is failed.
     */
    public function isFailed(): bool
    {
        return $this->status === WebhookStatus::FAILED;
    }

    /**
     * Check if the webhook log is pending.
     */
    public function isPending(): bool
    {
        return $this->status === WebhookStatus::PENDING;
    }

    /**
     * Get a summary for API responses.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'event_type' => $this->event_type,
            'endpoint_url' => $this->endpoint_url,
            'status' => $this->status->value,
            'http_status_code' => $this->http_status_code,
            'attempts' => $this->attempts,
            'next_retry_at' => $this->next_retry_at,
            'created_at' => $this->created_at,
        ];
    }
}
