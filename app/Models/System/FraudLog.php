<?php

declare(strict_types=1);

namespace App\Models\System;

use App\Enums\System\FraudLogType;
use App\Enums\System\SeverityLevel;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property string $id
 * @property mixed $type e.g., suspicious_order, login_attempt_failed, payout_risk
 * @property mixed|null $severity
 * @property string|null $loggable_id Polymorphic link to entity/event
 * @property string|null $loggable_type
 * @property array<array-key, mixed>|null $details Details about the fraud event
 * @property string|null $user_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $severity_color_class
 * @property-read Model|\Eloquent|null $loggable
 * @property-read \App\Models\User\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLog bySeverity(string $severity)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLog byType(string $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLog highSeverity()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLog newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLog newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLog query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLog whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLog whereDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLog whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLog whereLoggableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLog whereLoggableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLog whereSeverity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLog whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLog whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|FraudLog whereUserId($value)
 *
 * @mixin \Eloquent
 */
class FraudLog extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'type',
        'severity',
        'loggable_id',
        'loggable_type',
        'details',
        'user_id',
    ];

    protected $casts = [
        'type' => FraudLogType::class,
        'severity' => SeverityLevel::class,
        'details' => 'array',
    ];

    /**
     * Get the user associated with this fraud log.
     *
     * @return BelongsTo<User,FraudLog>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'user_id');
    }

    /**
     * Get the loggable model (polymorphic relationship).
     */
    public function loggable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope to get fraud logs by type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get fraud logs by severity.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeBySeverity($query, string $severity)
    {
        return $query->where('severity', $severity);
    }

    /**
     * Scope to get high severity fraud logs.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeHighSeverity($query)
    {
        return $query->where('severity', SeverityLevel::HIGH);
    }

    /**
     * Check if this is a high severity fraud log.
     */
    public function isHighSeverity(): bool
    {
        return $this->severity === SeverityLevel::HIGH;
    }

    /**
     * Check if this is a medium severity fraud log.
     */
    public function isMediumSeverity(): bool
    {
        return $this->severity === SeverityLevel::MEDIUM;
    }

    /**
     * Check if this is a low severity fraud log.
     */
    public function isLowSeverity(): bool
    {
        return $this->severity === SeverityLevel::LOW;
    }

    /**
     * Get the severity color class for UI.
     */
    public function getSeverityColorClassAttribute(): string
    {
        return match ($this->severity) {
            SeverityLevel::HIGH => 'text-red-600 bg-red-100',
            SeverityLevel::MEDIUM => 'text-yellow-600 bg-yellow-100',
            SeverityLevel::LOW => 'text-green-600 bg-green-100',
            default => 'text-gray-600 bg-gray-100',
        };
    }

    /**
     * Get a summary for API responses.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'severity' => $this->severity,
            'user_id' => $this->user_id,
            'user_name' => $this->user?->name,
            'loggable_type' => $this->loggable_type,
            'loggable_id' => $this->loggable_id,
            'created_at' => $this->created_at,
            'severity_color' => $this->severity_color_class,
        ];
    }
}
