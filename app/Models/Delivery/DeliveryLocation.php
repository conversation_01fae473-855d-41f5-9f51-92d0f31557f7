<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $tenant_id FK to tenants.id (matches delivery's tenant_id)
 * @property string $delivery_id
 * @property numeric $latitude
 * @property numeric $longitude
 * @property numeric|null $accuracy in meters
 * @property numeric|null $speed in m/s or km/h
 * @property numeric|null $heading in degrees
 * @property \Illuminate\Support\Carbon $recorded_at Time device recorded location
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Delivery\Delivery $delivery
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryLocation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryLocation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryLocation orderedByTime()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryLocation query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryLocation recent(int $minutes = 30)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryLocation whereAccuracy($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryLocation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryLocation whereDeliveryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryLocation whereHeading($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryLocation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryLocation whereLatitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryLocation whereLongitude($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryLocation whereRecordedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryLocation whereSpeed($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryLocation whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryLocation whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class DeliveryLocation extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'delivery_id',
        'latitude',
        'longitude',
        'accuracy',
        'speed',
        'heading',
        'recorded_at',
    ];

    protected $casts = [
        'latitude' => 'decimal:8',
        'longitude' => 'decimal:8',
        'accuracy' => 'decimal:2',
        'speed' => 'decimal:2',
        'heading' => 'decimal:2',
        'recorded_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the delivery location.
     *
     * @return BelongsTo<Tenant,DeliveryLocation>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the delivery that owns the location.
     *
     * @return BelongsTo<Delivery,DeliveryLocation>
     */
    public function delivery(): BelongsTo
    {
        return $this->belongsTo(Delivery::class, 'delivery_id');
    }

    /**
     * Scope to get locations ordered by recorded time.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOrderedByTime($query)
    {
        return $query->orderBy('recorded_at', 'asc');
    }

    /**
     * Scope to get recent locations.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecent($query, int $minutes = 30)
    {
        return $query->where('recorded_at', '>=', now()->subMinutes($minutes));
    }
}
