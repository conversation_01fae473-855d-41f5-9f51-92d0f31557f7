<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use App\Models\Core\Capability;
use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $provider_id
 * @property string $capability_id
 * @property array<array-key, mixed>|null $details Specific details about this provider's capability (e.g., temperature range)
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Delivery\DeliveryProvider $provider
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderCapability newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderCapability newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderCapability query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderCapability whereCapabilityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderCapability whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderCapability whereDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderCapability whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderCapability whereProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderCapability whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderCapability whereUpdatedAt($value)
 *
 * @property-read Capability $capability
 *
 * @mixin \Eloquent
 */
class ProviderCapability extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'provider_id',
        'capability_id',
        'details',
    ];

    protected $casts = [
        'details' => 'array',
    ];

    /**
     * Get the tenant that owns the provider capability.
     *
     * @return BelongsTo<Tenant,ProviderCapability>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the delivery provider that owns the capability.
     *
     * @return BelongsTo<DeliveryProvider,ProviderCapability>
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(DeliveryProvider::class, 'provider_id');
    }

    /**
     * Get the capability that this provider capability references.
     *
     * @return BelongsTo<Capability,ProviderCapability>
     */
    public function capability(): BelongsTo
    {
        return $this->belongsTo(Capability::class, 'capability_id');
    }
}
