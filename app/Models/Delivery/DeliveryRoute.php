<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $tenant_id FK to tenants.id
 * @property string $delivery_id
 * @property string|null $driver_id
 * @property array<array-key, mixed> $planned_route Planned route coordinates and waypoints
 * @property array<array-key, mixed>|null $actual_route Actual route taken
 * @property numeric|null $planned_distance_km
 * @property numeric|null $actual_distance_km
 * @property int|null $planned_duration_minutes
 * @property int|null $actual_duration_minutes
 * @property array<array-key, mixed>|null $traffic_conditions Traffic data at time of delivery
 * @property array<array-key, mixed>|null $route_deviations Deviations from planned route
 * @property \Illuminate\Support\Carbon|null $route_started_at
 * @property \Illuminate\Support\Carbon|null $route_completed_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Delivery\Delivery $delivery
 * @property-read \App\Models\User\User|null $driver
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRoute newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRoute newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRoute query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRoute whereActualDistanceKm($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRoute whereActualDurationMinutes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRoute whereActualRoute($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRoute whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRoute whereDeliveryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRoute whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRoute whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRoute wherePlannedDistanceKm($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRoute wherePlannedDurationMinutes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRoute wherePlannedRoute($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRoute whereRouteCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRoute whereRouteDeviations($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRoute whereRouteStartedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRoute whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRoute whereTrafficConditions($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRoute whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class DeliveryRoute extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'delivery_id',
        'driver_id',
        'planned_route',
        'actual_route',
        'planned_distance_km',
        'actual_distance_km',
        'planned_duration_minutes',
        'actual_duration_minutes',
        'traffic_conditions',
        'route_deviations',
        'route_started_at',
        'route_completed_at',
    ];

    protected $casts = [
        'planned_route' => 'array',
        'actual_route' => 'array',
        'planned_distance_km' => 'decimal:2',
        'actual_distance_km' => 'decimal:2',
        'planned_duration_minutes' => 'integer',
        'actual_duration_minutes' => 'integer',
        'traffic_conditions' => 'array',
        'route_deviations' => 'array',
        'route_started_at' => 'datetime',
        'route_completed_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the delivery route.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the delivery associated with the route.
     */
    public function delivery(): BelongsTo
    {
        return $this->belongsTo(Delivery::class);
    }

    /**
     * Get the driver associated with the route.
     */
    public function driver(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'driver_id');
    }
}
