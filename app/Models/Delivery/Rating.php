<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use App\Enums\Delivery\RatingRateableType;
use App\Models\System\Tenant;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property string $id
 * @property string $tenant_id FK to tenants.id (matches rateable_id's tenant_id if business/provider)
 * @property string|null $order_id
 * @property string|null $user_delivery_id
 * @property string $rater_id
 * @property string $rateable_id Polymorphic (business_id, delivery_provider_id, or user_id for driver)
 * @property mixed $rateable_type e.g., business, delivery_provider, user
 * @property int $rating 1-5
 * @property string|null $comment
 * @property string|null $response Business/provider response to rating
 * @property \Illuminate\Support\Carbon|null $response_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Delivery\Order|null $order
 * @property-read Model|\Eloquent $rateable
 * @property-read \App\Models\User\User $rater
 * @property-read \App\Models\System\Tenant $tenant
 * @property-read \App\Models\Delivery\UserDelivery|null $userDelivery
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating aboveRating(int $rating)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating belowRating(int $rating)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating byRateableType(\App\Enums\RatingRateableType $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating byRating(int $rating)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating forOrders()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating forUserDeliveries()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating recent(int $days = 30)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating whereComment($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating whereRateableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating whereRateableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating whereRaterId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating whereRating($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating whereResponse($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating whereResponseAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating whereUserDeliveryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating withComments()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating withResponses()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Rating withoutResponses()
 *
 * @mixin \Eloquent
 */
class Rating extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'order_id',
        'user_delivery_id',
        'rater_id',
        'rateable_id',
        'rateable_type',
        'rating',
        'comment',
        'response',
        'response_at',
    ];

    protected $casts = [
        'rateable_type' => RatingRateableType::class,
        'rating' => 'integer',
        'response_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the rating.
     *
     * @return BelongsTo<Tenant,Rating>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the order associated with the rating.
     *
     * @return BelongsTo<Order,Rating>
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    /**
     * Get the user delivery associated with the rating.
     *
     * @return BelongsTo<UserDelivery,Rating>
     */
    public function userDelivery(): BelongsTo
    {
        return $this->belongsTo(UserDelivery::class, 'user_delivery_id');
    }

    /**
     * Get the user who gave the rating.
     *
     * @return BelongsTo<User,Rating>
     */
    public function rater(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'rater_id');
    }

    /**
     * Get the polymorphic rateable entity (Business, DeliveryProvider, or User).
     */
    public function rateable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope to get ratings by rating value.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByRating($query, int $rating)
    {
        return $query->where('rating', $rating);
    }

    /**
     * Scope to get ratings above a certain value.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAboveRating($query, int $rating)
    {
        return $query->where('rating', '>=', $rating);
    }

    /**
     * Scope to get ratings below a certain value.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeBelowRating($query, int $rating)
    {
        return $query->where('rating', '<=', $rating);
    }

    /**
     * Scope to get ratings with comments.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithComments($query)
    {
        return $query->whereNotNull('comment');
    }

    /**
     * Scope to get ratings with responses.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithResponses($query)
    {
        return $query->whereNotNull('response');
    }

    /**
     * Scope to get ratings without responses.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithoutResponses($query)
    {
        return $query->whereNull('response');
    }

    /**
     * Scope to get ratings for orders.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForOrders($query)
    {
        return $query->whereNotNull('order_id');
    }

    /**
     * Scope to get ratings for user deliveries.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForUserDeliveries($query)
    {
        return $query->whereNotNull('user_delivery_id');
    }

    /**
     * Scope to get recent ratings.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Scope to get ratings by rateable type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByRateableType($query, RatingRateableType $type)
    {
        return $query->where('rateable_type', $type);
    }

    /**
     * Check if the rating has a comment.
     */
    public function hasComment(): bool
    {
        return ! empty($this->comment);
    }

    /**
     * Check if the rating has a response.
     */
    public function hasResponse(): bool
    {
        return ! empty($this->response);
    }

    /**
     * Check if the rating is positive (4-5 stars).
     */
    public function isPositive(): bool
    {
        return $this->rating >= 4;
    }

    /**
     * Check if the rating is negative (1-2 stars).
     */
    public function isNegative(): bool
    {
        return $this->rating <= 2;
    }

    /**
     * Check if the rating is neutral (3 stars).
     */
    public function isNeutral(): bool
    {
        return $this->rating === 3;
    }

    /**
     * Get the star rating as an array for display.
     */
    public function getStarsArray(): array
    {
        $stars = [];
        for ($i = 1; $i <= 5; $i++) {
            $stars[] = [
                'filled' => $i <= $this->rating,
                'value' => $i,
            ];
        }

        return $stars;
    }

    /**
     * Get the rating category.
     */
    public function getCategory(): string
    {
        return match ($this->rating) {
            5 => 'Excellent',
            4 => 'Good',
            3 => 'Average',
            2 => 'Poor',
            1 => 'Terrible',
            default => 'Unknown',
        };
    }

    /**
     * Get the rating color class for UI.
     */
    public function getColorClass(): string
    {
        return match ($this->rating) {
            5 => 'text-green-600',
            4 => 'text-green-500',
            3 => 'text-yellow-500',
            2 => 'text-orange-500',
            1 => 'text-red-500',
            default => 'text-gray-500',
        };
    }

    /**
     * Add a response to the rating.
     */
    public function addResponse(string $response): bool
    {
        return $this->update([
            'response' => $response,
            'response_at' => now(),
        ]);
    }

    /**
     * Remove the response from the rating.
     */
    public function removeResponse(): bool
    {
        return $this->update([
            'response' => null,
            'response_at' => null,
        ]);
    }

    /**
     * Get the time since the rating was created.
     */
    public function getTimeAgo(): string
    {
        return $this->created_at->diffForHumans();
    }

    /**
     * Get the time since the response was added.
     */
    public function getResponseTimeAgo(): ?string
    {
        return $this->response_at?->diffForHumans();
    }

    /**
     * Get the transaction type (order or delivery).
     */
    public function getTransactionType(): string
    {
        if ($this->order_id) {
            return 'order';
        }

        if ($this->user_delivery_id) {
            return 'delivery';
        }

        return 'unknown';
    }

    /**
     * Get the related transaction (order or user delivery).
     *
     * @return Order|UserDelivery|null
     */
    public function getTransaction()
    {
        if ($this->order_id) {
            return $this->order;
        }

        if ($this->user_delivery_id) {
            return $this->userDelivery;
        }

        return null;
    }

    /**
     * Check if the rating can be edited by the rater.
     */
    public function canBeEdited(int $maxHours = 24): bool
    {
        return $this->created_at->addHours($maxHours)->isFuture();
    }

    /**
     * Check if a response can be added.
     */
    public function canAddResponse(): bool
    {
        return ! $this->hasResponse();
    }

    /**
     * Get rating statistics for a rateable entity.
     */
    public static function getStatistics(string $rateableId, string $rateableType): array
    {
        $ratings = static::where('rateable_id', $rateableId)
            ->where('rateable_type', $rateableType);

        $total = $ratings->count();
        $average = $ratings->avg('rating') ?? 0;

        $distribution = [];
        for ($i = 1; $i <= 5; $i++) {
            $count = $ratings->clone()->where('rating', $i)->count();
            $distribution[$i] = [
                'count' => $count,
                'percentage' => $total > 0 ? round(($count / $total) * 100, 1) : 0,
            ];
        }

        return [
            'total' => $total,
            'average' => round($average, 2),
            'distribution' => $distribution,
            'positive_percentage' => $total > 0 ? round((array_sum([
                $distribution[4]['count'],
                $distribution[5]['count'],
            ]) / $total) * 100, 1) : 0,
        ];
    }

    /**
     * Get formatted rating summary.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'rating' => $this->rating,
            'category' => $this->getCategory(),
            'comment' => $this->comment,
            'has_response' => $this->hasResponse(),
            'response' => $this->response,
            'rater_name' => $this->rater->name,
            'time_ago' => $this->getTimeAgo(),
            'transaction_type' => $this->getTransactionType(),
            'color_class' => $this->getColorClass(),
            'stars' => $this->getStarsArray(),
        ];
    }
}
