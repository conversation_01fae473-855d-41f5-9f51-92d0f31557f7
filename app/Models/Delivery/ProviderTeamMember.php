<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use App\Enums\Business\TeamMemberRelationshipType;
use App\Enums\Delivery\ProviderAvailabilityStatus;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $provider_id
 * @property string $user_id
 * @property string|null $primary_branch_id
 * @property TeamMemberRelationshipType $role Defines the type of relationship to the provider (e.g. admin, driver, support)
 * @property ProviderAvailabilityStatus $availability_status Real-time status for driver/staff
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Delivery\ProviderBranch|null $primaryBranch
 * @property-read \App\Models\Delivery\DeliveryProvider $provider
 * @property-read \App\Models\System\Tenant $tenant
 * @property-read User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderTeamMember newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderTeamMember newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderTeamMember query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderTeamMember whereAvailabilityStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderTeamMember whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderTeamMember whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderTeamMember wherePrimaryBranchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderTeamMember whereProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderTeamMember whereRole($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderTeamMember whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderTeamMember whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderTeamMember whereUserId($value)
 *
 * @mixin \Eloquent
 */
class ProviderTeamMember extends Model
{
    use BelongsToTenant, HasFactory, HasUuids;

    protected $fillable = [
        'id',
        'tenant_id',
        'provider_id',
        'user_id',
        'primary_branch_id',
        'role',
        'availability_status',
    ];

    protected $casts = [
        'role' => TeamMemberRelationshipType::class,
        'availability_status' => ProviderAvailabilityStatus::class,
    ];

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    public function provider(): BelongsTo
    {
        return $this->belongsTo(DeliveryProvider::class, 'provider_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    public function primaryBranch(): BelongsTo
    {
        return $this->belongsTo(ProviderBranch::class, 'primary_branch_id');
    }
}
