<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string|null $order_id
 * @property string|null $user_delivery_id
 * @property string $customer_id
 * @property string $tracking_token Secure token for tracking access
 * @property \Illuminate\Support\Carbon $session_started_at
 * @property \Illuminate\Support\Carbon $last_accessed_at
 * @property \Illuminate\Support\Carbon $session_expires_at
 * @property int $access_count
 * @property array<array-key, mixed>|null $access_log Log of tracking page accesses
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User\User $customer
 * @property-read \App\Models\Delivery\Order|null $order
 * @property-read \App\Models\Delivery\UserDelivery|null $userDelivery
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerTrackingSession newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerTrackingSession newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerTrackingSession query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerTrackingSession whereAccessCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerTrackingSession whereAccessLog($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerTrackingSession whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerTrackingSession whereCustomerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerTrackingSession whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerTrackingSession whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerTrackingSession whereLastAccessedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerTrackingSession whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerTrackingSession whereSessionExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerTrackingSession whereSessionStartedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerTrackingSession whereTrackingToken($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerTrackingSession whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CustomerTrackingSession whereUserDeliveryId($value)
 *
 * @mixin \Eloquent
 */
class CustomerTrackingSession extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'order_id',
        'user_delivery_id',
        'customer_id',
        'tracking_token',
        'session_started_at',
        'last_accessed_at',
        'session_expires_at',
        'access_count',
        'access_log',
        'is_active',
    ];

    protected $casts = [
        'session_started_at' => 'datetime',
        'last_accessed_at' => 'datetime',
        'session_expires_at' => 'datetime',
        'access_count' => 'integer',
        'access_log' => 'array',
        'is_active' => 'boolean',
    ];

    /**
     * Get the order associated with the tracking session.
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the user delivery associated with the tracking session.
     */
    public function userDelivery(): BelongsTo
    {
        return $this->belongsTo(UserDelivery::class);
    }

    /**
     * Get the customer associated with the tracking session.
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'customer_id');
    }
}
