<?php

namespace App\Models\Delivery;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $pickup_slot_id
 * @property string $order_batch_id
 * @property string $status
 * @property int $priority 1=low, 2=medium, 3=high
 * @property string|null $assigned_at
 * @property string|null $started_at
 * @property string|null $completed_at
 * @property string|null $notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\Order> $orders
 * @property-read int|null $orders_count
 * @property-read \App\Models\Delivery\PickupSlot $pickupSlot
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlotOrderBatch available()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlotOrderBatch newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlotOrderBatch newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlotOrderBatch query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlotOrderBatch whereAssignedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlotOrderBatch whereCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlotOrderBatch whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlotOrderBatch whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlotOrderBatch whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlotOrderBatch whereOrderBatchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlotOrderBatch wherePickupSlotId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlotOrderBatch wherePriority($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlotOrderBatch whereStartedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlotOrderBatch whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlotOrderBatch whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class PickupSlotOrderBatch extends Model
{
    use HasUuids;

    protected $fillable = [
        'pickup_slot_id',
        'batch_number',
        'status',
        'capacity',
        'start_time',
        'end_time',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
        'capacity' => 'integer',
    ];

    public function pickupSlot(): BelongsTo
    {
        return $this->belongsTo(PickupSlot::class);
    }

    public function orders(): HasMany
    {
        return $this->hasMany(Order::class);
    }

    public function scopeAvailable($query)
    {
        return $query->where('status', 'available')
            ->where('start_time', '>', now());
    }
}
