<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use App\Models\Core\City;
use App\Models\Core\Country;
use App\Models\Core\State;
use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $provider_id
 * @property string|null $country_id
 * @property string|null $state_id
 * @property string|null $city_id
 * @property numeric $base_fee Base fee for any delivery
 * @property numeric $fee_per_km Price per kilometer
 * @property numeric $fee_per_minute Price per minute (for traffic/wait time)
 * @property numeric $min_fee Minimum fee for a delivery
 * @property numeric|null $max_fee Maximum fee for a delivery
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Delivery\DeliveryProvider $provider
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderDeliveryPricing active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderDeliveryPricing newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderDeliveryPricing newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderDeliveryPricing query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderDeliveryPricing whereBaseFee($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderDeliveryPricing whereCityId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderDeliveryPricing whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderDeliveryPricing whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderDeliveryPricing whereFeePerKm($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderDeliveryPricing whereFeePerMinute($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderDeliveryPricing whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderDeliveryPricing whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderDeliveryPricing whereMaxFee($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderDeliveryPricing whereMinFee($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderDeliveryPricing whereProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderDeliveryPricing whereStateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderDeliveryPricing whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderDeliveryPricing whereUpdatedAt($value)
 *
 * @property-read City|null $city
 * @property-read Country|null $country
 * @property-read State|null $state
 *
 * @mixin \Eloquent
 */
class ProviderDeliveryPricing extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'provider_id',
        'country_id',
        'state_id',
        'city_id',
        'base_fee',
        'fee_per_km',
        'fee_per_minute',
        'min_fee',
        'max_fee',
        'is_active',
    ];

    protected $casts = [
        'base_fee' => 'decimal:2',
        'fee_per_km' => 'decimal:2',
        'fee_per_minute' => 'decimal:2',
        'min_fee' => 'decimal:2',
        'max_fee' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the tenant that owns the provider delivery pricing.
     *
     * @return BelongsTo<Tenant,ProviderDeliveryPricing>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the delivery provider that owns the pricing.
     *
     * @return BelongsTo<DeliveryProvider,ProviderDeliveryPricing>
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(DeliveryProvider::class, 'provider_id');
    }

    /**
     * Get the country for this pricing.
     *
     * @return BelongsTo<Country,ProviderDeliveryPricing>
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class, 'country_id');
    }

    /**
     * Get the state for this pricing.
     *
     * @return BelongsTo<State,ProviderDeliveryPricing>
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class, 'state_id');
    }

    /**
     * Get the city for this pricing.
     *
     * @return BelongsTo<City,ProviderDeliveryPricing>
     */
    public function city(): BelongsTo
    {
        return $this->belongsTo(City::class, 'city_id');
    }

    /**
     * Scope to get only active pricing.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }
}
