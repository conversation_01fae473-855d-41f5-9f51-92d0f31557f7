<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $provider_id
 * @property string $setting_key
 * @property array<array-key, mixed> $setting_value
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Delivery\DeliveryProvider $provider
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderSetting newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderSetting newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderSetting query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderSetting whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderSetting whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderSetting whereProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderSetting whereSettingKey($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderSetting whereSettingValue($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderSetting whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderSetting whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class ProviderSetting extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'provider_id',
        'setting_key',
        'setting_value',
    ];

    protected $casts = [
        'setting_value' => 'array',
    ];

    /**
     * Get the tenant that owns the provider setting.
     *
     * @return BelongsTo<Tenant,ProviderSetting>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the delivery provider that owns the setting.
     *
     * @return BelongsTo<DeliveryProvider,ProviderSetting>
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(DeliveryProvider::class, 'provider_id');
    }
}
