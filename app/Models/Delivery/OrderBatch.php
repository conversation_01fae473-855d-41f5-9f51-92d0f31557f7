<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use App\Enums\Delivery\BatchStatus;
use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $business_id
 * @property string|null $business_branch_id
 * @property string $name
 * @property string|null $description
 * @property mixed $status
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\Order> $orders
 * @property-read int|null $orders_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\PickupSlotOrderBatch> $pickupSlotBatches
 * @property-read int|null $pickup_slot_batches_count
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBatch byStatus(\App\Enums\BatchStatus $status)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBatch newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBatch newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBatch overdue()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBatch pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBatch pickedUp()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBatch query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBatch ready()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBatch scheduledToday()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBatch whereBusinessBranchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBatch whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBatch whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBatch whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBatch whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBatch whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBatch whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBatch whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderBatch whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class OrderBatch extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'name',
        'status',
        'scheduled_pickup_time',
        'actual_pickup_time',
        'notes',
    ];

    protected $casts = [
        'status' => BatchStatus::class,
        'scheduled_pickup_time' => 'datetime',
        'actual_pickup_time' => 'datetime',
    ];

    /**
     * Get the tenant that owns the order batch.
     *
     * @return BelongsTo<Tenant,OrderBatch>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the orders in this batch.
     *
     * @return HasMany<Order,OrderBatch>
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'batch_id');
    }

    /**
     * Get the pickup slot order batches for this batch.
     *
     * @return HasMany<PickupSlotOrderBatch,OrderBatch>
     */
    public function pickupSlotBatches(): HasMany
    {
        return $this->hasMany(PickupSlotOrderBatch::class, 'order_batch_id');
    }

    /**
     * Scope to get batches by status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByStatus($query, BatchStatus $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending batches.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', BatchStatus::PENDING);
    }

    /**
     * Scope to get ready batches.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeReady($query)
    {
        return $query->where('status', BatchStatus::READY);
    }

    /**
     * Scope to get picked up batches.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePickedUp($query)
    {
        return $query->where('status', BatchStatus::PICKED_UP);
    }

    /**
     * Scope to get batches scheduled for today.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeScheduledToday($query)
    {
        return $query->whereDate('scheduled_pickup_time', today());
    }

    /**
     * Scope to get overdue batches.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOverdue($query)
    {
        return $query->where('scheduled_pickup_time', '<', now())
            ->whereIn('status', [BatchStatus::PENDING, BatchStatus::READY]);
    }

    /**
     * Check if the batch is pending.
     */
    public function isPending(): bool
    {
        return $this->status === BatchStatus::PENDING;
    }

    /**
     * Check if the batch is ready for pickup.
     */
    public function isReady(): bool
    {
        return $this->status === BatchStatus::READY;
    }

    /**
     * Check if the batch has been picked up.
     */
    public function isPickedUp(): bool
    {
        return $this->status === BatchStatus::PICKED_UP;
    }

    /**
     * Check if the batch is overdue.
     */
    public function isOverdue(): bool
    {
        // TODO: fix
        return $this->scheduled_pickup_time < now() &&
               in_array($this->status, [BatchStatus::PENDING, BatchStatus::READY]);
    }

    /**
     * Get the total number of orders in this batch.
     */
    public function getOrdersCount(): int
    {
        return $this->orders()->count();
    }

    /**
     * Get the total value of all orders in this batch.
     */
    public function getTotalValue(): float
    {
        return $this->orders()->sum('total_amount');
    }

    /**
     * Get the time until scheduled pickup.
     */
    public function getTimeUntilPickup(): string
    {
        // TODO:: investigate
        if ($this->isOverdue()) {
            return 'Overdue by '.$this->scheduled_pickup_time->diffForHumans(null, true);
        }

        return $this->scheduled_pickup_time->diffForHumans();
    }

    /**
     * Mark the batch as ready for pickup.
     */
    public function markAsReady(): bool
    {
        return $this->update(['status' => BatchStatus::READY]);
    }

    /**
     * Mark the batch as picked up.
     */
    public function markAsPickedUp(): bool
    {
        return $this->update([
            'status' => BatchStatus::PICKED_UP,
            'actual_pickup_time' => now(),
        ]);
    }

    /**
     * Add an order to this batch.
     */
    public function addOrder(string $orderId): bool
    {
        return (bool) Order::where('id', $orderId)->update(['batch_id' => $this->id]);
    }

    /**
     * Remove an order from this batch.
     */
    public function removeOrder(string $orderId): bool
    {
        return (bool) Order::where('id', $orderId)->update(['batch_id' => null]);
    }

    /**
     * Get batch summary for API responses.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'status' => $this->status->value,
            'orders_count' => $this->getOrdersCount(),
            'total_value' => $this->getTotalValue(),
            'scheduled_pickup_time' => $this->scheduled_pickup_time,
            'actual_pickup_time' => $this->actual_pickup_time,
            'time_until_pickup' => $this->getTimeUntilPickup(),
            'is_overdue' => $this->isOverdue(),
            'notes' => $this->notes,
        ];
    }

    /**
     * Get orders summary for this batch.
     */
    public function getOrdersSummary(): array
    {
        return $this->orders->map(function ($order) {
            return [
                'id' => $order->id,
                'order_reference' => $order->order_reference,
                'customer_name' => $order->customer->name,
                'total_amount' => $order->total_amount,
                'status' => $order->status->value,
            ];
        })->toArray();
    }

    /**
     * Generate a unique batch name.
     */
    public static function generateUniqueName(string $prefix = 'BATCH'): string
    {
        $date = now()->format('Ymd');
        $counter = 1;

        do {
            $name = "{$prefix}-{$date}-".str_pad((string) $counter, 3, '0', STR_PAD_LEFT);
            $exists = static::where('name', $name)->exists();
            $counter++;
        } while ($exists);

        return $name;
    }
}
