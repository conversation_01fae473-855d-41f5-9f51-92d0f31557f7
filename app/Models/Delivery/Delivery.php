<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use App\Enums\Delivery\DeliveryStatus;
use App\Models\Business\BusinessBranch;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Stancl\Tenancy\Database\Models\Tenant;

/**
 * @property string $id
 * @property string $tenant_id FK to tenants.id (matches delivery_provider_tenant_id if assigned, or business_tenant_id if self-delivered)
 * @property string $deliverable_id Polymorphic FK to orders.id or user_deliveries.id
 * @property string $deliverable_type order or user_delivery
 * @property string|null $delivery_provider_tenant_id FK to tenants.id (the delivery provider tenant, nullable)
 * @property string|null $delivery_provider_id
 * @property string|null $assigned_driver_id
 * @property string|null $vehicle_id
 * @property string|null $pickup_branch_id
 * @property DeliveryStatus $status
 * @property string $tracking_id
 * @property \Illuminate\Support\Carbon|null $estimated_pickup_time
 * @property \Illuminate\Support\Carbon|null $estimated_delivery_time
 * @property \Illuminate\Support\Carbon|null $actual_pickup_time
 * @property \Illuminate\Support\Carbon|null $actual_delivery_time
 * @property numeric|null $distance Distance in km
 * @property int|null $duration_minutes Actual delivery duration in minutes
 * @property string|null $route_polyline Encoded polyline for route
 * @property string|null $proof_of_delivery_url
 * @property string|null $recipient_name
 * @property string|null $recipient_signature_url
 * @property string|null $delivery_notes Driver/provider notes on delivery
 * @property string|null $failure_reason
 * @property numeric $platform_commission_amount Commission charged to provider
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read User|null $assignedDriver
 * @property-read Model|\Eloquent $deliverable
 * @property-read \App\Models\Delivery\DeliveryProvider|null $deliveryProvider
 * @property-read Tenant|null $deliveryProviderTenant
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\DeliveryLocation> $locations
 * @property-read int|null $locations_count
 * @property-read BusinessBranch|null $pickupBranch
 * @property-read \App\Models\Delivery\Vehicle|null $vehicle
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery assignedToDriver(string $driverId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery byStatus(\App\Enums\Delivery\DeliveryStatus $status)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery forProvider(string $providerId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereActualDeliveryTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereActualPickupTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereAssignedDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereDeliverableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereDeliverableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereDeliveryNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereDeliveryProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereDeliveryProviderTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereDistance($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereDurationMinutes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereEstimatedDeliveryTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereEstimatedPickupTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereFailureReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery wherePickupBranchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery wherePlatformCommissionAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereProofOfDeliveryUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereRecipientName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereRecipientSignatureUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereRoutePolyline($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereTrackingId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Delivery whereVehicleId($value)
 *
 * @mixin \Eloquent
 */
class Delivery extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'deliverable_id',
        'deliverable_type',
        'delivery_provider_tenant_id',
        'delivery_provider_id',
        'assigned_driver_id',
        'vehicle_id',
        'pickup_branch_id',
        'status',
        'tracking_id',
        'estimated_pickup_time',
        'estimated_delivery_time',
        'actual_pickup_time',
        'actual_delivery_time',
        'distance',
        'duration_minutes',
        'route_polyline',
        'proof_of_delivery_url',
        'recipient_name',
        'recipient_signature_url',
        'delivery_notes',
        'failure_reason',
        'platform_commission_amount',
    ];

    protected $casts = [
        'status' => DeliveryStatus::class,
        'estimated_pickup_time' => 'datetime',
        'estimated_delivery_time' => 'datetime',
        'actual_pickup_time' => 'datetime',
        'actual_delivery_time' => 'datetime',
        'distance' => 'decimal:2',
        'duration_minutes' => 'integer',
        'platform_commission_amount' => 'decimal:2',
    ];

    /**
     * Get the polymorphic deliverable (Order or UserDelivery).
     */
    public function deliverable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the delivery provider tenant for this delivery.
     *
     * @return BelongsTo<Tenant,Delivery>
     */
    public function deliveryProviderTenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'delivery_provider_tenant_id');
    }

    /**
     * Get the delivery provider for this delivery.
     *
     * @return BelongsTo<DeliveryProvider,Delivery>
     */
    public function deliveryProvider(): BelongsTo
    {
        return $this->belongsTo(DeliveryProvider::class, 'delivery_provider_id');
    }

    /**
     * Get the assigned driver for this delivery.
     *
     * @return BelongsTo<User,Delivery>
     */
    public function assignedDriver(): BelongsTo
    {
        return $this->belongsTo(User::class, 'assigned_driver_id');
    }

    /**
     * Get the vehicle used for this delivery.
     *
     * @return BelongsTo<Vehicle,Delivery>
     */
    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class, 'vehicle_id');
    }

    /**
     * Get the pickup branch for this delivery.
     *
     * @return BelongsTo<BusinessBranch,Delivery>
     */
    public function pickupBranch(): BelongsTo
    {
        return $this->belongsTo(BusinessBranch::class, 'pickup_branch_id');
    }

    /**
     * Get the delivery locations for this delivery.
     *
     * @return HasMany<DeliveryLocation,Delivery>
     */
    public function locations(): HasMany
    {
        return $this->hasMany(DeliveryLocation::class);
    }

    /**
     * Scope to get deliveries by status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByStatus($query, DeliveryStatus $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get deliveries assigned to a specific driver.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAssignedToDriver($query, string $driverId)
    {
        return $query->where('assigned_driver_id', $driverId);
    }

    /**
     * Scope to get deliveries for a specific provider.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForProvider($query, string $providerId)
    {
        return $query->where('delivery_provider_id', $providerId);
    }
}
