<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $provider_id
 * @property string $zone_id
 * @property float $pricing_multiplier
 * @property bool $is_active
 * @property array<array-key, mixed>|null $service_config
 * @property int $delivery_count
 * @property float|null $average_rating
 * @property \Illuminate\Support\Carbon|null $last_delivery_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Delivery\DeliveryProvider $provider
 * @property-read \App\Models\Delivery\DeliveryZone $zone
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderServiceArea active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderServiceArea newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderServiceArea newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderServiceArea query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderServiceArea whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderServiceArea whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderServiceArea whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderServiceArea whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderServiceArea wherePolygonCoordinates($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderServiceArea wherePriceMultiplier($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderServiceArea whereProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderServiceArea whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderServiceArea whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderServiceArea whereAverageRating($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderServiceArea whereDeliveryCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderServiceArea whereLastDeliveryAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderServiceArea wherePricingMultiplier($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderServiceArea whereServiceConfig($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderServiceArea whereZoneId($value)
 *
 * @mixin \Eloquent
 */
class ProviderServiceArea extends Model
{
    use BelongsToTenant, HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'provider_id',
        'zone_id',
        'pricing_multiplier',
        'is_active',
        'service_config',
        'delivery_count',
        'average_rating',
        'last_delivery_at',
    ];

    protected $casts = [
        'pricing_multiplier' => 'decimal:2',
        'is_active' => 'boolean',
        'service_config' => 'array',
        'delivery_count' => 'integer',
        'average_rating' => 'decimal:2',
        'last_delivery_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the service area.
     *
     * @return BelongsTo<Tenant,ProviderServiceArea>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the delivery provider that owns the service area.
     *
     * @return BelongsTo<DeliveryProvider,ProviderServiceArea>
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(DeliveryProvider::class, 'provider_id');
    }

    /**
     * Get the delivery zone for this service area.
     *
     * @return BelongsTo<DeliveryZone,ProviderServiceArea>
     */
    public function zone(): BelongsTo
    {
        return $this->belongsTo(DeliveryZone::class, 'zone_id');
    }

    /**
     * Scope to get only active service areas.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Check if the service area is active.
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Update delivery statistics.
     */
    public function updateDeliveryStats(float $rating): void
    {
        $this->increment('delivery_count');

        // Calculate new average rating
        if ($this->average_rating === null) {
            $this->average_rating = $rating;
        } else {
            $this->average_rating = (($this->average_rating * ($this->delivery_count - 1)) + $rating) / $this->delivery_count;
        }

        $this->last_delivery_at = now();
        $this->save();
    }

    /**
     * Get the effective pricing multiplier (zone base * provider multiplier).
     */
    public function getEffectivePricingMultiplier(): float
    {
        return $this->zone->base_multiplier * $this->pricing_multiplier;
    }

    /**
     * Check if provider can serve a specific route through this zone.
     */
    public function canServeRoute(string $fromStateId, string $toStateId): bool
    {
        if (! $this->is_active || ! $this->zone->is_active) {
            return false;
        }

        return $this->zone->canServeRoute($fromStateId, $toStateId);
    }

    /**
     * Get service configuration value.
     */
    public function getServiceConfig(string $key, mixed $default = null): mixed
    {
        return data_get($this->service_config, $key, $default);
    }

    /**
     * Set service configuration value.
     */
    public function setServiceConfig(string $key, mixed $value): void
    {
        $config = $this->service_config ?? [];
        data_set($config, $key, $value);
        $this->service_config = $config;
        $this->save();
    }

    /**
     * Get performance summary.
     */
    public function getPerformanceSummary(): array
    {
        return [
            'delivery_count' => $this->delivery_count,
            'average_rating' => $this->average_rating,
            'last_delivery_at' => $this->last_delivery_at,
            'effective_multiplier' => $this->getEffectivePricingMultiplier(),
            'zone_name' => $this->zone->name,
            'zone_type' => $this->zone->zone_type->getLabel(),
        ];
    }
}
