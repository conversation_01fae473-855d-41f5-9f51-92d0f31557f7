<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $tenant_id FK to tenants.id
 * @property string $delivery_id
 * @property \Illuminate\Support\Carbon $predicted_at When this ETA was calculated
 * @property \Illuminate\Support\Carbon|null $estimated_pickup_time
 * @property \Illuminate\Support\Carbon|null $estimated_delivery_time
 * @property int $confidence_percentage Confidence in prediction (0-100)
 * @property array<array-key, mixed>|null $prediction_factors Factors used in ETA calculation
 * @property string|null $prediction_model AI model used for prediction
 * @property numeric|null $accuracy_score Accuracy when compared to actual time
 * @property bool $is_active Whether this is the current active prediction
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Delivery\Delivery $delivery
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryEtaPrediction newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryEtaPrediction newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryEtaPrediction query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryEtaPrediction whereAccuracyScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryEtaPrediction whereConfidencePercentage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryEtaPrediction whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryEtaPrediction whereDeliveryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryEtaPrediction whereEstimatedDeliveryTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryEtaPrediction whereEstimatedPickupTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryEtaPrediction whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryEtaPrediction whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryEtaPrediction wherePredictedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryEtaPrediction wherePredictionFactors($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryEtaPrediction wherePredictionModel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryEtaPrediction whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryEtaPrediction whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class DeliveryEtaPrediction extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'delivery_id',
        'predicted_at',
        'estimated_pickup_time',
        'estimated_delivery_time',
        'confidence_percentage',
        'prediction_factors',
        'prediction_model',
        'accuracy_score',
        'is_active',
    ];

    protected $casts = [
        'predicted_at' => 'datetime',
        'estimated_pickup_time' => 'datetime',
        'estimated_delivery_time' => 'datetime',
        'confidence_percentage' => 'integer',
        'prediction_factors' => 'array',
        'accuracy_score' => 'decimal:4',
        'is_active' => 'boolean',
    ];

    /**
     * Get the tenant that owns the ETA prediction.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the delivery associated with the ETA prediction.
     */
    public function delivery(): BelongsTo
    {
        return $this->belongsTo(Delivery::class);
    }
}
