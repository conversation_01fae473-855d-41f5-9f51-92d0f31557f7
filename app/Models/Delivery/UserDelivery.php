<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use App\Enums\Delivery\AdHocDeliveryStatus;
use App\Enums\Financial\PaymentStatus;
use App\Models\Core\Attachment;
use App\Models\Financial\Dispute;
use App\Models\Financial\Payment;
use App\Models\User\Address;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * @property string $id
 * @property string $user_id
 * @property string $delivery_reference User-friendly ID for ad-hoc delivery
 * @property string $pickup_address_id
 * @property string $delivery_address_id
 * @property string $item_description What is being sent (e.g., "Documents", "Small package")
 * @property numeric|null $item_weight_kg in kg
 * @property array<array-key, mixed>|null $item_dimensions_cm {length, width, height} in cm
 * @property string|null $delivery_instructions
 * @property mixed $status Specific status for ad-hoc deliveries
 * @property numeric|null $estimated_cost
 * @property numeric|null $actual_cost
 * @property string $currency Currency of the ad-hoc delivery
 * @property PaymentStatus $payment_status
 * @property string|null $payment_reference Reference from the payment gateway/transaction
 * @property \Illuminate\Support\Carbon|null $scheduled_pickup_time
 * @property \Illuminate\Support\Carbon|null $scheduled_delivery_time
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Delivery\Delivery|null $delivery
 * @property-read \App\Models\User\Address $deliveryAddress
 * @property-read \App\Models\User\Address $pickupAddress
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\Rating> $ratings
 * @property-read int|null $ratings_count
 * @property-read \App\Models\User\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery byStatus(\App\Enums\AdHocDeliveryStatus $status)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery cancelled()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery confirmed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery delivered()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery forUser(string $userId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery recent(int $days = 30)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery whereActualCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery whereDeliveryAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery whereDeliveryInstructions($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery whereDeliveryReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery whereEstimatedCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery whereItemDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery whereItemDimensionsCm($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery whereItemWeightKg($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery wherePaymentReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery wherePaymentStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery wherePickupAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery whereScheduledDeliveryTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery whereScheduledPickupTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserDelivery whereUserId($value)
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Attachment> $attachments
 * @property-read int|null $attachments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Dispute> $disputes
 * @property-read int|null $disputes_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Payment> $payments
 * @property-read int|null $payments_count
 *
 * @mixin \Eloquent
 */
class UserDelivery extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'user_id',
        'delivery_reference',
        'pickup_address_id',
        'delivery_address_id',
        'item_description',
        'item_weight_kg',
        'item_dimensions_cm',
        'delivery_instructions',
        'status',
        'estimated_cost',
        'actual_cost',
        'currency',
        'payment_status',
        'payment_reference',
        'scheduled_pickup_time',
        'scheduled_delivery_time',
    ];

    protected $casts = [
        'status' => AdHocDeliveryStatus::class,
        'payment_status' => PaymentStatus::class,
        'item_weight_kg' => 'decimal:3',
        'item_dimensions_cm' => 'array',
        'estimated_cost' => 'decimal:2',
        'actual_cost' => 'decimal:2',
        'scheduled_pickup_time' => 'datetime',
        'scheduled_delivery_time' => 'datetime',
    ];

    /**
     * Get the user that requested the delivery.
     *
     * @return BelongsTo<User,UserDelivery>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'user_id');
    }

    /**
     * Get the pickup address for the delivery.
     *
     * @return BelongsTo<Address,UserDelivery>
     */
    public function pickupAddress(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\Address::class, 'pickup_address_id');
    }

    /**
     * Get the delivery address for the delivery.
     *
     * @return BelongsTo<Address,UserDelivery>
     */
    public function deliveryAddress(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\Address::class, 'delivery_address_id');
    }

    /**
     * Get the delivery for this user delivery.
     *
     * @return HasOne<Delivery,UserDelivery>
     */
    public function delivery(): HasOne
    {
        return $this->hasOne(Delivery::class, 'deliverable_id')->where('deliverable_type', 'user_delivery');
    }

    /**
     * Get the payments for this user delivery.
     *
     * @return HasMany<Payment,UserDelivery>
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'user_delivery_id');
    }

    /**
     * Get the ratings for this user delivery.
     *
     * @return HasMany<Rating,UserDelivery>
     */
    public function ratings(): HasMany
    {
        return $this->hasMany(Rating::class, 'user_delivery_id');
    }

    /**
     * Get the disputes for this user delivery.
     *
     * @return HasMany<Dispute,UserDelivery>
     */
    public function disputes(): HasMany
    {
        return $this->hasMany(Dispute::class, 'user_delivery_id');
    }

    /**
     * Get the attachments for this user delivery.
     *
     * @return MorphMany<Attachment,UserDelivery>
     */
    public function attachments(): MorphMany
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }

    /**
     * Scope to get deliveries by status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByStatus($query, AdHocDeliveryStatus $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending deliveries.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', AdHocDeliveryStatus::PENDING);
    }

    /**
     * Scope to get confirmed deliveries.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeConfirmed($query)
    {
        return $query->where('status', AdHocDeliveryStatus::PROVIDER_ASSIGNED);
    }

    /**
     * Scope to get delivered deliveries.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDelivered($query)
    {
        return $query->where('status', AdHocDeliveryStatus::DELIVERED);
    }

    /**
     * Scope to get cancelled deliveries.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', AdHocDeliveryStatus::CANCELLED);
    }

    /**
     * Scope to get deliveries for a specific user.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForUser($query, string $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to get recent deliveries.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRecent($query, int $days = 30)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Check if the delivery is pending.
     */
    public function isPending(): bool
    {
        return $this->status === AdHocDeliveryStatus::PENDING;
    }

    /**
     * Check if the delivery is confirmed.
     */
    public function isConfirmed(): bool
    {
        return $this->status === AdHocDeliveryStatus::PROVIDER_ASSIGNED;
    }

    /**
     * Check if the delivery is completed.
     */
    public function isDelivered(): bool
    {
        return $this->status === AdHocDeliveryStatus::DELIVERED;
    }

    /**
     * Check if the delivery is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === AdHocDeliveryStatus::CANCELLED;
    }

    /**
     * Check if payment is completed.
     */
    public function isPaymentCompleted(): bool
    {
        return $this->payment_status === PaymentStatus::PAID;
    }

    /**
     * Get the final cost (actual or estimated).
     */
    public function getFinalCost(): ?float
    {
        return $this->actual_cost ?? $this->estimated_cost;
    }

    /**
     * Get the formatted final cost with currency.
     */
    public function getFormattedFinalCost(): string
    {
        $cost = $this->getFinalCost();

        if (is_null($cost)) {
            return 'Not calculated';
        }

        return $this->currency.' '.number_format($cost, 2);
    }

    /**
     * Get the item dimensions as a formatted string.
     */
    public function getFormattedDimensions(): ?string
    {
        if (! $this->item_dimensions_cm) {
            return null;
        }

        $dimensions = $this->item_dimensions_cm;

        if (isset($dimensions['length'], $dimensions['width'], $dimensions['height'])) {
            return "{$dimensions['length']} x {$dimensions['width']} x {$dimensions['height']} cm";
        }

        return null;
    }

    /**
     * Get the delivery distance if available.
     */
    public function getDeliveryDistance(): ?float
    {
        if (! $this->pickupAddress->hasCoordinates() || ! $this->deliveryAddress->hasCoordinates()) {
            return null;
        }

        $pickupLat = $this->pickupAddress->latitude;
        $pickupLng = $this->pickupAddress->longitude;
        $deliveryLat = $this->deliveryAddress->latitude;
        $deliveryLng = $this->deliveryAddress->longitude;

        // Calculate distance using Haversine formula
        $earthRadius = 6371; // Earth's radius in kilometers

        $latDelta = deg2rad($deliveryLat - $pickupLat);
        $lngDelta = deg2rad($deliveryLng - $pickupLng);

        $a = sin($latDelta / 2) * sin($latDelta / 2) +
             cos(deg2rad($pickupLat)) * cos(deg2rad($deliveryLat)) *
             sin($lngDelta / 2) * sin($lngDelta / 2);

        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));

        return $earthRadius * $c;
    }

    /**
     * Get the estimated delivery time based on distance.
     *
     * @param  float  $averageSpeed  Average speed in km/h
     * @return int|null Minutes
     */
    public function getEstimatedDeliveryTime(float $averageSpeed = 30): ?int
    {
        $distance = $this->getDeliveryDistance();

        if (is_null($distance)) {
            return null;
        }

        // Convert to minutes
        return (int) round(($distance / $averageSpeed) * 60);
    }

    /**
     * Update the delivery status.
     */
    public function updateStatus(AdHocDeliveryStatus $status): bool
    {
        return $this->update(['status' => $status]);
    }

    /**
     * Mark as confirmed.
     */
    public function markAsConfirmed(?float $actualCost = null): bool
    {
        // TODO:: fiGURE OUT CORRECT ENUM TO USE
        $updates = ['status' => AdHocDeliveryStatus::PROVIDER_ASSIGNED];

        if (! is_null($actualCost)) {
            $updates['actual_cost'] = $actualCost;
        }

        return $this->update($updates);
    }

    /**
     * Mark as delivered.
     */
    public function markAsDelivered(): bool
    {
        return $this->update(['status' => AdHocDeliveryStatus::DELIVERED]);
    }

    /**
     * Cancel the delivery.
     */
    public function cancel(?string $reason = null): bool
    {
        $updates = ['status' => AdHocDeliveryStatus::CANCELLED];

        if ($reason) {
            $updates['delivery_instructions'] = ($this->delivery_instructions ?? '')."\n\nCancellation reason: ".$reason;
        }

        return $this->update($updates);
    }

    /**
     * Get user delivery summary for API responses.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'delivery_reference' => $this->delivery_reference,
            'item_description' => $this->item_description,
            'item_weight_kg' => $this->item_weight_kg,
            'formatted_dimensions' => $this->getFormattedDimensions(),
            'status' => $this->status->value,
            'payment_status' => $this->payment_status->value,
            'final_cost' => $this->getFinalCost(),
            'formatted_final_cost' => $this->getFormattedFinalCost(),
            'currency' => $this->currency,
            'pickup_address' => $this->pickupAddress->full_address,
            'delivery_address' => $this->deliveryAddress->full_address,
            'delivery_distance_km' => $this->getDeliveryDistance(),
            'estimated_delivery_time_minutes' => $this->getEstimatedDeliveryTime(),
            'scheduled_pickup_time' => $this->scheduled_pickup_time,
            'scheduled_delivery_time' => $this->scheduled_delivery_time,
            'created_at' => $this->created_at,
        ];
    }

    /**
     * Generate a unique delivery reference.
     */
    public static function generateUniqueReference(string $prefix = 'UD'): string
    {
        $date = now()->format('Ymd');
        $counter = 1;

        do {
            $reference = "{$prefix}-{$date}-".str_pad((string) $counter, 4, '0', STR_PAD_LEFT);
            $exists = static::where('delivery_reference', $reference)->exists();
            $counter++;
        } while ($exists);

        return $reference;
    }

    /**
     * Get delivery statistics for a user.
     */
    public static function getUserStats(string $userId, int $days = 30): array
    {
        $deliveries = static::forUser($userId)
            ->where('created_at', '>=', now()->subDays($days))
            ->get();

        $totalDeliveries = $deliveries->count();
        $completedDeliveries = $deliveries->where('status', AdHocDeliveryStatus::DELIVERED)->count();
        $cancelledDeliveries = $deliveries->where('status', AdHocDeliveryStatus::CANCELLED)->count();
        $totalSpent = $deliveries->where('payment_status', PaymentStatus::PAID)->sum('actual_cost');

        return [
            'total_deliveries' => $totalDeliveries,
            'completed_deliveries' => $completedDeliveries,
            'cancelled_deliveries' => $cancelledDeliveries,
            'completion_rate' => $totalDeliveries > 0 ? round(($completedDeliveries / $totalDeliveries) * 100, 2) : 0,
            'total_spent' => $totalSpent,
            'average_cost' => $completedDeliveries > 0 ? round($totalSpent / $completedDeliveries, 2) : 0,
        ];
    }
}
