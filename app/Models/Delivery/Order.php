<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use App\Enums\Delivery\OrderSource;
use App\Enums\Delivery\OrderStatus;
use App\Enums\Delivery\OrderType;
use App\Enums\Financial\PaymentMethodType;
use App\Enums\Financial\PaymentStatus;
use App\Models\Business\Business;
use App\Models\Business\BusinessBranch;
use App\Models\Core\Attachment;
use App\Models\Financial\Dispute;
use App\Models\Financial\Payment;
use App\Models\System\Tenant;
use App\Models\User\Address;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * @property string $id
 * @property string $order_reference User-friendly ID
 * @property string $customer_id
 * @property string $business_tenant_id FK to tenants.id (the business tenant placing/receiving the order)
 * @property string $business_id
 * @property string|null $business_branch_id
 * @property string|null $delivery_provider_tenant_id FK to tenants.id (the delivery provider tenant, nullable)
 * @property string|null $delivery_address_id
 * @property string|null $pickup_address_id
 * @property OrderType $order_type
 * @property OrderSource $source
 * @property OrderStatus $status
 * @property OrderStatus|null $previous_status For tracking state changes
 * @property numeric $sub_total In order currency
 * @property numeric $tax_amount In order currency
 * @property numeric|null $delivery_fee In order currency
 * @property numeric $platform_commission_amount Commission charged to business
 * @property numeric $total_amount In order currency
 * @property string $currency Currency of the order (matches business/customer location?)
 * @property PaymentMethodType|null $payment_method Use PaymentMethodType enum
 * @property PaymentStatus $payment_status
 * @property string|null $payment_reference Reference from the payment gateway/transaction
 * @property string|null $pickup_slot_id
 * @property \Illuminate\Support\Carbon|null $scheduled_pickup_time
 * @property int|null $estimated_preparation_time Minutes
 * @property int|null $estimated_delivery_time Minutes
 * @property string|null $customer_notes
 * @property string|null $business_notes
 * @property \Illuminate\Support\Carbon|null $accepted_at
 * @property \Illuminate\Support\Carbon|null $prepared_at
 * @property \Illuminate\Support\Carbon|null $completed_at
 * @property \Illuminate\Support\Carbon|null $cancelled_at
 * @property string|null $cancellation_reason
 * @property string|null $cancelled_by_user_id
 * @property string|null $batch_id
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Attachment> $attachments
 * @property-read int|null $attachments_count
 * @property-read OrderBatch|null $batch
 * @property-read Business $business
 * @property-read BusinessBranch|null $businessBranch
 * @property-read Tenant $businessTenant
 * @property-read User|null $cancelledBy
 * @property-read User $customer
 * @property-read Delivery|null $delivery
 * @property-read Address|null $deliveryAddress
 * @property-read Tenant|null $deliveryProviderTenant
 * @property-read \Illuminate\Database\Eloquent\Collection<int, DeliveryRequest> $deliveryRequests
 * @property-read int|null $delivery_requests_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Dispute> $disputes
 * @property-read int|null $disputes_count
 * @property-read string $formatted_total
 * @property-read \Illuminate\Database\Eloquent\Collection<int, OrderItem> $items
 * @property-read int|null $items_count
 * @property-read OrderDelivery|null $orderDelivery
 * @property-read \Illuminate\Database\Eloquent\Collection<int, OrderItem> $orderItems
 * @property-read int|null $order_items_count
 * @property-read Payment|null $payment
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Payment> $payments
 * @property-read int|null $payments_count
 * @property-read Address|null $pickupAddress
 * @property-read PickupSlot|null $pickupSlot
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\Rating> $ratings
 * @property-read int|null $ratings_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order byStatus(\App\Enums\Delivery\OrderStatus $status)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order byType(\App\Enums\Delivery\OrderType $orderType)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order cancelled()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order confirmed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order delivered()
 * @method static \Database\Factories\OrderFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order forBusiness(string $businessId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order forCurrentTenant()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order forCustomer(string $customerId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order forTenant(string $tenantId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereAcceptedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereBatchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereBusinessBranchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereBusinessNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereBusinessTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereCancellationReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereCancelledAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereCancelledByUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereCustomerId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereCustomerNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereDeliveryAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereDeliveryFee($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereDeliveryProviderTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereEstimatedDeliveryTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereEstimatedPreparationTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereOrderReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereOrderType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order wherePaymentMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order wherePaymentReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order wherePaymentStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order wherePickupAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order wherePickupSlotId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order wherePlatformCommissionAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order wherePreparedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order wherePreviousStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereScheduledPickupTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereSource($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereSubTotal($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereTaxAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereTotalAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Order withinDateRange(string $startDate, string $endDate)
 *
 * @mixin \Eloquent
 */
class Order extends Model
{
    use HasFactory, HasUuids;

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Database\Factories\OrderFactory::new();
    }

    protected $fillable = [
        'order_reference',
        'customer_id',
        'business_tenant_id',
        'business_id',
        'business_branch_id',
        'delivery_provider_tenant_id',
        'delivery_address_id',
        'pickup_address_id',
        'order_type',
        'source',
        'status',
        'previous_status',
        'sub_total',
        'tax_amount',
        'delivery_fee',
        'platform_commission_amount',
        'total_amount',
        'currency',
        'payment_method',
        'payment_status',
        'payment_reference',
        'pickup_slot_id',
        'scheduled_pickup_time',
        'estimated_preparation_time',
        'estimated_delivery_time',
        'customer_notes',
        'business_notes',
        'accepted_at',
        'prepared_at',
        'completed_at',
        'cancelled_at',
        'cancellation_reason',
        'cancelled_by_user_id',
        'batch_id',
    ];

    protected $casts = [
        'order_type' => OrderType::class,
        'source' => OrderSource::class,
        'status' => OrderStatus::class,
        'previous_status' => OrderStatus::class,
        'payment_method' => PaymentMethodType::class,
        'payment_status' => PaymentStatus::class,
        'sub_total' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'delivery_fee' => 'decimal:2',
        'platform_commission_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
        'estimated_preparation_time' => 'integer',
        'estimated_delivery_time' => 'integer',
        'scheduled_pickup_time' => 'datetime',
        'accepted_at' => 'datetime',
        'prepared_at' => 'datetime',
        'completed_at' => 'datetime',
        'cancelled_at' => 'datetime',
    ];

    /**
     * Get the customer that owns the order.
     *
     * @return BelongsTo<User,Order>
     */
    public function customer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_id');
    }

    /**
     * Get the business tenant for the order.
     *
     * @return BelongsTo<Tenant,Order>
     */
    public function businessTenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'business_tenant_id');
    }

    /**
     * Get the business that owns the order.
     *
     * @return BelongsTo<Business,Order>
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }

    /**
     * Get the business branch for the order.
     *
     * @return BelongsTo<BusinessBranch,Order>
     */
    public function businessBranch(): BelongsTo
    {
        return $this->belongsTo(BusinessBranch::class, 'business_branch_id');
    }

    /**
     * Get the delivery provider tenant for the order.
     *
     * @return BelongsTo<Tenant,Order>
     */
    public function deliveryProviderTenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'delivery_provider_tenant_id');
    }

    /**
     * Get the delivery address for the order.
     *
     * @return BelongsTo<Address,Order>
     */
    public function deliveryAddress(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'delivery_address_id');
    }

    /**
     * Get the pickup address for the order.
     *
     * @return BelongsTo<Address,Order>
     */
    public function pickupAddress(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'pickup_address_id');
    }

    /**
     * Get the pickup slot for the order.
     *
     * @return BelongsTo<PickupSlot,Order>
     */
    public function pickupSlot(): BelongsTo
    {
        return $this->belongsTo(PickupSlot::class, 'pickup_slot_id');
    }

    /**
     * Get the user who cancelled the order.
     *
     * @return BelongsTo<User,Order>
     */
    public function cancelledBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'cancelled_by_user_id');
    }

    /**
     * Get the order batch for the order.
     *
     * @return BelongsTo<OrderBatch,Order>
     */
    public function batch(): BelongsTo
    {
        return $this->belongsTo(OrderBatch::class, 'batch_id');
    }

    /**
     * Get the order items for the order.
     *
     * @return HasMany<OrderItem,Order>
     */
    public function orderItems(): HasMany
    {
        return $this->hasMany(OrderItem::class);
    }

    /**
     * Get the items for the order (alias).
     *
     * @return HasMany<OrderItem,Order>
     */
    public function items(): HasMany
    {
        return $this->orderItems();
    }

    /**
     * Get the payment for the order.
     *
     * @return HasOne<Payment,Order>
     */
    public function payment(): HasOne
    {
        return $this->hasOne(Payment::class);
    }

    /**
     * Get the payments for the order.
     *
     * @return HasMany<Payment,Order>
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class);
    }

    /**
     * Get the delivery for the order.
     *
     * @return HasOne<Delivery,Order>
     */
    public function delivery(): HasOne
    {
        return $this->hasOne(Delivery::class, 'deliverable_id')->where('deliverable_type', 'App\Models\Delivery\Order');
    }

    /**
     * Get the order delivery (if exists).
     *
     * @return HasOne<OrderDelivery,Order>
     */
    public function orderDelivery(): HasOne
    {
        return $this->hasOne(OrderDelivery::class);
    }

    /**
     * Get the delivery requests for this order.
     *
     * @return HasMany<DeliveryRequest,Order>
     */
    public function deliveryRequests(): HasMany
    {
        return $this->hasMany(DeliveryRequest::class);
    }

    /**
     * Get the ratings for the order.
     *
     * @return HasMany<\App\Models\Delivery\Rating,Order>
     */
    public function ratings(): HasMany
    {
        return $this->hasMany(\App\Models\Delivery\Rating::class);
    }

    /**
     * Get the disputes for the order.
     *
     * @return HasMany<Dispute,Order>
     */
    public function disputes(): HasMany
    {
        return $this->hasMany(Dispute::class);
    }

    /**
     * Get the attachments for the order.
     *
     * @return MorphMany<Attachment,Order>
     */
    public function attachments(): MorphMany
    {
        return $this->morphMany(Attachment::class, 'attachable');
    }

    /**
     * Scope to get orders by status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByStatus($query, OrderStatus $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending orders.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', OrderStatus::PENDING);
    }

    /**
     * Scope to get confirmed orders.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeConfirmed($query)
    {
        return $query->where('status', OrderStatus::CONFIRMED);
    }

    /**
     * Scope to get delivered orders.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDelivered($query)
    {
        return $query->where('status', OrderStatus::DELIVERED);
    }

    /**
     * Scope to get cancelled orders.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', OrderStatus::CANCELLED);
    }

    /**
     * Scope to get orders by customer.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForCustomer($query, string $customerId)
    {
        return $query->where('customer_id', $customerId);
    }

    /**
     * Scope to get orders by business.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForBusiness($query, string $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    /**
     * Scope to get orders by order type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByType($query, OrderType $orderType)
    {
        return $query->where('order_type', $orderType);
    }

    /**
     * Scope to get orders within a date range.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithinDateRange($query, string $startDate, string $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Check if the order is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === OrderStatus::CANCELLED;
    }

    /**
     * Check if the order is delivered.
     */
    public function isDelivered(): bool
    {
        return $this->status === OrderStatus::DELIVERED;
    }

    /**
     * Check if the order is pending.
     */
    public function isPending(): bool
    {
        return $this->status === OrderStatus::PENDING;
    }

    /**
     * Check if the order is confirmed.
     */
    public function isConfirmed(): bool
    {
        return $this->status === OrderStatus::CONFIRMED;
    }

    /**
     * Get the total items count.
     */
    public function getTotalItemsCount(): int
    {
        return $this->orderItems()->sum('quantity');
    }

    /**
     * Get the formatted total amount with currency.
     */
    public function getFormattedTotalAttribute(): string
    {
        return $this->currency.' '.number_format($this->total_amount, 2);
    }

    /**
     * Calculate the net amount (total - commission).
     */
    public function getNetAmount(): float
    {
        return $this->total_amount - $this->platform_commission_amount;
    }

    /**
     * Check if the order requires delivery.
     */
    public function requiresDelivery(): bool
    {
        return $this->order_type === OrderType::DELIVERY && ! is_null($this->delivery_address_id);
    }

    /**
     * Check if the order is for pickup.
     */
    public function isPickup(): bool
    {
        return $this->order_type === OrderType::PICKUP;
    }

    /**
     * Scope orders to current tenant context.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForCurrentTenant($query)
    {
        if (tenant()) {
            return $query->where('business_tenant_id', tenant()->id);
        }

        return $query;
    }

    /**
     * Scope orders to specific tenant.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForTenant($query, string $tenantId)
    {
        return $query->where('business_tenant_id', $tenantId);
    }

    /**
     * Get the estimated total time in minutes.
     */
    public function getEstimatedTotalTime(): ?int
    {
        if (is_null($this->estimated_preparation_time) || is_null($this->estimated_delivery_time)) {
            return null;
        }

        return $this->estimated_preparation_time + $this->estimated_delivery_time;
    }
}
