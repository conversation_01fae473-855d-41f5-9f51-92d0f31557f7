<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use App\Enums\Delivery\DeliveryProviderStatus;
use App\Enums\Delivery\DeliveryProviderTier;
use App\Enums\Delivery\ServiceScope;
use App\Enums\User\KycLevel;
use App\Models\Business\Business;
use App\Models\Business\Expense;
use App\Models\Core\Country;
use App\Models\Core\State;
use App\Models\Financial\PlatformAccount;
use App\Models\Financial\UserSubscription;
use App\Models\System\Tenant;
use App\Models\System\TenantWhiteLabelSetting;
use App\Models\User\Address;
use App\Models\User\BankAccountVerification;
use App\Models\User\EntityVerification;
use App\Models\User\IdentityVerification;
use App\Models\User\KycDocument;
use App\Models\User\User;
use App\Models\User\Verification;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Laravel\Scout\Searchable;
use Silber\Bouncer\Database\HasRolesAndAbilities;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $user_id
 * @property string|null $business_id
 * @property bool $is_internal_provider Flag for internal provider
 * @property string $company_name
 * @property string|null $description
 * @property string|null $logo_url
 * @property string|null $primary_address_id
 * @property string|null $contact_email
 * @property string|null $contact_phone
 * @property string $country_id
 * @property string|null $state_id
 * @property DeliveryProviderStatus $status
 * @property DeliveryProviderTier $tier
 * @property ServiceScope $service_scope
 * @property int $fleet_size
 * @property KycLevel $kyc_level
 * @property array<array-key, mixed>|null $verification_documents
 * @property array<array-key, mixed>|null $operating_hours
 * @property bool $global_auto_accept_deliveries
 * @property array<array-key, mixed>|null $auto_acceptance_criteria Criteria for auto-accepting deliveries (e.g., distance, vehicle type)
 * @property bool $auto_driver_assignment_enabled Enable auto-assignment of accepted deliveries to drivers
 * @property array<array-key, mixed>|null $driver_assignment_rules Rules for auto-assigning drivers (e.g., nearest, availability)
 * @property array<array-key, mixed>|null $scheduled_auto_accept_config
 * @property float|null $performance_rating_avg
 * @property array<array-key, mixed>|null $settings Can keep for miscellaneous, unstructured settings if needed
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $current_subscription_id
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Silber\Bouncer\Database\Ability> $abilities
 * @property-read int|null $abilities_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\ProviderBranch> $branches
 * @property-read int|null $branches_count
 * @property-read Business|null $business
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\ProviderCapability> $capabilities
 * @property-read int|null $capabilities_count
 * @property-read Country $country
 * @property-read UserSubscription|null $currentSubscription
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\Delivery> $deliveries
 * @property-read int|null $deliveries_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\ProviderDeliveryPricing> $deliveryPricing
 * @property-read int|null $delivery_pricing_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\ProviderTeamMember> $drivers
 * @property-read int|null $drivers_count
 * @property-read User $owner
 * @property-read Address|null $primaryAddress
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\ProviderSetting> $providerSettings
 * @property-read int|null $provider_settings_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\Rating> $ratings
 * @property-read int|null $ratings_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \Silber\Bouncer\Database\Role> $roles
 * @property-read int|null $roles_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\ProviderServiceArea> $serviceAreas
 * @property-read int|null $service_areas_count
 * @property-read State|null $state
 * @property-read \Illuminate\Database\Eloquent\Collection<int, UserSubscription> $subscriptions
 * @property-read int|null $subscriptions_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\ProviderTeamMember> $teamMembers
 * @property-read int|null $team_members_count
 * @property-read Tenant $tenant
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\Vehicle> $vehicles
 * @property-read int|null $vehicles_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider autoAcceptEnabled()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider autoDriverAssignmentEnabled()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider externalProviders()
 * @method static \Database\Factories\DeliveryProviderFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider internalProviders()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider verified()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereAutoAcceptanceCriteria($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereAutoDriverAssignmentEnabled($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereCompanyName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereContactEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereContactPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereCountryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereCurrentSubscriptionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereDriverAssignmentRules($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereGlobalAutoAcceptDeliveries($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereIs($role)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereIsAll($role)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereIsInternalProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereIsNot($role)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereLogoUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereOperatingHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider wherePerformanceRatingAvg($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider wherePrimaryAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereScheduledAutoAcceptConfig($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereSettings($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereStateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereVerificationDocuments($value)
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, EntityVerification> $entityVerifications
 * @property-read int|null $entity_verifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Expense> $expenses
 * @property-read int|null $expenses_count
 * @property-read PlatformAccount|null $platformAccount
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Verification> $verifications
 * @property-read int|null $verifications_count
 * @property-read TenantWhiteLabelSetting|null $whiteLabelSettings
 * @property-read \Illuminate\Database\Eloquent\Collection<int, BankAccountVerification> $bankVerifications
 * @property-read int|null $bank_verifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, DeliveryZone> $deliveryZones
 * @property-read int|null $delivery_zones_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, IdentityVerification> $identityVerifications
 * @property-read int|null $identity_verifications_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, KycDocument> $kycDocuments
 * @property-read int|null $kyc_documents_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereFleetSize($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereKycLevel($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereServiceScope($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryProvider whereTier($value)
 *
 * @mixin \Eloquent
 */
class DeliveryProvider extends Model
{
    use BelongsToTenant, HasFactory, HasRolesAndAbilities, HasUuids, Searchable;

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Database\Factories\DeliveryProviderFactory::new();
    }

    protected $fillable = [
        'tenant_id',
        'user_id',
        'business_id',
        'is_internal_provider',
        'company_name',
        'description',
        'logo_url',
        'primary_address_id',
        'contact_email',
        'contact_phone',
        'country_id',
        'state_id',
        'status',
        'tier',
        'service_scope',
        'fleet_size',
        'kyc_level',
        'verification_documents',
        'operating_hours',
        'global_auto_accept_deliveries',
        'auto_acceptance_criteria',
        'auto_driver_assignment_enabled',
        'driver_assignment_rules',
        'scheduled_auto_accept_config',
        'performance_rating_avg',
        'current_subscription_id',
        'settings',
    ];

    protected $casts = [
        'is_internal_provider' => 'boolean',
        'status' => DeliveryProviderStatus::class,
        'tier' => DeliveryProviderTier::class,
        'service_scope' => ServiceScope::class,
        'fleet_size' => 'integer',
        'kyc_level' => KycLevel::class,
        'verification_documents' => 'array',
        'operating_hours' => 'array',
        'global_auto_accept_deliveries' => 'boolean',
        'auto_acceptance_criteria' => 'array',
        'auto_driver_assignment_enabled' => 'boolean',
        'driver_assignment_rules' => 'array',
        'scheduled_auto_accept_config' => 'array',
        'performance_rating_avg' => 'float',
        'settings' => 'array',
    ];

    /**
     * Get the tenant that owns the delivery provider.
     *
     * @return BelongsTo<Tenant,DeliveryProvider>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    /**
     * Get the user that owns the delivery provider.
     *
     * @return BelongsTo<User,DeliveryProvider>
     */
    public function owner(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Get the business associated with the delivery provider.
     *
     * @return BelongsTo<Business,DeliveryProvider>
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }

    /**
     * Get the primary address of the delivery provider.
     *
     * @return BelongsTo<Address,DeliveryProvider>
     */
    public function primaryAddress(): BelongsTo
    {
        return $this->belongsTo(Address::class, 'primary_address_id');
    }

    /**
     * Get the country where the delivery provider is located.
     *
     * @return BelongsTo<Country,DeliveryProvider>
     */
    public function country(): BelongsTo
    {
        return $this->belongsTo(Country::class);
    }

    /**
     * Get the state where the delivery provider is located.
     *
     * @return BelongsTo<State,DeliveryProvider>
     */
    public function state(): BelongsTo
    {
        return $this->belongsTo(State::class);
    }

    /**
     * Get the current subscription of the delivery provider.
     *
     * @return BelongsTo<UserSubscription,DeliveryProvider>
     */
    public function currentSubscription(): BelongsTo
    {
        return $this->belongsTo(UserSubscription::class, 'current_subscription_id');
    }

    /**
     * Get the branches of the delivery provider.
     *
     * @return HasMany<ProviderBranch,DeliveryProvider>
     */
    public function branches(): HasMany
    {
        return $this->hasMany(ProviderBranch::class, 'provider_id');
    }

    /**
     * Get the team members of the delivery provider.
     *
     * @return HasMany<ProviderTeamMember,DeliveryProvider>
     */
    public function teamMembers(): HasMany
    {
        return $this->hasMany(ProviderTeamMember::class, 'provider_id');
    }

    /**
     * Get the drivers of the delivery provider.
     *
     * @return HasMany<ProviderTeamMember,DeliveryProvider>
     */
    public function drivers(): HasMany
    {
        return $this->hasMany(ProviderTeamMember::class, 'provider_id')
            ->where('role', 'driver');
    }

    /**
     * Get the capabilities of the delivery provider.
     *
     * @return HasMany<ProviderCapability,DeliveryProvider>
     */
    public function capabilities(): HasMany
    {
        return $this->hasMany(ProviderCapability::class, 'provider_id');
    }

    /**
     * Get the vehicles of the delivery provider.
     *
     * @return HasMany<Vehicle,DeliveryProvider>
     */
    public function vehicles(): HasMany
    {
        return $this->hasMany(Vehicle::class, 'provider_id');
    }

    /**
     * Get the service areas of the delivery provider.
     *
     * @return HasMany<ProviderServiceArea,DeliveryProvider>
     */
    public function serviceAreas(): HasMany
    {
        return $this->hasMany(ProviderServiceArea::class, 'provider_id');
    }

    /**
     * Get the delivery pricing of the delivery provider.
     *
     * @return HasMany<ProviderDeliveryPricing,DeliveryProvider>
     */
    public function deliveryPricing(): HasMany
    {
        return $this->hasMany(ProviderDeliveryPricing::class, 'provider_id');
    }

    /**
     * Get the deliveries of the delivery provider.
     *
     * @return HasMany<Delivery,DeliveryProvider>
     */
    public function deliveries(): HasMany
    {
        return $this->hasMany(Delivery::class, 'delivery_provider_id');
    }

    /**
     * Get the settings of the delivery provider.
     *
     * @return HasMany<ProviderSetting,DeliveryProvider>
     */
    public function providerSettings(): HasMany
    {
        return $this->hasMany(ProviderSetting::class, 'provider_id');
    }

    /**
     * Get the delivery provider's entity verification records (complex verification steps).
     *
     * @return HasMany<EntityVerification,DeliveryProvider>
     */
    public function entityVerifications(): HasMany
    {
        return $this->hasMany(EntityVerification::class, 'entity_id')
            ->where('entity_type', 'delivery_providers');
    }

    /**
     * Get the delivery provider's verification records (simple verification codes).
     *
     * @return MorphMany<Verification,DeliveryProvider>
     */
    public function verifications(): MorphMany
    {
        return $this->morphMany(Verification::class, 'verifiable');
    }

    /**
     * Get the KYC documents for this delivery provider.
     *
     * @return MorphMany<KycDocument,DeliveryProvider>
     */
    public function kycDocuments(): MorphMany
    {
        return $this->morphMany(KycDocument::class, 'verifiable');
    }

    /**
     * Get the bank account verifications for this delivery provider.
     *
     * @return HasMany<BankAccountVerification,DeliveryProvider>
     */
    public function bankVerifications(): HasMany
    {
        return $this->hasMany(BankAccountVerification::class, 'user_id', 'user_id');
    }

    /**
     * Get the identity verifications for this delivery provider.
     *
     * @return HasMany<IdentityVerification,DeliveryProvider>
     */
    public function identityVerifications(): HasMany
    {
        return $this->hasMany(IdentityVerification::class, 'user_id', 'user_id');
    }

    /**
     * Get the delivery zones this provider serves through service areas.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany<DeliveryZone,DeliveryProvider>
     */
    public function deliveryZones(): \Illuminate\Database\Eloquent\Relations\BelongsToMany
    {
        return $this->belongsToMany(DeliveryZone::class, 'provider_service_areas', 'provider_id', 'zone_id')
            ->withPivot(['pricing_multiplier', 'is_active', 'service_config', 'delivery_count', 'average_rating', 'last_delivery_at'])
            ->withTimestamps();
    }

    /**
     * Get the white label settings of the delivery provider.
     *
     * @return HasOne<TenantWhiteLabelSetting,DeliveryProvider>
     */
    public function whiteLabelSettings(): HasOne
    {
        return $this->hasOne(TenantWhiteLabelSetting::class, 'tenant_id', 'tenant_id');
    }

    /**
     * Get all subscriptions of the delivery provider.
     *
     * @return HasMany<UserSubscription,DeliveryProvider>
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class, 'subscriber_id')
            ->where('subscriber_type', 'delivery_provider');
    }

    /**
     * Get the platform account of the delivery provider.
     *
     * @return MorphOne<PlatformAccount,DeliveryProvider>
     */
    public function platformAccount(): MorphOne
    {
        return $this->morphOne(PlatformAccount::class, 'accountable');
    }

    /**
     * Get the ratings of the delivery provider.
     *
     * @return MorphMany<Rating,DeliveryProvider>
     */
    public function ratings(): MorphMany
    {
        return $this->morphMany(Rating::class, 'rateable');
    }

    /**
     * Get the expenses of the delivery provider.
     *
     * @return MorphMany<Expense,DeliveryProvider>
     */
    public function expenses(): MorphMany
    {
        return $this->morphMany(Expense::class, 'expensable');
    }

    /**
     * Scope to get only active delivery providers.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', DeliveryProviderStatus::ACTIVE);
    }

    /**
     * Scope to get only verified delivery providers.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeVerified($query)
    {
        return $query->where('status', DeliveryProviderStatus::VERIFIED);
    }

    /**
     * Scope to get only internal delivery providers.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInternalProviders($query)
    {
        return $query->where('is_internal_provider', true);
    }

    /**
     * Scope to get only external delivery providers.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeExternalProviders($query)
    {
        return $query->where('is_internal_provider', false);
    }

    /**
     * Scope to get delivery providers with auto-accept enabled.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAutoAcceptEnabled($query)
    {
        return $query->where('global_auto_accept_deliveries', true);
    }

    /**
     * Scope to get delivery providers with auto driver assignment enabled.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAutoDriverAssignmentEnabled($query)
    {
        return $query->where('auto_driver_assignment_enabled', true);
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array<string, mixed>
     */
    public function toSearchableArray(): array
    {
        return [
            'id' => $this->id,
            'tenant_id' => $this->tenant_id,
            'company_name' => $this->company_name,
            'description' => $this->description,
            'contact_email' => $this->contact_email,
            'contact_phone' => $this->contact_phone,
            'country_id' => $this->country_id,
            'state_id' => $this->state_id,
            'status' => $this->status?->value,
            'tier' => $this->tier?->value,
            'service_scope' => $this->service_scope?->value,
            'fleet_size' => $this->fleet_size,
            'kyc_level' => $this->kyc_level?->value,
            'is_internal_provider' => $this->is_internal_provider,
            'performance_rating_avg' => $this->performance_rating_avg,
            'created_at' => $this->created_at?->timestamp,
        ];
    }

    /**
     * Get the Scout search index name.
     */
    public function searchableAs(): string
    {
        return 'delivery_providers';
    }

    /**
     * Determine if the model should be searchable.
     */
    public function shouldBeSearchable(): bool
    {
        return $this->status === DeliveryProviderStatus::ACTIVE || $this->status === DeliveryProviderStatus::VERIFIED;
    }

    /**
     * Get the Scout search key.
     */
    public function getScoutKey(): mixed
    {
        return $this->id;
    }

    /**
     * Get the Scout search key name.
     */
    public function getScoutKeyName(): string
    {
        return 'id';
    }

    /**
     * Get the human-readable tier label.
     */
    public function getTierLabel(): string
    {
        return $this->tier->getLabel();
    }

    /**
     * Get the service scope label.
     */
    public function getServiceScopeLabel(): string
    {
        return $this->service_scope->getLabel();
    }

    /**
     * Get the KYC level label.
     */
    public function getKycLevelLabel(): string
    {
        return $this->kyc_level->getLabel();
    }

    /**
     * Check if provider can serve interstate deliveries.
     */
    public function canServeInterstate(): bool
    {
        return $this->service_scope->supportsInterstate();
    }

    /**
     * Check if provider supports a specific feature based on tier.
     */
    public function supportsFeature(string $feature): bool
    {
        return $this->tier->hasFeature($feature);
    }

    /**
     * Check if provider can access API features.
     */
    public function hasApiAccess(): bool
    {
        return $this->tier->supportsApiAccess();
    }

    /**
     * Check if provider has reached vehicle limit.
     */
    public function hasReachedVehicleLimit(): bool
    {
        if ($this->tier->hasUnlimitedVehicles()) {
            return false;
        }

        return $this->fleet_size >= $this->tier->getMaxVehicles();
    }

    /**
     * Get remaining vehicle slots.
     */
    public function getRemainingVehicleSlots(): int
    {
        if ($this->tier->hasUnlimitedVehicles()) {
            return PHP_INT_MAX;
        }

        return max(0, $this->tier->getMaxVehicles() - $this->fleet_size);
    }

    /**
     * Check if provider can upgrade to a specific tier.
     */
    public function canUpgradeTo(DeliveryProviderTier $targetTier): bool
    {
        return $this->tier->canUpgradeTo($targetTier);
    }

    /**
     * Check if provider meets KYC requirements for interstate delivery.
     */
    public function meetsInterstateKycRequirements(): bool
    {
        return $this->kyc_level->canAccessFeature('interstate_delivery');
    }

    /**
     * Get current KYC completion percentage.
     */
    public function getKycCompletionPercentage(): int
    {
        $requiredDocuments = $this->kyc_level->getRequiredDocuments();
        $completedDocuments = $this->kycDocuments()->where('verification_status', 'verified')->count();

        if (empty($requiredDocuments)) {
            return 100;
        }

        return (int) round(($completedDocuments / count($requiredDocuments)) * 100);
    }
}
