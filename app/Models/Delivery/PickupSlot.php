<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use App\Models\Business\Business;
use App\Models\Business\BusinessBranch;
use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $business_id
 * @property string $business_branch_id
 * @property int $day_of_week 0-6 (Sunday-Saturday)
 * @property \Illuminate\Support\Carbon $start_time
 * @property \Illuminate\Support\Carbon $end_time
 * @property int|null $max_orders
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Business $business
 * @property-read BusinessBranch $businessBranch
 * @property-read string $display_name
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\Order> $orders
 * @property-read int|null $orders_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\PickupSlotOrderBatch> $pickupSlotOrderBatches
 * @property-read int|null $pickup_slot_order_batches_count
 * @property-read Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot available()
 * @method static \Database\Factories\PickupSlotFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot forBranch(string $branchId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot forDay(int $dayOfWeek)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot forToday()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot ordered()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot whereBusinessBranchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot whereDayOfWeek($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot whereEndTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot whereMaxOrders($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot whereStartTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PickupSlot whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class PickupSlot extends Model
{
    use HasFactory, HasUuids;

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): \Database\Factories\PickupSlotFactory
    {
        return \Database\Factories\PickupSlotFactory::new();
    }

    protected $fillable = [
        'tenant_id',
        'business_id',
        'business_branch_id',
        'day_of_week',
        'start_time',
        'end_time',
        'max_orders',
        'is_active',
    ];

    protected $casts = [
        'day_of_week' => 'integer',
        'start_time' => 'datetime:H:i:s',
        'end_time' => 'datetime:H:i:s',
        'max_orders' => 'integer',
        'is_active' => 'boolean',
    ];

    /**
     * Get the tenant that owns the pickup slot.
     *
     * @return BelongsTo<Tenant,PickupSlot>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    /**
     * Get the business that owns the pickup slot.
     *
     * @return BelongsTo<Business,PickupSlot>
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }

    /**
     * Get the business branch that owns the pickup slot.
     *
     * @return BelongsTo<BusinessBranch,PickupSlot>
     */
    public function businessBranch(): BelongsTo
    {
        return $this->belongsTo(BusinessBranch::class, 'business_branch_id');
    }

    /**
     * Get the orders using this pickup slot.
     *
     * @return HasMany<Order,PickupSlot>
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'pickup_slot_id');
    }

    /**
     * Get the pickup slot order batches for this slot.
     *
     * @return HasMany<PickupSlotOrderBatch,PickupSlot>
     */
    public function pickupSlotOrderBatches(): HasMany
    {
        return $this->hasMany(PickupSlotOrderBatch::class, 'pickup_slot_id');
    }

    /**
     * Scope to get only active pickup slots.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get pickup slots for a specific day of week.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForDay($query, int $dayOfWeek)
    {
        return $query->where('day_of_week', $dayOfWeek);
    }

    /**
     * Scope to get pickup slots for today.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForToday($query)
    {
        return $query->where('day_of_week', now()->dayOfWeek);
    }

    /**
     * Scope to get pickup slots for a specific branch.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForBranch($query, string $branchId)
    {
        return $query->where('business_branch_id', $branchId);
    }

    /**
     * Scope to get available pickup slots (not at capacity).
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAvailable($query)
    {
        return $query->where(function ($query) {
            $query->whereNull('max_orders')
                ->orWhereRaw('(SELECT COUNT(*) FROM orders WHERE pickup_slot_id = pickup_slots.id AND DATE(orders.scheduled_pickup_time) = CURDATE()) < pickup_slots.max_orders');
        });
    }

    /**
     * Scope to order by day and time.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('day_of_week')->orderBy('start_time');
    }

    /**
     * Check if the pickup slot is active.
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Check if the pickup slot has a maximum order limit.
     */
    public function hasOrderLimit(): bool
    {
        return ! is_null($this->max_orders);
    }

    /**
     * Get the day name for this pickup slot.
     */
    public function getDayName(): string
    {
        $days = [
            0 => 'Sunday',
            1 => 'Monday',
            2 => 'Tuesday',
            3 => 'Wednesday',
            4 => 'Thursday',
            5 => 'Friday',
            6 => 'Saturday',
        ];

        return $days[$this->day_of_week] ?? 'Unknown';
    }

    /**
     * Get the formatted time range for this pickup slot.
     */
    public function getTimeRange(): string
    {
        return $this->start_time->format('H:i').' - '.$this->end_time->format('H:i');
    }

    /**
     * Get the display name for this pickup slot.
     */
    public function getDisplayNameAttribute(): string
    {
        return $this->getDayName().' '.$this->getTimeRange();
    }

    /**
     * Get current orders count for today.
     */
    public function getCurrentOrdersCount(): int
    {
        return $this->orders()
            ->whereDate('scheduled_pickup_time', today())
            ->count();
    }

    /**
     * Get orders count for a specific date.
     */
    public function getOrdersCountForDate(string $date): int
    {
        return $this->orders()
            ->whereDate('scheduled_pickup_time', $date)
            ->count();
    }

    /**
     * Check if the slot is available for a specific date.
     */
    public function isAvailableForDate(string $date): bool
    {
        if (! $this->is_active) {
            return false;
        }

        if (! $this->hasOrderLimit()) {
            return true;
        }

        return $this->getOrdersCountForDate($date) < $this->max_orders;
    }

    /**
     * Get remaining capacity for today.
     */
    public function getRemainingCapacity(): ?int
    {
        if (! $this->hasOrderLimit()) {
            return null; // Unlimited capacity
        }

        return max(0, $this->max_orders - $this->getCurrentOrdersCount());
    }

    /**
     * Get remaining capacity for a specific date.
     */
    public function getRemainingCapacityForDate(string $date): ?int
    {
        if (! $this->hasOrderLimit()) {
            return null; // Unlimited capacity
        }

        return max(0, $this->max_orders - $this->getOrdersCountForDate($date));
    }

    /**
     * Check if the pickup slot is currently open.
     */
    public function isCurrentlyOpen(): bool
    {
        $now = now();

        if ($now->dayOfWeek !== $this->day_of_week) {
            return false;
        }

        $currentTime = $now->format('H:i:s');

        return $currentTime >= $this->start_time->format('H:i:s') && $currentTime <= $this->end_time->format('H:i:s');
    }

    /**
     * Get the next occurrence of this pickup slot.
     */
    public function getNextOccurrence(): \Carbon\Carbon
    {
        $now = now();
        $targetDay = $this->day_of_week;

        // Get the next occurrence of this day
        $nextDate = $now->copy();

        if ($now->dayOfWeek === $targetDay && $now->format('H:i:s') < $this->start_time->format('H:i:s')) {
            // Today but before start time
            $nextDate = $nextDate->setTimeFromTimeString($this->start_time->format('H:i:s'));
        } else {
            // Next occurrence of this day
            $nextDate = $nextDate->next($targetDay)->setTimeFromTimeString($this->start_time->format('H:i:s'));
        }

        return $nextDate;
    }

    /**
     * Book an order for this pickup slot.
     */
    public function bookOrder(string $orderId, string $pickupDate): bool
    {
        if (! $this->isAvailableForDate($pickupDate)) {
            return false;
        }

        $updated = Order::where('id', $orderId)->update([
            'pickup_slot_id' => $this->id,
            'scheduled_pickup_time' => $pickupDate.' '.$this->start_time->format('H:i:s'),
        ]);

        return $updated > 0;
    }

    /**
     * Get pickup slot summary for API responses.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'day_of_week' => $this->day_of_week,
            'day_name' => $this->getDayName(),
            'start_time' => $this->start_time->format('H:i'),
            'end_time' => $this->end_time->format('H:i'),
            'time_range' => $this->getTimeRange(),
            'display_name' => $this->display_name,
            'max_orders' => $this->max_orders,
            'has_limit' => $this->hasOrderLimit(),
            'is_active' => $this->is_active,
            'current_orders_count' => $this->getCurrentOrdersCount(),
            'remaining_capacity' => $this->getRemainingCapacity(),
            'is_currently_open' => $this->isCurrentlyOpen(),
            'next_occurrence' => $this->getNextOccurrence(),
        ];
    }

    /**
     * Get pickup slots for a specific business and date.
     */
    public static function getAvailableForBusinessAndDate(string $businessId, string $date): \Illuminate\Database\Eloquent\Collection
    {
        $dayOfWeek = \Carbon\Carbon::parse($date)->dayOfWeek;

        return static::where('business_id', $businessId)
            ->where('day_of_week', $dayOfWeek)
            ->active()
            ->ordered()
            ->get()
            ->filter(function ($slot) use ($date) {
                return $slot->isAvailableForDate($date);
            });
    }

    /**
     * Generate weekly schedule for a business.
     */
    public static function getWeeklySchedule(string $businessId, ?string $branchId = null): array
    {
        $query = static::where('business_id', $businessId)->active();

        if ($branchId) {
            $query->where('business_branch_id', $branchId);
        }

        $slots = $query->ordered()->get();

        $schedule = [];
        for ($day = 0; $day <= 6; $day++) {
            $daySlots = $slots->where('day_of_week', $day);
            $schedule[$day] = [
                'day_name' => $daySlots->first()?->getDayName() ?? ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][$day],
                'slots' => $daySlots->map(function ($slot) {
                    return $slot->getSummary();
                })->toArray(),
            ];
        }

        return $schedule;
    }
}
