<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use App\Models\Business\Product;
use App\Models\Business\ProductVariant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $tenant_id FK to tenants.id (matches order's business_tenant_id)
 * @property string $order_id
 * @property string $product_id
 * @property string|null $variant_id
 * @property int $quantity
 * @property string $name Product name at time of order (denormalized)
 * @property string|null $variant_name Variant name at time of order, nullable (denormalized)
 * @property numeric $price_per_unit Price at time of order (denormalized)
 * @property numeric|null $unit_cost Cost per unit at time of order (denormalized from product/variant cost_price)
 * @property numeric $total_price
 * @property string|null $notes Item-specific notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $display_name
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\OrderItemOption> $options
 * @property-read int|null $options_count
 * @property-read \App\Models\Delivery\Order $order
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\OrderItemOption> $orderItemOptions
 * @property-read int|null $order_item_options_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem forProduct(string $productId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem forVariant(string $variantId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem wherePricePerUnit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereProductId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereQuantity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereTotalPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereUnitCost($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereVariantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItem whereVariantName($value)
 *
 * @property-read Product $product
 * @property-read ProductVariant|null $variant
 *
 * @mixin \Eloquent
 */
class OrderItem extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'order_id',
        'product_id',
        'variant_id',
        'quantity',
        'name',
        'variant_name',
        'price_per_unit',
        'unit_cost',
        'total_price',
        'notes',
    ];

    protected $casts = [
        'quantity' => 'integer',
        'price_per_unit' => 'decimal:2',
        'unit_cost' => 'decimal:2',
        'total_price' => 'decimal:2',
    ];

    /**
     * Get the order that owns the order item.
     *
     * @return BelongsTo<Order,OrderItem>
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    /**
     * Get the product for this order item.
     *
     * @return BelongsTo<Product,OrderItem>
     */
    public function product(): BelongsTo
    {
        return $this->belongsTo(Product::class, 'product_id');
    }

    /**
     * Get the product variant for this order item.
     *
     * @return BelongsTo<ProductVariant,OrderItem>
     */
    public function variant(): BelongsTo
    {
        return $this->belongsTo(ProductVariant::class, 'variant_id');
    }

    /**
     * Get the order item options for this order item.
     *
     * @return HasMany<OrderItemOption,OrderItem>
     */
    public function orderItemOptions(): HasMany
    {
        return $this->hasMany(OrderItemOption::class, 'order_item_id');
    }

    /**
     * Get the options for this order item (alias).
     *
     * @return HasMany<OrderItemOption,OrderItem>
     */
    public function options(): HasMany
    {
        return $this->orderItemOptions();
    }

    /**
     * Scope to get order items by product.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForProduct($query, string $productId)
    {
        return $query->where('product_id', $productId);
    }

    /**
     * Scope to get order items by variant.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForVariant($query, string $variantId)
    {
        return $query->where('variant_id', $variantId);
    }

    /**
     * Get the profit for this order item.
     */
    public function getProfit(): float
    {
        if (is_null($this->unit_cost)) {
            return 0;
        }

        return ($this->price_per_unit - $this->unit_cost) * $this->quantity;
    }

    /**
     * Get the profit margin percentage.
     */
    public function getProfitMargin(): ?float
    {
        if (is_null($this->unit_cost) || $this->price_per_unit == 0) {
            return null;
        }

        return (($this->price_per_unit - $this->unit_cost) / $this->price_per_unit) * 100;
    }

    /**
     * Get the formatted total price with currency.
     */
    public function getFormattedTotalPrice(string $currency = 'NGN'): string
    {
        return $currency.' '.number_format($this->total_price, 2);
    }

    /**
     * Get the formatted unit price with currency.
     */
    public function getFormattedUnitPrice(string $currency = 'NGN'): string
    {
        return $currency.' '.number_format($this->price_per_unit, 2);
    }

    /**
     * Get the display name for the order item.
     */
    public function getDisplayNameAttribute(): string
    {
        if ($this->variant_name) {
            return $this->name.' ('.$this->variant_name.')';
        }

        return $this->name;
    }

    /**
     * Calculate and update the total price.
     */
    public function calculateTotalPrice(): bool
    {
        $optionsTotal = $this->orderItemOptions()->sum('price');
        $itemTotal = $this->price_per_unit * $this->quantity;
        $total = $itemTotal + ($optionsTotal * $this->quantity);

        return $this->update(['total_price' => $total]);
    }

    /**
     * Get the total options price.
     */
    public function getOptionsTotal(): float
    {
        return $this->orderItemOptions()->sum('price');
    }

    /**
     * Check if the order item has options.
     */
    public function hasOptions(): bool
    {
        return $this->orderItemOptions()->exists();
    }

    /**
     * Get the order item summary for API responses.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'product_name' => $this->name,
            'variant_name' => $this->variant_name,
            'display_name' => $this->display_name,
            'quantity' => $this->quantity,
            'price_per_unit' => $this->price_per_unit,
            'total_price' => $this->total_price,
            'options_count' => $this->orderItemOptions()->count(),
            'has_notes' => ! empty($this->notes),
            'notes' => $this->notes,
        ];
    }

    /**
     * Update the quantity and recalculate total.
     */
    public function updateQuantity(int $quantity): bool
    {
        $this->quantity = $quantity;
        $this->calculateTotalPrice();

        return $this->save();
    }
}
