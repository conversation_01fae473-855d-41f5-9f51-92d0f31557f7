<?php

namespace App\Models\Delivery;

use App\Models\Business\ProductOption;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $tenant_id FK to tenants.id (matches order_item's tenant_id)
 * @property string $order_item_id
 * @property string $product_option_id
 * @property string $name Option name at time of order (denormalized)
 * @property numeric $price_adjustment Price adjustment at time of order (denormalized, in order currency)
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Delivery\OrderItem $orderItem
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItemOption newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItemOption newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItemOption query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItemOption whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItemOption whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItemOption whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItemOption whereOrderItemId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItemOption wherePriceAdjustment($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItemOption whereProductOptionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItemOption whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderItemOption whereUpdatedAt($value)
 *
 * @property-read ProductOption $productOption
 *
 * @mixin \Eloquent
 */
class OrderItemOption extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'id',
        'tenant_id',
        'order_item_id',
        'product_option_id',
        'name',
        'price_adjustment',
    ];

    protected $casts = [
        'price_adjustment' => 'decimal:2',
    ];

    public function orderItem(): BelongsTo
    {
        return $this->belongsTo(OrderItem::class, 'order_item_id');
    }

    public function productOption(): BelongsTo
    {
        return $this->belongsTo(ProductOption::class, 'product_option_id');
    }
}
