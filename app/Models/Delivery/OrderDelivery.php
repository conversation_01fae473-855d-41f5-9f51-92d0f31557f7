<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use App\Enums\Delivery\DeliveryStatus;
use App\Models\User\Address;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $order_id
 * @property string|null $user_delivery_id
 * @property DeliveryStatus $status
 * @property string|null $delivery_provider_id
 * @property string|null $driver_id
 * @property string $pickup_address_id
 * @property string $delivery_address_id
 * @property \Illuminate\Support\Carbon|null $scheduled_pickup_time
 * @property \Illuminate\Support\Carbon|null $actual_pickup_time
 * @property \Illuminate\Support\Carbon|null $actual_delivery_time
 * @property string|null $failure_reason
 * @property array<array-key, mixed>|null $proof_of_delivery Photos, signatures, etc.
 * @property array<array-key, mixed>|null $route_polyline GPS route coordinates
 * @property string|null $notes
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\Delivery> $deliveries
 * @property-read int|null $deliveries_count
 * @property-read \App\Models\User\Address $deliveryAddress
 * @property-read \App\Models\Delivery\DeliveryProvider|null $deliveryProvider
 * @property-read \App\Models\User\User|null $driver
 * @property-read \App\Models\Delivery\Order $order
 * @property-read \App\Models\User\Address $pickupAddress
 * @property-read \App\Models\Delivery\UserDelivery|null $userDelivery
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery whereActualDeliveryTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery whereActualPickupTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery whereDeliveryAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery whereDeliveryProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery whereFailureReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery wherePickupAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery whereProofOfDelivery($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery whereRoutePolyline($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery whereScheduledPickupTime($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|OrderDelivery whereUserDeliveryId($value)
 *
 * @mixin \Eloquent
 */
class OrderDelivery extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'id',
        'order_id',
        'user_delivery_id',
        'status',
        'delivery_provider_id',
        'driver_id',
        'pickup_address_id',
        'delivery_address_id',
        'scheduled_pickup_time',
        'actual_pickup_time',
        'actual_delivery_time',
        'failure_reason',
        'proof_of_delivery',
        'route_polyline',
        'notes',
    ];

    protected $casts = [
        'status' => DeliveryStatus::class,
        'scheduled_pickup_time' => 'datetime',
        'actual_pickup_time' => 'datetime',
        'actual_delivery_time' => 'datetime',
        'proof_of_delivery' => 'array',
        'route_polyline' => 'array',
    ];

    /**
     * Get the order for this delivery.
     *
     * @return BelongsTo<Order,OrderDelivery>
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    /**
     * Get the user delivery for this order delivery.
     *
     * @return BelongsTo<UserDelivery,OrderDelivery>
     */
    public function userDelivery(): BelongsTo
    {
        return $this->belongsTo(UserDelivery::class, 'user_delivery_id');
    }

    /**
     * Get the deliveries for this order delivery.
     *
     * @return HasMany<Delivery,OrderDelivery>
     */
    public function deliveries(): HasMany
    {
        return $this->hasMany(Delivery::class, 'order_delivery_id');
    }

    /**
     * Get the delivery provider for this order delivery.
     *
     * @return BelongsTo<DeliveryProvider,OrderDelivery>
     */
    public function deliveryProvider(): BelongsTo
    {
        return $this->belongsTo(DeliveryProvider::class, 'delivery_provider_id');
    }

    /**
     * Get the driver for this order delivery.
     *
     * @return BelongsTo<User,OrderDelivery>
     */
    public function driver(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'driver_id');
    }

    /**
     * Get the pickup address for this order delivery.
     *
     * @return BelongsTo<Address,OrderDelivery>
     */
    public function pickupAddress(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\Address::class, 'pickup_address_id');
    }

    /**
     * Get the delivery address for this order delivery.
     *
     * @return BelongsTo<Address,OrderDelivery>
     */
    public function deliveryAddress(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\Address::class, 'delivery_address_id');
    }
}
