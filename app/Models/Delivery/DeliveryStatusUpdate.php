<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $tenant_id FK to tenants.id
 * @property string $delivery_id
 * @property string|null $updated_by_user_id
 * @property string|null $previous_status
 * @property string $new_status
 * @property string|null $status_reason Reason for status change
 * @property array<array-key, mixed>|null $location_data GPS coordinates when status was updated
 * @property array<array-key, mixed>|null $metadata Additional context data
 * @property bool $customer_notified
 * @property bool $business_notified
 * @property \Illuminate\Support\Carbon $status_changed_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Delivery\Delivery $delivery
 * @property-read \App\Models\System\Tenant $tenant
 * @property-read \App\Models\User\User|null $updatedByUser
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryStatusUpdate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryStatusUpdate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryStatusUpdate query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryStatusUpdate whereBusinessNotified($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryStatusUpdate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryStatusUpdate whereCustomerNotified($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryStatusUpdate whereDeliveryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryStatusUpdate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryStatusUpdate whereLocationData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryStatusUpdate whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryStatusUpdate whereNewStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryStatusUpdate wherePreviousStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryStatusUpdate whereStatusChangedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryStatusUpdate whereStatusReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryStatusUpdate whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryStatusUpdate whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryStatusUpdate whereUpdatedByUserId($value)
 *
 * @mixin \Eloquent
 */
class DeliveryStatusUpdate extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'delivery_id',
        'updated_by_user_id',
        'previous_status',
        'new_status',
        'status_reason',
        'location_data',
        'metadata',
        'customer_notified',
        'business_notified',
        'status_changed_at',
    ];

    protected $casts = [
        'location_data' => 'array',
        'metadata' => 'array',
        'customer_notified' => 'boolean',
        'business_notified' => 'boolean',
        'status_changed_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the delivery status update.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the delivery associated with the status update.
     */
    public function delivery(): BelongsTo
    {
        return $this->belongsTo(Delivery::class);
    }

    /**
     * Get the user who updated the status.
     */
    public function updatedByUser(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'updated_by_user_id');
    }
}
