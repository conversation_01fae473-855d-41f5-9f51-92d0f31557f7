<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use App\Enums\Delivery\VehicleType;
use App\Models\Core\State;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $from_state_id
 * @property string $to_state_id
 * @property VehicleType $vehicle_type
 * @property float $base_price
 * @property float $price_per_km
 * @property float $minimum_price
 * @property float|null $maximum_price
 * @property float|null $estimated_distance_km
 * @property int|null $estimated_duration_minutes
 * @property array<array-key, mixed>|null $route_waypoints
 * @property bool $is_active
 * @property array<array-key, mixed>|null $operational_config
 * @property int $delivery_count
 * @property float|null $average_actual_distance
 * @property int|null $average_actual_duration
 * @property \Illuminate\Support\Carbon|null $last_delivery_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read State $fromState
 * @property-read State $toState
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing forRoute(string $fromStateId, string $toStateId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing forVehicleType(VehicleType $vehicleType)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing whereAverageActualDistance($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing whereAverageActualDuration($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing whereBasePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing whereDeliveryCount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing whereEstimatedDistanceKm($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing whereEstimatedDurationMinutes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing whereFromStateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing whereLastDeliveryAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing whereMaximumPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing whereMinimumPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing whereOperationalConfig($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing wherePricePerKm($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing whereRouteWaypoints($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing whereToStateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|InterstatePricing whereVehicleType($value)
 * @method static \Database\Factories\InterstatePricingFactory factory($count = null, $state = [])
 *
 * @mixin \Eloquent
 */
class InterstatePricing extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'interstate_pricing';

    protected $fillable = [
        'from_state_id',
        'to_state_id',
        'vehicle_type',
        'base_price',
        'price_per_km',
        'minimum_price',
        'maximum_price',
        'estimated_distance_km',
        'estimated_duration_minutes',
        'route_waypoints',
        'is_active',
        'operational_config',
        'delivery_count',
        'average_actual_distance',
        'average_actual_duration',
        'last_delivery_at',
    ];

    protected $casts = [
        'vehicle_type' => VehicleType::class,
        'base_price' => 'decimal:2',
        'price_per_km' => 'decimal:2',
        'minimum_price' => 'decimal:2',
        'maximum_price' => 'decimal:2',
        'estimated_distance_km' => 'decimal:2',
        'estimated_duration_minutes' => 'integer',
        'route_waypoints' => 'array',
        'is_active' => 'boolean',
        'operational_config' => 'array',
        'delivery_count' => 'integer',
        'average_actual_distance' => 'decimal:2',
        'average_actual_duration' => 'integer',
        'last_delivery_at' => 'datetime',
    ];

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): \Database\Factories\InterstatePricingFactory
    {
        return \Database\Factories\InterstatePricingFactory::new();
    }

    /**
     * Get the origin state for this pricing.
     *
     * @return BelongsTo<State,InterstatePricing>
     */
    public function fromState(): BelongsTo
    {
        return $this->belongsTo(State::class, 'from_state_id');
    }

    /**
     * Get the destination state for this pricing.
     *
     * @return BelongsTo<State,InterstatePricing>
     */
    public function toState(): BelongsTo
    {
        return $this->belongsTo(State::class, 'to_state_id');
    }

    /**
     * Scope to get only active pricing.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by route.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForRoute($query, string $fromStateId, string $toStateId)
    {
        return $query->where('from_state_id', $fromStateId)
            ->where('to_state_id', $toStateId);
    }

    /**
     * Scope to filter by vehicle type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForVehicleType($query, VehicleType $vehicleType)
    {
        return $query->where('vehicle_type', $vehicleType);
    }

    /**
     * Check if the pricing is active.
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Calculate price for a given distance.
     */
    public function calculatePrice(float $distanceKm): float
    {
        $price = $this->base_price + ($distanceKm * $this->price_per_km);

        // Apply minimum price
        $price = max($price, $this->minimum_price);

        // Apply maximum price if set
        if ($this->maximum_price) {
            $price = min($price, $this->maximum_price);
        }

        return round($price, 2);
    }

    /**
     * Get estimated price using the estimated distance.
     */
    public function getEstimatedPrice(): float
    {
        if (! $this->estimated_distance_km) {
            return $this->minimum_price;
        }

        return $this->calculatePrice($this->estimated_distance_km);
    }

    /**
     * Update delivery statistics.
     */
    public function updateDeliveryStats(float $actualDistance, int $actualDuration): void
    {
        $this->increment('delivery_count');

        // Calculate new average distance
        if ($this->average_actual_distance === null) {
            $this->average_actual_distance = $actualDistance;
        } else {
            $this->average_actual_distance = (($this->average_actual_distance * ($this->delivery_count - 1)) + $actualDistance) / $this->delivery_count;
        }

        // Calculate new average duration
        if ($this->average_actual_duration === null) {
            $this->average_actual_duration = $actualDuration;
        } else {
            $this->average_actual_duration = (int) round((($this->average_actual_duration * ($this->delivery_count - 1)) + $actualDuration) / $this->delivery_count);
        }

        $this->last_delivery_at = now();
        $this->save();
    }

    /**
     * Get route description.
     */
    public function getRouteDescription(): string
    {
        return sprintf('%s to %s', $this->fromState->name, $this->toState->name);
    }

    /**
     * Get vehicle type label.
     */
    public function getVehicleTypeLabel(): string
    {
        return $this->vehicle_type->getLabel();
    }

    /**
     * Get pricing summary.
     */
    public function getPricingSummary(): array
    {
        return [
            'route' => $this->getRouteDescription(),
            'vehicle_type' => $this->getVehicleTypeLabel(),
            'base_price' => $this->base_price,
            'price_per_km' => $this->price_per_km,
            'minimum_price' => $this->minimum_price,
            'maximum_price' => $this->maximum_price,
            'estimated_price' => $this->getEstimatedPrice(),
            'estimated_distance' => $this->estimated_distance_km,
            'estimated_duration' => $this->estimated_duration_minutes,
        ];
    }

    /**
     * Get performance metrics.
     */
    public function getPerformanceMetrics(): array
    {
        return [
            'delivery_count' => $this->delivery_count,
            'average_actual_distance' => $this->average_actual_distance,
            'average_actual_duration' => $this->average_actual_duration,
            'distance_accuracy' => $this->getDistanceAccuracy(),
            'duration_accuracy' => $this->getDurationAccuracy(),
            'last_delivery_at' => $this->last_delivery_at,
        ];
    }

    /**
     * Get distance estimation accuracy percentage.
     */
    public function getDistanceAccuracy(): ?float
    {
        if (! $this->estimated_distance_km || ! $this->average_actual_distance) {
            return null;
        }

        $accuracy = 100 - (abs($this->estimated_distance_km - $this->average_actual_distance) / $this->estimated_distance_km * 100);

        return max(0, round($accuracy, 2));
    }

    /**
     * Get duration estimation accuracy percentage.
     */
    public function getDurationAccuracy(): ?float
    {
        if (! $this->estimated_duration_minutes || ! $this->average_actual_duration) {
            return null;
        }

        $accuracy = 100 - (abs($this->estimated_duration_minutes - $this->average_actual_duration) / $this->estimated_duration_minutes * 100);

        return max(0, round($accuracy, 2));
    }

    /**
     * Check if pricing needs review based on performance.
     */
    public function needsReview(): bool
    {
        // Review if distance accuracy is below 80%
        $distanceAccuracy = $this->getDistanceAccuracy();
        if ($distanceAccuracy !== null && $distanceAccuracy < 80) {
            return true;
        }

        // Review if duration accuracy is below 70%
        $durationAccuracy = $this->getDurationAccuracy();
        if ($durationAccuracy !== null && $durationAccuracy < 70) {
            return true;
        }

        // Review if no deliveries in the last 6 months
        if ($this->last_delivery_at && $this->last_delivery_at->lt(now()->subMonths(6))) {
            return true;
        }

        return false;
    }

    /**
     * Get operational configuration value.
     */
    public function getOperationalConfig(string $key, mixed $default = null): mixed
    {
        return data_get($this->operational_config, $key, $default);
    }

    /**
     * Set operational configuration value.
     */
    public function setOperationalConfig(string $key, mixed $value): void
    {
        $config = $this->operational_config ?? [];
        data_set($config, $key, $value);
        $this->operational_config = $config;
        $this->save();
    }
}
