<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use App\Enums\Delivery\VehicleStatus;
use App\Enums\Delivery\VehicleType;
use App\Models\System\Tenant;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $provider_id
 * @property mixed $type
 * @property string|null $license_plate
 * @property string|null $driver_id
 * @property mixed $status
 * @property numeric|null $capacity Weight capacity in kg
 * @property \Illuminate\Support\Carbon|null $last_maintenance_date
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Delivery\Delivery> $deliveries
 * @property-read int|null $deliveries_count
 * @property-read \App\Models\User\User|null $driver
 * @property-read int|null $days_since_maintenance
 * @property-read string $display_name
 * @property-read string|null $formatted_capacity
 * @property-read \App\Models\Delivery\DeliveryProvider $provider
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle available()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle byType(\App\Enums\VehicleType $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle inUse()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle needsMaintenance(int $days = 90)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle underMaintenance()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle whereCapacity($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle whereDriverId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle whereLastMaintenanceDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle whereLicensePlate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle whereProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle withDriver()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Vehicle withoutDriver()
 *
 * @mixin \Eloquent
 */
class Vehicle extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'provider_id',
        'type',
        'license_plate',
        'driver_id',
        'status',
        'capacity',
        'last_maintenance_date',
    ];

    protected $casts = [
        'type' => VehicleType::class,
        'status' => VehicleStatus::class,
        'capacity' => 'decimal:2',
        'last_maintenance_date' => 'datetime',
    ];

    /**
     * Get the tenant that owns the vehicle.
     *
     * @return BelongsTo<Tenant,Vehicle>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the delivery provider that owns the vehicle.
     *
     * @return BelongsTo<DeliveryProvider,Vehicle>
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(DeliveryProvider::class, 'provider_id');
    }

    /**
     * Get the driver assigned to the vehicle.
     *
     * @return BelongsTo<User,Vehicle>
     */
    public function driver(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'driver_id');
    }

    /**
     * Get the deliveries using this vehicle.
     *
     * @return HasMany<Delivery,Vehicle>
     */
    public function deliveries(): HasMany
    {
        return $this->hasMany(Delivery::class, 'vehicle_id');
    }

    /**
     * Scope to get available vehicles.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAvailable($query)
    {
        return $query->where('status', VehicleStatus::AVAILABLE);
    }

    /**
     * Scope to get active vehicles.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->whereIn('status', [VehicleStatus::AVAILABLE, VehicleStatus::IN_USE]);
    }

    /**
     * Scope to get vehicles in use.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeInUse($query)
    {
        return $query->where('status', VehicleStatus::IN_USE);
    }

    /**
     * Scope to get vehicles under maintenance.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUnderMaintenance($query)
    {
        return $query->where('status', VehicleStatus::MAINTENANCE);
    }

    /**
     * Scope to get vehicles by type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByType($query, VehicleTypE $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get vehicles with assigned drivers.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithDriver($query)
    {
        return $query->whereNotNull('driver_id');
    }

    /**
     * Scope to get vehicles without assigned drivers.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithoutDriver($query)
    {
        return $query->whereNull('driver_id');
    }

    /**
     * Scope to get vehicles that need maintenance.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeNeedsMaintenance($query, int $days = 90)
    {
        return $query->where(function ($query) use ($days) {
            $query->whereNull('last_maintenance_date')
                ->orWhere('last_maintenance_date', '<=', now()->subDays($days));
        });
    }

    /**
     * Check if the vehicle is available.
     */
    public function isAvailable(): bool
    {
        return $this->status === VehicleStatus::AVAILABLE;
    }

    /**
     * Check if the vehicle is in use.
     */
    public function isInUse(): bool
    {
        return $this->status === VehicleStatus::IN_USE;
    }

    /**
     * Check if the vehicle is under maintenance.
     */
    public function isUnderMaintenance(): bool
    {
        return $this->status === VehicleStatus::MAINTENANCE;
    }

    /**
     * Check if the vehicle is out of service.
     */
    public function isOutOfService(): bool
    {
        return $this->status === VehicleStatus::OUT_OF_SERVICE;
    }

    /**
     * Check if the vehicle has a driver assigned.
     */
    public function hasDriver(): bool
    {
        return ! is_null($this->driver_id);
    }

    /**
     * Check if the vehicle needs maintenance.
     */
    public function needsMaintenance(int $days = 90): bool
    {
        if (is_null($this->last_maintenance_date)) {
            return true;
        }

        return $this->last_maintenance_date->addDays($days)->isPast();
    }

    /**
     * Assign a driver to the vehicle.
     */
    public function assignDriver(string $driverId): bool
    {
        return $this->update(['driver_id' => $driverId]);
    }

    /**
     * Unassign the driver from the vehicle.
     */
    public function unassignDriver(): bool
    {
        return $this->update(['driver_id' => null]);
    }

    /**
     * Mark the vehicle as in use.
     */
    public function markAsInUse(): bool
    {
        return $this->update(['status' => VehicleStatus::IN_USE]);
    }

    /**
     * Mark the vehicle as available.
     */
    public function markAsAvailable(): bool
    {
        return $this->update(['status' => VehicleStatus::AVAILABLE]);
    }

    /**
     * Mark the vehicle as under maintenance.
     */
    public function markAsUnderMaintenance(): bool
    {
        return $this->update([
            'status' => VehicleStatus::MAINTENANCE,
            'last_maintenance_date' => now(),
        ]);
    }

    /**
     * Mark the vehicle as out of service.
     */
    public function markAsOutOfService(): bool
    {
        return $this->update(['status' => VehicleStatus::OUT_OF_SERVICE]);
    }

    /**
     * Get the vehicle's display name.
     */
    public function getDisplayNameAttribute(): string
    {
        $typeName = $this->type->value;

        if ($this->license_plate) {
            return "{$typeName} ({$this->license_plate})";
        }

        return $typeName;
    }

    /**
     * Get the vehicle's status color class.
     */
    public function getStatusColorClass(): string
    {
        return match ($this->status) {
            VehicleStatus::AVAILABLE => 'text-green-600',
            VehicleStatus::IN_USE => 'text-blue-600',
            VehicleStatus::MAINTENANCE => 'text-yellow-600',
            VehicleStatus::OUT_OF_SERVICE => 'text-red-600',
            default => 'text-gray-600',
        };
    }

    /**
     * Get the vehicle's status icon.
     */
    public function getStatusIcon(): string
    {
        return match ($this->status) {
            VehicleStatus::AVAILABLE => 'fas fa-check-circle',
            VehicleStatus::IN_USE => 'fas fa-shipping-fast',
            VehicleStatus::MAINTENANCE => 'fas fa-tools',
            VehicleStatus::OUT_OF_SERVICE => 'fas fa-exclamation-triangle',
            default => 'fas fa-question-circle',
        };
    }

    /**
     * Get the vehicle type icon.
     */
    public function getTypeIcon(): string
    {
        return match ($this->type) {
            VehicleType::BICYCLE => 'fas fa-bicycle',
            VehicleType::MOTORCYCLE => 'fas fa-motorcycle',
            VehicleType::CAR => 'fas fa-car',
            VehicleType::VAN => 'fas fa-shuttle-van',
            VehicleType::TRUCK => 'fas fa-truck',
            default => 'fas fa-car',
        };
    }

    /**
     * Get the days since last maintenance.
     */
    public function getDaysSinceMaintenanceAttribute(): ?int
    {
        if (is_null($this->last_maintenance_date)) {
            return null;
        }

        return (int) $this->last_maintenance_date->diffInDays(now());
    }

    /**
     * Get the formatted capacity.
     */
    public function getFormattedCapacityAttribute(): ?string
    {
        if (is_null($this->capacity)) {
            return null;
        }

        return $this->capacity.' kg';
    }

    /**
     * Get current delivery count.
     */
    public function getCurrentDeliveryCount(): int
    {
        return $this->deliveries()
            ->whereIn('status', ['assigned', 'picked_up', 'in_transit'])
            ->count();
    }

    /**
     * Get total completed deliveries.
     */
    public function getTotalCompletedDeliveries(): int
    {
        return $this->deliveries()
            ->where('status', 'delivered')
            ->count();
    }

    /**
     * Get vehicle utilization rate.
     */
    public function getUtilizationRate(int $days = 30): float
    {
        $totalDays = $days;
        $maintenanceDays = $this->deliveries()
            ->where('created_at', '>=', now()->subDays($days))
            ->whereDate('created_at', $this->last_maintenance_date)
            ->count();

        $usageDays = $this->deliveries()
            ->where('created_at', '>=', now()->subDays($days))
            ->distinct('created_at')
            ->count();

        $availableDays = $totalDays - $maintenanceDays;

        if ($availableDays <= 0) {
            return 0;
        }

        return ($usageDays / $availableDays) * 100;
    }

    /**
     * Check if vehicle can handle a specific capacity.
     */
    public function canHandle(float $requiredCapacity): bool
    {
        if (is_null($this->capacity)) {
            return true; // Assume unlimited capacity if not specified
        }

        return $this->capacity >= $requiredCapacity;
    }

    /**
     * Get vehicle summary for API responses.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'display_name' => $this->display_name,
            'type' => $this->type->value,
            'license_plate' => $this->license_plate,
            'status' => $this->status->value,
            'capacity' => $this->formatted_capacity,
            'has_driver' => $this->hasDriver(),
            'driver_name' => $this->driver?->name,
            'status_color' => $this->getStatusColorClass(),
            'status_icon' => $this->getStatusIcon(),
            'type_icon' => $this->getTypeIcon(),
            'needs_maintenance' => $this->needsMaintenance(),
            'days_since_maintenance' => $this->days_since_maintenance,
            'current_deliveries' => $this->getCurrentDeliveryCount(),
        ];
    }
}
