<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use App\Enums\Delivery\ZoneType;
use App\Models\Core\City;
use App\Models\Core\State;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * @property string $id
 * @property string $name
 * @property ZoneType $zone_type
 * @property array<array-key, mixed>|null $states
 * @property array<array-key, mixed>|null $cities
 * @property array<array-key, mixed>|null $polygon_coordinates
 * @property float $base_multiplier
 * @property bool $is_active
 * @property string|null $description
 * @property array<array-key, mixed>|null $metadata
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, DeliveryProvider> $providers
 * @property-read int|null $providers_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryZone newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryZone newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryZone query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryZone active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryZone byType(ZoneType $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryZone coveringState(string $stateId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryZone coveringCity(string $cityId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryZone whereBaseMultiplier($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryZone whereCities($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryZone whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryZone whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryZone whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryZone whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryZone whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryZone whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryZone wherePolygonCoordinates($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryZone whereStates($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryZone whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryZone whereZoneType($value)
 * @method static \Database\Factories\DeliveryZoneFactory factory($count = null, $state = [])
 *
 * @mixin \Eloquent
 */
class DeliveryZone extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'zone_type',
        'states',
        'cities',
        'polygon_coordinates',
        'base_multiplier',
        'is_active',
        'description',
        'metadata',
    ];

    protected $casts = [
        'zone_type' => ZoneType::class,
        'states' => 'array',
        'cities' => 'array',
        'polygon_coordinates' => 'array',
        'base_multiplier' => 'decimal:2',
        'is_active' => 'boolean',
        'metadata' => 'array',
    ];

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory(): \Database\Factories\DeliveryZoneFactory
    {
        return \Database\Factories\DeliveryZoneFactory::new();
    }

    /**
     * Get the delivery providers that serve this zone.
     *
     * @return BelongsToMany<DeliveryProvider,DeliveryZone>
     */
    public function providers(): BelongsToMany
    {
        return $this->belongsToMany(DeliveryProvider::class, 'provider_service_areas', 'zone_id', 'provider_id')
            ->withPivot(['pricing_multiplier', 'is_active', 'service_config', 'delivery_count', 'average_rating', 'last_delivery_at'])
            ->withTimestamps();
    }

    /**
     * Scope to get only active zones.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to filter by zone type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByType($query, ZoneType $type)
    {
        return $query->where('zone_type', $type);
    }

    /**
     * Scope to get zones covering a specific state.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCoveringState($query, string $stateId)
    {
        return $query->where(function ($query) use ($stateId) {
            $query->whereJsonContains('states', $stateId)
                ->orWhere('zone_type', ZoneType::INTERSTATE->value);
        });
    }

    /**
     * Scope to get zones covering a specific city.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCoveringCity($query, string $cityId)
    {
        return $query->where(function ($query) use ($cityId) {
            $query->whereJsonContains('cities', $cityId)
                ->orWhere('zone_type', ZoneType::STATE->value)
                ->orWhere('zone_type', ZoneType::INTERSTATE->value);
        });
    }

    /**
     * Check if the zone is active.
     */
    public function isActive(): bool
    {
        return $this->is_active;
    }

    /**
     * Check if zone covers a specific state.
     */
    public function coversState(string $stateId): bool
    {
        if (! $this->states) {
            return false;
        }

        return in_array($stateId, $this->states, true);
    }

    /**
     * Check if zone covers a specific city.
     */
    public function coversCity(string $cityId): bool
    {
        if (! $this->cities) {
            return false;
        }

        return in_array($cityId, $this->cities, true);
    }

    /**
     * Check if zone can serve a route between two states.
     */
    public function canServeRoute(string $fromStateId, string $toStateId): bool
    {
        return match ($this->zone_type) {
            ZoneType::CITY => $fromStateId === $toStateId && $this->coversState($fromStateId),
            ZoneType::STATE => $fromStateId === $toStateId && $this->coversState($fromStateId),
            ZoneType::INTERSTATE => $this->coversState($fromStateId) && $this->coversState($toStateId),
            ZoneType::POLYGON => true, // Polygon zones would need geometric calculation
        };
    }

    /**
     * Get zone type label.
     */
    public function getZoneTypeLabel(): string
    {
        return $this->zone_type->getLabel();
    }

    /**
     * Get zone complexity level.
     */
    public function getComplexityLevel(): string
    {
        return $this->zone_type->getComplexityLevel();
    }

    /**
     * Check if zone requires map interface.
     */
    public function requiresMapInterface(): bool
    {
        return $this->zone_type->requiresMapInterface();
    }

    /**
     * Get recommended use cases for this zone.
     *
     * @return array<string>
     */
    public function getRecommendedUseCases(): array
    {
        return $this->zone_type->getRecommendedUseCases();
    }

    /**
     * Get pricing method for this zone type.
     */
    public function getPricingMethod(): string
    {
        return $this->zone_type->getPricingMethod();
    }

    /**
     * Get configuration requirements for this zone type.
     *
     * @return array<string>
     */
    public function getConfigurationRequirements(): array
    {
        return $this->zone_type->getConfigurationRequirements();
    }

    /**
     * Get the states covered by this zone as models.
     *
     * @return \Illuminate\Support\Collection<int, State>
     */
    public function getStatesAsModels(): \Illuminate\Support\Collection
    {
        if (! $this->states) {
            return collect();
        }

        return State::whereIn('id', $this->states)->get();
    }

    /**
     * Get the cities covered by this zone as models.
     *
     * @return \Illuminate\Support\Collection<int, City>
     */
    public function getCitiesAsModels(): \Illuminate\Support\Collection
    {
        if (! $this->cities) {
            return collect();
        }

        return City::whereIn('id', $this->cities)->get();
    }

    /**
     * Get zone coverage summary.
     */
    public function getCoverageSummary(): string
    {
        return match ($this->zone_type) {
            ZoneType::CITY => sprintf('%d cities', count($this->cities ?? [])),
            ZoneType::STATE => sprintf('%d states', count($this->states ?? [])),
            ZoneType::INTERSTATE => sprintf('%d states (interstate)', count($this->states ?? [])),
            ZoneType::POLYGON => 'Custom polygon area',
        };
    }

    /**
     * Get active provider count for this zone.
     */
    public function getActiveProviderCount(): int
    {
        return $this->providers()->wherePivot('is_active', true)->count();
    }

    /**
     * Get average pricing multiplier from providers.
     */
    public function getAverageProviderMultiplier(): float
    {
        $average = $this->providers()
            ->wherePivot('is_active', true)
            ->avg('provider_service_areas.pricing_multiplier');

        return round($average ?? $this->base_multiplier, 2);
    }

    /**
     * Check if zone is currently implemented.
     */
    public function isImplemented(): bool
    {
        return $this->zone_type->isImplemented();
    }

    /**
     * Get estimated setup time for this zone type.
     */
    public function getEstimatedSetupTime(): string
    {
        return $this->zone_type->getEstimatedSetupTime();
    }

    /**
     * Get performance impact level.
     */
    public function getPerformanceImpact(): string
    {
        return $this->zone_type->getPerformanceImpact();
    }
}
