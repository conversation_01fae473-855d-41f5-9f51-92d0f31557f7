<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $tenant_id FK to tenants.id
 * @property string $provider_id
 * @property \Illuminate\Support\Carbon $metric_date
 * @property string $period_type
 * @property int $total_deliveries
 * @property int $completed_deliveries
 * @property int $failed_deliveries
 * @property numeric $total_earnings
 * @property numeric $total_commission_paid
 * @property numeric|null $average_delivery_time_minutes
 * @property numeric|null $average_distance_km
 * @property numeric|null $customer_rating_average Average customer rating 1-5
 * @property numeric|null $on_time_delivery_rate Percentage of on-time deliveries
 * @property int $active_drivers
 * @property array<array-key, mixed>|null $coverage_areas Areas covered during this period
 * @property string $currency
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Delivery\DeliveryProvider $provider
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric whereActiveDrivers($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric whereAverageDeliveryTimeMinutes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric whereAverageDistanceKm($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric whereCompletedDeliveries($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric whereCoverageAreas($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric whereCustomerRatingAverage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric whereFailedDeliveries($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric whereMetricDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric whereOnTimeDeliveryRate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric wherePeriodType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric whereProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric whereTotalCommissionPaid($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric whereTotalDeliveries($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric whereTotalEarnings($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderPerformanceMetric whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class ProviderPerformanceMetric extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'provider_id',
        'metric_date',
        'period_type',
        'total_deliveries',
        'completed_deliveries',
        'failed_deliveries',
        'total_earnings',
        'total_commission_paid',
        'average_delivery_time_minutes',
        'average_distance_km',
        'customer_rating_average',
        'on_time_delivery_rate',
        'active_drivers',
        'coverage_areas',
        'currency',
    ];

    protected $casts = [
        'metric_date' => 'date',
        'total_deliveries' => 'integer',
        'completed_deliveries' => 'integer',
        'failed_deliveries' => 'integer',
        'total_earnings' => 'decimal:2',
        'total_commission_paid' => 'decimal:2',
        'average_delivery_time_minutes' => 'decimal:2',
        'average_distance_km' => 'decimal:2',
        'customer_rating_average' => 'decimal:2',
        'on_time_delivery_rate' => 'decimal:4',
        'active_drivers' => 'integer',
        'coverage_areas' => 'array',
    ];

    /**
     * Get the tenant that owns the provider performance metric.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the delivery provider associated with the performance metric.
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(DeliveryProvider::class, 'provider_id');
    }
}
