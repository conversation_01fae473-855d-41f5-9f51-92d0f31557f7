<?php

namespace App\Models\Delivery;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Stancl\Tenancy\Database\Concerns\BelongsToTenant;

/**
 * @property string $id
 * @property string $tenant_id
 * @property string $provider_id
 * @property string $name Name of the branch/depot (e.g., "Downtown Depot")
 * @property string $address_id
 * @property string|null $contact_email
 * @property string|null $contact_phone
 * @property string|null $operating_hours Specific hours for this depot/branch
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User\Address $address
 * @property-read \App\Models\Delivery\DeliveryProvider $provider
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderBranch newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderBranch newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderBranch query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderBranch whereAddressId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderBranch whereContactEmail($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderBranch whereContactPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderBranch whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderBranch whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderBranch whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderBranch whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderBranch whereOperatingHours($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderBranch whereProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderBranch whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|ProviderBranch whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class ProviderBranch extends Model
{
    use BelongsToTenant, HasFactory, HasUuids;

    protected $fillable = [
        'id',
        'tenant_id',
        'provider_id',
        'address_id',
        'name',
        'contact_email',
        'contact_phone',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    public function provider(): BelongsTo
    {
        return $this->belongsTo(DeliveryProvider::class, 'provider_id');
    }

    public function address(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\Address::class, 'address_id');
    }
}
