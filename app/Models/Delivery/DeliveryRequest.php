<?php

declare(strict_types=1);

namespace App\Models\Delivery;

use App\Enums\Delivery\DeliveryRequestStatus;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $order_id
 * @property string $provider_id
 * @property string $broadcast_id Groups requests from the same broadcast
 * @property mixed $status
 * @property \Illuminate\Support\Carbon $expires_at When this request expires
 * @property \Illuminate\Support\Carbon|null $accepted_at When provider accepted the request
 * @property \Illuminate\Support\Carbon|null $cancelled_at When request was cancelled
 * @property \Illuminate\Support\Carbon|null $expired_at When request expired
 * @property array<array-key, mixed> $request_data Order details sent to provider
 * @property array<array-key, mixed>|null $response_metadata Provider response metadata
 * @property string|null $cancellation_reason Reason for cancellation
 * @property array<array-key, mixed>|null $cancellation_metadata Additional cancellation data
 * @property string|null $expiration_reason Reason for expiration
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Delivery\Order $order
 * @property-read \App\Models\Delivery\DeliveryProvider $provider
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest accepted()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest cancelled()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest expired()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest expiringWithin(int $minutes)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest forBroadcast(string $broadcastId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest forOrder(string $orderId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest forProvider(string $providerId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest valid()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest whereAcceptedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest whereBroadcastId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest whereCancellationMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest whereCancellationReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest whereCancelledAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest whereExpirationReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest whereExpiredAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest whereProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest whereRequestData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest whereResponseMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DeliveryRequest whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class DeliveryRequest extends Model
{
    use HasFactory, HasUuids;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'order_id',
        'provider_id',
        'broadcast_id',
        'status',
        'expires_at',
        'accepted_at',
        'cancelled_at',
        'expired_at',
        'request_data',
        'response_metadata',
        'cancellation_reason',
        'cancellation_metadata',
        'expiration_reason',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'status' => DeliveryRequestStatus::class,
        'expires_at' => 'datetime',
        'accepted_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'expired_at' => 'datetime',
        'request_data' => 'array',
        'response_metadata' => 'array',
        'cancellation_metadata' => 'array',
    ];

    /**
     * Get the order this request belongs to.
     *
     * @return BelongsTo<Order,DeliveryRequest>
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class);
    }

    /**
     * Get the provider this request was sent to.
     *
     * @return BelongsTo<DeliveryProvider,DeliveryRequest>
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(DeliveryProvider::class);
    }

    /**
     * Check if the request is still pending.
     */
    public function isPending(): bool
    {
        return $this->status === DeliveryRequestStatus::PENDING;
    }

    /**
     * Check if the request was accepted.
     */
    public function isAccepted(): bool
    {
        return $this->status === DeliveryRequestStatus::ACCEPTED;
    }

    /**
     * Check if the request was cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === DeliveryRequestStatus::CANCELLED;
    }

    /**
     * Check if the request has expired.
     */
    public function isExpired(): bool
    {
        return $this->status === DeliveryRequestStatus::EXPIRED || $this->expires_at->isPast();
    }

    /**
     * Check if the request is still valid (not expired and pending).
     */
    public function isValid(): bool
    {
        return $this->isPending() && ! $this->expires_at->isPast();
    }

    /**
     * Get the response time in seconds (if accepted).
     */
    public function getResponseTimeSeconds(): ?int
    {
        if (! $this->accepted_at) {
            return null;
        }

        return (int) $this->created_at->diffInSeconds($this->accepted_at);
    }

    /**
     * Get the time remaining until expiration.
     */
    public function getTimeRemainingSeconds(): int
    {
        if ($this->expires_at->isPast()) {
            return 0;
        }

        return (int) now()->diffInSeconds($this->expires_at);
    }

    /**
     * Get request data value by key.
     */
    public function getRequestData(string $key, mixed $default = null): mixed
    {
        return data_get($this->request_data, $key, $default);
    }

    /**
     * Get response metadata value by key.
     */
    public function getResponseMetadata(string $key, mixed $default = null): mixed
    {
        return data_get($this->response_metadata, $key, $default);
    }

    /**
     * Get cancellation metadata value by key.
     */
    public function getCancellationMetadata(string $key, mixed $default = null): mixed
    {
        return data_get($this->cancellation_metadata, $key, $default);
    }

    /**
     * Scope a query to only include pending requests.
     */
    public function scopePending($query)
    {
        return $query->where('status', DeliveryRequestStatus::PENDING);
    }

    /**
     * Scope a query to only include accepted requests.
     */
    public function scopeAccepted($query)
    {
        return $query->where('status', DeliveryRequestStatus::ACCEPTED);
    }

    /**
     * Scope a query to only include cancelled requests.
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', DeliveryRequestStatus::CANCELLED);
    }

    /**
     * Scope a query to only include expired requests.
     */
    public function scopeExpired($query)
    {
        return $query->where('status', DeliveryRequestStatus::EXPIRED);
    }

    /**
     * Scope a query to only include valid (pending and not expired) requests.
     */
    public function scopeValid($query)
    {
        return $query->where('status', DeliveryRequestStatus::PENDING)
            ->where('expires_at', '>', now());
    }

    /**
     * Scope a query to filter by broadcast ID.
     */
    public function scopeForBroadcast($query, string $broadcastId)
    {
        return $query->where('broadcast_id', $broadcastId);
    }

    /**
     * Scope a query to filter by provider.
     */
    public function scopeForProvider($query, string $providerId)
    {
        return $query->where('provider_id', $providerId);
    }

    /**
     * Scope a query to filter by order.
     */
    public function scopeForOrder($query, string $orderId)
    {
        return $query->where('order_id', $orderId);
    }

    /**
     * Scope a query to include requests that are about to expire.
     */
    public function scopeExpiringWithin($query, int $minutes)
    {
        return $query->where('status', DeliveryRequestStatus::PENDING)
            ->where('expires_at', '<=', now()->addMinutes($minutes))
            ->where('expires_at', '>', now());
    }

    /**
     * Mark the request as accepted.
     */
    public function markAsAccepted(array $responseMetadata = []): bool
    {
        if (! $this->isPending()) {
            return false;
        }

        return $this->update([
            'status' => DeliveryRequestStatus::ACCEPTED,
            'accepted_at' => now(),
            'response_metadata' => array_merge($this->response_metadata ?? [], $responseMetadata),
        ]);
    }

    /**
     * Mark the request as cancelled.
     */
    public function markAsCancelled(string $reason, array $metadata = []): bool
    {
        if (! $this->isPending()) {
            return false;
        }

        return $this->update([
            'status' => DeliveryRequestStatus::CANCELLED,
            'cancelled_at' => now(),
            'cancellation_reason' => $reason,
            'cancellation_metadata' => array_merge($this->cancellation_metadata ?? [], $metadata),
        ]);
    }

    /**
     * Mark the request as expired.
     */
    public function markAsExpired(string $reason = 'Request timeout'): bool
    {
        if (! $this->isPending()) {
            return false;
        }

        return $this->update([
            'status' => DeliveryRequestStatus::EXPIRED,
            'expired_at' => now(),
            'expiration_reason' => $reason,
        ]);
    }
}
