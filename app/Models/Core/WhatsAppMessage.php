<?php

declare(strict_types=1);

namespace App\Models\Core;

use App\Enums\System\WhatsAppMessageType;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $conversation_id
 * @property string $message_id WhatsApp message ID from API
 * @property string $direction Message direction
 * @property WhatsAppMessageType $message_type WhatsApp message type
 * @property string|null $content Message content
 * @property string|null $media_url URL for media messages
 * @property array<array-key, mixed>|null $metadata Additional message metadata from WhatsApp API
 * @property \Illuminate\Support\Carbon $sent_at When message was sent
 * @property \Illuminate\Support\Carbon|null $delivered_at When message was delivered
 * @property \Illuminate\Support\Carbon|null $read_at When message was read
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Core\WhatsAppConversation $conversation
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereContent($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereConversationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereDeliveredAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereDirection($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereMediaUrl($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereMessageId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereMessageType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereReadAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereSentAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppMessage whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class WhatsAppMessage extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'whatsapp_messages';

    protected $fillable = [
        'conversation_id',
        'message_id',
        'direction',
        'message_type',
        'content',
        'media_url',
        'metadata',
        'sent_at',
        'delivered_at',
        'read_at',
    ];

    protected $casts = [
        'message_type' => WhatsAppMessageType::class,
        'metadata' => 'array',
        'sent_at' => 'datetime',
        'delivered_at' => 'datetime',
        'read_at' => 'datetime',
    ];

    /**
     * Get the conversation that owns the message.
     */
    public function conversation(): BelongsTo
    {
        return $this->belongsTo(WhatsAppConversation::class, 'conversation_id');
    }
}
