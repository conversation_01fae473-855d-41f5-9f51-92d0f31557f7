<?php

declare(strict_types=1);

namespace App\Models\Core;

use App\Models\Business\Business;
use App\Models\System\Tenant;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string|null $tenant_id FK to tenants.id, nullable for customer conversations
 * @property string|null $business_id
 * @property string $customer_phone Customer's WhatsApp number in E.164 format
 * @property string|null $customer_user_id
 * @property string $conversation_status
 * @property string|null $current_step Current step in conversation flow
 * @property array<array-key, mixed>|null $conversation_data Store conversation state and collected data
 * @property \Illuminate\Support\Carbon $started_at
 * @property \Illuminate\Support\Carbon $last_activity_at
 * @property \Illuminate\Support\Carbon|null $completed_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Core\WhatsAppMessage> $messages
 * @property-read int|null $messages_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppConversation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppConversation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppConversation query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppConversation whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppConversation whereCompletedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppConversation whereConversationData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppConversation whereConversationStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppConversation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppConversation whereCurrentStep($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppConversation whereCustomerPhone($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppConversation whereCustomerUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppConversation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppConversation whereLastActivityAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppConversation whereStartedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppConversation whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppConversation whereUpdatedAt($value)
 *
 * @property-read Business|null $business
 * @property-read User|null $customerUser
 * @property-read Tenant|null $tenant
 *
 * @mixin \Eloquent
 */
class WhatsAppConversation extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'whatsapp_conversations';

    protected $fillable = [
        'tenant_id',
        'business_id',
        'customer_phone',
        'customer_user_id',
        'conversation_status',
        'current_step',
        'conversation_data',
        'started_at',
        'last_activity_at',
        'completed_at',
    ];

    protected $casts = [
        'conversation_data' => 'array',
        'started_at' => 'datetime',
        'last_activity_at' => 'datetime',
        'completed_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the conversation.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    /**
     * Get the business associated with the conversation.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the customer user associated with the conversation.
     */
    public function customerUser(): BelongsTo
    {
        return $this->belongsTo(User::class, 'customer_user_id');
    }

    /**
     * Get the messages for the conversation.
     */
    public function messages(): HasMany
    {
        return $this->hasMany(WhatsAppMessage::class, 'conversation_id');
    }
}
