<?php

declare(strict_types=1);

namespace App\Models\Core;

use App\Models\Delivery\ProviderCapability;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $name e.g., hazardous_materials, temperature_controlled, oversized_items
 * @property string|null $description
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Capability newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Capability newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Capability query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Capability whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Capability whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Capability whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Capability whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Capability whereUpdatedAt($value)
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, ProviderCapability> $providerCapabilities
 * @property-read int|null $provider_capabilities_count
 *
 * @mixin \Eloquent
 */
class Capability extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'description',
    ];

    /**
     * Get the provider capabilities for this capability.
     *
     * @return HasMany<ProviderCapability,Capability>
     */
    public function providerCapabilities(): HasMany
    {
        return $this->hasMany(ProviderCapability::class, 'capability_id');
    }
}
