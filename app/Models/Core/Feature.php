<?php

declare(strict_types=1);

namespace App\Models\Core;

use App\Enums\System\FeatureTargetType;
use App\Models\Financial\PlanFeature;
use App\Models\Financial\SubscriptionPlan;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $name
 * @property string $slug
 * @property string|null $description
 * @property FeatureTargetType $target_type
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Feature bySlug(string $slug)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Feature forTargetType(\App\Enums\System\FeatureTargetType $targetType)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Feature newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Feature newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Feature query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Feature whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Feature whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Feature whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Feature whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Feature whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Feature whereTargetType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Feature whereUpdatedAt($value)
 *
 * @property-read \Illuminate\Database\Eloquent\Collection<int, PlanFeature> $planFeatures
 * @property-read int|null $plan_features_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, SubscriptionPlan> $subscriptionPlans
 * @property-read int|null $subscription_plans_count
 *
 * @mixin \Eloquent
 */
class Feature extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'slug',
        'description',
        'target_type',
    ];

    protected $casts = [
        'target_type' => FeatureTargetType::class,
    ];

    /**
     * Get the plan features for the feature.
     *
     * @return HasMany<PlanFeature,Feature>
     */
    public function planFeatures(): HasMany
    {
        return $this->hasMany(PlanFeature::class, 'feature_id');
    }

    /**
     * Get the subscription plans that have this feature.
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function subscriptionPlans()
    {
        return $this->belongsToMany(SubscriptionPlan::class, 'plan_features', 'feature_id', 'plan_id')
            ->withPivot('limit')
            ->withTimestamps();
    }

    /**
     * Scope to get features by target type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForTargetType($query, FeatureTargetType $targetType)
    {
        return $query->where('target_type', $targetType);
    }

    /**
     * Scope to find a feature by slug.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeBySlug($query, string $slug)
    {
        return $query->where('slug', $slug);
    }

    /**
     * Check if this feature is available in a specific plan.
     *
     * @param  string|SubscriptionPlan  $plan
     */
    public function isAvailableInPlan($plan): bool
    {
        $planId = $plan instanceof SubscriptionPlan ? $plan->id : $plan;

        return $this->planFeatures()
            ->where('plan_id', $planId)
            ->exists();
    }

    /**
     * Get the limit for this feature in a specific plan.
     *
     * @param  string|SubscriptionPlan  $plan
     */
    public function getLimitInPlan($plan): ?int
    {
        $planId = $plan instanceof SubscriptionPlan ? $plan->id : $plan;

        $planFeature = $this->planFeatures()
            ->where('plan_id', $planId)
            ->first();

        return $planFeature?->limit;
    }
}
