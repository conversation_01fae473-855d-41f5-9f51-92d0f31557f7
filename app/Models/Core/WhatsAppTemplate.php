<?php

declare(strict_types=1);

namespace App\Models\Core;

use App\Enums\System\WhatsAppTemplateCategory;
use App\Models\Business\Business;
use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string|null $tenant_id FK to tenants.id, nullable for platform-wide templates
 * @property string|null $business_id
 * @property string $template_name WhatsApp template name
 * @property string $language_code Template language code
 * @property WhatsAppTemplateCategory $category WhatsApp template category
 * @property string $status
 * @property array<array-key, mixed> $template_components Template header, body, footer, buttons
 * @property string|null $rejection_reason Reason for template rejection
 * @property \Illuminate\Support\Carbon|null $approved_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppTemplate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppTemplate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppTemplate query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppTemplate whereApprovedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppTemplate whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppTemplate whereCategory($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppTemplate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppTemplate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppTemplate whereLanguageCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppTemplate whereRejectionReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppTemplate whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppTemplate whereTemplateComponents($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppTemplate whereTemplateName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppTemplate whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|WhatsAppTemplate whereUpdatedAt($value)
 *
 * @property-read Business|null $business
 * @property-read Tenant|null $tenant
 *
 * @mixin \Eloquent
 */
class WhatsAppTemplate extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'whatsapp_templates';

    protected $fillable = [
        'tenant_id',
        'business_id',
        'template_name',
        'language_code',
        'category',
        'status',
        'template_components',
        'rejection_reason',
        'approved_at',
    ];

    protected $casts = [
        'category' => WhatsAppTemplateCategory::class,
        'template_components' => 'array',
        'approved_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the template.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    /**
     * Get the business that owns the template.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }
}
