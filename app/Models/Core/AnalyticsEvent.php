<?php

declare(strict_types=1);

namespace App\Models\Core;

use App\Models\Business\Business;
use App\Models\Delivery\DeliveryProvider;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string|null $tenant_id FK to tenants.id, nullable (can be platform-level event)
 * @property string|null $business_id
 * @property string|null $provider_id
 * @property string|null $user_id
 * @property string $event_type
 * @property array<array-key, mixed> $event_data
 * @property \Illuminate\Support\Carbon $created_at
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AnalyticsEvent forBusiness(string $businessId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AnalyticsEvent forProvider(string $providerId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AnalyticsEvent forTenant(string $tenantId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AnalyticsEvent forUser(string $userId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AnalyticsEvent newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AnalyticsEvent newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AnalyticsEvent ofType(string $eventType)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AnalyticsEvent query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AnalyticsEvent whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AnalyticsEvent whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AnalyticsEvent whereEventData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AnalyticsEvent whereEventType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AnalyticsEvent whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AnalyticsEvent whereProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AnalyticsEvent whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AnalyticsEvent whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AnalyticsEvent withinDateRange(string $startDate, string $endDate)
 *
 * @property-read Business|null $business
 * @property-read DeliveryProvider|null $provider
 * @property-read User|null $user
 *
 * @mixin \Eloquent
 */
class AnalyticsEvent extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'business_id',
        'provider_id',
        'user_id',
        'event_type',
        'event_data',
        'created_at',
    ];

    protected $casts = [
        'event_data' => 'array',
        'created_at' => 'datetime',
    ];

    public $timestamps = false;

    /**
     * Get the business associated with the event.
     *
     * @return BelongsTo<Business,AnalyticsEvent>
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }

    /**
     * Get the provider associated with the event.
     *
     * @return BelongsTo<DeliveryProvider,AnalyticsEvent>
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(DeliveryProvider::class, 'provider_id');
    }

    /**
     * Get the user associated with the event.
     *
     * @return BelongsTo<User,AnalyticsEvent>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'user_id');
    }

    /**
     * Scope to filter by event type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfType($query, string $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    /**
     * Scope to filter by tenant.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForTenant($query, string $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }

    /**
     * Scope to filter by business.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForBusiness($query, string $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    /**
     * Scope to filter by provider.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForProvider($query, string $providerId)
    {
        return $query->where('provider_id', $providerId);
    }

    /**
     * Scope to filter by user.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForUser($query, string $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * Scope to filter events within a date range.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithinDateRange($query, string $startDate, string $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Get a summary of the analytics event.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'event_type' => $this->event_type,
            'event_data' => $this->event_data,
            'business_id' => $this->business_id,
            'provider_id' => $this->provider_id,
            'user_id' => $this->user_id,
            'created_at' => $this->created_at,
        ];
    }
}
