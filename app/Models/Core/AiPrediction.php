<?php

declare(strict_types=1);

namespace App\Models\Core;

use App\Enums\System\AiPredictionType;
use App\Models\Business\Business;
use App\Models\Delivery\DeliveryProvider;
use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property string $id
 * @property string|null $tenant_id FK to tenants.id, nullable (if prediction is global or cross-tenant)
 * @property AiPredictionType $prediction_type e.g., demand_forecast, price_suggestion, fraud_risk, eta
 * @property string $target_id Polymorphic (order, product, business, provider, user_delivery etc.)
 * @property string $target_type Polymorphic (order, product, business, provider, user_delivery)
 * @property string|null $business_id
 * @property string|null $provider_id
 * @property array<array-key, mixed> $prediction_data
 * @property numeric|null $confidence_score 0-1
 * @property \Illuminate\Support\Carbon $created_at
 * @property \Illuminate\Support\Carbon|null $expires_at
 * @property-read Model|\Eloquent $target
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiPrediction byType(string $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiPrediction forBusiness(string $businessId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiPrediction forProvider(string $providerId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiPrediction forTarget(string $targetId, string $targetType)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiPrediction newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiPrediction newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiPrediction query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiPrediction whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiPrediction whereConfidenceScore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiPrediction whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiPrediction whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiPrediction whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiPrediction wherePredictionData($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiPrediction wherePredictionType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiPrediction whereProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiPrediction whereTargetId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiPrediction whereTargetType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiPrediction whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|AiPrediction withinDateRange(string $startDate, string $endDate)
 *
 * @property-read Business|null $business
 * @property-read DeliveryProvider|null $provider
 * @property-read Tenant|null $tenant
 *
 * @mixin \Eloquent
 */
class AiPrediction extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'prediction_type',
        'target_id',
        'target_type',
        'business_id',
        'provider_id',
        'prediction_data',
        'confidence_score',
        'created_at',
        'expires_at',
    ];

    protected $casts = [
        'prediction_type' => AiPredictionType::class,
        'prediction_data' => 'array',
        'confidence_score' => 'decimal:4',
        'created_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the prediction.
     *
     * @return BelongsTo<Tenant,AiPrediction>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(Tenant::class, 'tenant_id');
    }

    /**
     * Get the business associated with the prediction.
     *
     * @return BelongsTo<Business,AiPrediction>
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }

    /**
     * Get the provider associated with the prediction.
     *
     * @return BelongsTo<DeliveryProvider,AiPrediction>
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(DeliveryProvider::class, 'provider_id');
    }

    /**
     * Get the polymorphic target of the prediction.
     */
    public function target(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope to get predictions by type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByType($query, string $type)
    {
        return $query->where('prediction_type', $type);
    }

    /**
     * Scope to get predictions for a specific target.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForTarget($query, string $targetId, string $targetType)
    {
        return $query->where('target_id', $targetId)
            ->where('target_type', $targetType);
    }

    /**
     * Scope to get predictions for a business.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForBusiness($query, string $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    /**
     * Scope to get predictions for a provider.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForProvider($query, string $providerId)
    {
        return $query->where('provider_id', $providerId);
    }

    /**
     * Scope to get predictions within a date range.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithinDateRange($query, string $startDate, string $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Check if the prediction is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at !== null && $this->expires_at->isPast();
    }

    /**
     * Get a summary of the prediction for API responses.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'prediction_type' => $this->prediction_type,
            'target_id' => $this->target_id,
            'target_type' => $this->target_type,
            'business_id' => $this->business_id,
            'provider_id' => $this->provider_id,
            'prediction_data' => $this->prediction_data,
            'confidence_score' => $this->confidence_score,
            'created_at' => $this->created_at,
            'expires_at' => $this->expires_at,
            'is_expired' => $this->isExpired(),
        ];
    }
}
