<?php

declare(strict_types=1);

namespace App\Models\Financial;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property string $id
 * @property string $accountable_id FK to business or provider, or a fixed ID for the platform's account
 * @property string $accountable_type business, delivery_provider, or platform
 * @property numeric $balance Current balance
 * @property string $currency e.g., NGN, USD. Matches country's currency or potentially multi-currency accounts.
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read Model|\Eloquent $accountable
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Financial\PlatformTransaction> $transactions
 * @property-read int|null $transactions_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformAccount byCurrency(string $currency)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformAccount forBusinesses()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformAccount forDeliveryProviders()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformAccount forPlatform()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformAccount newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformAccount newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformAccount query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformAccount whereAccountableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformAccount whereAccountableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformAccount whereBalance($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformAccount whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformAccount whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformAccount whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformAccount whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformAccount withNegativeBalance()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformAccount withPositiveBalance()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformAccount withZeroBalance()
 *
 * @mixin \Eloquent
 */
class PlatformAccount extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'accountable_id',
        'accountable_type',
        'balance',
        'currency',
    ];

    protected $casts = [
        'balance' => 'decimal:2',
    ];

    public function accountable(): MorphTo
    {
        return $this->morphTo();
    }

    public function transactions(): HasMany
    {
        return $this->hasMany(PlatformTransaction::class, 'account_id');
    }

    public function scopeForPlatform($query)
    {
        return $query->where('accountable_type', 'platform');
    }

    public function scopeForBusinesses($query)
    {
        return $query->where('accountable_type', 'business');
    }

    public function scopeForDeliveryProviders($query)
    {
        return $query->where('accountable_type', 'delivery_provider');
    }

    public function scopeByCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }

    public function scopeWithPositiveBalance($query)
    {
        return $query->where('balance', '>', 0);
    }

    public function scopeWithNegativeBalance($query)
    {
        return $query->where('balance', '<', 0);
    }

    public function scopeWithZeroBalance($query)
    {
        return $query->where('balance', 0);
    }
}
