<?php

declare(strict_types=1);

namespace App\Models\Financial;

use App\Models\Core\Feature;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $plan_id
 * @property string $feature_id
 * @property int|null $limit
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Financial\SubscriptionPlan $plan
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanFeature forFeature(string $featureId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanFeature forPlan(string $planId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanFeature limited()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanFeature newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanFeature newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanFeature query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanFeature unlimited()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanFeature whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanFeature whereFeatureId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanFeature whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanFeature whereLimit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanFeature wherePlanId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanFeature whereUpdatedAt($value)
 *
 * @property-read Feature $feature
 *
 * @mixin \Eloquent
 */
class PlanFeature extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'plan_id',
        'feature_id',
        'limit',
    ];

    protected $casts = [
        'limit' => 'integer',
    ];

    /**
     * Get the subscription plan that owns the plan feature.
     *
     * @return BelongsTo<SubscriptionPlan,PlanFeature>
     */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class, 'plan_id');
    }

    /**
     * Get the feature that owns the plan feature.
     *
     * @return BelongsTo<Feature,PlanFeature>
     */
    public function feature(): BelongsTo
    {
        return $this->belongsTo(Feature::class, 'feature_id');
    }

    /**
     * Scope to get plan features by plan.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForPlan($query, string $planId)
    {
        return $query->where('plan_id', $planId);
    }

    /**
     * Scope to get plan features by feature.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForFeature($query, string $featureId)
    {
        return $query->where('feature_id', $featureId);
    }

    /**
     * Scope to get plan features with unlimited access (null limit).
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeUnlimited($query)
    {
        return $query->whereNull('limit');
    }

    /**
     * Scope to get plan features with limited access (has limit).
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeLimited($query)
    {
        return $query->whereNotNull('limit');
    }

    /**
     * Check if this feature has unlimited access.
     */
    public function isUnlimited(): bool
    {
        return is_null($this->limit);
    }

    /**
     * Check if this feature has a specific limit.
     */
    public function hasLimit(): bool
    {
        return ! is_null($this->limit);
    }

    /**
     * Get the limit value or a default for unlimited features.
     */
    public function getLimitOrDefault(int $unlimitedDefault = -1): int
    {
        return $this->limit ?? $unlimitedDefault;
    }
}
