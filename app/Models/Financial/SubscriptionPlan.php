<?php

declare(strict_types=1);

namespace App\Models\Financial;

use App\Enums\Financial\SubscriptionTargetType;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $name
 * @property string $slug
 * @property string|null $description
 * @property mixed $target_type
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Financial\PlanFeature> $planFeatures
 * @property-read int|null $plan_features_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Financial\PlanPrice> $planPrices
 * @property-read int|null $plan_prices_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Financial\UserSubscription> $userSubscriptions
 * @property-read int|null $user_subscriptions_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionPlan active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionPlan forTargetType(\App\Enums\SubscriptionTargetType $targetType)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionPlan newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionPlan newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionPlan query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionPlan whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionPlan whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionPlan whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionPlan whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionPlan whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionPlan whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionPlan whereTargetType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|SubscriptionPlan whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class SubscriptionPlan extends Model
{
    use HasFactory, HasUuids;

    protected $table = 'subscription_plans';

    protected $fillable = [
        'name',
        'slug',
        'description',
        'target_type',
        'is_active',
    ];

    protected $casts = [
        'target_type' => SubscriptionTargetType::class,
        'is_active' => 'boolean',
    ];

    /**
     * Get the plan prices for the subscription plan.
     *
     * @return HasMany<PlanPrice,SubscriptionPlan>
     */
    public function planPrices(): HasMany
    {
        return $this->hasMany(PlanPrice::class, 'plan_id');
    }

    /**
     * Get the plan features for the subscription plan.
     *
     * @return HasMany<PlanFeature,SubscriptionPlan>
     */
    public function planFeatures(): HasMany
    {
        return $this->hasMany(PlanFeature::class, 'plan_id');
    }

    /**
     * Get the user subscriptions for this plan.
     *
     * @return HasMany<UserSubscription,SubscriptionPlan>
     */
    public function userSubscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class, 'plan_id');
    }

    /**
     * Scope to get only active plans.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get plans by target type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForTargetType($query, SubscriptionTargetType $targetType)
    {
        return $query->where('target_type', $targetType);
    }

    /**
     * Get the active plan price for a specific billing interval and currency.
     */
    public function getActivePriceFor(string $billingInterval, string $currency): ?PlanPrice
    {
        return $this->planPrices()
            ->where('billing_interval', $billingInterval)
            ->where('currency', $currency)
            ->where('is_active', true)
            ->first();
    }

    /**
     * Check if the plan has a specific feature.
     */
    public function hasFeature(string $featureSlug): bool
    {
        return $this->planFeatures()
            ->whereHas('feature', function ($query) use ($featureSlug) {
                $query->where('slug', $featureSlug);
            })
            ->exists();
    }

    /**
     * Get the limit for a specific feature.
     */
    public function getFeatureLimit(string $featureSlug): ?int
    {
        $planFeature = $this->planFeatures()
            ->whereHas('feature', function ($query) use ($featureSlug) {
                $query->where('slug', $featureSlug);
            })
            ->first();

        return $planFeature?->limit;
    }
}
