<?php

declare(strict_types=1);

namespace App\Models\Financial;

use App\Enums\BillingInterval;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $plan_price_id
 * @property numeric $old_price
 * @property numeric $new_price
 * @property string $currency e.g., NGN, USD
 * @property mixed $billing_interval
 * @property string|null $changed_by_user_id
 * @property string|null $reason Reason for price change
 * @property \Illuminate\Support\Carbon $effective_date When the price change took effect
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User\User|null $changedBy
 * @property-read string $formatted_new_price
 * @property-read string $formatted_old_price
 * @property-read float $price_change
 * @property-read float $price_change_percentage
 * @property-read \App\Models\Financial\PlanPrice $planPrice
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPriceHistory newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPriceHistory newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPriceHistory query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPriceHistory whereBillingInterval($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPriceHistory whereChangedByUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPriceHistory whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPriceHistory whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPriceHistory whereEffectiveDate($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPriceHistory whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPriceHistory whereNewPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPriceHistory whereOldPrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPriceHistory wherePlanPriceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPriceHistory whereReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPriceHistory whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class PlanPriceHistory extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'plan_price_id',
        'old_price',
        'new_price',
        'currency',
        'billing_interval',
        'changed_by_user_id',
        'reason',
        'effective_date',
    ];

    protected $casts = [
        'old_price' => 'decimal:2',
        'new_price' => 'decimal:2',
        'billing_interval' => BillingInterval::class,
        'effective_date' => 'datetime',
    ];

    /**
     * Get the plan price that owns this history record.
     *
     * @return BelongsTo<PlanPrice,PlanPriceHistory>
     */
    public function planPrice(): BelongsTo
    {
        return $this->belongsTo(PlanPrice::class, 'plan_price_id');
    }

    /**
     * Get the user who made the price change.
     *
     * @return BelongsTo<User,PlanPriceHistory>
     */
    public function changedBy(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'changed_by_user_id');
    }

    /**
     * Get the price change amount.
     */
    public function getPriceChangeAttribute(): float
    {
        return $this->new_price - $this->old_price;
    }

    /**
     * Get the price change percentage.
     */
    public function getPriceChangePercentageAttribute(): float
    {
        if ($this->old_price == 0) {
            return 0;
        }

        return (($this->new_price - $this->old_price) / $this->old_price) * 100;
    }

    /**
     * Check if this was a price increase.
     */
    public function isPriceIncrease(): bool
    {
        return $this->new_price > $this->old_price;
    }

    /**
     * Check if this was a price decrease.
     */
    public function isPriceDecrease(): bool
    {
        return $this->new_price < $this->old_price;
    }

    /**
     * Get formatted old price with currency.
     */
    public function getFormattedOldPriceAttribute(): string
    {
        return $this->currency.' '.number_format($this->old_price, 2);
    }

    /**
     * Get formatted new price with currency.
     */
    public function getFormattedNewPriceAttribute(): string
    {
        return $this->currency.' '.number_format($this->new_price, 2);
    }
}
