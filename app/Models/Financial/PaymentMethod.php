<?php

declare(strict_types=1);

namespace App\Models\Financial;

use App\Enums\Financial\PaymentMethodProvider;
use App\Enums\Financial\PaymentMethodType;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $user_id
 * @property mixed $provider
 * @property string $reference Token or identifier from payment provider
 * @property mixed $type
 * @property array<array-key, mixed>|null $details Masked card number, bank name, etc.
 * @property bool $is_default
 * @property \Illuminate\Support\Carbon|null $expires_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $display_name
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Financial\Payment> $payments
 * @property-read int|null $payments_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Financial\UserSubscription> $subscriptions
 * @property-read int|null $subscriptions_count
 * @property-read \App\Models\User\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod byProvider(\App\Enums\PaymentMethodProvider $provider)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod byType(\App\Enums\PaymentMethodType $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod default()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod expired()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereIsDefault($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereProvider($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PaymentMethod whereUserId($value)
 * @method static \Database\Factories\Financial\PaymentMethodFactory factory($count = null, $state = [])
 *
 * @mixin \Eloquent
 */
class PaymentMethod extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'user_id',
        'provider',
        'reference',
        'type',
        'details',
        'is_default',
        'expires_at',
    ];

    protected $casts = [
        'provider' => PaymentMethodProvider::class,
        'type' => PaymentMethodType::class,
        'details' => 'array',
        'is_default' => 'boolean',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the user that owns the payment method.
     *
     * @return BelongsTo<User,PaymentMethod>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'user_id');
    }

    /**
     * Get the payments made with this payment method.
     *
     * @return HasMany<Payment,PaymentMethod>
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'payment_method_id');
    }

    /**
     * Get the subscriptions using this payment method.
     *
     * @return HasMany<UserSubscription,PaymentMethod>
     */
    public function subscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class, 'payment_method_id');
    }

    /**
     * Scope to get default payment methods.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeDefault($query)
    {
        return $query->where('is_default', true);
    }

    /**
     * Scope to get active payment methods (not expired).
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where(function ($query) {
            $query->whereNull('expires_at')
                ->orWhere('expires_at', '>', now());
        });
    }

    /**
     * Scope to get expired payment methods.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeExpired($query)
    {
        return $query->where('expires_at', '<=', now());
    }

    /**
     * Scope to get payment methods by type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByType($query, PaymentMethodType $type)
    {
        return $query->where('type', $type);
    }

    /**
     * Scope to get payment methods by provider.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByProvider($query, PaymentMethodProvider $provider)
    {
        return $query->where('provider', $provider);
    }

    /**
     * Check if the payment method is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast();
    }

    /**
     * Check if the payment method is active.
     */
    public function isActive(): bool
    {
        return ! $this->isExpired();
    }

    /**
     * Check if this is the default payment method.
     */
    public function isDefault(): bool
    {
        return $this->is_default;
    }

    /**
     * Check if the payment method is expiring soon.
     */
    public function isExpiringSoon(int $days = 30): bool
    {
        if (! $this->expires_at) {
            return false;
        }

        return $this->expires_at->isBefore(now()->addDays($days));
    }

    /**
     * Set this payment method as default.
     */
    public function setAsDefault(): bool
    {
        // First, unset all other default payment methods for this user
        static::where('user_id', $this->user_id)
            ->where('id', '!=', $this->id)
            ->update(['is_default' => false]);

        // Then set this one as default
        return $this->update(['is_default' => true]);
    }

    /**
     * Get the masked card number or account details.
     */
    public function getMaskedNumber(): ?string
    {
        return $this->details['masked_number'] ?? $this->details['last_four'] ?? null;
    }

    /**
     * Get the brand/network of the payment method.
     */
    public function getBrand(): ?string
    {
        return $this->details['brand'] ?? $this->details['network'] ?? null;
    }

    /**
     * Get the expiry month.
     */
    public function getExpiryMonth(): ?int
    {
        return $this->details['exp_month'] ?? null;
    }

    /**
     * Get the expiry year.
     */
    public function getExpiryYear(): ?int
    {
        return $this->details['exp_year'] ?? null;
    }

    /**
     * Get the display name for the payment method.
     */
    public function getDisplayNameAttribute(): string
    {
        $brand = $this->getBrand();
        $maskedNumber = $this->getMaskedNumber();

        if ($this->type === PaymentMethodType::CARD) {
            if ($brand && $maskedNumber) {
                return "{$brand} •••• {$maskedNumber}";
            }

            return 'Card';
        }

        if ($this->type === PaymentMethodType::BANK_ACCOUNT) {
            $bankName = $this->details['bank_name'] ?? 'Bank';

            return "{$bankName} Account";
        }

        if ($this->type === PaymentMethodType::WALLET) {
            return $this->details['wallet_name'] ?? 'Digital Wallet';
        }

        return $this->type->value;
    }

    /**
     * Get the icon class for the payment method.
     */
    public function getIconClass(): string
    {
        $brand = strtolower($this->getBrand() ?? '');

        return match ($brand) {
            'visa' => 'fab fa-cc-visa',
            'mastercard' => 'fab fa-cc-mastercard',
            'amex', 'american express' => 'fab fa-cc-amex',
            'discover' => 'fab fa-cc-discover',
            'paypal' => 'fab fa-paypal',
            'stripe' => 'fab fa-stripe',
            default => 'fas fa-credit-card',
        };
    }

    /**
     * Check if the payment method can be used for payments.
     */
    public function canBeUsed(): bool
    {
        return $this->isActive() && ! $this->isExpired();
    }

    /**
     * Get the formatted expiry date.
     */
    public function getFormattedExpiry(): ?string
    {
        $month = $this->getExpiryMonth();
        $year = $this->getExpiryYear();

        if (! $month || ! $year) {
            return null;
        }

        return sprintf('%02d/%02d', $month, $year % 100);
    }

    /**
     * Delete the payment method from the provider.
     */
    public function deleteFromProvider(): bool
    {
        // Here you would typically make an API call to the payment provider
        // to delete the payment method from their system

        // For now, just delete from our database
        return $this->delete();
    }

    /**
     * Get usage statistics for this payment method.
     */
    public function getUsageStats(): array
    {
        $totalPayments = $this->payments()->count();
        $successfulPayments = $this->payments()->where('status', 'completed')->count();
        $totalAmount = $this->payments()->where('status', 'completed')->sum('amount');
        $lastUsed = $this->payments()->latest()->first()?->created_at;

        return [
            'total_payments' => $totalPayments,
            'successful_payments' => $successfulPayments,
            'success_rate' => $totalPayments > 0 ? ($successfulPayments / $totalPayments) * 100 : 0,
            'total_amount' => $totalAmount,
            'last_used' => $lastUsed,
        ];
    }
}
