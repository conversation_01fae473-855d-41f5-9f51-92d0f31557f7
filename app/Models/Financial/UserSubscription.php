<?php

declare(strict_types=1);

namespace App\Models\Financial;

use App\Enums\User\UserSubscriptionStatus;
use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property string $id
 * @property string|null $tenant_id FK to tenants.id (null for user subscriptions)
 * @property string $subscriber_id Polymorphic (user_id, business_id, provider_id)
 * @property string $subscriber_type Polymorphic (user, business, provider)
 * @property string $plan_price_id
 * @property \Illuminate\Support\Carbon $started_at
 * @property \Illuminate\Support\Carbon|null $expires_at
 * @property mixed $status
 * @property bool $auto_renew
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Financial\PaymentMethod|null $paymentMethod
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Financial\Payment> $payments
 * @property-read int|null $payments_count
 * @property-read \App\Models\Financial\SubscriptionPlan|null $plan
 * @property-read \App\Models\Financial\PlanPrice $planPrice
 * @property-read Model|\Eloquent $subscriber
 * @property-read \App\Models\System\Tenant|null $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription autoRenewing()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription cancelled()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription expired()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription expiringSoon(int $days = 7)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription forSubscriberType(string $subscriberType)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription trial()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereAutoRenew($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription wherePlanPriceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereStartedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereSubscriberId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereSubscriberType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserSubscription whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class UserSubscription extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'subscriber_id',
        'subscriber_type',
        'plan_id',
        'plan_price_id',
        'started_at',
        'expires_at',
        'cancelled_at',
        'status',
        'auto_renew',
        'payment_method_id',
        'trial_ends_at',
        'grace_period_ends_at',
        'billing_cycle_anchor',
        'metadata',
    ];

    protected $casts = [
        'status' => UserSubscriptionStatus::class,
        'started_at' => 'datetime',
        'expires_at' => 'datetime',
        'cancelled_at' => 'datetime',
        'trial_ends_at' => 'datetime',
        'grace_period_ends_at' => 'datetime',
        'billing_cycle_anchor' => 'datetime',
        'auto_renew' => 'boolean',
        'metadata' => 'array',
    ];

    // TODO:: figure out all this magic methods
    /**
     * Get the tenant that owns the subscription.
     *
     * @return BelongsTo<Tenant,UserSubscription>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the subscription plan.
     *
     * @return BelongsTo<SubscriptionPlan,UserSubscription>
     */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class, 'plan_id');
    }

    /**
     * Get the plan price for this subscription.
     *
     * @return BelongsTo<PlanPrice,UserSubscription>
     */
    public function planPrice(): BelongsTo
    {
        return $this->belongsTo(PlanPrice::class, 'plan_price_id');
    }

    /**
     * Get the payment method for this subscription.
     *
     * @return BelongsTo<PaymentMethod,UserSubscription>
     */
    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class, 'payment_method_id');
    }

    /**
     * Get the polymorphic subscriber (User, Business, or DeliveryProvider).
     */
    public function subscriber(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the payments for this subscription.
     *
     * @return HasMany<Payment,UserSubscription>
     */
    public function payments(): HasMany
    {
        return $this->hasMany(Payment::class, 'subscription_id');
    }

    /**
     * Scope to get active subscriptions.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', UserSubscriptionStatus::ACTIVE);
    }

    /**
     * Scope to get cancelled subscriptions.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCancelled($query)
    {
        return $query->where('status', UserSubscriptionStatus::CANCELLED);
    }

    /**
     * Scope to get expired subscriptions.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeExpired($query)
    {
        return $query->where('status', UserSubscriptionStatus::EXPIRED);
    }

    /**
     * Scope to get subscriptions in trial.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeTrial($query)
    {
        return $query->where('status', UserSubscriptionStatus::TRIAL);
    }

    /**
     * Scope to get subscriptions by subscriber type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForSubscriberType($query, string $subscriberType)
    {
        return $query->where('subscriber_type', $subscriberType);
    }

    /**
     * Scope to get subscriptions expiring soon.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeExpiringSoon($query, int $days = 7)
    {
        return $query->where('expires_at', '<=', now()->addDays($days))
            ->where('expires_at', '>', now());
    }

    /**
     * Scope to get auto-renewing subscriptions.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeAutoRenewing($query)
    {
        return $query->where('auto_renew', true);
    }

    /**
     * Check if the subscription is active.
     */
    public function isActive(): bool
    {
        return $this->status === UserSubscriptionStatus::ACTIVE &&
               $this->expires_at > now();
    }

    /**
     * Check if the subscription is cancelled.
     */
    public function isCancelled(): bool
    {
        return $this->status === UserSubscriptionStatus::CANCELLED;
    }

    /**
     * Check if the subscription is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at <= now() ||
               $this->status === UserSubscriptionStatus::EXPIRED;
    }

    /**
     * Check if the subscription is in trial period.
     */
    public function isOnTrial(): bool
    {
        return $this->status === UserSubscriptionStatus::TRIAL &&
               $this->trial_ends_at &&
               $this->trial_ends_at > now();
    }

    /**
     * Check if the subscription has grace period.
     */
    public function hasGracePeriod(): bool
    {
        return $this->grace_period_ends_at &&
               $this->grace_period_ends_at > now();
    }

    /**
     * Check if the subscription will auto-renew.
     */
    public function willAutoRenew(): bool
    {
        return $this->auto_renew && $this->isActive();
    }

    /**
     * Get the days remaining until expiration.
     */
    public function getDaysUntilExpiration(): int
    {
        if ($this->isExpired()) {
            return 0;
        }

        return (int) now()->diffInDays($this->expires_at, false);
    }

    /**
     * Get the days remaining in trial.
     */
    public function getDaysRemainingInTrial(): int
    {
        if (! $this->isOnTrial()) {
            return 0;
        }

        return (int) now()->diffInDays($this->trial_ends_at, false);
    }

    /**
     * Cancel the subscription.
     */
    public function cancel(bool $immediately = false): bool
    {
        $updates = [
            'status' => UserSubscriptionStatus::CANCELLED,
            'cancelled_at' => now(),
            'auto_renew' => false,
        ];

        if ($immediately) {
            $updates['expires_at'] = now();
        }

        return $this->update($updates);
    }

    /**
     * Resume a cancelled subscription.
     */
    public function resume(): bool
    {
        if (! $this->isCancelled() || $this->isExpired()) {
            return false;
        }

        return $this->update([
            'status' => UserSubscriptionStatus::ACTIVE,
            'cancelled_at' => null,
        ]);
    }

    /**
     * Extend the subscription by a certain period.
     */
    public function extend(int $days): bool
    {
        $newExpiryDate = $this->expires_at->addDays($days);

        return $this->update([
            'expires_at' => $newExpiryDate,
        ]);
    }

    /**
     * Renew the subscription for the next billing cycle.
     */
    public function renew(): bool
    {
        $billingInterval = $this->planPrice->billing_interval;

        $newExpiryDate = match ($billingInterval->value) {
            'monthly' => $this->expires_at->addMonth(),
            'quarterly' => $this->expires_at->addMonths(3),
            'annually' => $this->expires_at->addYear(),
            default => $this->expires_at->addMonth(),
        };

        return $this->update([
            'expires_at' => $newExpiryDate,
            'status' => UserSubscriptionStatus::ACTIVE,
        ]);
    }

    /**
     * Get the subscription's current period start date.
     */
    public function getCurrentPeriodStart(): \Carbon\Carbon
    {
        return $this->billing_cycle_anchor ?? $this->started_at;
    }

    /**
     * Get the subscription's current period end date.
     */
    public function getCurrentPeriodEnd(): \Carbon\Carbon
    {
        return $this->expires_at;
    }

    /**
     * Get the total amount paid for this subscription.
     */
    public function getTotalPaidAmount(): float
    {
        return $this->payments()
            ->where('status', 'completed')
            ->sum('amount');
    }

    /**
     * Check if subscription has access to a specific feature.
     */
    public function hasFeature(string $featureSlug): bool
    {
        return $this->plan->hasFeature($featureSlug);
    }

    /**
     * Get the limit for a specific feature.
     */
    public function getFeatureLimit(string $featureSlug): ?int
    {
        return $this->plan->getFeatureLimit($featureSlug);
    }

    /**
     * Get the formatted subscription period.
     */
    public function getFormattedPeriod(): string
    {
        $start = $this->getCurrentPeriodStart()->format('M j, Y');
        $end = $this->getCurrentPeriodEnd()->format('M j, Y');

        return "{$start} - {$end}";
    }

    /**
     * Get the next billing date.
     */
    public function getNextBillingDate(): ?\Carbon\Carbon
    {
        if (! $this->willAutoRenew()) {
            return null;
        }

        return $this->expires_at;
    }
}
