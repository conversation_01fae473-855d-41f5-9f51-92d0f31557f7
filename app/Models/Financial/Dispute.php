<?php

declare(strict_types=1);

namespace App\Models\Financial;

use App\Enums\DisputeStatus;
use App\Models\Delivery\Order;
use App\Models\Delivery\UserDelivery;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string|null $tenant_id FK to tenants.id, nullable - if dispute is inter-tenant or platform-level
 * @property string|null $order_id
 * @property string|null $user_delivery_id
 * @property string $initiator_id Polymorphic FK
 * @property string $initiator_type users, businesses, delivery_providers
 * @property string $respondent_id Polymorphic FK
 * @property string $respondent_type users, businesses, delivery_providers
 * @property string $reason Short reason
 * @property string $description
 * @property mixed $status open, under_review, resolved, closed
 * @property string|null $resolution
 * @property string|null $resolved_by_user_id
 * @property \Illuminate\Support\Carbon|null $resolved_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Financial\DisputeMessage> $messages
 * @property-read int|null $messages_count
 * @property-read \App\Models\System\Tenant|null $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dispute newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dispute newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dispute query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dispute whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dispute whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dispute whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dispute whereInitiatorId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dispute whereInitiatorType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dispute whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dispute whereReason($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dispute whereResolution($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dispute whereResolvedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dispute whereResolvedByUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dispute whereRespondentId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dispute whereRespondentType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dispute whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dispute whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dispute whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Dispute whereUserDeliveryId($value)
 *
 * @property-read Order|null $order
 * @property-read User|null $resolvedBy
 * @property-read UserDelivery|null $userDelivery
 *
 * @mixin \Eloquent
 */
class Dispute extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'order_id',
        'user_delivery_id',
        'initiator_id',
        'initiator_type',
        'respondent_id',
        'respondent_type',
        'reason',
        'description',
        'status',
        'resolution',
        'resolved_by_user_id',
        'resolved_at',
    ];

    protected $casts = [
        'status' => DisputeStatus::class,
        'resolved_at' => 'datetime',
    ];

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    public function userDelivery(): BelongsTo
    {
        return $this->belongsTo(UserDelivery::class, 'user_delivery_id');
    }

    public function resolvedBy(): BelongsTo
    {
        return $this->belongsTo(User::class, 'resolved_by_user_id');
    }

    public function messages(): HasMany
    {
        return $this->hasMany(DisputeMessage::class, 'dispute_id');
    }
}
