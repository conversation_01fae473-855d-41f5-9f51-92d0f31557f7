<?php

declare(strict_types=1);

namespace App\Models\Financial;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $commission_calculation_id
 * @property string $recipient_type platform, business, provider, referrer
 * @property string|null $recipient_id ID of the recipient (business_id, provider_id, etc.)
 * @property numeric $share_percentage Percentage of commission this recipient gets
 * @property numeric $share_amount Actual amount for this recipient
 * @property string $currency Currency of the share
 * @property string $status
 * @property \Illuminate\Support\Carbon|null $allocated_at
 * @property \Illuminate\Support\Carbon|null $paid_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Financial\CommissionCalculation $commissionCalculation
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RevenueShare newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RevenueShare newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RevenueShare query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RevenueShare whereAllocatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RevenueShare whereCommissionCalculationId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RevenueShare whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RevenueShare whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RevenueShare whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RevenueShare wherePaidAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RevenueShare whereRecipientId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RevenueShare whereRecipientType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RevenueShare whereShareAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RevenueShare whereSharePercentage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RevenueShare whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|RevenueShare whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class RevenueShare extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'commission_calculation_id',
        'recipient_type',
        'recipient_id',
        'share_percentage',
        'share_amount',
        'currency',
        'status',
        'allocated_at',
        'paid_at',
    ];

    protected $casts = [
        'share_percentage' => 'decimal:4',
        'share_amount' => 'decimal:2',
        'allocated_at' => 'datetime',
        'paid_at' => 'datetime',
    ];

    /**
     * Get the commission calculation that owns the revenue share.
     */
    public function commissionCalculation(): BelongsTo
    {
        return $this->belongsTo(CommissionCalculation::class);
    }
}
