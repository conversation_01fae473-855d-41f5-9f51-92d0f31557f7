<?php

declare(strict_types=1);

namespace App\Models\Financial;

use App\Enums\Financial\PlatformTransactionSourceType;
use App\Enums\Financial\PlatformTransactionType;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property string $id
 * @property mixed $source_type e.g., order, delivery, subscription, payout, adjustment, fee, refund, topup, commission, reward_credit
 * @property string|null $source_id FK to specific source record (order, payout, etc.)
 * @property string|null $tenant_id FK to tenants.id (business or provider tenant)
 * @property string|null $tenant_type business or delivery_provider
 * @property string|null $user_id
 * @property string $account_id
 * @property mixed $type credit (increases account balance), debit (decreases account balance)
 * @property numeric $amount The value of this specific transaction entry
 * @property string $currency Currency of the transaction
 * @property string|null $description Human-readable description
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Financial\PlatformAccount $account
 * @property-read Model|\Eloquent|null $source
 * @property-read \App\Models\System\Tenant|null $tenant
 * @property-read \App\Models\User\User|null $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction betweenDates(string $startDate, string $endDate)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction credits()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction debits()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction forAccount(string $accountId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction forTenant(string $tenantId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction fromSource(\App\Enums\PlatformTransactionSourceType $sourceType)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction inCurrency(string $currency)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction ofType(\App\Enums\PlatformTransactionType $type)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction whereAccountId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction whereSourceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction whereSourceType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction whereTenantType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlatformTransaction whereUserId($value)
 *
 * @mixin \Eloquent
 */
class PlatformTransaction extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'source_type',
        'source_id',
        'tenant_id',
        'tenant_type',
        'user_id',
        'account_id',
        'type',
        'amount',
        'currency',
        'description',
    ];

    protected $casts = [
        'source_type' => PlatformTransactionSourceType::class,
        'type' => PlatformTransactionType::class,
        'amount' => 'decimal:2',
    ];

    public function account(): BelongsTo
    {
        return $this->belongsTo(PlatformAccount::class, 'account_id');
    }

    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'user_id');
    }

    public function source(): MorphTo
    {
        return $this->morphTo();
    }

    public function scopeOfType($query, PlatformTransactionType $type)
    {
        return $query->where('type', $type);
    }

    public function scopeFromSource($query, PlatformTransactionSourceType $sourceType)
    {
        return $query->where('source_type', $sourceType);
    }

    public function scopeForTenant($query, string $tenantId)
    {
        return $query->where('tenant_id', $tenantId);
    }

    public function scopeForAccount($query, string $accountId)
    {
        return $query->where('account_id', $accountId);
    }

    public function scopeInCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }

    public function scopeCredits($query)
    {
        return $query->where('type', PlatformTransactionType::CREDIT);
    }

    public function scopeDebits($query)
    {
        return $query->where('type', PlatformTransactionType::DEBIT);
    }

    public function scopeBetweenDates($query, string $startDate, string $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }
}
