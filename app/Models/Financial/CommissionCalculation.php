<?php

declare(strict_types=1);

namespace App\Models\Financial;

use App\Enums\Financial\CommissionStatus;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property string $id
 * @property string|null $tenant_id FK to tenants.id
 * @property string $source_id Polymorphic FK to order, delivery, subscription, etc.
 * @property string $source_type Polymorphic type (order, delivery, subscription)
 * @property string $commission_rate_id
 * @property numeric $base_amount Amount commission is calculated on
 * @property numeric $commission_amount Calculated commission amount
 * @property string $currency Currency of the commission
 * @property array<array-key, mixed>|null $calculation_details Breakdown of how commission was calculated
 * @property mixed $status
 * @property \Illuminate\Support\Carbon $calculated_at When commission was calculated
 * @property \Illuminate\Support\Carbon|null $confirmed_at When commission was confirmed
 * @property \Illuminate\Support\Carbon|null $paid_at When commission was paid to platform
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Financial\CommissionRate $commissionRate
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Financial\RevenueShare> $revenueShares
 * @property-read int|null $revenue_shares_count
 * @property-read Model|\Eloquent $source
 * @property-read \App\Models\System\Tenant|null $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionCalculation newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionCalculation newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionCalculation query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionCalculation whereBaseAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionCalculation whereCalculatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionCalculation whereCalculationDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionCalculation whereCommissionAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionCalculation whereCommissionRateId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionCalculation whereConfirmedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionCalculation whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionCalculation whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionCalculation whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionCalculation wherePaidAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionCalculation whereSourceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionCalculation whereSourceType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionCalculation whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionCalculation whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionCalculation whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class CommissionCalculation extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'source_id',
        'source_type',
        'commission_rate_id',
        'base_amount',
        'commission_amount',
        'currency',
        'calculation_details',
        'status',
        'calculated_at',
        'confirmed_at',
        'paid_at',
    ];

    protected $casts = [
        'base_amount' => 'decimal:2',
        'commission_amount' => 'decimal:2',
        'calculation_details' => 'array',
        'status' => CommissionStatus::class,
        'calculated_at' => 'datetime',
        'confirmed_at' => 'datetime',
        'paid_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the commission calculation.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the commission rate used for this calculation.
     */
    public function commissionRate(): BelongsTo
    {
        return $this->belongsTo(CommissionRate::class);
    }

    /**
     * Get the source model (order, delivery, etc.).
     */
    public function source(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the revenue shares for this calculation.
     */
    public function revenueShares(): HasMany
    {
        return $this->hasMany(RevenueShare::class);
    }
}
