<?php

declare(strict_types=1);

namespace App\Models\Financial;

use App\Enums\Financial\PayoutMethodType;
use App\Enums\Financial\PayoutStatus;
use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property string $id
 * @property string $tenant_id FK to tenants.id (matches payable_id's tenant_id)
 * @property string $payable_id Polymorphic (business_id or delivery_provider_id)
 * @property string $payable_type Polymorphic (business, delivery_provider)
 * @property numeric $amount Amount paid out
 * @property string $currency Currency of payout
 * @property numeric $transaction_fee Payout fee
 * @property mixed $status
 * @property mixed|null $payment_method
 * @property array<array-key, mixed>|null $bank_details Bank account info used for payout (should be masked/securely stored)
 * @property string $reference Internal or external payout reference
 * @property array<array-key, mixed>|null $related_payments_json IDs/references of payments contributing to this payout (e.g., [1, 5, 9])
 * @property numeric|null $balance_before Tenant's balance before payout (for audit)
 * @property numeric|null $balance_after Tenant's balance after payout (for audit)
 * @property string|null $notes
 * @property \Illuminate\Support\Carbon|null $processed_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $formatted_amount
 * @property-read string $formatted_transaction_fee
 * @property-read Model|\Eloquent $payable
 * @property-read \App\Models\System\Tenant $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout failed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout paid()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout processing()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout whereBalanceAfter($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout whereBalanceBefore($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout whereBankDetails($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout whereNotes($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout wherePayableId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout wherePayableType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout wherePaymentMethod($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout whereProcessedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout whereReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout whereRelatedPaymentsJson($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout whereTransactionFee($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payout whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class Payout extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'payable_id',
        'payable_type',
        'amount',
        'currency',
        'transaction_fee',
        'status',
        'payment_method',
        'bank_details',
        'reference',
        'related_payments_json',
        'balance_before',
        'balance_after',
        'notes',
        'processed_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'transaction_fee' => 'decimal:2',
        'balance_before' => 'decimal:2',
        'balance_after' => 'decimal:2',
        'status' => PayoutStatus::class,
        'payment_method' => PayoutMethodType::class,
        'bank_details' => 'json',
        'related_payments_json' => 'json',
        'processed_at' => 'datetime',
    ];

    /**
     * Get the tenant that owns the payout.
     *
     * @return BelongsTo<Tenant,Payout>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the payable entity (business or delivery provider).
     */
    public function payable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope to get pending payouts.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', PayoutStatus::PENDING);
    }

    /**
     * Scope to get processing payouts.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeProcessing($query)
    {
        return $query->where('status', PayoutStatus::PROCESSING);
    }

    /**
     * Scope to get paid payouts.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePaid($query)
    {
        return $query->where('status', PayoutStatus::PAID);
    }

    /**
     * Scope to get failed payouts.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFailed($query)
    {
        return $query->where('status', PayoutStatus::FAILED);
    }

    /**
     * Check if the payout is pending.
     */
    public function isPending(): bool
    {
        return $this->status === PayoutStatus::PENDING;
    }

    /**
     * Check if the payout is processing.
     */
    public function isProcessing(): bool
    {
        return $this->status === PayoutStatus::PROCESSING;
    }

    /**
     * Check if the payout is paid.
     */
    public function isPaid(): bool
    {
        return $this->status === PayoutStatus::PAID;
    }

    /**
     * Check if the payout has failed.
     */
    public function hasFailed(): bool
    {
        return $this->status === PayoutStatus::FAILED;
    }

    /**
     * Get the net amount after transaction fee.
     */
    public function getNetAmount(): float
    {
        return $this->amount - $this->transaction_fee;
    }

    /**
     * Get the formatted amount with currency.
     */
    public function getFormattedAmountAttribute(): string
    {
        return $this->currency.' '.number_format($this->amount, 2);
    }

    /**
     * Get the formatted transaction fee with currency.
     */
    public function getFormattedTransactionFeeAttribute(): string
    {
        return $this->currency.' '.number_format($this->transaction_fee, 2);
    }
}
