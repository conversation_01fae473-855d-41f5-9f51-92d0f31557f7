<?php

declare(strict_types=1);

namespace App\Models\Financial;

use App\Enums\Financial\PaymentGateway;
use App\Enums\Financial\PaymentStatus;
use App\Models\Business\Business;
use App\Models\Business\BusinessBranch;
use App\Models\Delivery\Order;
use App\Models\Delivery\UserDelivery;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;

/**
 * @property string $id
 * @property string $tenant_id FK to tenants.id (matches business_tenant_id or subscription's tenant_id)
 * @property string|null $order_id
 * @property string|null $user_delivery_id
 * @property string|null $subscription_id
 * @property string $user_id
 * @property string|null $business_id
 * @property string|null $business_branch_id
 * @property numeric $amount
 * @property string $currency Matches order/subscription/user_delivery currency or platform currency?
 * @property PaymentGateway $gateway
 * @property string $gateway_reference Reference from the payment gateway
 * @property string|null $payment_method_id
 * @property PaymentStatus $status
 * @property numeric $transaction_fee
 * @property array<array-key, mixed>|null $metadata Gateway response, etc.
 * @property \Illuminate\Support\Carbon|null $paid_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $formatted_amount
 * @property-read string $formatted_transaction_fee
 * @property-read string $payment_type
 * @property-read \App\Models\Financial\PaymentMethod|null $paymentMethod
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Financial\Payout> $refunds
 * @property-read int|null $refunds_count
 * @property-read \App\Models\Financial\UserSubscription|null $subscription
 * @property-read \App\Models\User\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment byGateway(\App\Enums\Financial\PaymentGateway $gateway)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment completed()
 * @method static \Database\Factories\PaymentFactory factory($count = null, $state = [])
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment failed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment forOrders()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment forSubscriptions()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment forUserDeliveries()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment refunded()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereAmount($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereBusinessBranchId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereGateway($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereGatewayReference($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereMetadata($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereOrderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment wherePaidAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment wherePaymentMethodId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereSubscriptionId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereTransactionFee($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereUserDeliveryId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment whereUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Payment withinDateRange(string $startDate, string $endDate)
 *
 * @property-read Business|null $business
 * @property-read BusinessBranch|null $businessBranch
 * @property-read Order|null $order
 * @property-read UserDelivery|null $userDelivery
 *
 * @mixin \Eloquent
 */
class Payment extends Model
{
    use HasFactory, HasUuids;

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return \Database\Factories\PaymentFactory::new();
    }

    protected $fillable = [
        'order_id',
        'user_delivery_id',
        'subscription_id',
        'user_id',
        'business_id',
        'business_branch_id',
        'amount',
        'currency',
        'gateway',
        'gateway_reference',
        'payment_method_id',
        'status',
        'transaction_fee',
        'metadata',
        'paid_at',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'transaction_fee' => 'decimal:2',
        'gateway' => PaymentGateway::class,
        'status' => PaymentStatus::class,
        'metadata' => 'array',
        'paid_at' => 'datetime',
    ];

    /**
     * Get the order for this payment.
     *
     * @return BelongsTo<Order,Payment>
     */
    public function order(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'order_id');
    }

    /**
     * Get the user delivery for this payment.
     *
     * @return BelongsTo<UserDelivery,Payment>
     */
    public function userDelivery(): BelongsTo
    {
        return $this->belongsTo(UserDelivery::class, 'user_delivery_id');
    }

    /**
     * Get the subscription for this payment.
     *
     * @return BelongsTo<UserSubscription,Payment>
     */
    public function subscription(): BelongsTo
    {
        return $this->belongsTo(UserSubscription::class, 'subscription_id');
    }

    /**
     * Get the user who made the payment.
     *
     * @return BelongsTo<User,Payment>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'user_id');
    }

    /**
     * Get the business receiving the payment.
     *
     * @return BelongsTo<Business,Payment>
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'business_id');
    }

    /**
     * Get the business branch where payment was made.
     *
     * @return BelongsTo<BusinessBranch,Payment>
     */
    public function businessBranch(): BelongsTo
    {
        return $this->belongsTo(BusinessBranch::class, 'business_branch_id');
    }

    /**
     * Get the payment method used.
     *
     * @return BelongsTo<PaymentMethod,Payment>
     */
    public function paymentMethod(): BelongsTo
    {
        return $this->belongsTo(PaymentMethod::class, 'payment_method_id');
    }

    /**
     * Get the refunds for this payment.
     *
     * @return MorphMany<Payout,Payment>
     */
    public function refunds(): MorphMany
    {
        return $this->morphMany(Payout::class, 'payoutable');
    }

    /**
     * Scope to get completed payments.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', PaymentStatus::PAID);
    }

    /**
     * Scope to get pending payments.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', PaymentStatus::PENDING);
    }

    /**
     * Scope to get failed payments.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFailed($query)
    {
        return $query->where('status', PaymentStatus::FAILED);
    }

    /**
     * Scope to get refunded payments.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRefunded($query)
    {
        return $query->where('status', PaymentStatus::REFUNDED);
    }

    /**
     * Scope to get payments by gateway.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByGateway($query, PaymentGateway $gateway)
    {
        return $query->where('gateway', $gateway);
    }

    /**
     * Scope to get payments for orders.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForOrders($query)
    {
        return $query->whereNotNull('order_id');
    }

    /**
     * Scope to get payments for subscriptions.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForSubscriptions($query)
    {
        return $query->whereNotNull('subscription_id');
    }

    /**
     * Scope to get payments for user deliveries.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForUserDeliveries($query)
    {
        return $query->whereNotNull('user_delivery_id');
    }

    /**
     * Scope to get payments within date range.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithinDateRange($query, string $startDate, string $endDate)
    {
        return $query->whereBetween('created_at', [$startDate, $endDate]);
    }

    /**
     * Check if the payment is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === PaymentStatus::PAID;
    }

    /**
     * Check if the payment is pending.
     */
    public function isPending(): bool
    {
        return $this->status === PaymentStatus::PENDING;
    }

    /**
     * Check if the payment has failed.
     */
    public function hasFailed(): bool
    {
        return $this->status === PaymentStatus::FAILED;
    }

    /**
     * Check if the payment is refunded.
     */
    public function isRefunded(): bool
    {
        return $this->status === PaymentStatus::REFUNDED;
    }

    /**
     * Check if the payment is for an order.
     */
    public function isOrderPayment(): bool
    {
        return ! is_null($this->order_id);
    }

    /**
     * Check if the payment is for a subscription.
     */
    public function isSubscriptionPayment(): bool
    {
        return ! is_null($this->subscription_id);
    }

    /**
     * Check if the payment is for a user delivery.
     */
    public function isUserDeliveryPayment(): bool
    {
        return ! is_null($this->user_delivery_id);
    }

    /**
     * Get the net amount after transaction fees.
     */
    public function getNetAmount(): float
    {
        return $this->amount - $this->transaction_fee;
    }

    /**
     * Get the formatted amount with currency.
     */
    public function getFormattedAmountAttribute(): string
    {
        return $this->currency.' '.number_format($this->amount, 2);
    }

    /**
     * Get the formatted transaction fee with currency.
     */
    public function getFormattedTransactionFeeAttribute(): string
    {
        return $this->currency.' '.number_format($this->transaction_fee, 2);
    }

    /**
     * Get the payment type description.
     */
    public function getPaymentTypeAttribute(): string
    {
        if ($this->isOrderPayment()) {
            return 'Order Payment';
        }

        if ($this->isSubscriptionPayment()) {
            return 'Subscription Payment';
        }

        if ($this->isUserDeliveryPayment()) {
            return 'Delivery Payment';
        }

        return 'Unknown Payment';
    }

    /**
     * Mark the payment as completed.
     */
    public function markAsCompleted(): bool
    {
        return (bool) $this->update([
            'status' => PaymentStatus::PAID,
            'paid_at' => now(),
        ]);
    }

    /**
     * Mark the payment as failed.
     */
    public function markAsFailed(?string $reason = null): bool
    {
        $metadata = $this->metadata ?? [];
        if ($reason) {
            $metadata['failure_reason'] = $reason;
        }

        return (bool) $this->update([
            'status' => PaymentStatus::FAILED,
            'metadata' => $metadata,
        ]);
    }

    /**
     * Mark the payment as refunded.
     */
    public function markAsRefunded(?float $refundAmount = null): bool
    {
        $metadata = $this->metadata ?? [];
        if ($refundAmount) {
            $metadata['refund_amount'] = $refundAmount;
            $metadata['refunded_at'] = now()->toISOString();
        }

        return (bool) $this->update([
            'status' => PaymentStatus::REFUNDED,
            'metadata' => $metadata,
        ]);
    }

    /**
     * Get the refund amount from metadata.
     */
    public function getRefundAmount(): ?float
    {
        return $this->metadata['refund_amount'] ?? null;
    }

    /**
     * Check if payment can be refunded.
     */
    public function canBeRefunded(): bool
    {
        return $this->isCompleted() && ! $this->isRefunded();
    }

    /**
     * Get the gateway transaction URL if available.
     */
    public function getGatewayTransactionUrl(): ?string
    {
        return $this->metadata['transaction_url'] ?? null;
    }

    /**
     * Add metadata to the payment.
     */
    public function addMetadata(array $data): bool
    {
        $metadata = $this->metadata ?? [];
        $metadata = array_merge($metadata, $data);

        return (bool) $this->update(['metadata' => $metadata]);
    }
}
