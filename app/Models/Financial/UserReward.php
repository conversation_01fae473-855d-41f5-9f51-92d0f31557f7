<?php

declare(strict_types=1);

namespace App\Models\Financial;

use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property string $id
 * @property string $user_id
 * @property string $reward_id
 * @property string $source_type referral, promotion, loyalty_program, etc.
 * @property string|null $source_id Polymorphic FK to referrals.id or promotions.id etc.
 * @property numeric $amount_earned The actual value earned (e.g., 500.00 for NGN 500 cash credit)
 * @property string|null $currency Currency of the earned amount (if applicable)
 * @property string $status active, redeemed, expired, cancelled
 * @property \Illuminate\Support\Carbon|null $redeemed_at
 * @property \Illuminate\Support\Carbon|null $expires_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Financial\Reward $reward
 * @property-read Model|\Eloquent|null $source
 * @property-read \App\Models\User\User $user
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward bySourceType(string $sourceType)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward byStatus(string $status)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward expired()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward redeemed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward whereAmountEarned($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward whereRedeemedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward whereRewardId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward whereSourceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward whereSourceType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|UserReward whereUserId($value)
 *
 * @mixin \Eloquent
 */
class UserReward extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'user_id',
        'reward_id',
        'source_type',
        'source_id',
        'amount_earned',
        'currency',
        'status',
        'redeemed_at',
        'expires_at',
    ];

    protected $casts = [
        'amount_earned' => 'decimal:2',
        'redeemed_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the user that owns the reward.
     *
     * @return BelongsTo<User,UserReward>
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'user_id');
    }

    /**
     * Get the reward definition.
     *
     * @return BelongsTo<Reward,UserReward>
     */
    public function reward(): BelongsTo
    {
        return $this->belongsTo(Reward::class, 'reward_id');
    }

    /**
     * Get the polymorphic source (Referral, Promotion, etc.).
     */
    public function source(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope to get active rewards.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    /**
     * Scope to get redeemed rewards.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeRedeemed($query)
    {
        return $query->where('status', 'redeemed');
    }

    /**
     * Scope to get expired rewards.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeExpired($query)
    {
        return $query->where('status', 'expired')
            ->orWhere(function ($query) {
                $query->where('expires_at', '<', now())
                    ->where('status', '!=', 'redeemed');
            });
    }

    /**
     * Scope to get rewards by status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get rewards by source type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeBySourceType($query, string $sourceType)
    {
        return $query->where('source_type', $sourceType);
    }

    /**
     * Check if the reward is expired.
     */
    public function isExpired(): bool
    {
        return $this->expires_at && $this->expires_at->isPast() && $this->status !== 'redeemed';
    }

    /**
     * Check if the reward is redeemable.
     */
    public function isRedeemable(): bool
    {
        return $this->status === 'active' && ! $this->isExpired();
    }
}
