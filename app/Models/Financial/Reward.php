<?php

declare(strict_types=1);

namespace App\Models\Financial;

use App\Enums\Financial\RewardType;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $name
 * @property string|null $description
 * @property mixed $type
 * @property numeric $value Amount for cash_credit/discount, or number of points
 * @property string|null $currency Required if type is cash_credit/discount_coupon (e.g., NGN)
 * @property int|null $usage_limit_per_user
 * @property int|null $total_usage_limit
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon $starts_at
 * @property \Illuminate\Support\Carbon|null $expires_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Financial\UserReward> $userRewards
 * @property-read int|null $user_rewards_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereExpiresAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereName($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereStartsAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereTotalUsageLimit($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereUsageLimitPerUser($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Reward whereValue($value)
 *
 * @mixin \Eloquent
 */
class Reward extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'name',
        'description',
        'type',
        'value',
        'currency',
        'usage_limit_per_user',
        'total_usage_limit',
        'is_active',
        'starts_at',
        'expires_at',
    ];

    protected $casts = [
        'type' => RewardType::class,
        'value' => 'decimal:2',
        'usage_limit_per_user' => 'integer',
        'total_usage_limit' => 'integer',
        'is_active' => 'boolean',
        'starts_at' => 'datetime',
        'expires_at' => 'datetime',
    ];

    /**
     * Get the user rewards for this reward.
     *
     * @return HasMany<UserReward,Reward>
     */
    public function userRewards(): HasMany
    {
        return $this->hasMany(UserReward::class, 'reward_id');
    }

    /**
     * Scope to get only active rewards.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->where(function ($q) {
                $q->whereNull('expires_at')->orWhere('expires_at', '>', now());
            });
    }

    /**
     * Check if the reward is currently active.
     */
    public function isActive(): bool
    {
        if (! $this->is_active) {
            return false;
        }
        if ($this->starts_at && $this->starts_at->isFuture()) {
            return false;
        }
        if ($this->expires_at && $this->expires_at->isPast()) {
            return false;
        }

        return true;
    }

    /**
     * Get the formatted value with currency.
     */
    public function getFormattedValue(): string
    {
        if ($this->currency) {
            return $this->currency.' '.number_format($this->value, 2);
        }

        return (string) $this->value;
    }

    /**
     * Get the total number of times this reward has been used.
     */
    public function getTotalUsageCount(): int
    {
        return $this->userRewards()->count();
    }

    /**
     * Get the number of times a user has used this reward.
     */
    public function getUserUsageCount(string $userId): int
    {
        return $this->userRewards()->where('user_id', $userId)->count();
    }

    /**
     * Check if a user can use this reward.
     */
    public function canBeUsedByUser(string $userId): bool
    {
        if (! $this->isActive()) {
            return false;
        }
        if ($this->usage_limit_per_user !== null && $this->getUserUsageCount($userId) >= $this->usage_limit_per_user) {
            return false;
        }
        if ($this->total_usage_limit !== null && $this->getTotalUsageCount() >= $this->total_usage_limit) {
            return false;
        }

        return true;
    }
}
