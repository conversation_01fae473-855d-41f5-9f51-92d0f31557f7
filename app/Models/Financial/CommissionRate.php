<?php

declare(strict_types=1);

namespace App\Models\Financial;

use App\Enums\Financial\CommissionRateType;
use App\Models\Business\Business;
use App\Models\Delivery\DeliveryProvider;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string|null $tenant_id FK to tenants.id, nullable for platform-wide rates
 * @property string|null $business_id
 * @property string|null $provider_id
 * @property mixed $rate_type Type of commission
 * @property numeric $rate_percentage Commission rate as percentage (e.g., 0.015 for 1.5%)
 * @property numeric $flat_fee Flat fee component
 * @property numeric $minimum_fee Minimum commission amount
 * @property numeric|null $maximum_fee Maximum commission amount
 * @property string $currency Currency for fees
 * @property array<array-key, mixed>|null $conditions Conditions for applying this rate
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon $effective_from When this rate becomes effective
 * @property \Illuminate\Support\Carbon|null $effective_until When this rate expires
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Financial\CommissionCalculation> $calculations
 * @property-read int|null $calculations_count
 * @property-read \App\Models\System\Tenant|null $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionRate newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionRate newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionRate query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionRate whereBusinessId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionRate whereConditions($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionRate whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionRate whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionRate whereEffectiveFrom($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionRate whereEffectiveUntil($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionRate whereFlatFee($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionRate whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionRate whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionRate whereMaximumFee($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionRate whereMinimumFee($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionRate whereProviderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionRate whereRatePercentage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionRate whereRateType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionRate whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|CommissionRate whereUpdatedAt($value)
 *
 * @property-read Business|null $business
 * @property-read DeliveryProvider|null $provider
 *
 * @mixin \Eloquent
 */
class CommissionRate extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'business_id',
        'provider_id',
        'rate_type',
        'rate_percentage',
        'flat_fee',
        'minimum_fee',
        'maximum_fee',
        'currency',
        'conditions',
        'is_active',
        'effective_from',
        'effective_until',
    ];

    protected $casts = [
        'rate_type' => CommissionRateType::class,
        'rate_percentage' => 'decimal:4',
        'flat_fee' => 'decimal:2',
        'minimum_fee' => 'decimal:2',
        'maximum_fee' => 'decimal:2',
        'conditions' => 'array',
        'is_active' => 'boolean',
        'effective_from' => 'date',
        'effective_until' => 'date',
    ];

    /**
     * Get the tenant that owns the commission rate.
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the business associated with the commission rate.
     */
    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class);
    }

    /**
     * Get the delivery provider associated with the commission rate.
     */
    public function provider(): BelongsTo
    {
        return $this->belongsTo(DeliveryProvider::class, 'provider_id');
    }

    /**
     * Get the commission calculations for this rate.
     */
    public function calculations(): HasMany
    {
        return $this->hasMany(CommissionCalculation::class);
    }
}
