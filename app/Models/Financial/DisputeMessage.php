<?php

declare(strict_types=1);

namespace App\Models\Financial;

use App\Models\System\Tenant;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property string $id
 * @property string|null $tenant_id FK to tenants.id, nullable - if message is inter-tenant or platform-level
 * @property string $dispute_id
 * @property string $sender_id Polymorphic FK
 * @property string $sender_type users, businesses, delivery_providers, platform
 * @property string $message
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\Financial\Dispute $dispute
 * @property-read string $sender_display_name
 * @property-read string $sender_type_display_name
 * @property-read Model|\Eloquent $sender
 * @property-read \App\Models\System\Tenant|null $tenant
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DisputeMessage bySenderType(string $senderType)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DisputeMessage forDispute(string $disputeId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DisputeMessage newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DisputeMessage newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DisputeMessage orderedByDate(string $direction = 'asc')
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DisputeMessage query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DisputeMessage whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DisputeMessage whereDisputeId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DisputeMessage whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DisputeMessage whereMessage($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DisputeMessage whereSenderId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DisputeMessage whereSenderType($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DisputeMessage whereTenantId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|DisputeMessage whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class DisputeMessage extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'tenant_id',
        'dispute_id',
        'sender_id',
        'sender_type',
        'message',
    ];

    /**
     * Get the tenant that owns the dispute message.
     *
     * @return BelongsTo<Tenant,DisputeMessage>
     */
    public function tenant(): BelongsTo
    {
        return $this->belongsTo(\App\Models\System\Tenant::class, 'tenant_id');
    }

    /**
     * Get the dispute that owns this message.
     *
     * @return BelongsTo<Dispute,DisputeMessage>
     */
    public function dispute(): BelongsTo
    {
        return $this->belongsTo(Dispute::class, 'dispute_id');
    }

    /**
     * Get the sender model (polymorphic relationship).
     */
    public function sender(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Scope to get messages by dispute.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForDispute($query, string $disputeId)
    {
        return $query->where('dispute_id', $disputeId);
    }

    /**
     * Scope to get messages by sender type.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeBySenderType($query, string $senderType)
    {
        return $query->where('sender_type', $senderType);
    }

    /**
     * Scope to get messages ordered by creation date.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOrderedByDate($query, string $direction = 'asc')
    {
        return $query->orderBy('created_at', $direction);
    }

    /**
     * Check if the message is from platform admin.
     */
    public function isFromPlatform(): bool
    {
        return $this->sender_type === 'platform';
    }

    /**
     * Check if the message is from a user.
     */
    public function isFromUser(): bool
    {
        return $this->sender_type === 'users';
    }

    /**
     * Check if the message is from a business.
     */
    public function isFromBusiness(): bool
    {
        return $this->sender_type === 'businesses';
    }

    /**
     * Check if the message is from a delivery provider.
     */
    public function isFromProvider(): bool
    {
        return $this->sender_type === 'delivery_providers';
    }

    /**
     * Get the sender display name.
     */
    public function getSenderDisplayNameAttribute(): string
    {
        if ($this->isFromPlatform()) {
            return 'Platform Support';
        }

        return $this->sender?->name ?? 'Unknown Sender';
    }

    /**
     * Get the sender type display name.
     */
    public function getSenderTypeDisplayNameAttribute(): string
    {
        return match ($this->sender_type) {
            'users' => 'Customer',
            'businesses' => 'Business',
            'delivery_providers' => 'Delivery Provider',
            'platform' => 'Platform Support',
            default => 'Unknown',
        };
    }

    /**
     * Get message summary for API responses.
     */
    public function getSummary(): array
    {
        return [
            'id' => $this->id,
            'dispute_id' => $this->dispute_id,
            'sender_id' => $this->sender_id,
            'sender_type' => $this->sender_type,
            'sender_display_name' => $this->sender_display_name,
            'sender_type_display_name' => $this->sender_type_display_name,
            'message' => $this->message,
            'created_at' => $this->created_at,
            'is_from_platform' => $this->isFromPlatform(),
        ];
    }
}
