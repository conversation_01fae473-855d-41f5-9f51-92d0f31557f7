<?php

declare(strict_types=1);

namespace App\Models\Financial;

use App\Enums\Financial\ReferralStatus;
use App\Models\User\User;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property string $id
 * @property string $referrer_user_id
 * @property string|null $referred_user_id
 * @property string $referral_code The code used for tracking
 * @property mixed $status
 * @property string|null $reward_id
 * @property \Illuminate\Support\Carbon|null $reward_granted_at When the reward was processed/granted
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User\User|null $referred
 * @property-read \App\Models\User\User $referrer
 * @property-read \App\Models\Financial\Reward|null $reward
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Referral byStatus(\App\Enums\ReferralStatus $status)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Referral completed()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Referral forReferred(string $userId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Referral forReferrer(string $userId)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Referral newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Referral newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Referral pending()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Referral query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Referral whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Referral whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Referral whereReferralCode($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Referral whereReferredUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Referral whereReferrerUserId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Referral whereRewardGrantedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Referral whereRewardId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Referral whereStatus($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|Referral whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class Referral extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'referrer_user_id',
        'referred_user_id',
        'referral_code',
        'status',
        'reward_id',
        'reward_granted_at',
    ];

    protected $casts = [
        'status' => ReferralStatus::class,
        'reward_granted_at' => 'datetime',
    ];

    /**
     * Get the user who made the referral.
     *
     * @return BelongsTo<User,Referral>
     */
    public function referrer(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'referrer_user_id');
    }

    /**
     * Get the user who was referred.
     *
     * @return BelongsTo<User,Referral>
     */
    public function referred(): BelongsTo
    {
        return $this->belongsTo(\App\Models\User\User::class, 'referred_user_id');
    }

    /**
     * Get the reward associated with this referral.
     *
     * @return BelongsTo<Reward,Referral>
     */
    public function reward(): BelongsTo
    {
        return $this->belongsTo(Reward::class, 'reward_id');
    }

    /**
     * Scope to get referrals by status.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByStatus($query, ReferralStatus $status)
    {
        return $query->where('status', $status);
    }

    /**
     * Scope to get pending referrals.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePending($query)
    {
        return $query->where('status', ReferralStatus::PENDING);
    }

    /**
     * Scope to get completed referrals.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', ReferralStatus::SUCCESSFUL);
    }

    /**
     * Scope to get referrals for a specific referrer.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForReferrer($query, string $userId)
    {
        return $query->where('referrer_user_id', $userId);
    }

    /**
     * Scope to get referrals for a specific referred user.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeForReferred($query, string $userId)
    {
        return $query->where('referred_user_id', $userId);
    }

    /**
     * Check if the referral is pending.
     */
    public function isPending(): bool
    {
        return $this->status === ReferralStatus::PENDING;
    }

    /**
     * Check if the referral is completed.
     */
    public function isCompleted(): bool
    {
        return $this->status === ReferralStatus::SUCCESSFUL;
    }

    /**
     * Check if the reward has been granted.
     */
    public function isRewardGranted(): bool
    {
        return ! is_null($this->reward_granted_at);
    }
}
