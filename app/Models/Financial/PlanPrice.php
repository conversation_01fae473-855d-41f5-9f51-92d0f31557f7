<?php

declare(strict_types=1);

namespace App\Models\Financial;

use App\Enums\Financial\BillingInterval;
use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property string $id
 * @property string $plan_id
 * @property mixed $billing_interval
 * @property numeric $price
 * @property string $currency e.g., NGN, USD
 * @property bool $is_active
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $formatted_price
 * @property-read \App\Models\Financial\SubscriptionPlan $plan
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Financial\PlanPriceHistory> $priceHistory
 * @property-read int|null $price_history_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, \App\Models\Financial\UserSubscription> $userSubscriptions
 * @property-read int|null $user_subscriptions_count
 *
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPrice active()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPrice byCurrency(string $currency)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPrice byInterval(\App\Enums\BillingInterval $interval)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPrice newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPrice newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPrice query()
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPrice whereBillingInterval($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPrice whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPrice whereCurrency($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPrice whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPrice whereIsActive($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPrice wherePlanId($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPrice wherePrice($value)
 * @method static \Illuminate\Database\Eloquent\Builder<static>|PlanPrice whereUpdatedAt($value)
 *
 * @mixin \Eloquent
 */
class PlanPrice extends Model
{
    use HasFactory, HasUuids;

    protected $fillable = [
        'plan_id',
        'billing_interval',
        'price',
        'currency',
        'is_active',
    ];

    protected $casts = [
        'billing_interval' => BillingInterval::class,
        'price' => 'decimal:2',
        'is_active' => 'boolean',
    ];

    /**
     * Get the subscription plan that owns the plan price.
     *
     * @return BelongsTo<SubscriptionPlan,PlanPrice>
     */
    public function plan(): BelongsTo
    {
        return $this->belongsTo(SubscriptionPlan::class, 'plan_id');
    }

    /**
     * Get the price history records for this plan price.
     *
     * @return HasMany<PlanPriceHistory,PlanPrice>
     */
    public function priceHistory(): HasMany
    {
        return $this->hasMany(PlanPriceHistory::class, 'plan_price_id');
    }

    /**
     * Get the user subscriptions using this plan price.
     *
     * @return HasMany<UserSubscription,PlanPrice>
     */
    public function userSubscriptions(): HasMany
    {
        return $this->hasMany(UserSubscription::class, 'plan_price_id');
    }

    /**
     * Scope to get only active plan prices.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * Scope to get plan prices by billing interval.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByInterval($query, BillingInterval $interval)
    {
        return $query->where('billing_interval', $interval);
    }

    /**
     * Scope to get plan prices by currency.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeByCurrency($query, string $currency)
    {
        return $query->where('currency', $currency);
    }

    /**
     * Get the formatted price with currency.
     */
    public function getFormattedPriceAttribute(): string
    {
        return $this->currency.' '.number_format($this->price, 2);
    }

    /**
     * Get the price per month equivalent.
     */
    public function getMonthlyEquivalentPrice(): float
    {
        return match ($this->billing_interval) {
            BillingInterval::MONTHLY => $this->price,
            BillingInterval::QUARTERLY => $this->price / 3,
            BillingInterval::ANNUALLY => $this->price / 12,
            default => $this->price,
        };
    }
}
