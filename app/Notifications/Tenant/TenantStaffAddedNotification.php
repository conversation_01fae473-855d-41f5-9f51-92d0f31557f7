<?php

declare(strict_types=1);

namespace App\Notifications\Tenant;

use App\Models\Business\Business;
use App\Models\Delivery\DeliveryProvider;
use App\Models\User\User;
use App\Services\System\DomainService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

/**
 * Unified Tenant Staff Added Notification
 *
 * Handles staff addition notifications for both businesses and delivery providers.
 * Replaces separate StaffAddedNotification and ProviderStaffAddedNotification.
 */
class TenantStaffAddedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        private readonly User $staffMember,
        private readonly Business|DeliveryProvider $tenant,
        private readonly User $addedBy,
        private readonly string $role,
        private readonly string $temporaryPassword
    ) {}

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $tenantType = $this->tenant instanceof Business ? 'business' : 'provider';
        $tenantName = $this->getTenantName();
        $loginUrl = $this->getLoginUrl($tenantType);
        $portalName = $this->getPortalName($tenantType);

        return (new MailMessage)
            ->subject("Welcome to {$tenantName} Team")
            ->greeting("Hello {$this->staffMember->first_name}!")
            ->line("You have been added as a {$this->role} to {$tenantName} by {$this->addedBy->full_name}.")
            ->line('Here are your login credentials:')
            ->line("**Email:** {$this->staffMember->email}")
            ->line("**Temporary Password:** {$this->temporaryPassword}")
            ->line('**Important:** Please change your password after your first login for security.')
            ->action("Login to {$portalName}", $loginUrl)
            ->line('If you have any questions, please contact your team administrator.')
            ->salutation("Welcome to the {$tenantName} team!");
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        $tenantType = $this->tenant instanceof Business ? 'business' : 'provider';
        $tenantName = $this->getTenantName();
        $loginUrl = $this->getLoginUrl($tenantType);
        $portalName = $this->getPortalName($tenantType);

        return [
            'title' => "Added to {$this->getEntityLabel()} Team",
            'message' => "You have been added as a {$this->role} to {$tenantName} by {$this->addedBy->full_name}. Please check your email for login credentials.",
            'type' => 'tenant_staff_added',
            'tenant_type' => $tenantType,
            'tenant' => [
                'id' => $this->tenant->id,
                'name' => $tenantName,
                'type' => $tenantType,
            ],
            'added_by' => [
                'id' => $this->addedBy->id,
                'name' => $this->addedBy->full_name,
                'email' => $this->addedBy->email,
            ],
            'role' => $this->role,
            'action_url' => $loginUrl,
            'action_text' => "Login to {$portalName}",
        ];
    }

    /**
     * Get tenant name
     */
    private function getTenantName(): string
    {
        return $this->tenant->name ?? $this->tenant->company_name ?? $this->tenant->business_name ?? 'Unknown';
    }

    /**
     * Get login URL based on tenant type
     */
    private function getLoginUrl(string $tenantType): string
    {
        $domainService = app(DomainService::class);

        // For tenants, use their specific subdomain frontend
        if ($this->tenant instanceof Business && $this->tenant->subdomain) {
            return $domainService->getTenantFrontendUrl($this->tenant->subdomain) . '/login';
        }

        if ($this->tenant instanceof DeliveryProvider && $this->tenant->subdomain) {
            return $domainService->getTenantFrontendUrl($this->tenant->subdomain) . '/login';
        }

        // Fallback to customer app
        return $domainService->getCustomerAppUrl() . '/login';
    }

    /**
     * Get portal name based on tenant type
     */
    private function getPortalName(string $tenantType): string
    {
        return match ($tenantType) {
            'business' => 'Business Portal',
            'provider' => 'Provider Portal',
            default => 'Portal'
        };
    }

    /**
     * Get entity label based on tenant type
     */
    private function getEntityLabel(): string
    {
        return $this->tenant instanceof Business ? 'Business' : 'Provider';
    }
}
