<?php

declare(strict_types=1);

namespace App\Notifications\Delivery;

use App\Models\AdHocDelivery;
use App\Models\User\User;
use App\Services\Communication\NotificationDecisionEngine;
use App\Services\System\DomainService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;
use NotificationChannels\Fcm\FcmMessage;
use NotificationChannels\Fcm\Resources\Notification as FcmNotification;

/**
 * Ad-Hoc Delivery Created Notification
 */
class AdHocDeliveryCreatedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public readonly AdHocDelivery $delivery,
        public readonly User $customer,
        public readonly string $pickupAddress,
        public readonly string $deliveryAddress,
        public readonly float $estimatedDistance,
        public readonly float $estimatedCost,
        public readonly string $urgency = 'standard'
    ) {}

    public function via(object $notifiable): array
    {
        $engine = app(NotificationDecisionEngine::class);

        return $engine->getChannels('adhoc_delivery_created', $notifiable);
    }

    public function toMail(object $notifiable): MailMessage
    {
        $urgencyText = match ($this->urgency) {
            'urgent' => 'Urgent',
            'express' => 'Express',
            default => 'Standard'
        };

        return (new MailMessage)
            ->subject("🚚 Ad-Hoc Delivery Request Created - {$urgencyText}")
            ->greeting("Hello {$notifiable->name}!")
            ->line("Your ad-hoc delivery request has been successfully created and we're finding the best driver for you.")
            ->line('**Delivery Details:**')
            ->line("• **Pickup:** {$this->pickupAddress}")
            ->line("• **Delivery:** {$this->deliveryAddress}")
            ->line("• **Distance:** {$this->estimatedDistance} km")
            ->line("• **Estimated Cost:** ₦{$this->estimatedCost}")
            ->line("• **Priority:** {$urgencyText}")
            ->when($this->urgency === 'urgent', function ($mail) {
                return $mail->line("⚡ **Urgent delivery** - We'll assign a driver within 5 minutes!");
            })
            ->action('Track Delivery', url("/deliveries/{$this->delivery->id}"))
            ->line('You\'ll receive updates as we assign a driver and track your delivery.')
            ->line('**Support:** ' . app(DomainService::class)->getSupportEmail() . ' | +234-800-DELIVERY');
    }

    public function toFcm(object $notifiable): FcmMessage
    {
        $urgencyIcon = match ($this->urgency) {
            'urgent' => '⚡',
            'express' => '🚀',
            default => '🚚'
        };

        return FcmMessage::create()
            ->setData([
                'delivery_id' => $this->delivery->id,
                'customer_id' => $this->customer->id,
                'pickup_address' => $this->pickupAddress,
                'delivery_address' => $this->deliveryAddress,
                'estimated_distance' => $this->estimatedDistance,
                'estimated_cost' => $this->estimatedCost,
                'urgency' => $this->urgency,
                'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
                'route' => "/deliveries/{$this->delivery->id}",
            ])
            ->setNotification(
                FcmNotification::create()
                    ->setTitle("{$urgencyIcon} Delivery Request Created")
                    ->setBody("We're finding a driver for your delivery. Estimated cost: ₦{$this->estimatedCost}")
                    ->setImage(null)
            );
    }

    public function toArray(object $notifiable): array
    {
        return [
            'delivery_id' => $this->delivery->id,
            'customer_id' => $this->customer->id,
            'pickup_address' => $this->pickupAddress,
            'delivery_address' => $this->deliveryAddress,
            'estimated_distance' => $this->estimatedDistance,
            'estimated_cost' => $this->estimatedCost,
            'urgency' => $this->urgency,
            'created_at' => $this->delivery->created_at->toISOString(),
        ];
    }
}
