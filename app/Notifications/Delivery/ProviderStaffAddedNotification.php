<?php

declare(strict_types=1);

namespace App\Notifications\Delivery;

use App\Models\Delivery\DeliveryProvider;
use App\Models\User\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

/**
 * Provider Staff Added Notification
 *
 * Sent when a new staff member is added to a delivery provider.
 */
class ProviderStaffAddedNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        private readonly User $staffMember,
        private readonly DeliveryProvider $provider,
        private readonly User $addedBy,
        private readonly string $role,
        private readonly string $temporaryPassword
    ) {}

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $loginUrl = config('app.tenant_url', 'https://provider.deliverynexus.com').'/login';

        return (new MailMessage)
            ->subject("Welcome to {$this->provider->company_name} Team")
            ->greeting("Hello {$this->staffMember->first_name}!")
            ->line("You have been added as a {$this->role} to {$this->provider->company_name} by {$this->addedBy->full_name}.")
            ->line('Here are your login credentials:')
            ->line("**Email:** {$this->staffMember->email}")
            ->line("**Temporary Password:** {$this->temporaryPassword}")
            ->line('**Important:** Please change your password after your first login for security.')
            ->action('Login to Provider Portal', $loginUrl)
            ->line('If you have any questions, please contact your team administrator.')
            ->salutation("Welcome to the {$this->provider->company_name} team!");
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'title' => 'Added to Provider Team',
            'message' => "You have been added as a {$this->role} to {$this->provider->company_name} by {$this->addedBy->full_name}. Please check your email for login credentials.",
            'type' => 'provider_staff_added',
            'provider' => [
                'id' => $this->provider->id,
                'name' => $this->provider->company_name,
            ],
            'added_by' => [
                'id' => $this->addedBy->id,
                'name' => $this->addedBy->full_name,
                'email' => $this->addedBy->email,
            ],
            'role' => $this->role,
            'action_url' => config('app.tenant_url', 'https://provider.deliverynexus.com').'/login',
            'action_text' => 'Login to Provider Portal',
        ];
    }
}
