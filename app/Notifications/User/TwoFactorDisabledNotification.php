<?php

declare(strict_types=1);

namespace App\Notifications\User;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

/**
 * Two-Factor Authentication Disabled Notification
 *
 * Sent when a user disables 2FA on their account.
 */
class TwoFactorDisabledNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Two-Factor Authentication Disabled')
            ->greeting("Hello {$notifiable->first_name}!")
            ->line('Two-factor authentication has been disabled on your DeliveryNexus account.')
            ->line('Your account security has been reduced. We recommend re-enabling two-factor authentication to keep your account secure.')
            ->line('If you did not disable this feature, please contact our support team immediately and change your password.')
            ->action('Manage Security Settings', url('/profile/security'))
            ->salutation('Best regards, The DeliveryNexus Team');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'title' => 'Two-Factor Authentication Disabled',
            'message' => 'Two-factor authentication has been disabled on your account.',
            'type' => 'security_warning',
            'action_url' => url('/profile/security'),
            'action_text' => 'Manage Security Settings',
        ];
    }
}
