<?php

declare(strict_types=1);

namespace App\Notifications\User;

use App\Models\User\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

/**
 * User Credentials Notification
 *
 * Sent when an admin manually creates a user account and provides login credentials.
 */
class UserCredentialsNotification extends Notification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        private readonly string $temporaryPassword,
        private readonly User $createdBy
    ) {}

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        $loginUrl = config('app.frontend_url', 'https://app.deliverynexus.com').'/login';

        return (new MailMessage)
            ->subject('Your DeliveryNexus Account Has Been Created')
            ->greeting("Hello {$notifiable->first_name}!")
            ->line("An account has been created for you on DeliveryNexus by {$this->createdBy->full_name}.")
            ->line('Here are your login credentials:')
            ->line("**Email:** {$notifiable->email}")
            ->line("**Temporary Password:** {$this->temporaryPassword}")
            ->line('**Important:** Please change your password after your first login for security.')
            ->action('Login to Your Account', $loginUrl)
            ->line('If you have any questions, please contact our support team.')
            ->salutation('Welcome to DeliveryNexus!');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'title' => 'Account Created',
            'message' => "Your DeliveryNexus account has been created by {$this->createdBy->full_name}. Please check your email for login credentials.",
            'type' => 'account_created',
            'created_by' => [
                'id' => $this->createdBy->id,
                'name' => $this->createdBy->full_name,
                'email' => $this->createdBy->email,
            ],
            'action_url' => config('app.frontend_url', 'https://app.deliverynexus.com').'/login',
            'action_text' => 'Login to Account',
        ];
    }
}
