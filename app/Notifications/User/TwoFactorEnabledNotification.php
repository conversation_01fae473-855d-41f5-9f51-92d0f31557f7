<?php

declare(strict_types=1);

namespace App\Notifications\User;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Notifications\Notification;

/**
 * Two-Factor Authentication Enabled Notification
 *
 * Sent when a user successfully enables 2FA on their account.
 */
class TwoFactorEnabledNotification extends Notification implements ShouldQueue
{
    use Queueable;

    /**
     * Get the notification's delivery channels.
     */
    public function via(object $notifiable): array
    {
        return ['mail', 'database'];
    }

    /**
     * Get the mail representation of the notification.
     */
    public function toMail(object $notifiable): MailMessage
    {
        return (new MailMessage)
            ->subject('Two-Factor Authentication Enabled')
            ->greeting("Hello {$notifiable->first_name}!")
            ->line('Two-factor authentication has been successfully enabled on your DeliveryNexus account.')
            ->line('Your account is now more secure. You will need to provide a verification code from your authenticator app when logging in.')
            ->line('If you did not enable this feature, please contact our support team immediately.')
            ->action('Manage Security Settings', url('/profile/security'))
            ->salutation('Best regards, The DeliveryNexus Team');
    }

    /**
     * Get the array representation of the notification.
     */
    public function toArray(object $notifiable): array
    {
        return [
            'title' => 'Two-Factor Authentication Enabled',
            'message' => 'Two-factor authentication has been successfully enabled on your account.',
            'type' => 'security',
            'action_url' => url('/profile/security'),
            'action_text' => 'Manage Security Settings',
        ];
    }
}
