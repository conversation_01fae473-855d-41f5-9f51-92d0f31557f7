<?php

declare(strict_types=1);

use App\Enums\Financial\PaymentStatus;

describe('PaymentStatus Enum', function () {
    describe('Enum Values', function () {
        it('has all expected enum values', function () {
            $expectedValues = [
                'pending',
                'authorized',
                'paid',
                'partially_refunded',
                'refunded',
                'voided',
                'failed',
            ];

            $actualValues = array_column(PaymentStatus::cases(), 'value');

            expect($actualValues)->toBe($expectedValues);
        });

        it('can create enum instances from values', function () {
            expect(PaymentStatus::PENDING->value)->toBe('pending');
            expect(PaymentStatus::AUTHORIZED->value)->toBe('authorized');
            expect(PaymentStatus::PAID->value)->toBe('paid');
            expect(PaymentStatus::PARTIALLY_REFUNDED->value)->toBe('partially_refunded');
            expect(PaymentStatus::REFUNDED->value)->toBe('refunded');
            expect(PaymentStatus::VOIDED->value)->toBe('voided');
            expect(PaymentStatus::FAILED->value)->toBe('failed');
        });
    });

    describe('Backward Compatibility', function () {
        it('provides COMPLETED alias for PAID status', function () {
            expect(PaymentStatus::COMPLETED())->toBe(PaymentStatus::PAID);
        });
    });

    describe('Payment Status Checks', function () {
        it('correctly identifies successful payments', function () {
            expect(PaymentStatus::PAID->isSuccessful())->toBeTrue();

            expect(PaymentStatus::PENDING->isSuccessful())->toBeFalse();
            expect(PaymentStatus::AUTHORIZED->isSuccessful())->toBeFalse();
            expect(PaymentStatus::PARTIALLY_REFUNDED->isSuccessful())->toBeFalse();
            expect(PaymentStatus::REFUNDED->isSuccessful())->toBeFalse();
            expect(PaymentStatus::VOIDED->isSuccessful())->toBeFalse();
            expect(PaymentStatus::FAILED->isSuccessful())->toBeFalse();
        });

        it('correctly identifies refundable payments', function () {
            expect(PaymentStatus::PAID->canBeRefunded())->toBeTrue();

            expect(PaymentStatus::PENDING->canBeRefunded())->toBeFalse();
            expect(PaymentStatus::AUTHORIZED->canBeRefunded())->toBeFalse();
            expect(PaymentStatus::PARTIALLY_REFUNDED->canBeRefunded())->toBeFalse();
            expect(PaymentStatus::REFUNDED->canBeRefunded())->toBeFalse();
            expect(PaymentStatus::VOIDED->canBeRefunded())->toBeFalse();
            expect(PaymentStatus::FAILED->canBeRefunded())->toBeFalse();
        });

        it('correctly identifies final payment states', function () {
            $finalStates = [
                PaymentStatus::PAID,
                PaymentStatus::REFUNDED,
                PaymentStatus::VOIDED,
                PaymentStatus::FAILED,
            ];

            foreach ($finalStates as $status) {
                expect($status->isFinal())->toBeTrue();
            }

            $nonFinalStates = [
                PaymentStatus::PENDING,
                PaymentStatus::AUTHORIZED,
                PaymentStatus::PARTIALLY_REFUNDED,
            ];

            foreach ($nonFinalStates as $status) {
                expect($status->isFinal())->toBeFalse();
            }
        });
    });

    describe('Payment State Categories', function () {
        it('categorizes pending states', function () {
            $pendingStates = [
                PaymentStatus::PENDING,
                PaymentStatus::AUTHORIZED,
            ];

            foreach ($pendingStates as $status) {
                expect($status->isFinal())->toBeFalse();
                expect($status->isSuccessful())->toBeFalse();
            }
        });

        it('categorizes successful states', function () {
            $successfulStates = [
                PaymentStatus::PAID,
                PaymentStatus::PARTIALLY_REFUNDED, // Still considered successful as original payment went through
            ];

            foreach ($successfulStates as $status) {
                // PAID is successful, PARTIALLY_REFUNDED is a special case
                if ($status === PaymentStatus::PAID) {
                    expect($status->isSuccessful())->toBeTrue();
                }
            }
        });

        it('categorizes failed states', function () {
            $failedStates = [
                PaymentStatus::FAILED,
                PaymentStatus::VOIDED,
            ];

            foreach ($failedStates as $status) {
                expect($status->isFinal())->toBeTrue();
                expect($status->isSuccessful())->toBeFalse();
                expect($status->canBeRefunded())->toBeFalse();
            }
        });

        it('categorizes refund-related states', function () {
            $refundStates = [
                PaymentStatus::PARTIALLY_REFUNDED,
                PaymentStatus::REFUNDED,
            ];

            foreach ($refundStates as $status) {
                expect(str_contains($status->value, 'refund'))->toBeTrue();
            }
        });
    });

    describe('Business Logic Validation', function () {
        it('ensures mutually exclusive status properties', function () {
            foreach (PaymentStatus::cases() as $status) {
                // A payment cannot be both successful and failed
                if ($status->isSuccessful()) {
                    expect($status)->not->toBe(PaymentStatus::FAILED);
                    expect($status)->not->toBe(PaymentStatus::VOIDED);
                }

                // Final states should not be changeable
                if ($status->isFinal()) {
                    // In a real implementation, you might check:
                    // expect($status->canTransitionTo(PaymentStatus::PENDING))->toBeFalse();
                }
            }
        });

        it('validates refund logic', function () {
            // Only PAID payments can be refunded
            expect(PaymentStatus::PAID->canBeRefunded())->toBeTrue();

            // Refunded payments cannot be refunded again
            expect(PaymentStatus::REFUNDED->canBeRefunded())->toBeFalse();
            expect(PaymentStatus::PARTIALLY_REFUNDED->canBeRefunded())->toBeFalse();

            // Failed/voided payments cannot be refunded
            expect(PaymentStatus::FAILED->canBeRefunded())->toBeFalse();
            expect(PaymentStatus::VOIDED->canBeRefunded())->toBeFalse();
        });

        it('validates authorization flow', function () {
            // AUTHORIZED is a transitional state
            expect(PaymentStatus::AUTHORIZED->isFinal())->toBeFalse();
            expect(PaymentStatus::AUTHORIZED->isSuccessful())->toBeFalse();
            expect(PaymentStatus::AUTHORIZED->canBeRefunded())->toBeFalse();
        });
    });

    describe('Payment Flow Validation', function () {
        it('identifies valid payment progression', function () {
            // Typical payment flow: PENDING -> AUTHORIZED -> PAID
            $typicalFlow = [
                PaymentStatus::PENDING,
                PaymentStatus::AUTHORIZED,
                PaymentStatus::PAID,
            ];

            // Each step should be logically valid
            expect($typicalFlow[0]->isFinal())->toBeFalse(); // PENDING is not final
            expect($typicalFlow[1]->isFinal())->toBeFalse(); // AUTHORIZED is not final
            expect($typicalFlow[2]->isFinal())->toBeTrue();  // PAID is final
            expect($typicalFlow[2]->isSuccessful())->toBeTrue(); // PAID is successful
        });

        it('identifies refund progression', function () {
            // Refund flow: PAID -> PARTIALLY_REFUNDED -> REFUNDED
            expect(PaymentStatus::PAID->canBeRefunded())->toBeTrue();
            expect(PaymentStatus::PARTIALLY_REFUNDED->isFinal())->toBeFalse();
            expect(PaymentStatus::REFUNDED->isFinal())->toBeTrue();
        });

        it('identifies failure scenarios', function () {
            // Failure can happen from any non-final state
            $nonFinalStates = [
                PaymentStatus::PENDING,
                PaymentStatus::AUTHORIZED,
                PaymentStatus::PARTIALLY_REFUNDED,
            ];

            foreach ($nonFinalStates as $status) {
                expect($status->isFinal())->toBeFalse();
                // In real implementation: expect($status->canTransitionTo(PaymentStatus::FAILED))->toBeTrue();
            }
        });
    });

    describe('Edge Cases', function () {
        it('handles partially refunded status correctly', function () {
            // PARTIALLY_REFUNDED is a special case
            expect(PaymentStatus::PARTIALLY_REFUNDED->isFinal())->toBeFalse(); // Can still be fully refunded
            expect(PaymentStatus::PARTIALLY_REFUNDED->isSuccessful())->toBeFalse(); // Not considered successful
            expect(PaymentStatus::PARTIALLY_REFUNDED->canBeRefunded())->toBeFalse(); // Cannot be refunded again
        });

        it('handles voided status correctly', function () {
            // VOIDED is different from FAILED - it's an administrative action
            expect(PaymentStatus::VOIDED->isFinal())->toBeTrue();
            expect(PaymentStatus::VOIDED->isSuccessful())->toBeFalse();
            expect(PaymentStatus::VOIDED->canBeRefunded())->toBeFalse();
        });

        it('validates authorized status behavior', function () {
            // AUTHORIZED means payment is approved but not yet captured
            expect(PaymentStatus::AUTHORIZED->isFinal())->toBeFalse();
            expect(PaymentStatus::AUTHORIZED->isSuccessful())->toBeFalse();
            expect(PaymentStatus::AUTHORIZED->canBeRefunded())->toBeFalse();
        });
    });
});
