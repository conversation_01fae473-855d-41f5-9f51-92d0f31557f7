<?php

declare(strict_types=1);

use App\Enums\Delivery\DeliveryStatus;

describe('DeliveryStatus Enum', function () {
    describe('Enum Values', function () {
        it('has all expected enum values', function () {
            $expectedValues = [
                'pending_acceptance',
                'accepted',
                'pickup_scheduled',
                'at_pickup_location',
                'picked_up',
                'en_route',
                'at_delivery_location',
                'delivered',
                'delivery_failed',
                'cancelled',
            ];

            $actualValues = array_column(DeliveryStatus::cases(), 'value');

            expect($actualValues)->toBe($expectedValues);
        });

        it('can create enum instances from values', function () {
            expect(DeliveryStatus::PENDING_ACCEPTANCE->value)->toBe('pending_acceptance');
            expect(DeliveryStatus::ACCEPTED->value)->toBe('accepted');
            expect(DeliveryStatus::PICKUP_SCHEDULED->value)->toBe('pickup_scheduled');
            expect(DeliveryStatus::AT_PICKUP_LOCATION->value)->toBe('at_pickup_location');
            expect(DeliveryStatus::PICKED_UP->value)->toBe('picked_up');
            expect(DeliveryStatus::EN_ROUTE->value)->toBe('en_route');
            expect(DeliveryStatus::AT_DELIVERY_LOCATION->value)->toBe('at_delivery_location');
            expect(DeliveryStatus::DELIVERED->value)->toBe('delivered');
            expect(DeliveryStatus::DELIVERY_FAILED->value)->toBe('delivery_failed');
            expect(DeliveryStatus::CANCELLED->value)->toBe('cancelled');
        });
    });

    describe('Backward Compatibility Aliases', function () {
        it('provides backward compatibility aliases', function () {
            expect(DeliveryStatus::PENDING())->toBe(DeliveryStatus::PENDING_ACCEPTANCE);
            expect(DeliveryStatus::ASSIGNED())->toBe(DeliveryStatus::ACCEPTED);
            expect(DeliveryStatus::IN_TRANSIT())->toBe(DeliveryStatus::EN_ROUTE);
            expect(DeliveryStatus::FAILED())->toBe(DeliveryStatus::DELIVERY_FAILED);
        });
    });

    describe('Status Checks', function () {
        it('correctly identifies status transition capabilities', function () {
            // Test canTransitionTo method
            expect(DeliveryStatus::PENDING_ACCEPTANCE->canTransitionTo(DeliveryStatus::ACCEPTED))->toBeTrue();
            expect(DeliveryStatus::PENDING_ACCEPTANCE->canTransitionTo(DeliveryStatus::CANCELLED))->toBeTrue();
            expect(DeliveryStatus::PENDING_ACCEPTANCE->canTransitionTo(DeliveryStatus::DELIVERED))->toBeFalse();

            expect(DeliveryStatus::ACCEPTED->canTransitionTo(DeliveryStatus::PICKUP_SCHEDULED))->toBeTrue();
            expect(DeliveryStatus::ACCEPTED->canTransitionTo(DeliveryStatus::CANCELLED))->toBeTrue();
            expect(DeliveryStatus::ACCEPTED->canTransitionTo(DeliveryStatus::DELIVERED))->toBeFalse();

            // Terminal statuses cannot transition
            expect(DeliveryStatus::DELIVERED->canTransitionTo(DeliveryStatus::CANCELLED))->toBeFalse();
            expect(DeliveryStatus::DELIVERY_FAILED->canTransitionTo(DeliveryStatus::ACCEPTED))->toBeFalse();
            expect(DeliveryStatus::CANCELLED->canTransitionTo(DeliveryStatus::ACCEPTED))->toBeFalse();
        });

        it('correctly identifies in-progress deliveries', function () {
            expect(DeliveryStatus::PENDING_ACCEPTANCE->isInProgress())->toBeFalse();

            expect(DeliveryStatus::ACCEPTED->isInProgress())->toBeTrue();
            expect(DeliveryStatus::PICKUP_SCHEDULED->isInProgress())->toBeTrue();
            expect(DeliveryStatus::AT_PICKUP_LOCATION->isInProgress())->toBeTrue();
            expect(DeliveryStatus::PICKED_UP->isInProgress())->toBeTrue();
            expect(DeliveryStatus::EN_ROUTE->isInProgress())->toBeTrue();
            expect(DeliveryStatus::AT_DELIVERY_LOCATION->isInProgress())->toBeTrue();

            expect(DeliveryStatus::DELIVERED->isInProgress())->toBeFalse();
            expect(DeliveryStatus::DELIVERY_FAILED->isInProgress())->toBeFalse();
            expect(DeliveryStatus::CANCELLED->isInProgress())->toBeFalse();
        });

        it('correctly identifies completed deliveries', function () {
            expect(DeliveryStatus::DELIVERED->isCompleted())->toBeTrue();

            expect(DeliveryStatus::PENDING_ACCEPTANCE->isCompleted())->toBeFalse();
            expect(DeliveryStatus::ACCEPTED->isCompleted())->toBeFalse();
            expect(DeliveryStatus::PICKUP_SCHEDULED->isCompleted())->toBeFalse();
            expect(DeliveryStatus::AT_PICKUP_LOCATION->isCompleted())->toBeFalse();
            expect(DeliveryStatus::PICKED_UP->isCompleted())->toBeFalse();
            expect(DeliveryStatus::EN_ROUTE->isCompleted())->toBeFalse();
            expect(DeliveryStatus::AT_DELIVERY_LOCATION->isCompleted())->toBeFalse();
            expect(DeliveryStatus::DELIVERY_FAILED->isCompleted())->toBeFalse();
            expect(DeliveryStatus::CANCELLED->isCompleted())->toBeFalse();
        });

        it('correctly identifies failed deliveries', function () {
            expect(DeliveryStatus::DELIVERY_FAILED->hasFailed())->toBeTrue();
            expect(DeliveryStatus::CANCELLED->hasFailed())->toBeTrue();

            expect(DeliveryStatus::PENDING_ACCEPTANCE->hasFailed())->toBeFalse();
            expect(DeliveryStatus::ACCEPTED->hasFailed())->toBeFalse();
            expect(DeliveryStatus::PICKUP_SCHEDULED->hasFailed())->toBeFalse();
            expect(DeliveryStatus::AT_PICKUP_LOCATION->hasFailed())->toBeFalse();
            expect(DeliveryStatus::PICKED_UP->hasFailed())->toBeFalse();
            expect(DeliveryStatus::EN_ROUTE->hasFailed())->toBeFalse();
            expect(DeliveryStatus::AT_DELIVERY_LOCATION->hasFailed())->toBeFalse();
            expect(DeliveryStatus::DELIVERED->hasFailed())->toBeFalse();
        });
    });

    describe('Status Progression Logic', function () {
        it('validates logical status progression', function () {
            // Test that statuses follow logical progression
            $progressionOrder = [
                DeliveryStatus::PENDING_ACCEPTANCE,
                DeliveryStatus::ACCEPTED,
                DeliveryStatus::PICKUP_SCHEDULED,
                DeliveryStatus::AT_PICKUP_LOCATION,
                DeliveryStatus::PICKED_UP,
                DeliveryStatus::EN_ROUTE,
                DeliveryStatus::AT_DELIVERY_LOCATION,
                DeliveryStatus::DELIVERED,
            ];

            // Each status should be "less than" the next in progression
            for ($i = 0; $i < count($progressionOrder) - 1; $i++) {
                $current = $progressionOrder[$i];
                $next = $progressionOrder[$i + 1];

                // This would require implementing a progression order method
                expect($current->value)->not->toBe($next->value);
            }
        });

        it('identifies terminal statuses', function () {
            $terminalStatuses = [
                DeliveryStatus::DELIVERED,
                DeliveryStatus::DELIVERY_FAILED,
                DeliveryStatus::CANCELLED,
            ];

            foreach ($terminalStatuses as $status) {
                expect($status->isCompleted() || $status->hasFailed())->toBeTrue();
            }

            $nonTerminalStatuses = [
                DeliveryStatus::PENDING_ACCEPTANCE,
                DeliveryStatus::ACCEPTED,
                DeliveryStatus::PICKUP_SCHEDULED,
                DeliveryStatus::AT_PICKUP_LOCATION,
                DeliveryStatus::PICKED_UP,
                DeliveryStatus::EN_ROUTE,
                DeliveryStatus::AT_DELIVERY_LOCATION,
            ];

            foreach ($nonTerminalStatuses as $status) {
                expect($status->isCompleted() || $status->hasFailed())->toBeFalse();
            }
        });
    });

    describe('Status Categories', function () {
        it('categorizes pickup-related statuses', function () {
            $pickupStatuses = [
                DeliveryStatus::PICKUP_SCHEDULED,
                DeliveryStatus::AT_PICKUP_LOCATION,
                DeliveryStatus::PICKED_UP,
            ];

            foreach ($pickupStatuses as $status) {
                $isPickupRelated = str_contains($status->value, 'pickup') || str_contains($status->value, 'picked');
                expect($isPickupRelated)->toBeTrue();
            }
        });

        it('categorizes delivery-related statuses', function () {
            $deliveryStatuses = [
                DeliveryStatus::EN_ROUTE,
                DeliveryStatus::AT_DELIVERY_LOCATION,
                DeliveryStatus::DELIVERED,
                DeliveryStatus::DELIVERY_FAILED,
            ];

            foreach ($deliveryStatuses as $status) {
                $isDeliveryRelated = str_contains($status->value, 'delivery') ||
                                   str_contains($status->value, 'route') ||
                                   str_contains($status->value, 'delivered');
                expect($isDeliveryRelated)->toBeTrue();
            }
        });
    });

    describe('Business Logic Validation', function () {
        it('ensures mutually exclusive status categories', function () {
            // A delivery cannot be both completed and failed
            foreach (DeliveryStatus::cases() as $status) {
                if ($status->isCompleted()) {
                    expect($status->hasFailed())->toBeFalse();
                }
                if ($status->hasFailed()) {
                    expect($status->isCompleted())->toBeFalse();
                }
            }
        });

        it('ensures in-progress statuses are not terminal', function () {
            foreach (DeliveryStatus::cases() as $status) {
                if ($status->isInProgress()) {
                    // In-progress statuses should not be completed or failed
                    expect($status->isCompleted())->toBeFalse();
                    expect($status->hasFailed())->toBeFalse();
                }
            }
        });
    });

    describe('Status Transitions', function () {
        it('identifies valid next statuses for each current status', function () {
            // This would test business logic for valid status transitions
            // For example, from PENDING_ACCEPTANCE, you can only go to ACCEPTED or CANCELLED

            // Test some key transitions
            expect(DeliveryStatus::PENDING_ACCEPTANCE->value)->toBe('pending_acceptance');
            expect(DeliveryStatus::ACCEPTED->value)->toBe('accepted');

            // In a real implementation, you might have a method like:
            // expect(DeliveryStatus::PENDING_ACCEPTANCE->canTransitionTo(DeliveryStatus::ACCEPTED))->toBeTrue();
            // expect(DeliveryStatus::PENDING_ACCEPTANCE->canTransitionTo(DeliveryStatus::DELIVERED))->toBeFalse();
        });
    });
});
