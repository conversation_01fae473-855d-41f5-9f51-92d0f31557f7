<?php

declare(strict_types=1);

use App\Http\Controllers\Api\V1\Tenant\Business\BusinessCategoryController;
use App\Models\Business\Business;
use App\Models\Core\Country;
use App\Models\Core\State;
use App\Models\System\Tenant;
use App\Models\User\User;
use App\Services\Business\BusinessService;
use App\Services\System\LoggingService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Laravel\Sanctum\Sanctum;
use Stancl\Tenancy\Facades\Tenancy;

uses(RefreshDatabase::class);

describe('BusinessCategoryController', function () {
    beforeEach(function () {
        // Create test tenant and initialize tenancy
        $this->tenant = Tenant::create([
            'id' => 'test-category-tenant',
            'name' => 'Test Category Tenant',
            'tenant_type' => 'business',
        ]);

        // Create tenant domain
        $this->tenant->domains()->create([
            'domain' => 'test-category-tenant.localhost',
            'is_primary' => true,
            'verification_status' => \App\Enums\System\DomainVerificationStatus::VERIFIED,
        ]);

        Tenancy::initialize($this->tenant);

        // Set the default domain for testing
        $this->app['config']->set('app.url', 'http://test-category-tenant.localhost');

        // Create test user
        $this->user = User::factory()->create([
            'tenant_id' => $this->tenant->id,
        ]);

        // Create test country and state
        $this->country = Country::firstOrCreate(
            ['code' => 'NG'],
            [
                'name' => 'Nigeria',
                'currency_code' => 'NGN',
                'phone_code' => '234',
                'timezone' => 'Africa/Lagos',
                'is_active' => true,
            ]
        );
        $this->state = State::firstOrCreate(
            ['country_id' => $this->country->id, 'code' => 'LA'],
            ['name' => 'Lagos', 'is_active' => true]
        );

        // Create test business
        $this->business = Business::factory()->create([
            'tenant_id' => $this->tenant->id,
            'user_id' => $this->user->id,
            'country_id' => $this->country->id,
            'state_id' => $this->state->id,
            'business_name' => 'Test Category Business',
            'business_type' => \App\Enums\Business\BusinessType::FOOD,
            'status' => \App\Enums\Business\BusinessStatus::ACTIVE,
        ]);

        // Authenticate user
        Sanctum::actingAs($this->user);

        // Create controller instance
        $this->businessService = app(BusinessService::class);
        $this->loggingService = app(LoggingService::class);
        $this->controller = new BusinessCategoryController($this->businessService, $this->loggingService);
    });

    afterEach(function () {
        Tenancy::end();
    });

    describe('index method', function () {
        it('can retrieve categories list with default parameters', function () {
            $request = new \Illuminate\Http\Request;

            $response = $this->controller->index($request);

            expect($response->getStatusCode())->toBe(200);

            $data = json_decode($response->getContent(), true);
            expect($data['success'])->toBeTrue();
            expect($data['message'])->toBe('Categories retrieved successfully');

            // Check response structure
            expect($data['data'])->toHaveKeys([
                'categories',
                'pagination',
            ]);

            // Check pagination structure
            expect($data['data']['pagination'])->toHaveKeys([
                'current_page',
                'per_page',
                'total',
                'last_page',
            ]);

            // Categories should be an array (empty since no categories created)
            expect($data['data']['categories'])->toBeArray();
            expect(count($data['data']['categories']))->toBe(0);
        });

        it('can retrieve tree structure when requested', function () {
            $request = new \Illuminate\Http\Request(['tree' => true]);

            $response = $this->controller->index($request);

            expect($response->getStatusCode())->toBe(200);

            $data = json_decode($response->getContent(), true);
            expect($data['success'])->toBeTrue();
            expect($data['message'])->toBe('Category tree retrieved successfully');

            // Check tree response structure
            expect($data['data'])->toHaveKeys([
                'tree',
                'total_categories',
            ]);

            expect($data['data']['tree'])->toBeArray();
            expect($data['data']['total_categories'])->toBe(0);
        });

        it('validates pagination parameters', function () {
            $request = new \Illuminate\Http\Request(['per_page' => 150]);

            expect(fn () => $this->controller->index($request))
                ->toThrow(\Illuminate\Validation\ValidationException::class);
        });
    });

    describe('store method', function () {
        it('validates required fields', function () {
            $request = new \Illuminate\Http\Request([]);

            expect(fn () => $this->controller->store($request))
                ->toThrow(\Illuminate\Validation\ValidationException::class);
        });

        it('can create a category with valid data', function () {
            $request = new \Illuminate\Http\Request([
                'name' => 'Test Category',
                'description' => 'A test category',
                'is_active' => true,
                'display_order' => 1,
            ]);

            try {
                $response = $this->controller->store($request);
            } catch (\Exception $e) {
                dump('Exception: '.$e->getMessage());
                dump('Trace: '.$e->getTraceAsString());
                throw $e;
            }

            if ($response->getStatusCode() !== 201) {
                dump('Response status: '.$response->getStatusCode());
                dump('Response content: '.$response->getContent());
            }

            expect($response->getStatusCode())->toBe(201);

            $data = json_decode($response->getContent(), true);
            expect($data['success'])->toBeTrue();
            expect($data['message'])->toBe('Category created successfully');
            expect($data['data']['name'])->toBe('Test Category');
            expect($data['data']['slug'])->toBe('test-category');
            expect($data['data']['is_active'])->toBeTrue();
        });

        it('generates unique slug automatically', function () {
            $request = new \Illuminate\Http\Request([
                'name' => 'Test Category',
                'description' => 'A test category',
            ]);

            $response = $this->controller->store($request);

            expect($response->getStatusCode())->toBe(201);

            $data = json_decode($response->getContent(), true);
            expect($data['data']['slug'])->toBe('test-category');
        });
    });

    describe('show method', function () {
        it('returns 404 for non-existent category', function () {
            $nonExistentId = '019723aa-3202-70dd-a0c1-3565681dd87a';

            $response = $this->controller->show($nonExistentId);

            expect($response->getStatusCode())->toBe(404);

            $data = json_decode($response->getContent(), true);
            expect($data['success'])->toBeFalse();
            expect($data['message'])->toBe('Category not found');
        });
    });

    describe('update method', function () {
        it('returns 404 for non-existent category', function () {
            $nonExistentId = '019723aa-3202-70dd-a0c1-3565681dd87a';
            $request = new \Illuminate\Http\Request(['name' => 'Updated Name']);

            $response = $this->controller->update($request, $nonExistentId);

            expect($response->getStatusCode())->toBe(404);

            $data = json_decode($response->getContent(), true);
            expect($data['success'])->toBeFalse();
            expect($data['message'])->toBe('Category not found');
        });
    });

    describe('destroy method', function () {
        it('returns 404 for non-existent category', function () {
            $nonExistentId = '019723aa-3202-70dd-a0c1-3565681dd87a';

            $response = $this->controller->destroy($nonExistentId);

            expect($response->getStatusCode())->toBe(404);

            $data = json_decode($response->getContent(), true);
            expect($data['success'])->toBeFalse();
            expect($data['message'])->toBe('Category not found');
        });
    });

    describe('error handling', function () {
        it('returns error when no business found', function () {
            // Delete the business
            $this->business->delete();

            $request = new \Illuminate\Http\Request;
            $response = $this->controller->index($request);

            expect($response->getStatusCode())->toBe(422);

            $data = json_decode($response->getContent(), true);
            expect($data['success'])->toBeFalse();
            expect($data['message'])->toBe('No business found for current tenant');
        });
    });
});
