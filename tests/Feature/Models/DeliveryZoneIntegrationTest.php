<?php

declare(strict_types=1);

namespace Tests\Feature\Models;

use App\Enums\Delivery\ServiceScope;
use App\Enums\Delivery\VehicleType;
use App\Enums\Delivery\ZoneType;
use App\Models\Core\State;
use App\Models\Delivery\DeliveryProvider;
use App\Models\Delivery\DeliveryZone;
use App\Models\Delivery\InterstatePricing;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class DeliveryZoneIntegrationTest extends TestCase
{
    use RefreshDatabase;

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_can_create_delivery_zones_with_relationships()
    {
        // Create a state
        $state = State::factory()->create([
            'name' => 'Lagos',
            'code' => 'LA',
        ]);

        // Create a delivery zone
        $zone = DeliveryZone::factory()->create([
            'name' => 'Lagos Island',
            'zone_type' => ZoneType::CITY,
            'states' => [$state->id],
            'cities' => ['Lagos Island', 'Victoria Island', 'Ikoyi'],
            'base_multiplier' => 1.2,
            'is_active' => true,
        ]);

        // Test zone properties
        expect($zone->name)->toBe('Lagos Island');
        expect($zone->zone_type)->toBe(ZoneType::CITY);
        expect($zone->cities)->toContain('Victoria Island');
        expect((float) $zone->base_multiplier)->toBe(1.2);
        expect($zone->is_active)->toBeTrue();
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_can_create_delivery_providers_with_coverage()
    {
        // Create states
        $lagos = State::factory()->create(['name' => 'Lagos', 'code' => 'LA']);
        $abuja = State::factory()->create(['name' => 'FCT', 'code' => 'FC']);

        // Create intrastate provider
        $intrastateProvider = DeliveryProvider::factory()->create([
            'company_name' => 'Lagos Express',
            'service_scope' => ServiceScope::INTRASTATE,
        ]);

        // Create interstate provider
        $interstateProvider = DeliveryProvider::factory()->create([
            'company_name' => 'Nigeria Wide Delivery',
            'service_scope' => ServiceScope::INTERSTATE,
        ]);

        // Test provider properties
        expect($intrastateProvider->service_scope)->toBe(ServiceScope::INTRASTATE);
        expect($intrastateProvider->company_name)->toBe('Lagos Express');

        expect($interstateProvider->service_scope)->toBe(ServiceScope::INTERSTATE);
        expect($interstateProvider->company_name)->toBe('Nigeria Wide Delivery');
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_can_create_interstate_pricing_records()
    {
        // Create states
        $lagos = State::factory()->create(['name' => 'Lagos', 'code' => 'LA']);
        $abuja = State::factory()->create(['name' => 'FCT', 'code' => 'FC']);

        // Create pricing record
        $pricing = InterstatePricing::factory()->create([
            'from_state_id' => $lagos->id,
            'to_state_id' => $abuja->id,
            'vehicle_type' => VehicleType::MOTORCYCLE,
            'base_price' => 15000,
            'price_per_km' => 50,
            'minimum_price' => 10000,
            'maximum_price' => 50000,
            'estimated_distance_km' => 300,
            'delivery_count' => 25,
            'is_active' => true,
        ]);

        // Test pricing properties and relationships
        expect($pricing->fromState)->not->toBeNull();
        expect($pricing->toState)->not->toBeNull();
        expect($pricing->fromState->name)->toBe('Lagos');
        expect($pricing->toState->name)->toBe('FCT');
        expect($pricing->vehicle_type)->toBe(VehicleType::MOTORCYCLE);
        expect((float) $pricing->base_price)->toBe(15000.0);
        expect((float) $pricing->price_per_km)->toBe(50.0);
        expect((float) $pricing->minimum_price)->toBe(10000.0);
        expect((float) $pricing->maximum_price)->toBe(50000.0);
        expect((float) $pricing->estimated_distance_km)->toBe(300.0);
        expect($pricing->delivery_count)->toBe(25);
        expect($pricing->is_active)->toBeTrue();
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_can_query_zones_by_state()
    {
        // Create states
        $lagos = State::factory()->create(['name' => 'Lagos', 'code' => 'LA']);
        $abuja = State::factory()->create(['name' => 'FCT', 'code' => 'FC']);

        // Create zones for different states
        $lagosZone1 = DeliveryZone::factory()->create([
            'name' => 'Lagos Island',
            'states' => [$lagos->id],
            'is_active' => true,
        ]);

        $lagosZone2 = DeliveryZone::factory()->create([
            'name' => 'Lagos Mainland',
            'states' => [$lagos->id],
            'is_active' => true,
        ]);

        $abujaZone = DeliveryZone::factory()->create([
            'name' => 'Abuja Central',
            'states' => [$abuja->id],
            'is_active' => true,
        ]);

        // Test querying zones by checking if they contain the state ID
        $allZones = DeliveryZone::all();
        $lagosZones = $allZones->filter(function ($zone) use ($lagos) {
            return in_array($lagos->id, $zone->states ?? []);
        });
        $abujaZones = $allZones->filter(function ($zone) use ($abuja) {
            return in_array($abuja->id, $zone->states ?? []);
        });

        expect($lagosZones)->toHaveCount(2);
        expect($abujaZones)->toHaveCount(1);
        expect($lagosZones->pluck('name'))->toContain('Lagos Island');
        expect($lagosZones->pluck('name'))->toContain('Lagos Mainland');
        expect($abujaZones->first()->name)->toBe('Abuja Central');
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_can_query_providers_by_service_scope()
    {
        // Create providers with different service scopes
        $intrastateProvider1 = DeliveryProvider::factory()->create([
            'service_scope' => ServiceScope::INTRASTATE,
        ]);

        $intrastateProvider2 = DeliveryProvider::factory()->create([
            'service_scope' => ServiceScope::INTRASTATE,
        ]);

        $interstateProvider = DeliveryProvider::factory()->create([
            'service_scope' => ServiceScope::INTERSTATE,
        ]);

        // Test querying by service scope
        $intrastateProviders = DeliveryProvider::where('service_scope', ServiceScope::INTRASTATE)->get();
        $interstateProviders = DeliveryProvider::where('service_scope', ServiceScope::INTERSTATE)->get();

        expect($intrastateProviders)->toHaveCount(2);
        expect($interstateProviders)->toHaveCount(1);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_can_query_pricing_by_route_and_vehicle()
    {
        // Create states
        $lagos = State::factory()->create(['name' => 'Lagos', 'code' => 'LA']);
        $abuja = State::factory()->create(['name' => 'FCT', 'code' => 'FC']);
        $kano = State::factory()->create(['name' => 'Kano', 'code' => 'KN']);

        // Create pricing records for different routes and vehicles
        $lagosAbujaMotorcycle = InterstatePricing::factory()->create([
            'from_state_id' => $lagos->id,
            'to_state_id' => $abuja->id,
            'vehicle_type' => VehicleType::MOTORCYCLE,
            'base_price' => 15000,
        ]);

        $lagosAbujaTruck = InterstatePricing::factory()->create([
            'from_state_id' => $lagos->id,
            'to_state_id' => $abuja->id,
            'vehicle_type' => VehicleType::TRUCK,
            'base_price' => 45000,
        ]);

        $lagosKanoMotorcycle = InterstatePricing::factory()->create([
            'from_state_id' => $lagos->id,
            'to_state_id' => $kano->id,
            'vehicle_type' => VehicleType::MOTORCYCLE,
            'base_price' => 25000,
        ]);

        // Test querying by route
        $lagosAbujaRoutes = InterstatePricing::where('from_state_id', $lagos->id)
            ->where('to_state_id', $abuja->id)
            ->get();

        expect($lagosAbujaRoutes)->toHaveCount(2);

        // Test querying by vehicle type
        $motorcyclePricing = InterstatePricing::where('vehicle_type', VehicleType::MOTORCYCLE)->get();
        $truckPricing = InterstatePricing::where('vehicle_type', VehicleType::TRUCK)->get();

        expect($motorcyclePricing)->toHaveCount(2);
        expect($truckPricing)->toHaveCount(1);

        // Test specific route and vehicle combination
        $specificPricing = InterstatePricing::where('from_state_id', $lagos->id)
            ->where('to_state_id', $abuja->id)
            ->where('vehicle_type', VehicleType::MOTORCYCLE)
            ->first();

        expect($specificPricing)->not->toBeNull();
        expect((float) $specificPricing->base_price)->toBe(15000.0);
    }

    #[\PHPUnit\Framework\Attributes\Test]
    public function it_can_filter_active_records()
    {
        // Create active and inactive records
        $activeZone = DeliveryZone::factory()->create(['is_active' => true]);
        $inactiveZone = DeliveryZone::factory()->create(['is_active' => false]);

        $activeProvider = DeliveryProvider::factory()->active()->create();
        $inactiveProvider = DeliveryProvider::factory()->inactive()->create();

        $activePricing = InterstatePricing::factory()->create(['is_active' => true]);
        $inactivePricing = InterstatePricing::factory()->create(['is_active' => false]);

        // Test filtering active records
        $activeZones = DeliveryZone::where('is_active', true)->get();
        $activeProviders = DeliveryProvider::active()->get();
        $activePricings = InterstatePricing::where('is_active', true)->get();

        expect($activeZones)->toHaveCount(1);
        expect($activeProviders)->toHaveCount(1);
        expect($activePricings)->toHaveCount(1);

        // Test filtering inactive records
        $inactiveZones = DeliveryZone::where('is_active', false)->get();
        $inactivePricings = InterstatePricing::where('is_active', false)->get();

        expect($inactiveZones)->toHaveCount(1);
        expect($inactivePricings)->toHaveCount(1);
    }
}
